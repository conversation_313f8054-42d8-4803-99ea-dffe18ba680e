<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;06a16d85-5af9-4904-be85-568fee2f6d8e&quot;,&quot;conversations&quot;:{&quot;ead66e99-0218-4df3-b9c3-90be1f1741d9&quot;:{&quot;id&quot;:&quot;ead66e99-0218-4df3-b9c3-90be1f1741d9&quot;,&quot;createdAtIso&quot;:&quot;2025-06-04T01:13:40.673Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-04T01:13:40.673Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0},&quot;06a16d85-5af9-4904-be85-568fee2f6d8e&quot;:{&quot;id&quot;:&quot;06a16d85-5af9-4904-be85-568fee2f6d8e&quot;,&quot;createdAtIso&quot;:&quot;2025-06-04T01:13:40.729Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-06-04T01:13:40.729Z&quot;,&quot;chatHistory&quot;:[],&quot;feedbackStates&quot;:{},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:false,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;2566cb21-2ea8-44bd-9227-4a687d2326ef&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>