package com.inspur.cloud.jtgk.goldwind.billmanage.api;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

import java.util.Map;

import static javax.ws.rs.core.MediaType.APPLICATION_JSON;

@Path("/jfskbillmanageapi")
@Produces(value = APPLICATION_JSON)
public interface JfskBillManageApi {
    @Path("/getdraftsigntradepage")
    @POST
    @ResponseBody
    void getDraftSignTradePage();

    @Path("/jfksBillReceiptPushSettlement")
    @POST
    @ResponseBody
    void jfksBillReceiptPushSettlement();

    //未推送成功的，修改状态1-》0，然后定时任务重新推送
    @Path("/jfksBillReceiptRepush")
    @POST
    @ResponseBody
    Map<String, Object> jfksBillReceiptRepush(@RequestBody Map param);
}
