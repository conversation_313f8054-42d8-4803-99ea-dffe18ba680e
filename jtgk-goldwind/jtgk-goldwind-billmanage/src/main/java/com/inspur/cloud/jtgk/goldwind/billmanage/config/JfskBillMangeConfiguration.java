package com.inspur.cloud.jtgk.goldwind.billmanage.config;

import com.inspur.cloud.jtgk.goldwind.billmanage.api.JfskBillManageApi;
import com.inspur.cloud.jtgk.goldwind.billmanage.service.JfskBillManageService;
import io.iec.edp.caf.rest.RESTEndpoint;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@EntityScan({"com.inspur.cloud.jtgk.goldwind.billmanage.entity"})
@EnableJpaRepositories(basePackages = "com.inspur.cloud.jtgk.goldwind.billmanage.repository")
@ComponentScan({"com.inspur.cloud.jtgk.goldwind.billmanage.*"})
@Configuration
public class JfskBillMangeConfiguration {
    @Bean
    public RESTEndpoint JfskBillManageApi(JfskBillManageApi jfskBillManageApi) {
        return new RESTEndpoint("/jtgk/goldwind/billmanage/v1.0/", jfskBillManageApi);
    }

    @Bean("e1f29dff-0553-6cf0-2d8d-17ca90b56a32")
    @Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)
    public JfskBillManageService JfskBillManageService() {
        return new JfskBillManageService();
    }
}
