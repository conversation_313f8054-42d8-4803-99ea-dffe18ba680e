package com.inspur.cloud.jtgk.goldwind.billmanage.controller;


import com.inspur.cloud.jtgk.goldwind.billmanage.api.JfskBillManageApi;
import com.inspur.cloud.jtgk.goldwind.billmanage.service.JfskBillManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.Map;

/**
 * 待付池
 */
@Controller
public class JfskBillMangeController implements JfskBillManageApi {
    @Autowired
    JfskBillManageService jfskBillManageService;

    @Override
    public void getDraftSignTradePage() {
        jfskBillManageService.getDraftSignTradePage();
    }

    @Override
    public void jfksBillReceiptPushSettlement() {
        jfskBillManageService.BillReceiptPushSettlement();
    }

    @Override
    public Map<String, Object> jfksBillReceiptRepush(Map param) {
        return jfskBillManageService.jfksBillReceiptRepush(param);
    }


}
