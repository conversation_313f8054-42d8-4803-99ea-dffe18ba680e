package com.inspur.cloud.jtgk.goldwind.billmanage.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Timestamp;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "JTGKDRAFTSIGNTRADEPAGE")
public class JfskDraftSignTradePageEntity {
    @Id
    private String ID;

    private String billNo;//	String(30)	交易流水号（新一代唯一流水号）		N
    private String billStatus;//	String(25)	交易状态		N	4.3附录
    private String dwrPe;//	String(255)	处理结果说明		Y
    private String cdNo;//	String(64)	票据（包）号		N
    private String cdRange;//	String(64)	子票区间		Y
    private String subbillendsn;//子票区间止
    private String subbillstartsn;//子票区间起
    BigDecimal cdAmt;//	Decimal(16,2)	票据（包）金额		N
    private String cdType;//String(10)	票据种类		N	AC01:银票 AC02:商票
    Timestamp issDt;//	ISODate	出票日		N	YYYY-MM-DD
    Timestamp dueDt;//	ISODate	到期日		N	YYYY-MM-DD
    Timestamp applyDate;//	ISODate	申请日期		Y	YYYY-MM-DD
    Timestamp sgnDt;//	ISODate	应答日期		N	YYYY-MM-DD
    private String sgnUpMk;//String(10)	应答标识		N	SU00:同意签收 SU01:拒绝签收
    private String reqNote;//	String(255)	申请备注		Y
    private String sgnNote;//String(255)	应答备注		Y

    private String DrawerAcct_acctId;//	String(32)	出票人账户号		N
    private String DrawerAcct_name;//String(64)	出票人账户名称		N
    private String DrawerAcct_bankNum;//	String(16)	出票人开户行行号		N
    private String DrawerAcct_bankName;//String(128)	出票人开户行名称		N

    private String AcptrAcct_acctId;//String(32)	承兑人账户号		Y
    private String AcptrAcct_name;//	String(64)	承兑人账户名称		N
    private String AcptrAcct_bankNum;//	String(16)	承兑人开户行行号		N
    private String AcptrAcct_bankName;//String(128)	承兑人开户行名称		N

    private String PayeeAcct_acctId;//	String(32)	收款人账户号		N
    private String PayeeAcct_name;//String(64)	收款人账户名称		N
    private String PayeeAcct_bankNum;//String(16)	收款人开户行行号		N
    private String PayeeAcct_bankName;//	String(128)	收款人开户行名称		N

    /**
     * 插入应收票据库存 成功/失败 标志
     * '1' - 成功
     * '0' - 失败
     */
    private String flag;
    private String Message;//推送信息

    private BigDecimal amt01;//金额01
    private BigDecimal amt02;//金额02
    private BigDecimal amt03;//金额03
    private BigDecimal amt04;//金额04
    private BigDecimal amt05;//金额05
    private int num01;//数值01
    private int num04;//数值04
    private int num05;//数值05
    private String txt01;//文本01
    private int num02;//数值02
    private String txt02;//文本02
    private int num03;//数值03
    private String txt03;//文本03
    private String txt04;//文本04
    private String txt05;//文本05
    private String fk01;//外键01
    private String fk02;//外键02
    private String fk03;//外键03
    private String fk04;//外键04
    private String fk05;//外键05
    private Timestamp time05;//时间05
    private Timestamp time04;//时间04
    private Timestamp time03;//时间03
    private Timestamp time02;//时间02
    private Timestamp time01;//时间01

    private String timestamp_createdby;//创建人
    private Timestamp timestamp_createdon;//创建日期
    private String timestamp_lastchangedby;//修改人
    private Timestamp timestamp_lastchangedon;//修改日期
}
