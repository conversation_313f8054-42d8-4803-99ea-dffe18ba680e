package com.inspur.cloud.jtgk.goldwind.billmanage.service;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.inspur.cloud.jtgk.goldwind.billmanage.entity.JfskDraftSignTradePageEntity;
import com.inspur.cloud.jtgk.goldwind.billmanage.repository.JfskDraftSignTradePageRepository;
import com.inspur.cloud.jtgk.goldwind.billmanage.utils.JfskTypeUtils;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.common.JSONSerializer;
import io.iec.edp.caf.commons.transaction.JpaTransaction;
import org.springframework.transaction.annotation.Transactional;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.rpc.api.service.RpcClient;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.security.MessageDigest;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional(rollbackFor = Exception.class)
public class JfskBillManageService {
    LogService jtgklogService = SpringBeanUtils.getBean(LogService.class);
    EntityManager entityManager = SpringBeanUtils.getBean(EntityManager.class);
    RpcClient rpcClient = SpringBeanUtils.getBean(RpcClient.class);
    JfskDraftSignTradePageRepository tradePageRepository = SpringBeanUtils.getBean(JfskDraftSignTradePageRepository.class);

    // SQL语句：更新签票交易查询数据同步应收票据库存情况
    private final String UPDATE_SQL = "update JTGKDRAFTSIGNTRADEPAGE set FLAG= ?1,FK01 = ?2,Timestamp_lastchangedon= ?3,MESSAGE=?4 where BILLNO  = ?5";

    /**
     * 更新签票交易页面数据
     *
     * @param flag      标志
     * @param billId    票据ID
     * @param timestamp 时间戳
     * @param message   消息
     * @param srcdocid  签票交易流水号
     * @return 更新的记录数
     */
    public int updateTradePage(String flag, String billId, Timestamp timestamp, String message, String srcdocid) {
        Query sql = entityManager.createNativeQuery(UPDATE_SQL);
        sql.setParameter(1, flag);
        sql.setParameter(2, billId);
        sql.setParameter(3, timestamp);
        sql.setParameter(4, message);
        sql.setParameter(5, srcdocid);
        return sql.executeUpdate();
    }

    /**
     * 获取签票交易页面数据并保存到数据库
     */
    public void getDraftSignTradePage() {
        jtgklogService.init("getDraftSignTradePage");
        String logCode = "签票交易查询";
        try {
            // 调用提取的取数方法
            List<Map<String, Object>> contentList = fetchDraftSignTradeData(logCode);
            if (contentList == null || contentList.isEmpty()) {
                jtgklogService.info(logCode, "查询到0条数据");
                return;
            }

            if (contentList != null && !contentList.isEmpty()) {
                jtgklogService.info(logCode, "查询到{}条数据", contentList.size());

                // 将查询到的数据转换为实体对象并保存到数据库
                List<JfskDraftSignTradePageEntity> saveList = contentList.stream()
                        .map(map -> saveDraftSignTradePage(map, logCode))
                        .collect(Collectors.toList());
                tradePageRepository.saveAll(saveList);

                jtgklogService.info(logCode, "保存成功，数量：{}", saveList.size());
            }
        } catch (Throwable e) {
            jtgklogService.error(logCode, "异常：{}", ExceptionUtils.getStackTrace(e));
        } finally {
            jtgklogService.flush();
        }
    }

    /**
     * 获取签票交易数据
     *
     * @param logCode 日志代码
     * @return 包含签票交易数据的列表
     */
    private List<Map<String, Object>> fetchDraftSignTradeData(String logCode) {
        List<Map<String, Object>> allContentList = new ArrayList<>();
        int pageSize = 50; // 每页大小
        int pageNumber = 1; // 当前页码

        while (true) {
            try {
                // 查询通用字典配置
                List<Map<String, Object>> IDDList = fetchDictionaryData(logCode);
                if (IDDList == null || IDDList.isEmpty()) {
                    jtgklogService.error(logCode, "通用字典配置为空");
                    return null;
                }

                // 设置默认的api_url、业务查询开始日期、业务查询结束日期和密钥
                String api_url = "https://gwdraft-agw.goldwind.com";
                String busiDtStart = "2024-10-11";
                String busiDtEnd = "2024-10-11";
                String secretKey = "a6a482f34fa245b5b1862c55d9257ca8";

                // 如果通用字典中有配置，则使用配置中的值
                if (!IDDList.isEmpty()) {
                    busiDtStart = JfskTypeUtils.convert2String(IDDList.get(0).get("txt01"));
                    busiDtEnd = JfskTypeUtils.convert2String(IDDList.get(0).get("txt02"));
                    api_url = JfskTypeUtils.convert2String(IDDList.get(0).get("txt03"));
                    secretKey = JfskTypeUtils.convert2String(IDDList.get(0).get("txt04"));
                }

                // 构建请求地址
                String address = api_url + "/thirdparty-web/transaction/dff.open.get.getDraftSignTradePage";

                // 生成时间戳
                Long timeStamp = System.currentTimeMillis();

                // 构建请求参数Map
                jtgklogService.info(logCode, "第{}页，每页{}条", pageNumber, pageSize);
                Map<String, Object> paramMap = buildRequestParamMap(timeStamp, busiDtStart, busiDtEnd, secretKey, pageNumber, pageSize);
                jtgklogService.info(logCode, "组织请求报文paramMap：\n{}", JSONObject.toJSONString(paramMap));

                // 调用签票交易查询接口
                HttpResponse resmsg = callApiWithRetry(address, JSONObject.toJSONString(paramMap), logCode);
                if (resmsg == null || !resmsg.isOk()) {
                    jtgklogService.error(logCode, "接口调用失败，达到最大重试次数");
                    return null;
                }

                jtgklogService.info(logCode, "getDraftSignTradePage返回状态：{}", resmsg.getStatus());
                jtgklogService.error(logCode, "getDraftSignTradePage返回数据：\n{}", JSONSerializer.serialize(resmsg));

                if (resmsg.isOk()) {
                    jtgklogService.info(logCode, "请求成功，返回数据：\n{}", resmsg.body());

                    JSONObject resmsgO = JSONObject.parseObject(resmsg.body());
                    if ("Success".equals(resmsgO.get("resultCode"))) {
                        Map dataMap = JSONObject.parseObject((String) resmsgO.get("data"));
                        List<Map<String, Object>> contentList = (List<Map<String, Object>>) dataMap.get("contents");
                        if (contentList == null || contentList.isEmpty()) {
                            break; // 如果当前页没有数据，退出循环
                        }

                        allContentList.addAll(contentList); // 将当前页的数据添加到总列表中

                        if (contentList.size() < pageSize) {
                            break; // 如果当前页的数据少于pageSize，说明已经到达最后一页
                        }

                        pageNumber++; // 增加页码
                    } else {
                        jtgklogService.error(logCode, "请求失败：{}", resmsgO.get("resultMsg"));
                        break;
                    }
                } else {
                    jtgklogService.error(logCode, "请求失败，状态码：{}", resmsg.getStatus());
                    break;
                }
            } catch (Throwable e) {
                jtgklogService.error(logCode, "异常：{}", ExceptionUtils.getStackTrace(e));
                break;
            }
        }
        return allContentList;
    }

    /**
     * 获取通用字典数据
     *
     * @param logCode 日志代码
     * @return 通用字典数据列表
     */
    private List<Map<String, Object>> fetchDictionaryData(String logCode) {
        Query dictionaryQuery = entityManager.createNativeQuery("select TXT01,TXT02,TXT03,TXT04 from IDD_DATADICTIONARY  where categoryid IN (select ID from IDD_DATADICCATE  where code='ckpjrqfw')");
        dictionaryQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> IDDList = dictionaryQuery.getResultList();
        jtgklogService.info(logCode, "通用字典配置：{}", JSONSerializer.serialize(IDDList));
        return IDDList;
    }

    /**
     * 构建请求参数Map
     *
     * @param timeStamp   时间戳
     * @param busiDtStart 业务查询开始日期
     * @param busiDtEnd   业务查询结束日期
     * @return 请求参数Map
     * @throws Exception 异常
     */
    private Map<String, Object> buildRequestParamMap(Long timeStamp, String busiDtStart, String busiDtEnd, String secretKey, int page, int size) throws Exception {
        Map<String, Object> paramMap = new HashMap<>();

        // 字段	类型	描述	默认值	可为空	备注
        JSONObject msgBody = new JSONObject();
        msgBody.put("custSN", timeStamp); // 客户端交易的唯一流水号
        msgBody.put("page", page); // 页码数
        msgBody.put("size", size); // 每页数量
        msgBody.put("businessType", "BC03"); // 业务类型
        msgBody.put("busiKind", "reply"); // 业务请求类型
        msgBody.put("cdNo", ""); // 票据（包）号
        msgBody.put("billNo", ""); // 交易流水号
        msgBody.put("billStatus", "successful"); // 交易状态
        msgBody.put("sgnUpMk", "SU00"); // 应答标识 （仅busiKind为reply时，才有意义)SU00:同意签收 SU01:拒绝签收
        msgBody.put("busiDtStart", busiDtStart); // 业务查询开始日期
        msgBody.put("busiDtEnd", busiDtEnd); // 业务查询结束日期
        msgBody.put("dwrName", ""); // 出票人
        msgBody.put("dwrAcct", ""); // 出票人账户
        msgBody.put("minCdAmt", ""); // 票据包金额开始
        msgBody.put("maxCdAmt", ""); // 票据包金额结束
        msgBody.put("pyName", ""); // 收款人
        msgBody.put("pyAcct", ""); // 收款人账户
        msgBody.put("acptrName", ""); // 承兑人
        msgBody.put("acptrAcct", ""); // 承兑人账号

        String sign = generateSignature(secretKey, timeStamp, msgBody);//签名

        paramMap.put("data", JSONObject.toJSONString(msgBody));
        paramMap.put("appKey", "jf002");
        paramMap.put("sign", sign);
        paramMap.put("timeStamp", timeStamp);

        return paramMap;
    }

    /**
     * 调用API并重试
     *
     * @param address         请求地址
     * @param returnParamJson 请求参数JSON字符串
     * @param logCode         日志代码
     * @return HTTP响应
     */
    private HttpResponse callApiWithRetry(String address, String returnParamJson, String logCode) {
        int retryCount = 3;
        HttpResponse resmsg = null;
        for (int i = 0; i < retryCount; i++) {
            try {
                resmsg = HttpRequest.post(address)
                        .header("Content-Type", "application/json")
                        .body(JSONUtil.toJsonStr(returnParamJson))
                        .timeout(5000)
                        .execute();
                if (resmsg.isOk()) {
                    break;
                }
            } catch (Exception e) {
                jtgklogService.error(logCode, "接口调用失败，正在重试：" + (i + 1) + "/" + retryCount, e);
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    jtgklogService.error(logCode, "线程中断：{}", ExceptionUtils.getStackTrace(ie));
                }
            }
        }
        return resmsg;
    }

    /**
     * 生成签名串
     *
     * @param secretKey 密钥
     * @param timeStamp 时间戳
     * @param msgBody   请求报文
     * @return 签名串
     * @throws Exception 异常
     */
    private String generateSignature(String secretKey, Long timeStamp, JSONObject msgBody) throws Exception {
        String data = JSON.toJSONString(msgBody);
        String msg = secretKey + timeStamp + data + secretKey;
        MessageDigest digest = MessageDigest.getInstance("MD5");
        byte[] md5 = digest.digest(msg.getBytes("UTF-8"));
        return Hex.encodeHexString(md5).toUpperCase();
    }

    /**
     * 将查询到的数据转换为实体对象
     *
     * @param map     数据映射
     * @param logCode 日志代码
     * @return 实体对象
     */
    public JfskDraftSignTradePageEntity saveDraftSignTradePage(Map map, String logCode) {
        JfskDraftSignTradePageEntity entity = new JfskDraftSignTradePageEntity();

        // 根据billNo查询数据库中是否存在记录
        String billNo = JfskTypeUtils.convert2String(map.get("billNo"));
        Optional<JfskDraftSignTradePageEntity> billInfo = tradePageRepository.findByBillNo(billNo);
        if (billInfo.isPresent()) {
            entity = billInfo.get();
        } else {
            // 设置实体对象的各个属性
            entity.setBillNo(billNo);
            entity.setBillStatus(JfskTypeUtils.convert2String(map.get("billStatus")));
            entity.setDwrPe(JfskTypeUtils.convert2String(map.get("dwrPe")));
            entity.setCdNo(JfskTypeUtils.convert2String(map.get("cdNo")));

            String cdRange = JfskTypeUtils.convert2String(map.get("cdRange"));
            if (cdRange != null && cdRange.contains(",")) {
                int commaIndex = cdRange.indexOf(",");
                String leftNumber = cdRange.substring(0, commaIndex); // 子票区间起
                String rightNumber = cdRange.substring(commaIndex + 1); // 子票区间止
                entity.setSubbillstartsn(leftNumber);
                entity.setSubbillendsn(rightNumber);
            } else {
                jtgklogService.error(logCode, "cdRange字段格式不正确，未找到逗号分隔符: {}", cdRange);
                // 可以选择设置默认值或抛出异常
                entity.setSubbillstartsn("");
                entity.setSubbillendsn("");
            }
            entity.setCdRange(cdRange);

            entity.setCdAmt(JfskTypeUtils.convert2Decimal(map.get("cdAmt")));
            entity.setCdType(JfskTypeUtils.convert2String(map.get("cdType")));
            String issDt = JfskTypeUtils.convert2String(map.get("issDt"));
            entity.setIssDt(JfskTypeUtils.convert2Timestamp(issDt));
            String dueDt = JfskTypeUtils.convert2String(map.get("dueDt"));
            entity.setDueDt(JfskTypeUtils.convert2Timestamp(dueDt));
            String applyDate = JfskTypeUtils.convert2String(map.get("applyDate"));
            entity.setApplyDate(JfskTypeUtils.convert2Timestamp(applyDate));
            String sgnDt = JfskTypeUtils.convert2String(map.get("sgnDt"));
            entity.setSgnDt(JfskTypeUtils.convert2Timestamp(sgnDt));
            entity.setSgnUpMk(JfskTypeUtils.convert2String(map.get("sgnUpMk")));
            entity.setReqNote(JfskTypeUtils.convert2String(map.get("reNote")));
            entity.setSgnNote(JfskTypeUtils.convert2String(map.get("sgnNote")));

            // 设置出票人信息
            Map DrawerAcct = (Map) map.get("drawerAcct");
            entity.setDrawerAcct_acctId(JfskTypeUtils.convert2String(DrawerAcct.get("acctId")));
            entity.setDrawerAcct_name(JfskTypeUtils.convert2String(DrawerAcct.get("name")));
            entity.setDrawerAcct_bankNum(JfskTypeUtils.convert2String(DrawerAcct.get("bankNum")));
            entity.setDrawerAcct_bankName(JfskTypeUtils.convert2String(DrawerAcct.get("bankName")));

            // 设置承兑人信息
            Map AcptrAcct = (Map) map.get("acptrAcct");
            entity.setAcptrAcct_acctId(JfskTypeUtils.convert2String(AcptrAcct.get("acctId")));
            entity.setAcptrAcct_name(JfskTypeUtils.convert2String(AcptrAcct.get("name")));
            entity.setAcptrAcct_bankNum(JfskTypeUtils.convert2String(AcptrAcct.get("bankNum")));
            entity.setAcptrAcct_bankName(JfskTypeUtils.convert2String(AcptrAcct.get("bankName")));

            // 设置收款人信息
            Map PayeeAcct = (Map) map.get("payeeAcct");
            entity.setPayeeAcct_acctId(JfskTypeUtils.convert2String(PayeeAcct.get("acctId")));
            entity.setPayeeAcct_name(JfskTypeUtils.convert2String(PayeeAcct.get("name")));
            entity.setPayeeAcct_bankNum(JfskTypeUtils.convert2String(PayeeAcct.get("bankNum")));
            entity.setPayeeAcct_bankName(JfskTypeUtils.convert2String(PayeeAcct.get("bankName")));

            // 设置主键和其他属性
            entity.setID(UUID.randomUUID().toString());
            entity.setFlag("0");
            String userId = CAFContext.current.getUserId();
            Timestamp timestamp_createdon = Timestamp.valueOf(LocalDateTime.now());
            entity.setTimestamp_createdby(userId);
            entity.setTimestamp_createdon(timestamp_createdon);
            entity.setTimestamp_lastchangedby(userId);
            entity.setTimestamp_lastchangedon(timestamp_createdon);
        }
        return entity;
    }

    /**
     * 推送应收票据结算信息
     */
    public void BillReceiptPushSettlement() {
        jtgklogService.init("jfksBillReceiptPushSettlement");
        String logCode = "应收票据信息推送-开发";

        JpaTransaction tran = JpaTransaction.getTransaction();
        try {
            List<Map<String, Object>> saveList = fetchUnprocessedTradePages(logCode);
            if (saveList.isEmpty()) {
                jtgklogService.info(logCode, "没有未处理的交易页面数据");
                return;
            }

            for (Map<String, Object> map : saveList) {
                tran.begin();
                try {
                    List<Map<String, Object>> paramList = buildPushSettlementParams(map, logCode);
                    String jsonStr = JSONObject.toJSONString(paramList);
                    String result = callPushBillreceiptApi(jsonStr, logCode);
                    handlePushBillreceiptResult(result, map, logCode);
                    tran.commit();
                } catch (Exception e) {
                    tran.rollback();
                    jtgklogService.error(logCode, "处理单条交易页面数据时发生异常，回滚事务：{}", ExceptionUtils.getStackTrace(e));
                }
            }
        } catch (Throwable ex) {
            tran.rollback();
            jtgklogService.error(logCode, "异常，原因：{}", ExceptionUtils.getStackTrace(ex));
        } finally {
            jtgklogService.flush();
        }
    }

    /**
     * 获取未处理的签票交易页面数据
     *
     * @param logCode 日志代码
     * @return 未处理的签票交易页面数据列表
     */
    private List<Map<String, Object>> fetchUnprocessedTradePages(String logCode) {
        Query dataQuery = entityManager.createNativeQuery("select * from JTGKDRAFTSIGNTRADEPAGE where flag !='1'");
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> saveList = dataQuery.getResultList();
        jtgklogService.info(logCode, "未处理的签票交易页面数据数量：{}", saveList.size());
        return saveList;
    }

    /**
     * 构建推送应收票据参数
     *
     * @param map 数据映射
     * @return 参数列表
     */
    private List<Map<String, Object>> buildPushSettlementParams(Map<String, Object> map, String logCode) {
        List<Map<String, Object>> paramList = new ArrayList<>();

        // 设置参数
        Map<String, Object> param = new HashMap<>();//调产品rpc参数

        //                srcdocid	来源单据id	varchar(36)	Y	来源单据id
        String srcdocid = JfskTypeUtils.convert2String(map.get("billno"));
        jtgklogService.info(logCode, "srcdocid：" + srcdocid + "，srcdocno：" + map.get("cdno"));
        param.put("srcdocid", srcdocid);
        //                srcdocno	来源单据编号	varchar(50)	Y	来源单据编号
        param.put("srcdocno", map.get("cdno"));
        //                CPFS	持票方式	int	Y	1、持票方式：1-自管；2-统管。2、进行非空、数据准确性校验。
        param.put("CPFS", "1");
        //                BillType	票据类型	varchar(6)	Y	票据类型：AC01-银承；AC02-商承；3-供应链产品
        param.put("BillType", map.get("cdtype"));
        //                ReceiptDate	收票日期	date	Y	收票日期，输入格式为yyyy-MM-dd或yyyy/MM/dd或yyyyMMdd
        param.put("ReceiptDate", map.get("applydate"));
        //                TreasureOgr	资金组织	varchar(36)	N	1、资金组织名称。2、若CPFS=2;则进行非空、数据准确性校验。
        //                UnitName	交票单位名称	varchar(1000)	Y
        param.put("UnitName", "新疆金风科技集团财务有限公司");
        //                UnitCode	交票单位编号	varchar(36)	Y
        param.put("UnitCode", "2006");
        //                BillForm	票据形式	varchar（1）	Y	0-纸票；1-电票
        param.put("BillForm", "1");
        //                BillNo	票据号码	varchar(80)	Y
        param.put("BillNo", map.get("cdno"));
        //                BillOpenDate	出票日期	date	Y	出票日期，输入格式为yyyy-MM-dd或yyyy/MM/dd或yyyyMMdd
        param.put("BillOpenDate", map.get("issdt"));
        //                BillDueDate	到期日期	date	Y	到期日期，输入格式为yyyy-MM-dd或yyyy/MM/dd或yyyyMMdd
        param.put("BillDueDate", map.get("duedt"));
        //                BillAmt	票据金额	demical(28,8)	Y
        param.put("BillAmt", map.get("cdamt"));
        //                RecAccNo	收票银行账号	varchar(50)	N	若传入账号，接口内会依据该字段的值，收票单位进行账户基础数据的准确性校验。
        param.put("RecAccNo", map.get("payeeacct_acctid"));
        //                RecBankName	收票开户银行	varchar(1000)	N	若传入银行，接口内会依据该字段的值，进行银行基础数据的准确性校验。
        param.put("RecBankName", map.get("payeeacct_bankname"));
        //                DrawerName	出票人全称	varchar(1000)	Y	进行往来单位基础数据的准确性校验
        param.put("DrawerName", map.get("draweracct_name"));
        //                DrawerAccountNo	出票人账号	varchar(50)	N	1、背书次数等于0，且全局参数【收款校验对方信息】为是，出票人账号不允许为空。2、背书次数等于0，且全局参数【收款校验对方信息】为是，接口根据出票人全称和出票人账号匹配到系统内银行账户，进行数据准确性校验
        param.put("DrawerAccountNo", map.get("draweracct_acctid"));
        //                DrawerBankName	出票人开户银行	varchar(1000)	N	背书次数等于0，且全局参数【收款校验对方信息】为是，接口内进行出票人全称、出票人账号和出票人开户行数据一致性校验。
        param.put("DrawerBankName", map.get("draweracct_bankname"));
        //                EndorseTimes	背书次数	int	N	大于等于0校验
        param.put("EndorseTimes", 0);
        //                LastIndorserName	上手背书人名称	varchar(1000)	N	1、背书次数大于0，全局参数【收款校验对方信息】为是，系统进行非空校验。2、若非空，接口内进行往来单位基础数据的数据准确性校验。
        //                FirstRecName	第一收款人全称	varchar(1000)	Y	非空校验
        param.put("FirstRecName", map.get("payeeacct_name"));
        //                FirstRecAccNo	第一收款人账号	varchar(50)	N
        param.put("FirstRecAccNo", map.get("payeeacct_acctid"));
        //                FirstRecBankName	第一收款人开户银行	varchar(1000)	N
        param.put("FirstRecBankName", map.get("payeeacct_bankname"));
        // AcceptorName	承兑人全称	varchar(1000)	Y	必填
        param.put("AcceptorName", map.get("acptracct_name"));
        // AcceptorBankName	承兑银行全称	varchar(1000)	N	非空校验；若银行承兑汇票，则进行银行基础数据的数据准确性校验。
        param.put("AcceptorBankName", map.get("acptracct_bankname"));
        // AcceptorAccNo	承兑人账号	varchar(50)	N
        param.put("AcceptorAccNo", map.get("acptracct_acctid"));
        // AcceptorBankName	承兑人开户行名称	varchar(1000)	N	若非空，进行银行基础数据的数据准确性校验。
        // AcceptorBankNo	承兑人开户行行号	varchar(30)	N
        param.put("AcceptorBankNo", map.get("acptracct_bankNum"));
        //                InnerAccountCode	交票单位内部票据户	varchar(36)	N	1、交票单位内部票据户编号。 2、若CPFS=2;则进行非空、账户数据准确性校验（包含账号、开户单位、所属资金组织、启用状态、已初始化余额联合校验）。
        //                Remark	备注	varchar(256)	N
        param.put("Remark", map.get("reqnote"));
        //                AvaEndorse	能否转让	varchar(6)	Y(由非必填调整)	EM00(可转让)或者EM01(不可转让)
        param.put("AvaEndorse", "EM00");
        //                ContractNo	交易合同号	varchar(60)	N
        //                AcceptanceAgreementNo	承兑协议号	varchar(60)	N
        //                GYLCP	供应链产品	varchar(36)	N	供应链产品名称：若票据类型为3-供应链产品，则进行非空校验；供应链产品的数据准确性校验。
        //                avaDiscount	可贴现标记	varchar(6)	Y	EM00(可转让)或者EM01(不可转让)
        param.put("avaDiscount", "EM00");
        //                BillStatus	票据状态	varchar(6)	N	"31：已背书 EndorseComplete；32：已贴现 DiscountComplete；33：已托收 CollecttionComplete；34：已质押
        //                注：新增时不要传入，新增时默认为20库存"

        paramList.add(param);
        return paramList;
    }

    /**
     * 调用推送应收票据API
     *
     * @param jsonStr 参数JSON字符串
     * @param logCode 日志代码
     * @return API调用结果
     */
    private String callPushBillreceiptApi(String jsonStr, String logCode) {
        HashMap<Object, Object> paramMap = new HashMap<>();
        paramMap.put("operation", "add");
        paramMap.put("GenerateRecBillInventoryIn", jsonStr);
        LinkedHashMap<String, Object> remoteCall = new LinkedHashMap<>();
        remoteCall.put("params", paramMap);
        String su = "BM";
        String ServiceId = "com.inspur.gs.tm.bm.bmbillreceipt.core.service.BMBillReceivableRPCService.GenerateBillReceivableInventory";

        jtgklogService.info(logCode, "==============================产品接口入参：=============================={}", JSONObject.toJSONString(remoteCall));
        Object result = this.rpcClient.invoke(Object.class, ServiceId, su, remoteCall, null);
        jtgklogService.info(logCode, "=========================调产品接口完成,返回结果：=========================={}", result);
        String reJson = JSONObject.toJSONString(result);
        return reJson;
    }

    /**
     * 处理推送应收票据结果
     *
     * @param result  API调用结果
     * @param map     数据映射
     * @param logCode 日志代码
     */
    private void handlePushBillreceiptResult(String result, Map<String, Object> map, String logCode) {
        JSONObject json = JSONObject.parseObject(result);
        Integer code = (Integer) json.get("code");
        String srcdocid = JfskTypeUtils.convert2String(map.get("billno"));
        Timestamp timestampCreatedOn = Timestamp.valueOf(LocalDateTime.now());

        if (code == 1) {
            //成功
            JSONArray retBody = json.getJSONArray("GenerateRecBillInventoryOut");
            for (int i = 0; i < retBody.size(); i++) {
                JSONObject item = retBody.getJSONObject(i);
                String billId = JfskTypeUtils.convert2String(item.get("billId"));
                int count = updateTradePage("1", billId, timestampCreatedOn, "同步应收票据库存成功", srcdocid);
                if (count > 0) {
                    jtgklogService.info(logCode, "更新成功，受影响行数：{}", count);
                } else {
                    jtgklogService.error(logCode, "未找到匹配记录，更新失败，条件：BILLNO={}", srcdocid);
                }
            }
        } else {
            //失败
            String message = json.getString("message");
            jtgklogService.error(logCode, "message：" + message);
            int count = updateTradePage("0", "", timestampCreatedOn, message, srcdocid);
            if (count > 0) {
                jtgklogService.info(logCode, "更新成功，受影响行数：{}", count);
            } else {
                jtgklogService.error(logCode, "未找到匹配记录，更新失败，条件：BILLNO={}", srcdocid);
            }
        }
    }

    /**
     * 未推送成功的，修改状态1-》0，然后定时任务重新推送
     *
     * @param param 包含选中的交易页面数据的Map
     * @return 包含操作结果的Map
     */
    public Map<String, Object> jfksBillReceiptRepush(Map param) {
        String logCode = "应收票据库存重新推送";
        jtgklogService.init("jfksBillReceiptRepush");
        jtgklogService.info(logCode, "未推送成功的，修改状态1-》0，然后定时任务重新推送");
        Map<String, Object> result = new HashMap<>();
        try {
            // 获取选中的交易页面数据列表
            List<Map<String, Object>> dataList = (List<Map<String, Object>>) param.get("selected");
            if (dataList == null || dataList.size() == 0) {
                result.put("Code", "E");
                result.put("Msg", "未选择数据");
                return result;
            }

            // 提取选中的交易页面数据的ID列表
            List<String> ids = dataList.stream().map(map -> map.get("ID").toString()).collect(Collectors.toList());

            // 更新交易页面数据的状态为0
            Query repealSql = entityManager.createNativeQuery("update JTGKDRAFTSIGNTRADEPAGE set FLAG= '0' where ID IN (?1)");
            repealSql.setParameter(1, ids);
            int code = repealSql.executeUpdate();
            jtgklogService.info(logCode, "受影响行数：{}", code);

            // 设置操作结果
            result.put("Code", "S");
            result.put("Msg", "");
            return result;
        } catch (Throwable ex) {
            // 记录异常信息
            jtgklogService.error(logCode, "异常，原因：" + ExceptionUtils.getStackTrace(ex));
            result.put("Code", "E");
            result.put("Msg", ex.getMessage());
            return result;
        } finally {
            jtgklogService.flush();
        }
    }
}

