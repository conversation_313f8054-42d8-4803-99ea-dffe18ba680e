package com.inspur.cloud.jtgk.goldwind.billmanage.utils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class JfskTypeUtils {

    /**
     * 功能描述： 转 BigDecimal
     *
     * @return java.math.BigDecimal
     * @Param [object] 转换目标
     */
    public static BigDecimal convert2Decimal(Object object) {
        if (object == null)
            return BigDecimal.ZERO;
        if (object.getClass().equals(BigDecimal.class)) {
            return (BigDecimal) object;
        }
        if (object.toString().equals("")) {
            return BigDecimal.ZERO;
        }
        if (object.toString().equals("0E-8")) {
            return BigDecimal.ZERO;
        }
        try {
            BigDecimal decimal = new BigDecimal(object.toString());
            return decimal;
        } catch (Exception ex) {
//            MyCommonLogger.getLogger().error(ex.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 转Bigdecimal 并四舍五入
     * @param object 转换目标
     * @param precision 保留位数
     * @return BigDecimal
     */
    public static BigDecimal convert2Decimal(Object object,int precision){
        BigDecimal temp = convert2Decimal(object);
        return temp.setScale(precision,BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 转decimal并返回 四舍五入后的字符串
     * @param object 目标对象
     * @param precision 精度
     * @return 返回字符串值
     */
    public static String convert2DecimalStr(Object object,int precision) {
        return convert2String(convert2Decimal(object, precision));
    }
    /**
     * 转字符串
     * @param decimal 目标值decimal
     * @return 字符串
     */
    public static String convert2String(BigDecimal decimal){
        String temp = String.valueOf(decimal);
        if(temp.equals("null")) return "0.00";
        return temp;
    }
    /**
     * date转timestamp
     * @param date Date
     * @return Timestamp

     */
    @Deprecated
    public static Timestamp fromDate(Date date){
        return new Timestamp(date.getTime());
    }

    /**
     * timestamp 转Date
     * @param time timestamp
     * @return Date
     * @deprecated
     */
    @Deprecated
    public static Date fromTimestamp(Timestamp time){
        return time;
    }

    /**
     * 字符串转 Date
     * @param value String yyyy-MM-dd HH:mm:ss or yyyy-MM-dd 反斜杠会自动替换为 短横线
     * @return Date
     * @deprecated
     */
    @Deprecated
    public static Date convert2Date(String value) {
        SimpleDateFormat sdf;
        value = value.replace('/', '-');
        if (value.length() > 10)
            sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        else
            sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sdf.parse(value);
        } catch (ParseException pex) {
//            throw new MyException("convert2Date：[" + value + "],转换失败", ExceptionLevel.Error);
        }
        return null;
    }
    public static Date convertDate(String value) {
        SimpleDateFormat sdf;
        value = value.replace('/', '-');
        if (value.length() > 10)
            sdf = new SimpleDateFormat("yyyyMMdd");
        else
            sdf = new SimpleDateFormat("yyyyMMdd");
        try {
            return sdf.parse(value);
        } catch (ParseException pex) {
//            throw new MyException("convert2Date：[" + value + "],转换失败", ExceptionLevel.Error);
        }
        return null;
    }

    /**
     * 字符串转Timestamp
     * @param value String yyyy-MM-dd HH:mm:ss or yyyy-MM-dd 反斜杠会自动替换为 短横线
     * @return Timestamp
     */
    @Deprecated
    public static Timestamp convert2Timestamp(String value) {
        return fromDate(convert2Date(value));
    }

    /**
     * Date转字符串
     * @param date Date
     * @return yyyy-MM-dd HH:mm:ss
     */
    @Deprecated
    public static String date2String(Date date){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    /**
     * timestamp 转字符串
     * @param timestamp
     * @return String yyyy-MM-dd HH:mm:ss
     */
    @Deprecated
    public static String timestamp2String(Timestamp timestamp){
        return date2String(timestamp);
    }

    /**
     * 转换为integer方法
     * @param o 目标 Object
     * @return Integer
     */
    public static Integer convert2Integer(Object o) {
        String r = convert2String(o);
        return convert2Integer(r);
    }
    /**
     * 转换为integer方法
     * @param s 目标 字符串
     * @return Integer
     */
    public static Integer convert2Integer(String s) {
        try {
            if (s == null || s.isEmpty()) {
                return 0;
            }
            int b = Integer.valueOf(s).intValue();
            return b;
        } catch (NumberFormatException e) {
            return 0;
        }
    }
    /**
     * 对象转换为字符串,如果对象为null 返回空字符串
     * @param o Object
     * @return String
     */
    public static String convert2String(Object o){
        String temp = String.valueOf(o);
        if(temp.equals("null")) return "";
        return temp;
    }
    public static String convert2String(Boolean value) {
        if (value == null) return "0";
        return value ? "1" : "0";
    }
    public static  boolean toBoolean(String value) {
        if (value == null) return false;
        return value == "1" || value.toLowerCase() == "true" ? true : false;
    }
    public  static  boolean toBoolean(Integer value) {
        if (value == null) return false;
        return value.equals(1) ? true : false;
    }

    /**
     * 判断字符串是否为空
     * @param str 目标字符串
     * @return bool 是否空
     */
    public static boolean isNullOrEmpty(String str) {
        return str == null || str.isEmpty();
    }
    public static boolean isNullOrEmpty(Object obj) {
        if (obj == null) return true;
        return convert2String(obj).isEmpty();
    }

}
