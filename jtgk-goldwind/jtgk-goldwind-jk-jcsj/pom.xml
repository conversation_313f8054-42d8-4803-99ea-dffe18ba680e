<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>jtgk-goldwind</artifactId>
        <groupId>org.example</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>jtgk-goldwind-jk-jcsj</artifactId>
    <dependencies>
        <dependency>
            <groupId>com.inspur.edp</groupId>
            <artifactId>cdp-iapi-api</artifactId>
            <version>0.1.5</version>
        </dependency>
        <dependency>
            <groupId>com.inspur.fastdweb</groupId>
            <artifactId>fastdweb</artifactId>
            <version>1.7.2</version>
        </dependency>
        <dependency>
            <groupId>com.inspur.edp</groupId>
            <artifactId>bef-api</artifactId>
            <version>0.2.43</version>
        </dependency>

        <!--解析xml报文-->
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>
        <dependency>
            <groupId>io.iec.edp</groupId>
            <artifactId>caf-boot-commons-transaction</artifactId>
            <version>0.3.8</version>
        </dependency>
        <dependency>
            <groupId>com.inspur.edp</groupId>
            <artifactId>cdf-component-api</artifactId>
            <version>0.1.2</version>
        </dependency>
        <dependency>
            <groupId>io.iec.edp</groupId>
            <artifactId>caf-rpc-api</artifactId>
            <version>0.3.4</version>
        </dependency>
        <dependency>
            <groupId>io.iec.edp</groupId>
            <artifactId>caf-framework-scheduler-api</artifactId>
            <version>0.1.11</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.inspur.cloud</groupId>-->
<!--            <artifactId>idd-log-api</artifactId>-->
<!--            <version>1.0-SNAPSHOT</version>-->
<!--            <scope>system</scope>-->
<!--            <systemPath>${pom.basedir}/src/main/resources/idd-log-api.jar</systemPath>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.inspur.gs</groupId>
            <artifactId>tm-tmfnd-tmpub-api</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.inspur.gs</groupId>
            <artifactId>tm-tmfnd-fsjspub-core</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>io.iec.edp</groupId>
            <artifactId>caf-commons-exception</artifactId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>io.iec.edp</groupId>
            <artifactId>caf-lockservice-api</artifactId>
            <version>0.3.8</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.20</version>
        </dependency>
        <dependency>
            <groupId>com.inspur.idd</groupId>
            <artifactId>idd-log-api</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/libs/idd-log-api.jar</systemPath>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.inspur.cloud</groupId>-->
        <!--            <artifactId>hykf-tools-log-api</artifactId>-->
        <!--            <version>1.0-SNAPSHOT</version>-->
        <!--            <scope>system</scope>-->
        <!--            <systemPath>${pom.basedir}/src/main/resources/hykf-tools-log-1.0-SNAPSHOT.jar</systemPath>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.inspur.edp</groupId>
            <artifactId>data-connectors-api</artifactId>
            <version>0.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.inspur.edp</groupId>
            <artifactId>data-connectors-exec</artifactId>
            <version>0.2.0</version>
        </dependency>
        <dependency>
            <groupId>com.inspur.edp</groupId>
            <artifactId>bff-entity</artifactId>
            <version>0.1.0</version>
        </dependency>

        <dependency>
            <groupId>com.inspur.edp</groupId>
            <artifactId>cef-entity</artifactId>
            <version>0.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.6</version> <!-- 使用最新版本 -->
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>5.2.11.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.6</version>
        </dependency>
        <dependency>
            <groupId>com.inspur.edp</groupId>
            <artifactId>early-warning-api</artifactId>
            <version>0.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>io.iec.edp</groupId>
            <artifactId>caf-framework-sysmanager-api</artifactId>
            <version>0.2.22</version>
        </dependency>
        <dependency>
            <groupId>io.iec.edp</groupId>
            <artifactId>caf-scheduler-calendar-api</artifactId>
            <version>0.1.12</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.16</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.iec.edp</groupId>
            <artifactId>caf-message-api</artifactId>
            <version>0.2.0-SNAPSHOT</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/libs/caf-message-api.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>20210307</version>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.16.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-jpa</artifactId>
            <version>2.7.18</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

</project>