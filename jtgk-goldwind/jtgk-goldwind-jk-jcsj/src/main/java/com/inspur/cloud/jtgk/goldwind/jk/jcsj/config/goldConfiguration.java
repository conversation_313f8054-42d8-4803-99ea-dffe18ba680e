package com.inspur.cloud.jtgk.goldwind.jk.jcsj.config;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.controller.goldwindInterfaceController;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao.BaseEKRepositoryjs;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.service.*;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.service.CKGL.automaticDepositService;
import io.iec.edp.caf.rest.RESTEndpoint;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration(proxyBeanMethods = false)
@EntityScan({"com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity"})
@ComponentScan({"com.inspur.cloud.jtgk.goldwind.jk.jcsj.**"})
@EnableJpaRepositories(basePackages ="com.inspur.cloud.jtgk.goldwind.jk.jcsj.**")
public class goldConfiguration {
    @Bean
    public BaseEKRepositoryjs baseEKRepositoryjs(){return  new BaseEKRepositoryjs();}

    @Bean
    public RESTEndpoint zjjsekInterfaceController(goldwindInterfaceController goldInterfaceController) {
        return new RESTEndpoint("/jtgk/goldwind/ckgl/v1.0/", goldInterfaceController);
    }
    @Bean("com.inspur.cloud.jtgk.goldwind.jk.jcsj.service.planningImpService")
    public PlanningImpService planningImpService(){return new PlanningImpService();}

    @Bean("com.inspur.cloud.jtgk.goldwind.jk.jcsj.service.PlanningImpServicejhxh")
    public PlanningImpServicejhxh planningImpServicejhxh(){return new PlanningImpServicejhxh();}

    @Bean("com.inspur.cloud.jtgk.goldwind.jk.jcsj.service.PlanningImpServicexhpj")
    public PlanningImpServicexhpj planningImpServicexhpj(){return new PlanningImpServicexhpj();}

    @Bean("goldwind-jcsj-basicDataGenerationService")
    public basicDataGenerationService getbasicDataGenerationService(){
        return new basicDataGenerationService();
    }

    @Bean("goldwind-jcsj-automaticProcesService")
    public automaticProcesService getautomaticProcesService (){
        return new automaticProcesService();
    }

    @Bean("goldwind-jcsj-PayResultFeedbackService")
    public PayResultFeedbackService payResultFeedbackService (){
        return new PayResultFeedbackService();
    }

    @Bean("goldwind-jcsj-automaticDepositService")
    public automaticDepositService automaticDepositService(){ return new automaticDepositService(); }


}
