package com.inspur.cloud.jtgk.goldwind.jk.jcsj.controller;

import io.iec.edp.caf.rpc.api.annotation.GspServiceBundle;
import io.iec.edp.caf.rpc.api.annotation.RpcParam;
import io.iec.edp.caf.rpc.api.annotation.RpcServiceMethod;

import java.util.Map;

@GspServiceBundle(applicationName = "jtgk", serviceUnitName = "AM", serviceName = "ProcessComponentsController")
public interface ProcessComponentsController {
    /**
     *账户科目维护节点前一节点的 执行后事件，执行后修改账户信息FK01=1  可修改
     * @param data
     * @return
     */
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.jfkj.zhgl.controller.ProcessComponentsController.UpdateZhkmState_before")
    Map<String, Object> UpdateZhkmState_before(@RpcParam(paramName = "data") Map<String, Object> data);

    /**
     *账户科目维护通过后 修改账户自定义字段 FK01=2  不可修改
     * @param data
     * @return
     */
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.jfkj.zhgl.controller.ProcessComponentsController.UpdateZhkmState_after")
    Map<String, Object> UpdateZhkmState_after(@RpcParam(paramName = "data") Map<String, Object> data);


    /**
     *付款成功后修改待付池状态
     * @param data
     * @return
     */
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.jfkj.zhgl.controller.ProcessComponentsController.UpdateStatesuccess")
    Map<String, Object> UpdateStatesuccess(@RpcParam(paramName = "data") Map<String, Object> data);

}
