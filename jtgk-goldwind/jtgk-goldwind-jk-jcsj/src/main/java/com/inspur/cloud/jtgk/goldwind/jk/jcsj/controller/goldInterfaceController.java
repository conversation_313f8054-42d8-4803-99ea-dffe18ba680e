package com.inspur.cloud.jtgk.goldwind.jk.jcsj.controller;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.iec.edp.caf.rpc.api.annotation.GspServiceBundle;
import io.iec.edp.caf.rpc.api.annotation.RpcParam;
import io.iec.edp.caf.rpc.api.annotation.RpcServiceMethod;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;


@GspServiceBundle(applicationName = "jtgk", serviceUnitName = "goldwind", serviceName = "goldInterfaceController")
public interface goldInterfaceController {

    /**
     * 付款退回前端业务系统
     * @param param
     * @return
     */
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.jk.jcsj.controller.goldInterfaceController.payResultFeeback")
    String payResultFeeback(@RpcParam Map<String, Object> param);

    /**
     *企业付款指令表扩展
     * @param data  组织的BE
     * @return
     */
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.jk.jcsj.controller.goldInterfaceController.addPostInfo")
    String addPostInfo(@RpcParam(paramName = "inParam") Map<String, Object> data) throws JsonProcessingException;

}

