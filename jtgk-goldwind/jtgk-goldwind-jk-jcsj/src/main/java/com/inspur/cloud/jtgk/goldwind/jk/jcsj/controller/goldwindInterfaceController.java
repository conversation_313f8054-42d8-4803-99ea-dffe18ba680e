package com.inspur.cloud.jtgk.goldwind.jk.jcsj.controller;

import com.alibaba.fastjson.JSONObject;
import org.springframework.web.bind.annotation.RequestBody;

import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;

@Path("/")
@Consumes({"application/json"})
@Produces({"application/json"})
public interface goldwindInterfaceController {
    /**
     * 存款台账重新推送sap 生成凭证
     * @param param
     * @return
     */
    @POST
    @Path("/cktzpush")
    String cktzpush (@RequestBody JSONObject param);
}
