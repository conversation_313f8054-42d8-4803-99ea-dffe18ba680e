package com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class BankPaymentDtos {
    private	String	PayUnit	;//	单位编号		资金系统的单位编号
    private	String	PayUnitName	;//	单位名称	Y
    private	String	RequestDept	;//	申请部门
    private	String	SrcBizSys	;//	来源系统编号	Y	例：EAS
    private	String	SrcDocID	;//	来源单据内码	Y	同一来源系统、同一来源单据内码，在非接收失败（传入数据错误）时不允许重复发送
    private	String	SrcDocCode	;//	来源单据编号	Y
    private	String	DocType	;//	单据类型	Y	例：01:付款单、02:费用报销单、03:差旅费用报销单、04:备用金
    private	String	PayAccountNo	;//	付款方银行账号
    private	String	PayAccountName	;//	付款户名
    private	String	Currency	;//	付款账户币种编号	Y
    private	BigDecimal	RequestAmount	;//	申请金额	Y	境外业务时为预计付款金额，根据实际汇率*交易金额计算
    private	String	ExpPayDate	;//	期望付款日期	Y	yyyyMMdd格式,不传入时默认当天
    private	String	ExpSettleWay	;//	期望结算方式
    private	String	PrivateFlag	;//	对方性质	Y	1：往来单位，2：内部员工
    private	String	ReceivingUnit	;//	收款方单位编号	Y	资金系统的往来单位编号
    private	String	ReceivingUnitName	;//	收款方单位名称	Y
    private	String	Staff	;//	员工编号		对私必传，具体和资金系统如何匹配待定
    private	String	StaffName	;//	员工姓名
    private	String	ReceivingAccountNo	;//	收款银行账号	Y
    private	String	ReceivingAccountName	;//	收款户名	Y
    private	String	ReceivingAccountBank	;//	收款银行名称
    private	String	TransCurrency	;//	交易币种编号
    private BigDecimal TransExchangeRate	;//	交易币种汇率		不传入时默认为1.00
    private	BigDecimal	TransAmount	;//	交易币种金额		不传入交易币种和金额时带入申请币种和申请金额，汇率默认为1.00
    private	String	ReciprocalCountry	;//	收款国家
    private	String	ReciprocalProvince	;//	收款省
    private	String	ReciprocalCity	;//	收款城市
    private	String	SwiftCode	;//	收款方银行SwiftCode
    private	String	BizItem	;//	业务事项
    private	String	CashFlowItem	;//	现金流量项目
    private	String	IsUrgent	;//	是否加急		0非加急、1加急，默认为0
    private	String	Summary	;//	摘要	Y
    private	String	Description	;//	详细说明
    private	String	RequestUser	;//	申请人	Y	可以传手机号
    private	String	RequestUserName	;//	申请人姓名	Y
    private	String	RequestDate	;//	申请日期	Y	yyyyMMdd格式
    private	String	FundsUse	;//	付款用途
    private	String	IsBf	;//	是否拨付	Y	0，否 1是

}
