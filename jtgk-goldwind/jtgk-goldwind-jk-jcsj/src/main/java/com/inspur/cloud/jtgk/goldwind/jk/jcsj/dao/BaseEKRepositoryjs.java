package com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao;


import com.alibaba.fastjson.JSONArray;
import io.iec.edp.caf.common.JSONSerializer;
import io.iec.edp.caf.commons.utils.CollectionUtils;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.lockservice.api.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;

/**
 * 数据库操作基类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/11
 */
@Slf4j
public class BaseEKRepositoryjs {
    @PersistenceContext
    private EntityManager manager;

    @Autowired(required = false)
    private ILockService lockService;

    /**
     * 查询单个对象
     *
     * @param sql sql语句
     * @return 查询结果
     * <AUTHOR>
     * @date 2021/10/11
     */
    public Map<String, Object> queryOne(String sql) {
        EntityManager manager = SpringBeanUtils.getBean(EntityManager.class);
        Query nativeQuery = manager.createNativeQuery(sql);
        nativeQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        try {
            Map<String, Object> singleResult = (Map<String, Object>) nativeQuery.getSingleResult();

            for (Map.Entry<String, Object> entry : new HashSet<>(singleResult.entrySet())) {
                String key = entry.getKey();
                Object value = entry.getValue();
                //值变换
                if (value instanceof Number) {
                    double v = ((Number) value).doubleValue();
                    singleResult.put(key, v);
                }
                String lowerCase = key.toLowerCase();
                if (!singleResult.containsKey(lowerCase)) {
                    singleResult.put(lowerCase, singleResult.get(key));
                }
            }
            return singleResult;
        } catch (Throwable e) {
            //e.printStackTrace();
            return Collections.emptyMap();
        }
    }


    public   String ObjectToString(Object obj) {
        return (obj == null) ? "" : String.valueOf(obj);
    }

    /**
     * 查询列表
     *
     * @param sql sql语句
     * @return 查询结果
     * <AUTHOR>
     * @date 2021/10/11
     */
    public List<Map<String, Object>> queryList(String sql) {
        EntityManager manager = SpringBeanUtils.getBean(EntityManager.class);
        Query nativeQuery = manager.createNativeQuery(sql);
        nativeQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> resultList = nativeQuery.getResultList();
        for (Map<String, Object> singleResult : resultList) {
            for (String s : new HashSet<>(singleResult.keySet())) {
                String lowerCase = s.toLowerCase();
                if (!singleResult.containsKey(lowerCase)) {
                    singleResult.put(lowerCase, singleResult.get(s));
                }
            }
        }
        return resultList;
    }

    /**
     * 5.执行更新语句
     *
     * @param upSQL
     */
    public static int executeUpdateSQL(String upSQL, Object... params) {
        EntityManager entityManager = SpringBeanUtils.getBean(EntityManager.class);
        EntityManager tempManager = entityManager.getEntityManagerFactory().createEntityManager();
        EntityTransaction transaction = tempManager.getTransaction();
        try {
            transaction.begin();
            Query nativeQuery = tempManager.createNativeQuery(upSQL);
            for (int i = 0; i < params.length; i++) {
                nativeQuery.setParameter(i + 1, params[i]);
            }
            int ret = nativeQuery.executeUpdate();
            transaction.commit();
            return ret;
        } catch (Throwable e) {
           // e.printStackTrace();
            transaction.rollback();
            return 0;
        } finally {
            tempManager.close();
        }
    }


    /**
     * 查询列表
     *
     * @param sql   sql语句
     * @param clazz 返回对象类型
     * @param param 参数
     * @return 查询结果
     * <AUTHOR>
     * @date 2021/9/15
     */
    public  <T> List<T> queryList02(String sql, Class<T> clazz, String... param) {
        Query nativeQuery = createNativeQuery(sql, param);
        nativeQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);

        List<Object> resultList = nativeQuery.getResultList();

        List<T> result = null;

        if (!CollectionUtils.isEmpty(resultList)) {
            result = JSONArray.parseArray(JSONSerializer.serialize(resultList), clazz);
        }

        return result;
    }

    /**
     * 查询单个字段值
     *
     * @param sql sql语句
     * @param param 参数
     * @return 字段值
     * <AUTHOR>
     * @date 2021/10/11
     */
    public String queryString(String sql, String... param) {
        log.error("BaseRepository.queryString sql:" + sql);
        Query nativeQuery = createNativeQuery(sql, param);

        // 查单个getSingleResult没有数据会抛异常，还是用list
        List resultList = nativeQuery.getResultList();

        String result = null;

        if (!CollectionUtils.isEmpty(resultList)) {
            Object columnValue = resultList.get(0);

            if (null != columnValue) {
                result = String.valueOf(columnValue);
            }
        }

        return result;
    }

    /**
     * 批量新增数据
     *
     * @param list 数据集合
     * <AUTHOR>
     * @date 2021/10/14
     */
    public  <T> void batchInsert(List<T> list) {
        for (int i = 0; i < list.size(); i++) {
            manager.persist(list.get(i));

            if (i % 50 == 0) {
                manager.flush();
                manager.clear();
            }
        }
    }

    /**
     * 批量更新数据
     *
     * @param list 数据集合
     * <AUTHOR>
     * @date 2021/10/14
     */
    public  <T> void batchUpdate(List<T> list) {
        for (int i = 0; i < list.size(); i++) {
            manager.merge(list.get(i));

            if (i % 50 == 0) {
                manager.flush();
                manager.clear();
            }
        }
    }

    /**
     * 创建本地查询对象并设置参数
     *
     * @param sql sql语句
     * @param param 参数
     * @return 本地查询对象
     * <AUTHOR>
     * @date 2021/10/11
     */
    public Query createNativeQuery(String sql, String[] param) {
        Query nativeQuery = manager.createNativeQuery(sql);

        // 设置参数
        if (ArrayUtils.isNotEmpty(param)) {
            for (int i = 0; i < param.length; i++) {
                nativeQuery.setParameter(i + 1, param[i]);
            }
        }

        return nativeQuery;
    }

    private Map<String, Object> fillErrorResultMsg(String messageVal, Map<String,Object> valueVal) {
        return fillResultMsg(false, messageVal, 0, valueVal);
    }

    /**
     * 封装信息
     *
     * @param resultVal
     * @param messageVal
     * @param codeVal
     * @param valueVal
     * @return Map<Object>
     * <AUTHOR>
     * @date 2022/4/15 17:31
     */
    private Map<String, Object> fillResultMsg(boolean resultVal, String messageVal, int codeVal, Map<String,Object> valueVal) {
        Map<String, Object> result = new HashMap<>();
        result.put("result", resultVal);
        result.put("Message", messageVal);
        result.put("Code", codeVal);
        result.put("value", valueVal);
        return result;
    }


    public   Date ObjectToDate(Object obj) {
        try {
            if (obj == null) {
                return null;
            }
            //SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");//注意月份是MM
            if(obj instanceof Date){
                return (Date)obj;
            }
            SimpleDateFormat simpleDateFormat;
            if(ObjectToString(obj).contains("/"))
            {
                simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd");//注意月份是MM
            }
            else {
                simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");//注意月份是MM
            }
            Date date = simpleDateFormat.parse(ObjectToString(obj));
            return date;
        } catch (Throwable e) {
            return null;
        }
    }

    public String addLock(String id) {
        String className = "CM";
        String lockName = "DocSortLock";
        LockResult lockResult = lockService.addLock(
                className,
                "TM_CM_SettlementPlatfrom",
                id,
                new DataLockOptions(
                        Duration.ofMinutes(2400),
                        ReplacedScope.Exclusive,
                        LockedScope.AppInstance,
                        Duration.ofMinutes(2400)
                ),
                lockName,
                lockName + "操作加锁"
        );
        Assert.notNull(lockResult, "该单据正在操作，请稍后再试！");
        Assert.isTrue(lockResult.isSuccess(), "该单据正在操作，请稍后再试！");
        return lockResult.getLockId();
    }

    public void removeLock(String lockId) {
        if (StringUtils.isEmpty(lockId))
            return;
        try {
            lockService.removeLock(lockId);
        }catch (Throwable e){
            log.error("automaticGenerationService-removeLock exception:::"+e.getMessage());
        }
    }
}

