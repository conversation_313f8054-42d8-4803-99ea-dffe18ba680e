package com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao;

import com.alibaba.fastjson.JSON;
import io.netty.util.internal.StringUtil;

import java.util.HashMap;
import java.util.Map;

public class BooleanReturnValue <TData> {
    /**
     * 执行是否成功
     */
    private boolean success;
    /**
     * 设置执行结果
     * @param success 实行是否成功
     */
    public void setSuccess(boolean success) {
        this.success = success;
    }
    /**
     * 获取执行结果
     * @return 执行是否成功
     */
    public boolean getSuccess() {
        return this.success;
    }

    /**
     * 执行失败时错误消息
     */
    private String message;
    /**
     * 设置执行失败时的错误消息
     * @param message 错误消息
     */
    public void setMessage(String message) {
        this.message = message;
    }
    /**
     * 获取执行失败时的错误消息
     * @return 错误消息
     */
    public String getMessage() {
        return this.message;
    }

    /**
     * 扩展：执行结果
     */
    private TData data;
    /**
     * 扩展：设置执行结果
     * @param data
     */
    public void setData(TData data) {
        this.data = data;
    }
    /**
     * 扩展：获取执行结果
     * @return
     */
    public TData getData() {
        return this.data;
    }

    /**
     * 创建指定的实例
     * @return 实例
     */
    public static <TData> BooleanReturnValue ofSuccess(TData data) {
        BooleanReturnValue result = new BooleanReturnValue();
        result.setSuccess(true);
        result.setData(data);
        return result;
    }

    /**
     * 创建指定的实例
     * @param message 返回结果
     * @return 实例
     */
    public static <TData> BooleanReturnValue ofFail(String message) {
        BooleanReturnValue result = new BooleanReturnValue();
        result.setSuccess(false);
        result.setMessage(message);
        return result;
    }

    /**
     * 创建指定的实例
     * @return 实例
     */
    public static <TData> BooleanReturnValue create(boolean success, String message, TData data) {
        BooleanReturnValue result = new BooleanReturnValue();
        result.setSuccess(success);
        result.setMessage(message);
        result.setData(data);
        return result;
    }

    /**
     * 解析实例
     * @param json
     * @param <TData>
     * @return
     */
    public static <TData> BooleanReturnValue parse(String json) {
        try {
            BooleanReturnValue<TData> result = JSON.parseObject(json, BooleanReturnValue.class);
            return result;
        } catch (Throwable ex) {
            return BooleanReturnValue.ofFail(ex.getMessage());
        }
    }

    @Override
    public String toString() {
        Map<String, Object> results = new HashMap<>();
        results.put("success", this.success);
        if (false == StringUtil.isNullOrEmpty(this.message)) {
            results.put("message", this.message);
        }
        if (this.data != null) {
            results.put("data", this.data);
        }
        return JSON.toJSONString(results);
    }
}
