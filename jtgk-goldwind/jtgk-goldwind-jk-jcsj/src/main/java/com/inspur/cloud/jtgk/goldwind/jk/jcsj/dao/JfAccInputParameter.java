package com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao;

import lombok.*;

import java.math.BigDecimal;

/**
 * 产品接口入参
 * <AUTHOR>
 * @version 2020-10-21
 */
@AllArgsConstructor
@Builder
@Getter
@Setter
@NoArgsConstructor
public class JfAccInputParameter {
    /**
     *单位编号
     */
    private String corpCode;
    /**
     *账号编号
     */
    private String accountCode;
    /**
     *余额日期 yyyyMMdd格式
     */
    private String balanceDate;
    /**
     *币种编号
     */
    private String currencyCode;
    /**
     *可用余额
     */
    private BigDecimal availableBalance;
    /**
     *当前余额
     */
    private BigDecimal currentBalance;
    /**
     *归集金额
     */
    private BigDecimal accumulationBalance;
    /**
     *余额时间 HH:mm:ss格式
     */
    private String balanceDateTime;
    /**
     * ******** 账户类型ID
     */
    private String accountType;
}
