package com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao;

import lombok.*;

/**
 * 返回值  OfflineAccInputResult类的序列化字符串
 * 只要有一条账户(某币种)的余额数据被成功接收并插入数据库，relFlag即为true，可以进行下一步解析RET_DATA[]
 * relFlag为false，无需解析RET_DATA[]，输出relMsg即可
 * relMsg包含：1、入参不能为空，请检查！2、INPUT_JSON为空，请检查！3、银行账户ID、币种ID不能为空！4、Exception e.getMessage() + "&&" + e.getStackTrace()
 * <AUTHOR>
 * @version 2020-10-21
 */
@AllArgsConstructor
@Builder
@Getter
@Setter
@NoArgsConstructor
public class JfAccInputResult {
    /**
     * 是否处理成功
     */
    private boolean relFlag;
    /**
     * 错误说明
     */
    private String relMsg;
    /**
     * 明细处理结果
     */
    private JfAccInputResultItem[] relData;
}
