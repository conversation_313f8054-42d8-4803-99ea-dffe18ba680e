package com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao;

public class JfTextContent {
    private String content;

    public String toString() {
        return "JfTextContent(content=" + getContent() + ")";
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $content = getContent();
        return result * 59 + (($content == null) ? 43 : $content.hashCode());
    }

    protected boolean canEqual(Object other) {
        return other instanceof JfTextContent;
    }

    public boolean equals(Object o) {
        if (o == this) return true;
        if (!(o instanceof JfTextContent)) return false;
        JfTextContent other = (JfTextContent) o;
        if (!other.canEqual(this)) return false;
        Object this$content = getContent(), other$content = other.getContent();
        return !((this$content == null) ? (other$content != null) : !this$content.equals(other$content));
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return this.content;
    }
}
