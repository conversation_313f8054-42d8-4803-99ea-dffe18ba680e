package com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao;

public class JfTextcardContent {
    private String title;

    public void setTitle(String title) {
        this.title = title;
    }

    private String description;
    private String url;

    public void setDescription(String description) {
        this.description = description;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public boolean equals(Object o) {
        if (o == this) return true;
        if (!(o instanceof JfTextcardContent)) return false;
        JfTextcardContent other = (JfTextcardContent) o;
        if (!other.canEqual(this)) return false;
        Object this$title = getTitle(), other$title = other.getTitle();
        if ((this$title == null) ? (other$title != null) : !this$title.equals(other$title)) return false;
        Object this$description = getDescription(), other$description = other.getDescription();
        if ((this$description == null) ? (other$description != null) : !this$description.equals(other$description))
            return false;
        Object this$url = getUrl(), other$url = other.getUrl();
        return !((this$url == null) ? (other$url != null) : !this$url.equals(other$url));
    }

    protected boolean canEqual(Object other) {
        return other instanceof JfTextcardContent;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $title = getTitle();
        result = result * 59 + (($title == null) ? 43 : $title.hashCode());
        Object $description = getDescription();
        result = result * 59 + (($description == null) ? 43 : $description.hashCode());
        Object $url = getUrl();
        return result * 59 + (($url == null) ? 43 : $url.hashCode());
    }

    public String toString() {
        return "JfTextcardContent(title=" + getTitle() + ", description=" + getDescription() + ", url=" + getUrl() + ")";
    }

    public String getTitle() {
        return this.title;
    }

    public String getDescription() {
        return this.description;
    }

    public String getUrl() {
        return this.url;
    }
}

