package com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PayResultBackDto {
    private String	id	;
    private String	srcBizSys	;//	来源系统标识
    private String	srcDocType	;//	来源单据类型
    private String	srcDocId	;//	来源单据唯一ID
    private String	srcDocNo	;//	来源单据编号
    private String	payStatus	;//	办理结果
    private String	docNo	;//	司库付款单号
    private String	settlewayCode	;//	司库结算方式编号
    private String	settleway	;//	司库结算方式名称
    private String	backNode	;//	退回节点
    private String	message	;//	其他说明
    private String	bankflowNo	;//	关联流水号
    private String	billNo	;//	关联票据号
    private String	subbillStartSn	;//	子票区间起
    private String	subbillEndSn	;//	子票区间止
    private String	paidAmount	;//	支付成功金额
    private String	returnAmount	;//	退汇金额
    private String	operatedOn	;//	操作时间
    private String	bankflowUrl	;//	电子回单文件下载地址
}
