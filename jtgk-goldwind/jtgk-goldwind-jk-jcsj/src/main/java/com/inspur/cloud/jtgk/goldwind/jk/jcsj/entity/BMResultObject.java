package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub.JTGKStringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "JTGKBMRESULT")
public class BMResultObject {
    @Id
    private String id;
    private String orgCode;   //组织编码
    private String   orgDirectorLeader;   //直属领导
    private String   orgBranchLeader;   //分管领导
    private String   orgName;//   金风集团 组织中文名称
    private String   orgEName;//金风集团,   //组织英文名称
    private String   orgPostCode ;// 分管领导，去掉前面0
    private String   parentOrgCode;   //父级组织编码
    private String   parentOrgName;   //父级组织中文名称
    private String   parentOrgEName;   //父级组织英文名称
    private String   jglb;   //机构类别
    private String   jgbm;   //机构编码
    private String   type;   //数据变更状态（A新增，M修改，D删除）
    private String   updateDate;   //数据更新日期
    private String sksyncstatus;// 司库同步状态  0未同步、1已同步 2 异常
    private String sksyncmsg;// 司库同步备注

        public BMResultObject(BMResultObject param) {
                this.id=param.getOrgCode();//+param.getOrgName()+param.getParentOrgCode()
                this.orgCode=param.getOrgCode();
                this.orgDirectorLeader=param.getOrgDirectorLeader();
                this.orgBranchLeader=param.getOrgBranchLeader();
                this.orgName=param.getOrgName();
                this.orgEName=param.getOrgEName();
                this.orgPostCode=param.getOrgPostCode();
                this.parentOrgCode=param.getParentOrgCode();
                this.parentOrgName=param.getParentOrgName();
                this.parentOrgEName=param.getParentOrgEName();
                this.jglb=param.getJglb();
                this.jgbm=param.getJgbm();
                this.type=param.getType();

                if (param.getUpdateDate() != null) {
                    Date updateTime = JTGKStringUtil.stringToDateDefault(param.getUpdateDate());
                    if (updateTime != null) {
                        this.updateDate = new SimpleDateFormat("yyyy-MM-dd").format(updateTime);
                    } else {
                        this.updateDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());;
                    }
                }

        }

}
