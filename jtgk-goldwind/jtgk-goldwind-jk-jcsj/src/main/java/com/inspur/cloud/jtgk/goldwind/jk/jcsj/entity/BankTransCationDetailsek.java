package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "JTGKBANKTRANSCATIONDETAILS")
public class BankTransCationDetailsek {
    @Id
    private String	ID	;//	交易明细ID
    private String	BANK_FLOW_NO	;//	银行流水号
    private String	ACCOUNT_ID	;//	银行账号系统ID
    private String	ACCOUNT_NO	;//	银行账号
    private String	CRNCY_CODE	;//	币种编号
    private String	AMOUNT	;//	金额
    private String	OP_ACCOUNT_ID	;//	对方账户系统ID
    private String	OP_ACCOUNT_NO	;//	对方账号
    private String	OP_ACCOUNT_NAME	;//	对方户名
    private String	OP_NO	;//	对方单位编号
    private String	OP_NAME	;//	对方单位名称
    private String	OP_BANK_CNAPS_NAME	;//	对方开户行名
    private String	OP_BANK_CNAPS_NO	;//	对方开户行行号
    private String	OP_COUNTRY	;//	对方国家
    private String	OP_PROVINCE_ID	;//	对方省系统ID
    private String	OP_PROVINCE	;//	对方省
    private String	OP_CITY_ID	;//	对方城市系统ID
    private String	OP_CITY	;//	对方城市
    private String	BILLNO	;//	票据号
    private String	ABSTRACT	;//	摘要
    private String	BANK_ORIG_FLOW_NO	;//	银行原始流水号
    private String	POSNO	;//	商户编号
    private String	POSNAME	;//	商户名称
    private String	TRADE_DATE	;//	交易日期
    private String	TRADE_TIME	;//	交易时间
    private String	INNER_FLAG	;//	子母户标志
    private String	VIRTUAL_ACCOUNT_NO	;//	虚拟账户编号
    private String	VIRTUAL_ACCOUNT_NAME	;//	虚拟账户名称
    private String	RCV_PAY_FLAG	;//	收付属性
    private String	IMMEDIATE_BALANCE	;//	即时余额
    private String	IS_TIMECURRENTTRANS	;//	定活转标志
    private String	TRADE_TYPE	;//	交易类型

}
