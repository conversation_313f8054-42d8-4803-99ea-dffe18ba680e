package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub.JTGKStringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "JTGKEMPLOYEEQLRESULT")
public class EmployeeQlResultObject {
    @Id
    private String id;
    private String	userId	;//工号
    private String	userName	;//名称
    private String	unitId	;//业务单元编码
    private String	unitTxt	;//业务单元编码
    private String	systemId	;//
    private String	systemTxt	;//
    private String	stell	;//岗位编码
    private String	stext	;//
    private String	deptId	;//部门
    private String	deptName	;//部门名称
    private String	phoneNumber	;//手机号
    private String gesc;//性别
    private String	email	;//邮箱
    private String	centerId	;//
    private String	centerTxt	;//
    private String	zhrTime1	;//入职时间
    private String	zhrRzrq	;//
    private String	orgeh	;//组织编号
    private String	officeId	;//
    private String	officeTxt	;//
    private String	zhrOtext	;//
    private String	zhrCost	;//成本中心编码
    private String	zhrCosttxt	;//成本中心
    private String	jglb	;//
    private String	directorCode	;//
    private String	branchCode	;//
    private String	werks	;//
    private String	werksTxt	;//
    private String	inst	;//
    private String	persg	;//
    private String	zhrLoca	;//
    private String	levelpk	;//
    private String	zhrPtext	;//
    private String	company	;//
    private String	companyName	;//
    private String	zhrBank	;//
    private String	zhrAccount	;//账号
    private String	zzKhhs	;//
    private String	zzKhhd	;//
    private String	zzKhh	;//开户行
    private String	zzYhh	;//
    private String	zzLhh	;//账户联行号
    private String	status	;//
    private String	plans	;//
    private String flag ;//                         //修改标识N新增M修改D删除
    private String updateDate ;//数据更新时间
    private String sksyncstatus;// 司库同步状态  0未同步、1已同步 2 异常
    private String sksyncmsg;// 司库同步备注
    public EmployeeQlResultObject(EmployeeQlResultObject param) {
        this.id=param.getUserId();
        this.userId=param.getUserId();//
        this.userName=param.getUserName();//
        this.gesc=param.getGesc();//
        this.unitId=param.getUnitId();//
        this.unitTxt=param.getUnitTxt();//
        this.systemId=param.getSystemId();//
        this.systemTxt=param.getSystemTxt();//
        this.stell=param.getStell();//
        this.stext=param.getStext();//
        this.deptId=param.getDeptId();//
        this.deptName=param.getDeptName();//
        this.phoneNumber=param.getPhoneNumber();//
        this.email=param.getEmail();//
        this.centerId=param.getCenterId();//
        this.centerTxt=param.getCenterTxt();//
        this.zhrTime1=param.getZhrTime1();//
        this.zhrRzrq=param.getZhrRzrq();//
        this.orgeh=param.getOrgeh();//
        this.officeId=param.getOfficeId();//
        this.officeTxt=param.getOfficeTxt();//
        this.zhrOtext=param.getZhrOtext();//
        this.zhrCost=param.getZhrCost();//
        this.zhrCosttxt=param.getZhrCosttxt();//
        this.jglb=param.getJglb();//
        this.directorCode=param.getDirectorCode();//
        this.branchCode=param.getBranchCode();//
        this.zhrOtext=param.getZhrOtext();//
        this.werks=param.getWerks();//
        this.werksTxt=param.getWerksTxt();//
        this.inst=param.getInst();//
        this.persg=param.getPersg();//
        this.zhrLoca=param.getZhrLoca();//
        this.levelpk=param.getLevelpk();//
        this.zhrPtext=param.getZhrPtext();//
        this.company=param.getCompany();//
        this.companyName=param.getCompanyName();//
        this.zhrBank=param.getZhrBank();//
        this.zhrAccount=param.getZhrAccount();//
        this.zzKhhs=param.getZzKhhs();//
        this.zzKhhd=param.getZzKhhd();//
        this.zzKhh=param.getZzKhh();//
        this.zzYhh=param.getZzYhh();//
        this.zzLhh=param.getZzLhh();//
        this.status=param.getStatus();//
        this.plans=param.getPlans();//
        this.flag = param.getFlag();
        if (param.getUpdateDate() != null) {
            Date updateTime = JTGKStringUtil.stringToDateDefault(param.getUpdateDate());
            if (updateTime != null) {
                this.updateDate = new SimpleDateFormat("yyyy-MM-dd").format(updateTime);
            } else {
                this.updateDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            }
        }


    }

}
