package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub.JTGKStringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "JTGKEMPLOYEERESULT")
public class EmployeeResultObject {
    @Id
    private String id;
    private String pernr ;//                  工号
    private String ename ;//                    名称
    private String inits ;//                英文名
    private String gesc ;//                         性别：1男2女
    private String orgeh ;//                   组织编码
    private String plans ;//                   职位编码
    private String zhrOtext ;//                组织名称
    private String zhrPtext ;//           职位
    private String unitId ;//                  业务单元编码
    private String unitTxt ;//             c
    private String systemId ;//
    private String systemTxt ;//
    private String centerId ;//             中心编码
    private String centerTxt ;//          中心
    private String deptId ;//
    private String deptName ;//
    private String officeId ;//
    private String officeName ;//
    private String stell ;//                   岗位编码
    private String zhrStext ;//           岗位
    private String usrid ;//           身份证
    private String landx50 ;//
    private String ltext ;//
    private String ptext ;//
    private String zhrTime1 ;//             入职时间
    private String zhrPtype ;//
    private String ftext ;//
    private String zhrEmail ;// 邮箱
    private String zhrCell ;//               手机号
    private String zhrTell ;//
    private String zhrLoca ;//
    private String zhrXqtc ;//
    private String zhrProv ;//
    private String zhrCity ;//                      常驻办公地
    private String zhrBank ;//  账户大行
    private String zhrAccount ;// 账号
    private String gbdat ;//
    private String gbort ;//
    private String zhrBtype ;//
    private String zhrZsfbs ;//
    private String zhrCjgz ;//
    private String locat ;//地址
    private String zhrRzrq ;//
    private String zhrLzrq ;//
    private String zzKhhs ;//
    private String zzKhhd ;//
    private String zzKhh ;//开户行
    private String zzYhh ;//
    private String zzLhh ;// 账户联行号
    private String trfgr ;//                      职级
    private String werks ;//
    private String werksT ;//
    private String btrtl ;//
    private String btrtlT ;//
    private String qdzt ;//
    private String qdztT ;//
    private String persg ;//
    private String persk ;//
    private String levelpk ;//
    private String zhrCost ;//                成本中心编码
    private String zhrCosttxt ;//    成本中心
    private String zhrYglx ;//
    private String zhrHypy ;//
    private String zhrFlag ;//
    private String zhrStatus ;//
    private String backTime ;//
    private String zgslx ;//
    private String company ;//
    private String companyName ;//
    private String parentOrgCode ;//             //父级组织编号
    private String directorCode ;//          //直管领导
    private String branchCode ;//               //分管领导
    private String flag ;//                         //修改标识N新增M修改D删除
    private String updateDate ;//数据更新时间
    private String sksyncstatus;// 司库同步状态  0未同步、1已同步 2 异常
    private String sksyncmsg;// 司库同步备注
    private String ygzhstatus;// 司库同步状态-员工支付信息  0未同步、1已同步 2 异常
    private String ygzhmsg;// 司库同步备注-员工支付信息
    private String ygstatus;// 司库同步状态-员工 -个人往来单位  0未同步、1已同步 2 异常
    private String ygmsg;// 司库同步备注-员工 -个人往来单位

    public EmployeeResultObject(EmployeeResultObjectDto param){
         this.id=param.getPernr();
         this.pernr = param.getPernr();
         this.ename = param.getEname();
         this.inits = param.getInits();
         this.gesc = param.getGesc();
         this.orgeh = param.getOrgeh();
         this.plans = param.getPlans();
         this.zhrOtext = param.getZhrOtext();
         this.zhrPtext = param.getZhrPtext();
         this.unitId = param.getUnitId();
         this.unitTxt = param.getUnitTxt();
         this.systemId = param.getSystemId();
         this.systemTxt = param.getSystemTxt();
         this.centerId = param.getCenterId();
         this.centerTxt = param.getCenterTxt();
         this.deptId = param.getDeptId();
         this.deptName = param.getDeptName();
         this.officeId = param.getOfficeId();
         this.officeName = param.getOfficeName();
         this.stell = param.getStell();
         this.zhrStext = param.getZhrStext();
         this.usrid = param.getUsrid();
         this.landx50 = param.getLandx50();
         this.ltext = param.getLtext();
         this.ptext = param.getPtext();
         this.zhrTime1 = param.getZhrTime1();
         this.zhrPtype = param.getZhrPtype();
         this.ftext = param.getFtext();
         this.zhrEmail = param.getZhrEmail();
         this.zhrCell = param.getZhrCell();
         this.zhrTell = param.getZhrTell();
         this.zhrLoca = param.getZhrLoca();
         this.zhrXqtc = param.getZhrXqtc();
         this.zhrProv = param.getZhrProv();
         this.zhrCity = param.getZhrCity();
         this.zhrBank = param.getZhrBank();
         this.zhrAccount = param.getZhrAccount();
         this.gbdat = param.getGbdat();
         this.gbort = param.getGbort();
         this.zhrBtype = param.getZhrBtype();
         this.zhrZsfbs = param.getZhrZsfbs();
         this.zhrCjgz = param.getZhrCjgz();
         this.locat = param.getLocat();
         this.zhrRzrq = param.getZhrRzrq();
         this.zhrLzrq = param.getZhrLzrq();
         this.zzKhhs = param.getZzKhhs();
         this.zzKhhd = param.getZzKhhd();
         this.zzKhh = param.getZzKhh();
         this.zzYhh = param.getZzYhh();
         this.zzLhh = param.getZzLhh();
         this.trfgr = param.getTrfgr();
         this.werks = param.getWerks();
         this.werksT = param.getWerksT();
         this.btrtl = param.getBtrtl();
         this.btrtlT = param.getBtrtlT();
         this.qdzt = param.getQdzt();
         this.qdztT = param.getQdztT();
         this.persg = param.getPersg();
         this.persk = param.getPersk();
         this.levelpk = param.getLevelpk();
         this.zhrCost = param.getZhrCost();
         this.zhrCosttxt = param.getZhrCosttxt();
         this.zhrYglx = param.getZhrYglx();
         this.zhrHypy = param.getZhrHypy();
         this.zhrFlag = param.getZhrFlag();
         this.zhrStatus = param.getZhrStatus();
         this.backTime = param.getBackTime();
         this.zgslx = param.getZgslx();
         this.company = param.getCompany();
         this.companyName = param.getCompanyName();
         this.parentOrgCode = param.getParentOrgCode();
         this.directorCode = param.getDirectorCode();
         this.branchCode = param.getBranchCode();
         this.flag = param.getFlag();
        if (param.getUpdateDate() != null) {
            Date updateTime = JTGKStringUtil.stringToDateDefault(param.getUpdateDate());
            if (updateTime != null) {
                this.updateDate = new SimpleDateFormat("yyyy-MM-dd").format(updateTime);
            } else {
                this.updateDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
            }
        }

    }
}
