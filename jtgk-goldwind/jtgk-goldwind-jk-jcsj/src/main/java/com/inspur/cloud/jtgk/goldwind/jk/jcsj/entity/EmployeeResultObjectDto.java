package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EmployeeResultObjectDto {
    private String	pernr	;//	                 工号
    private String	ename	;//	                   名称
    private String	inits	;//	               英文名
    private String	gesc	;//	                        性别：1男2女
    private String	orgeh	;//	                  组织编码
    private String	plans	;//	                  职位编码
    private String	zhrOtext	;//	               组织名称
    private String	zhrPtext	;//	          职位
    private String	unitId	;//	                 业务单元编码
    private String	unitTxt	;//	            业务单元
    private String	systemId	;//
    private String	systemTxt	;//
    private String	centerId	;//	            中心编码
    private String	centerTxt	;//	         中心
    private String	deptId	;//
    private String	deptName	;//
    private String	officeId	;//
    private String	officeName	;//
    private String	stell	;//	                  岗位编码
    private String	zhrStext	;//	          岗位
    private String	usrid	;//	          身份证
    private String	landx50	;//
    private String	ltext	;//
    private String	ptext	;//
    private String	zhrTime1	;//	            入职时间
    private String	zhrPtype	;//
    private String	ftext	;//
    private String	zhrEmail	;//	邮箱
    private String	zhrCell	;//	              手机号
    private String	zhrTell	;//
    private String	zhrLoca	;//
    private String	zhrXqtc	;//
    private String	zhrProv	;//
    private String	zhrCity	;//	                     常驻办公地
    private String	zhrBank	;//  银行账户开户行
    private String	zhrAccount	;//  银行账号
    private String	gbdat	;//
    private String	gbort	;//
    private String	zhrBtype	;//
    private String	zhrZsfbs	;//
    private String	zhrCjgz	;//
    private String	locat	;//
    private String	zhrRzrq	;//
    private String	zhrLzrq	;//
    private String	zzKhhs	;//
    private String	zzKhhd	;//
    private String	zzKhh	;//
    private String	zzYhh	;//
    private String	zzLhh	;//
    private String	trfgr	;//	                     职级
    private String	werks	;//
    private String	werksT	;//
    private String	btrtl	;//
    private String	btrtlT	;//
    private String	qdzt	;//
    private String	qdztT	;//
    private String	persg	;//
    private String	persk	;//
    private String	levelpk	;//
    private String	zhrCost	;//	               成本中心编码
    private String	zhrCosttxt	;//	   成本中心
    private String	zhrYglx	;//
    private String	zhrHypy	;//
    private String	zhrFlag	;//
    private String	zhrStatus	;//
    private String	backTime	;//
    private String	zgslx	;//
    private String	company	;//
    private String	companyName	;//
    private String	parentOrgCode	;//	            //父级组织编号
    private String	directorCode	;//	         //直管领导
    private String	branchCode	;//	              //分管领导
    private String	flag	;//	                        //修改标识N新增M修改D删除
    private String	updateDate	;//

}
