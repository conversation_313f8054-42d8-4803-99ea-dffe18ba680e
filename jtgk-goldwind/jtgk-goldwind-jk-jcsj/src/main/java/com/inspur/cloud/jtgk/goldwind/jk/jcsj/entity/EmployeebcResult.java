package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "JTGKEMPLOYEEBCRESULT")
public class EmployeebcResult {
    @Id
    private String id;
    private String	userId	;//人员编码
    private String	userName	;//人员姓名
    private String	unitId	;//所在单位编码
    private String	unitTxt	;//所在单位名称
    private String	systemId	;//所在系统编码
    private String	systemTxt	;//所在系统名称
    private String	stell	;//岗位编码
    private String	stext	;//岗位名称
    private String	deptId	;//所在部门编码
    private String	deptName	;//所在部门名称
    private String	zhrOtext	;//	 //人员直属组织名称
    private String	phoneNumber	;//电话
    private String	email	;//
    private String	centerId	;//所在中心编码
    private String	centerTxt	;//所在中心名称
    private String	zhrTime1	;//入司日期
    private String	zhrRzrq	;//入职日期
    private String	orgeh	;//人员直属组织编码
    private String	officeId	;//所在科室编码
    private String	officeTxt	;//所在科室名称
    private String	directorCode	;//人员直管领导编码
    private String	branchCode	;//人员分管领导编码
    private String	zhrCost	;//成本中心编码
    private String	zhrCosttxt	;//
    private String	werks	;//
    private String	werksTxt	;//
    private String	inst	;//
    private String	gesc	;//
    private String	persg	;//
    private String	zhrLoca	;//
    private String	levelpk	;//
    private String	zhrPtext	;//
    private String	status	;//
    private String	company	;//
    private String	companyName	;//
    private String	zhrBank	;//
    private String	zhrAccount	;//
    private String	zzKhhs	;//
    private String	zzKhhd	;//
    private String	zzKhh	;//
    private String	zzYhh	;//
    private String	zzLhh	;//
    private String	plans	;//
    private String flag ;//                         //修改标识N新增M修改D删除
    private String ygzhstatus;// 司库同步状态-员工支付信息  0未同步、1已同步 2 异常
    private String ygzhmsg;// 司库同步备注-员工支付信息
    private String ygstatus;// 司库同步状态-员工 -个人往来单位  0未同步、1已同步 2 异常
    private String ygmsg;// 司库同步备注-员工 -个人往来单位
    public EmployeebcResult(EmployeebcResult param){
        this.id = UUID.randomUUID().toString();
        this.userId = param.getUserId();
        this.userName = param.getUserName();
        this.unitId = param.getUnitId();
        this.unitTxt = param.getUnitTxt();
        this.systemId = param.getSystemId();
        this.stell = param.getStell();
        this.stext = param.getStext();
        this.deptId = param.getDeptId();
        this.deptName = param.getDeptName();
        this.zhrOtext = param.getZhrOtext();
        this.phoneNumber = param.getPhoneNumber();
        this.email = param.getEmail();
        this.centerId = param.getCenterId();
        this.centerTxt = param.getCenterTxt();
        this.zhrTime1 = param.getZhrTime1();
        this.zhrRzrq = param.getZhrRzrq();
        this.orgeh = param.getOrgeh();
        this.officeId = param.getOfficeId();
        this.officeTxt = param.getOfficeTxt();
        this.directorCode = param.getDirectorCode();
        this.branchCode = param.getBranchCode();
        this.zhrCost = param.getZhrCost();
        this.zhrCosttxt = param.getZhrCosttxt();
        this.werks = param.getWerks();
        this.werksTxt = param.getWerksTxt();
        this.inst = param.getInst();
        this.gesc = param.getGesc();
        this.persg = param.getPersg();
        this.zhrLoca = param.getZhrLoca();
        this.levelpk = param.getLevelpk();
        this.zhrPtext = param.getZhrPtext();
        this.status = param.getStatus();
        this.company = param.getCompany();
        this.companyName = param.getCompanyName();
        this.zhrBank = param.getZhrBank();
        this.zhrAccount = param.getZhrAccount();
        this.zzKhh=param.getZzKhh();
        this.zzKhhd=param.getZzKhhd();
        this.zzKhhs=param.getZzKhhs();
        this.zzLhh=param.getZzLhh();
        this.zzYhh=param.getZzYhh();
        this.plans=param.getPlans();

    }

}
