package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;


import com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub.JTGKStringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "JTGKGSRESULT")
public class GSResultObject {
    @Id
    private String id;
    private String	BUKRS	;//	公司代码
    private String	FULL_NAME	;//	公司代码或公司的名称
    private String	SPRAS	;//	语言代码
    private String	LAND1	;//	国家/地区代码
    private String	WAERS	;//	货币码
    private String	STCD5	;//	税号5
    private String	PSTLZ	;//	邮编
   // private String		;//	地址
    private String	ORT01	;//	城市
    private String	JXGKBUSI	;//	管理主体
    private String	BUSI	;//	业务单元
    private String	ZZGXHS	;//	是否纳入共享
    private String	ZZJXGKLW	;//	交叉管理例外
    private String	ZZ_INVALID	;//	是否注销
    private String	IS_DISCOUNT	;//	计算抵扣
    private String	flag	;//	                        //修改标识N新增M修改D删除
    private String updateDate ;//数据更新时间
    private String sksyncstatus;// 司库同步状态  0未同步、1已同步 2 异常
    private String sksyncmsg;// 司库同步备注
        public GSResultObject(GSResultObjectDto param) {
            this.id=param.getBUKRS();//公司代码
            this.BUKRS=param.getBUKRS();//公司代码
            this.FULL_NAME=param.getFULL_NAME();//公司代码或公司的名称
            this.SPRAS=param.getSPRAS();//语言代码
            this.LAND1=param.getLAND1();//国家/地区代码
            this.WAERS=param.getWAERS();//货币码
            this.STCD5=param.getSTCD5();//税号5
            this.PSTLZ=param.getPSTLZ();//邮编
            this.ORT01=param.getORT01();//城市
            this.JXGKBUSI= "1100".equals(param.getJXGKBUSI())?"1100.":param.getJXGKBUSI() ;//管理主体
            this.BUSI=param.getBUSI();//业务单元
            this.ZZGXHS=param.getZZGXHS();//是否纳入共享
            this.ZZJXGKLW=param.getZZJXGKLW();//交叉管理例外
            this.ZZ_INVALID=param.getZZ_INVALID();//是否注销
            this.IS_DISCOUNT=param.getIS_DISCOUNT();//计算抵扣
            this.updateDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

        }

}
