package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GSResultObjectDto {
    private String	BUKRS	;//	公司代码
    private String	FULL_NAME	;//	公司代码或公司的名称
    private String	SPRAS	;//	语言代码
    private String	LAND1	;//	国家/地区代码
    private String	WAERS	;//	货币码
    private String	STCD5	;//	税号5
    private String	PSTLZ	;//	邮编
    // private String		;//	地址
    private String	ORT01	;//	城市
    private String	JXGKBUSI	;//	管理主体
    private String	BUSI	;//	业务单元  因为SAP表中没有业务单元这个字段，现用来存储地址
    private String	ZZGXHS	;//	是否纳入共享
    private String	ZZJXGKLW	;//	交叉管理例外
    private String	ZZ_INVALID	;//	是否注销
    private String	IS_DISCOUNT	;//	计算抵扣
}
