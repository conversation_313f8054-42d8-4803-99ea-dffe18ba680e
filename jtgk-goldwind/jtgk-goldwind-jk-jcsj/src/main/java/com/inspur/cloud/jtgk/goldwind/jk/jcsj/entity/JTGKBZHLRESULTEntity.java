package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;


import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "JTGKBZHLRESULT")
public class JTGKBZHLRESULTEntity {
    @Id
    private	String		id;
    private	String		mandt;
    private	String		gw_category;
    private	String		gw_inputcurrency;
    private	String		gw_r_account;
    private	String		gw_r_entity;
    private	String		gw_time;
    private	String		bpc_signdata;

}
