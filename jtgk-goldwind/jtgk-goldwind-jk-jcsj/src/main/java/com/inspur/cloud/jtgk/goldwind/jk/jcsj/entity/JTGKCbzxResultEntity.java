package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@Entity
@Table(name = "JTGKCBZXRESULT")
public class JTGKCbzxResultEntity {
    @Id
    private String id;
    private String	group_id	;//	集团
    private String	cost_center_code	;//	成本中心编码
    private String	end_date	;//	有效截至日期
    private String	control_area	;//	控制范围
    private String	language_code	;//	语言代码
    private String	general_text	;//	一般姓名
    private String	long_text	;//	长文本
    private String	match_text	;//	匹配码搜索的搜索条件
    private String	cost_center_type	;//	成本中心类型
    private String	create_date	;//	创建日期
    private String	start_date	;//	开始生效日期
    private String	change_date	;//	更改日期
    private String	function_area	;//	功能范围
    private String	user_name	;//	输入者
    private String	cost_center_person	;//	成本中心负责人
    private String	cost_center_user	;//	成本中心负责用户
    private String	company_code	;//	公司代码
    private String	profit_center_code	;//	利润中心编码
    private String	region_code	;//	国家 / 地区代码
    private String	organization_code	;//	机构编码
    private String	organization_type	;//	机构类别
    private String	dept_code	;//	部门编码
    private String	dept_name	;//	部门名称
    private String	dept2_name	;//	部门名称2
    private String	object_identifier	;//	对象标识
    private String	object_number	;//	对象号
    private String	currency	;//	货币码
    private String	tax_jurisdiction	;//	税务管辖权
    private String	record_complete_flag	;//	成本中心主记录的完整标志
    private String	complete_description	;//	成本中心完整描述
    private String	statistical_object_flag	;//	标志(对象是统计的)
    private String	budgeted_cost_center	;//	有预算的成本中心
    private String	budget_availability_profile	;//	预算可用性控制：参数文件 budget_availability_active_flag
    private String  budget_availab_active_flag;//	成本中心的预算可用性控制已激活
    private String	actual_primary_freeze_flag	;//	实际初级成本的冻结标志
    private String	actual_secondary_freeze_flag	;//	实际次级成本的冻结标志
    private String	actual_income_freeze_flag	;//	实际收入登记的冻结标志
    private String	plann_primary_freeze_flag	;//	计划初级成本冻结标志
    private String	plann_secondary_freeze_flag	;//	计划次级成本冻结标志
    private String	plann_income_freeze_flag	;//	计划收入冻结标志
    private String	open_item_freeze_flag	;//	未清项的冻结标志
    private String	standard_hierarchy_area	;//	标准层次结构区域
    private String	cost_aggregator_code	;//	成本汇集器代码
    private String	flag	;//	                        //修改标识N新增M修改D删除
    private String updateDate ;//数据更新时间
    private String sksyncstatus;// 司库同步状态  0未同步、1已同步 2 异常
    private String sksyncmsg;// 司库同步备注

    public JTGKCbzxResultEntity(JTGKCbzxResultEntity param) {
        this.id= param.getGroup_id()+param.getCost_center_code()+param.getControl_area()+param.getEnd_date()+param.getLanguage_code();
        this.group_id=param.getGroup_id();//
        this.cost_center_code=param.getCost_center_code();//
        this.general_text=param.getGeneral_text();//
        this.long_text=param.getLong_text();//
        this.match_text=param.getMatch_text();//
        this.cost_center_type=param.getCost_center_type();//
        this.create_date=param.getCreate_date();//
        this.start_date=param.getStart_date();//
        this.end_date=param.getEnd_date();//
        this.change_date=param.getChange_date();//
        this.control_area=param.getControl_area();//
        this.function_area=param.getFunction_area();//
        this.user_name=param.getUser_name();//
        this.cost_center_person=param.getCost_center_person();//
        this.cost_center_user=param.getCost_center_user();//
        this.language_code=param.getLanguage_code();//
        this.company_code=param.getCompany_code();//
        this.profit_center_code=param.getProfit_center_code();//
        this.region_code=param.getRegion_code();//
        this.organization_code=param.getOrganization_code();//
        this.organization_type=param.getOrganization_type();//
        this.dept_code=param.getDept_code();//
        this.dept_name=param.getDept_name();//
        this.dept2_name=param.getDept2_name();//
        this.object_identifier=param.getObject_identifier();//
        this.object_number=param.getObject_number();//
        this.currency=param.getCurrency();//
        this.tax_jurisdiction=param.getTax_jurisdiction();
        this.record_complete_flag=param.getRecord_complete_flag();
        this.complete_description=param.getComplete_description();
        this.statistical_object_flag=param.getStatistical_object_flag() ;
        this.budgeted_cost_center=param.getBudgeted_cost_center();
        this.budget_availability_profile=param.getBudget_availability_profile();
        this.budget_availab_active_flag=param.getBudget_availab_active_flag();
        this.actual_primary_freeze_flag=param.getActual_primary_freeze_flag();
        this.actual_secondary_freeze_flag=param.getActual_secondary_freeze_flag();
        this.actual_income_freeze_flag=param.getActual_income_freeze_flag();
        this.plann_primary_freeze_flag=param.getPlann_primary_freeze_flag();
        this.plann_secondary_freeze_flag=param.getPlann_secondary_freeze_flag();
        this.plann_income_freeze_flag=param.getPlann_income_freeze_flag();
        this.open_item_freeze_flag=param.getOpen_item_freeze_flag();
        this.standard_hierarchy_area=param.getStandard_hierarchy_area();
        this.cost_aggregator_code=param.getCost_aggregator_code();
        this.updateDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
    }

    public JTGKCbzxResultEntity() {

    }
}
