package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.security.PublicKey;
import java.util.UUID;

@Data
@Entity
@Table(name = "JTGKCBZXGSGXRESULT")
public class JTGKCbzxgsgxresultEntity {
    @Id
    private String id;
    private String group_id;//
    private String control_area;//
    private String profit_center_code;//
    private String company_code;//

    public JTGKCbzxgsgxresultEntity(JTGKCbzxgsgxresultEntity param){
        this.id = param.getGroup_id()+param.getControl_area()+param.getProfit_center_code()+param.getCompany_code();
        this.group_id = param.getGroup_id();
        this.control_area = param.getControl_area();
        this.profit_center_code = param.getProfit_center_code();
        this.company_code = param.getCompany_code();
    }

    public JTGKCbzxgsgxresultEntity() {

    }
}
