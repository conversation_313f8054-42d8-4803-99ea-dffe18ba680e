package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "JTGKCKZHYEB")
public class JTGKCkzhyebResult {
    @Id
    private String id;
    private String  accountno;
    private String  unitname;
    private String  unitno;
    private String  cdlx;
    private String  ckdjbh;
    private String  ckdjbh_old;
    private String  startdate;
    private String  enddate;
    private String  qx;
    private String  klje;
    private String  dqye;
    private String  status;
    private BigDecimal ll;
    private String  yjtlx;
    private String  ywdybh;
    private String  sfbb;

}
