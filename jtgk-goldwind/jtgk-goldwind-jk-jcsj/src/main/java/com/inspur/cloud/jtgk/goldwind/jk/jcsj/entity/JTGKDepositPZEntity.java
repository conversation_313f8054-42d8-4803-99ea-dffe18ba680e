package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.UUID;

@Data
@Entity
@Table(name = "JTGKDEPOSITPZ")
public class JTGKDepositPZEntity {
    @Id
    private	String		id	;
    private	String		skcjbh	;//	场景编号
    private	String		skcjmc	;//	业务场景
    private	String		DJID	;//	单号（唯一主键）
    private	String		DJBH	;//	单号（唯一主键）
    private	String		cdlx_code	;//	存款类型编码
    private	String		cdlx_name	;//	存单类型/票据类型
    private	String		bukrs	;//	公司编码
    private	String		butxt	;//	公司名称
    private	String		actnbr	;//	银行账户
    private	String		zszfs	;//	收支方式
    private	String		waers	;//	币种编号
    private	String		waers_nm	;//	币种名称
    private	String		amount	;//	本金
    private	String		interest	;//	利息
    private	String		bnklz	;//	存款银行
    private	String		banka	;//	存款银行名称
    private	String		kunnr	;//	客商编号
    private	String		prctr	;//	利润中心
    private	String		ltext	;//	利润中心名称
    private	String		kostl	;//	成本中心编号
    private	String		ltext_cb	;//	成本中心名称
    private	String		zsgtx	;//	摘要
    private	String		zsqr	;//	申请人
    private	String		zblr	;//	办理人
    private	String		zdate	;//	日期
    private	String		flag	;
    private	String		status	;//司库同步状态	0未同步、1已同步
    private	String		msg	;//司库同步备注
    private	String		proxy_id	;
    private String DEPOSITTYPE;
    private String cdlx;
    private String pushdate;//推送时间
    private String pushr;//推送人
    private String blrq;//办理日期


}
