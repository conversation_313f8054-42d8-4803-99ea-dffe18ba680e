package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * 1通用配置类
 *
 * <AUTHOR>
 * @date 2024/10/09
 */
@Data
@Entity
@Table(name = "JTGKINTERFACECONFIG")
public class JTGKInterfaceConfig {
    @Id
    private String id;
    private String code;
    private String name;
    private String isEnable;// 0是停用，1是启用
    private String keyValue;// 值
    private String url;
    private String cxdate;

}

