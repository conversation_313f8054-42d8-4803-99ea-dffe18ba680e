package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@Entity
@Table(name = "JTGKKSZSJRESULT")
public class JTGKKSZSJResultEntity {
    @Id
    private String	id	;//	主键ID
    private String	category_code	;//	类别编码
    private String	category_name	;//	类别名称
    private String	code	;//	编码
    private String	assistant_code1	;//	辅助编码1
    private String	assistant_code2	;//	辅助编码2
    private String	assistant_code3	;//	辅助编码3
    private String	assistant_code4	;//	辅助编码4
    private String	mnemonic_code	;//	助记码
    private String	parent_id	;//	父ID
    private String	recorder_code	;//	制单人编码
    private String	recorder_name	;//	制单人名称
    private String	recorder_time	;//	制单人时间
    private String	pretrial_code	;//	预审人编码
    private String	pretrial_name	;//	预审人名称
    private String	pretrial_time	;//	预审时间
    private String	auditor_code	;//	审核人编码
    private String	auditor_name	;//	审核人名称
    private String	audit_time	;//	审核时间
    private String	audit_level	;//	审核级别
    private String	audit_flag	;//	审核标志 0 未提交 1 待审核  2 审核通过
    private String	retrieve_flag	;//	回退标志
    private String	retriever_code	;//	回退人编码
    private String	retriever_name	;//	回退人名称
    private String	retrieve_time	;//	回退时间
    private String	freeze_flag	;//	注销标志：0:正常 1:冻结 2:删除标记 3:物理删除
    private String	freezer_code	;//	注销人编码
    private String	freezer_name	;//	注销人名称
    private String	freeze_time	;//	注销时间
    private String	workflow_id	;//	工作流ID
    private String	recorder_corp	;//	制单人单位
    private String	mdm_code	;//	MDMCODE
    private String	audit_node_name	;//	审核节点名称
    private String	master_data_version	;//	主数据版本
    private String	physical_model_version	;//	实体模型版本
    private String	filing_flag	;//	归档标志
    private String	filing_user_code	;//	归档人编码
    private String	filing_user_name	;//	归档人名称
    private String	filing_date	;//	归档时间
    private String	lucence_flag	;//	检索标志
    private String	lucence_time	;//	创建检索时间
    private String	submit_corp	;//	提报单位
    private String	desc_long	;//	长描述
    private String	desc_short	;//	短描述
    private String	partner_type	;//	合作伙伴类型
    private String	merchant_code	;//	客商编码
    private String	merchant_name	;//	客商名称
    private String	search_term	;//	搜索词（简称）
    private String	language	;//	语言
    private String	trade_partner	;//	贸易伙伴
    private String	country_region	;//	国家/地区
    private String	province_code	;//	省份代码
    private String	city	;//	城市
    private String	postal_code	;//	邮政编码
    private String	remark	;//	备注
    private String	validate_msg	;//	校验信息和校验级别
    private String	modify_group_label_code	;//	修改分组标签编码
    private String	category_version	;//	分类数据模型版本
    private String	mdm_code_createable	;//	是否可建立主数据 0:不可以建立 1:可以建立
    private String	last_modify_recorder_code	;//	上一次变更人编码
    private String	last_modify_recorder_name	;//	上一次变更人名称
    private String	last_modify_record_time	;//	上一次变更时间
    private String	submit_time	;//	提报时间
    private String	flow_para	;//	工作流附加参数
    private String	file_count	;//	附件个数
    private String	temp_save_flag	;//	暂存标志 0 否 1 是
    private String	task_flag	;//	0:排队中 1:执行完毕
    private String	uuid	;//	uuid
    private String	error_msg	;//	报错提醒
    private String	auditing_flag	;//	0:未在最后一级审核中 1:正在最后一级审核中
    private String	release_flag	;//	0:该节点的一下节点不是发布节点 1:该节点的一下节点是发布节点
    private String	data_source_flag	;//	数据来源标识
    private String	last_modify_submit_corp	;//	最后修改组织
    private String	workflow	;//	工作流
    private String	stand_info	;//	数据标准信息
    private String	security_level_code	;//	密级
    private String	submitter_name	;//	提报人名称
    private String	address	;//	地址
    private String	tax_category	;//	税类别
    private String	partner_nature	;//	合作伙伴性质
    private String	tax_number	;//	统一社会信用代码（税号）
    private String	Billing_phone	;//	开票电话
    private String	mobile_phone	;//	移动电话
    private String	email	;//	E-MAIL
    private String	file	;//	归档_XDELE
    private String	freeze_customer	;//	冻结客户
    private String	delete_customer	;//	删除客户
    private String	freeze_client_companies	;//	冻结所有客户公司
    private String	freeze_sales_organizations	;//	冻结所有销售组织（预留）
    private String	freeze_supplier	;//	冻结供应商
    private String	delete_supplier	;//	删除供应商
    private String	freeze_supplier_companies	;//	冻结所有供应商公司
    private String	freeze_purchasing_organizations	;//	冻结所有采购组织
    private String	supply_range	;//	供应范围
    private String	legal_nature	;//	Legal Nature
    private String	crt_number	;//	CRT Number
    private String	icms_taxpayer	;//	ICMS Taxpayer
    private String	industry_main_type	;//	Industry Main Type
    private String	tax_declaration_type	;//	Tax Declaration Type
    private String	customer_group_abbreviation	;//	客户集团简称
    private String	second_level_company	;//	二级公司
    private String	affiliated_group	;//	所属集团简称(天眼查)
    private String	employee_code	;//	工号
    private String	is_finance	;//	是否金融
    private String	data_source	;//	数据来源
    private String	merchant_type	;//	客商类型
    private String	supplier_type	;//	供应商类型
    private String	customer_classification	;//	客户分类
    private String	transportation_supplier	;//	运输供应商
    private String	is_portal_supplier	;//	是否门户供应商
    private String	reason	;//	不是门户供应商的原因
    private String	tax_jurisdiction	;//	税管辖区
    private String	house_number	;//	门牌号
    private String	province_name	;//	省份名称
    private String	gw_resource_dev_Engineer	;//	金风资源开发工程师
    private String	wind_turbine_supply_chain	;//	是否风机供应链
    private String	contact_person	;//	联系人
    private String flag ;//                         //修改标识N新增M修改D删除
    private String updateDate ;//数据更新时间
    private String sksyncstatus;// 司库同步状态  0未同步、1已同步 2 异常
    private String sksyncmsg;// 司库同步备注

        public JTGKKSZSJResultEntity (JTGKKSZSJResultEntity param) {
            this.id=param.getId();//主键ID  
            this.category_code=param.getCategory_code();//类别编码
            this.category_name=param.getCategory_name();//类别名称
            this.code=param.getCode();//编码
            this.assistant_code1=param.getAssistant_code1();//辅助编码1
            this.assistant_code2=param.getAssistant_code2();//辅助编码2
            this.assistant_code3=param.getAssistant_code3();//辅助编码3
            this.assistant_code4=param.getAssistant_code4();//辅助编码4
            this.mnemonic_code=param.getMnemonic_code();//助记码
            this.parent_id=param.getParent_id();//父ID
            this.recorder_code=param.getRecorder_code();//制单人编码
            this.recorder_name=param.getRecorder_name();//制单人名称
            this.recorder_time=param.getRecorder_time();//制单人时间
            this.pretrial_code=param.getPretrial_code();//预审人编码
            this.pretrial_name=param.getPretrial_name();//预审人名称
            this.pretrial_time=param.getPretrial_time();//预审时间
            this.auditor_code=param.getAuditor_code();//审核人编码
            this.auditor_name=param.getAuditor_name();//审核人名称
            this.audit_time=param.getAudit_time();//审核时间
            this.audit_level=param.getAudit_level();//审核级别
            this.audit_flag=param.getAudit_flag();//审核标志 0 未提交 1 待审核  2 审核通过
            this.retrieve_flag=param.getRetrieve_flag();//回退标志
            this.retriever_code=param.getRetriever_code();//回退人编码
            this.retriever_name=param.getRetriever_name();//回退人名称
            this.retrieve_time=param.getRetrieve_time();//回退时间
            this.freeze_flag=param.getFreeze_flag();//注销标志：0:正常 1:冻结 2:删除标记 3:物理删除
            this.freezer_code=param.getFreezer_code();//注销人编码
            this.freezer_name=param.getFreezer_name();//注销人名称
            this.freeze_time=param.getFreeze_time();//注销时间
            this.workflow_id=param.getWorkflow_id();//工作流ID
            this.recorder_corp=param.getRecorder_corp();//制单人单位
            this.mdm_code=param.getMdm_code();//MDMCODE
            this.audit_node_name=param.getAudit_node_name();//审核节点名称
            this.master_data_version=param.getMaster_data_version();//主数据版本
            this.physical_model_version=param.getPhysical_model_version();//实体模型版本
            this.filing_flag=param.getFiling_flag();//归档标志
            this.filing_user_code=param.getFiling_user_code();//归档人编码
            this.filing_user_name=param.getFiling_user_name();//归档人名称
            this.filing_date=param.getFiling_date();//归档时间
            this.lucence_flag=param.getLucence_flag();//检索标志
            this.lucence_time=param.getLucence_time();//创建检索时间
            this.submit_corp=param.getSubmit_corp();//提报单位
            this.desc_long=param.getDesc_long();//长描述
            this.desc_short=param.getDesc_short();//短描述
            this.partner_type=param.getPartner_type();//合作伙伴类型
            this.merchant_code=param.getMerchant_code();//客商编码
            this.merchant_name=param.getMerchant_name();//客商名称
            this.search_term=param.getSearch_term();//搜索词（简称）
            this.language=param.getLanguage();//语言
            this.trade_partner=param.getTrade_partner();//贸易伙伴
            this.country_region=param.getCountry_region();//国家/地区
            this.province_code=param.getProvince_code();//省份代码
            this.city=param.getCity();//城市
            this.postal_code=param.getPostal_code();//邮政编码
            this.remark=param.getRemark();//备注
            this.validate_msg=param.getValidate_msg();//校验信息和校验级别
            this.modify_group_label_code=param.getModify_group_label_code();//修改分组标签编码
            this.category_version=param.getCategory_version();//分类数据模型版本
            this.mdm_code_createable=param.getMdm_code_createable();//是否可建立主数据 0:不可以建立 1:可以建立
            this.last_modify_recorder_code=param.getLast_modify_recorder_code();//上一次变更人编码
            this.last_modify_recorder_name=param.getLast_modify_recorder_name();//上一次变更人名称
            this.last_modify_record_time=param.getLast_modify_record_time();//上一次变更时间
            this.submit_time=param.getSubmit_time();//提报时间
            this.flow_para=param.getFlow_para();//工作流附加参数
            this.file_count=param.getFile_count();//附件个数
            this.temp_save_flag=param.getTemp_save_flag();//暂存标志 0 否 1 是
            this.task_flag=param.getTask_flag();//0:排队中 1:执行完毕
            this.uuid=param.getUuid();//uuid
            this.error_msg=param.getError_msg();//报错提醒
            this.auditing_flag=param.getAuditing_flag();//0:未在最后一级审核中 1:正在最后一级审核中
            this.release_flag=param.getRelease_flag();//0:该节点的一下节点不是发布节点 1:该节点的一下节点是发布节点
            this.data_source_flag=param.getData_source_flag();//数据来源标识
            this.last_modify_submit_corp=param.getLast_modify_submit_corp();//最后修改组织
            this.workflow=param.getWorkflow();//工作流
            this.stand_info=param.getStand_info();//数据标准信息
            this.security_level_code=param.getSecurity_level_code();//密级
            this.submitter_name=param.getSubmitter_name();//提报人名称
            this.address=param.getAddress();//地址
            this.tax_category=param.getTax_category();//税类别
            this.partner_nature=param.getPartner_nature();//合作伙伴性质
            this.tax_number=param.getTax_number();//统一社会信用代码（税号）
            this.Billing_phone=param.getBilling_phone();//开票电话
            this.mobile_phone=param.getMobile_phone();//移动电话
            this.email=param.getEmail();//E-MAIL
            this.file=param.getFile();//归档_XDELE
            this.freeze_customer=param.getFreeze_customer();//冻结客户
            this.delete_customer=param.getDelete_customer();//删除客户
            this.freeze_client_companies=param.getFreeze_client_companies();//冻结所有客户公司
            this.freeze_sales_organizations=param.getFreeze_sales_organizations();//冻结所有销售组织（预留）
            this.freeze_supplier=param.getFreeze_supplier();//冻结供应商
            this.delete_supplier=param.getDelete_supplier();//删除供应商
            this.freeze_supplier_companies=param.getFreeze_supplier_companies();//冻结所有供应商公司
            this.freeze_purchasing_organizations=param.getFreeze_purchasing_organizations();//冻结所有采购组织
            this.supply_range=param.getSupply_range();//供应范围
            this.legal_nature=param.getLegal_nature();//Legal Nature
            this.crt_number=param.getCrt_number();//CRT Number
            this.icms_taxpayer=param.getIcms_taxpayer();//ICMS Taxpayer
            this.industry_main_type=param.getIndustry_main_type();//Industry Main Type
            this.tax_declaration_type=param.getTax_declaration_type();//Tax Declaration Type
            this.customer_group_abbreviation=param.getCustomer_group_abbreviation();//客户集团简称
            this.second_level_company=param.getSecond_level_company();//二级公司
            this.affiliated_group=param.getAffiliated_group();//所属集团简称(天眼查)
            this.employee_code=param.getEmployee_code();//工号
            this.is_finance=param.getIs_finance();//是否金融
            this.data_source=param.getData_source();//数据来源
            this.merchant_type=param.getMerchant_type();//客商类型
            this.supplier_type=param.getSupplier_type();//供应商类型
            this.customer_classification=param.getCustomer_classification();//客户分类
            this.transportation_supplier=param.getTransportation_supplier();//运输供应商
            this.is_portal_supplier=param.getIs_portal_supplier();//是否门户供应商
            this.reason=param.getReason();//不是门户供应商的原因
            this.tax_jurisdiction=param.getTax_jurisdiction();//税管辖区
            this.house_number=param.getHouse_number();//门牌号
            this.province_name=param.getProvince_name();//省份名称
            this.gw_resource_dev_Engineer=param.getGw_resource_dev_Engineer();//金风资源开发工程师
            this.wind_turbine_supply_chain=param.getWind_turbine_supply_chain();//是否风机供应链
            this.contact_person=param.getContact_person();//联系人
            this.updateDate=new SimpleDateFormat("yyyy-MM-dd").format(new Date());

        }

    public JTGKKSZSJResultEntity() {

    }
}
