package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.UUID;

@Data
@Entity
@Table(name = "JTGKLRZXRESULT")
public class JTGKLrzxResultEntity {
    @Id
    private String id;
    private String	group_id	;//	集团
    private String	profit_center_code	;//	利润中心编码
    private String	control_area	;//	控制范围
    private String	end_date	;//	有效截至日期
    private String	language_code	;//	语言代码
    private String	general_text	;//	一般姓名
    private String	long_text	;//	长文本
    private String	match_text	;//	匹配码搜索的搜索条件
    private String	create_date	;//	创建日期
    private String	start_date	;//	开始生效日期
    private String	user_name	;//	输入者
    private String	profit_center_person	;//	利润中心负责人姓名
    private String	profit_center_group_code	;//	利润中心组编码
    private String	unit_code	;//	业务单元编码
    private String	flag	;//	                        //修改标识N新增M修改D删除
    private String updateDate ;//数据更新时间
    private String sksyncstatus;// 司库同步状态  0未同步、1已同步 2 异常
    private String sksyncmsg;// 司库同步备注

    public JTGKLrzxResultEntity(JTGKLrzxResultEntity param){
        this.id = param.getGroup_id()+param.getProfit_center_code()+param.getControl_area()+param.getEnd_date()+param.getLanguage_code();
                //UUID.randomUUID().toString();
        this.group_id = param.getGroup_id();
        this.profit_center_code = param.getProfit_center_code();
        this.control_area = param.getControl_area();
        this.end_date = param.getEnd_date();
        this.language_code = param.getLanguage_code();
        this.general_text = param.getGeneral_text();
        this.long_text = param.getLong_text();
        this.match_text = param.getMatch_text();
        this.create_date = param.getCreate_date();
        this.start_date = param.getStart_date();
        this.user_name = param.getUser_name();
        this.profit_center_person = param.getProfit_center_person();
        this.profit_center_group_code = param.getProfit_center_group_code();
        this.unit_code = param.getUnit_code();
        this.flag = param.getFlag();
        this.updateDate = param.getUpdateDate();
        this.sksyncstatus = param.getSksyncstatus();
        this.sksyncmsg = param.getSksyncmsg();

    }

    public JTGKLrzxResultEntity() {

    }
}
