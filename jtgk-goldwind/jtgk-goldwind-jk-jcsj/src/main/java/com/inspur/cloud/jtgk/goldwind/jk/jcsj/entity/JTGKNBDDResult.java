package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "JTGKNBDDRESULT")
public class JTGKNBDDResult {
    @Id
    private String	id	;//
    private String	mandt	;//	集团
    private String	order_no	;//	订单号
    private String	order_type	;//	订单类型
    private String	order_category	;//	订单类别
    private String	order_currency	;//	订单货币
    private String	order_status	;//	订单状态
    private String	modify_date	;//	订单主数据的更改日期
    private String	order_flag	;//	有关统计订单标志
    private String	reference_order_no	;//	参考订单号
    private String	enterer	;//	输入者
    private String	applicant	;//	申请人
    private String	applicant_phone	;//	申请人的电话号
    private String	relevant_person_phone	;//	有关人员的电话号
    private String	responsible_person	;//	负责人
    private String	last_modifier	;//	最后更改人
    private String	order_estimated_cost	;//	订单估算成本
    private String	depart	;//	部门
    private String	object_no	;//	对象号
    private String	profit_center_code	;//	利润中心
    private String	release_date	;//	发行日期
    private String	technical_complete_date	;//	技术完成日期
    private String	settlement_date	;//	结算日期
    private String	modify_time	;//	更改时间
    private String	create_time	;//	创建的时间
    private String	timestamp	;//	时戳
    private String	descc	;//	描述
    private String	long_text	;//	长文本存在
    private String	factory1_code	;//	工厂1
    private String	factory2_code	;//	工厂2
    private String	business_range	;//	业务范围
    private String	control_range	;//	控制范围
    private String	collector_code	;//	成本汇集器代码
    private String	position	;//	位置
    private String	condition_table_use	;//	条件表的用途
    private String	application	;//	应用程序
    private String	cost_calculation_table	;//	成本核算表
    private String	distribute_group	;//	分配组
    private String	settlement_cost_element	;//	结算成本要素
    private String	company_code	;//	公司代码
    private String	request_company_code	;//	请求公司代码
    private String	request_cost_center_code	;//	请求成本中心
    private String	cost_center_code	;//	负责的成本中心
    private String	basic_list_cost_center	;//	基本清单的成本中心
    private String	posted_cost_center	;//	实际过帐成本的成本中心
    private String	maintain_task_work_center	;//	维护任务的工作中心
    private String	wbs	;//	工作分解结构要素
    private String	difference_code	;//	差异码
    private String	result_analysis_code	;//	结果分析码
    private String	functional_scope	;//	功能范围
    private String	object_class	;//	对象类
    private String	external_order_number	;//	外部订单号
    private String	product_process	;//	生产过程
    private String	process_category	;//	处理类别
    private String	responsibilities_relate_factory	;//	与工作中心职责相关联的工厂
    private String	regulatory	;//	Regulatory
    private String	maintenance_reason	;//	维修原因
    private String	production_line	;//	产线
    private String	supplier_account_number	;//	供应商帐户号
    private String	storage_location	;//	存储地点
    private String	quality_nation_alloca_proj	;//	质量/国拨项目
    private String	zhtbh	;//	备用字段2
    public JTGKNBDDResult(JTGKNBDDResult param){
        this.id	=	param.getMandt()+param.getOrder_no()+param.getOrder_type()+param.getOrder_category()+param.getOrder_currency()	;//
        this.mandt	=	param.getMandt()	;//	集团
        this.order_no	=	param.getOrder_no()	;//	订单号
        this.order_type	=	param.getOrder_type()	;//	订单类型
        this.order_category	=	param.getOrder_category()	;//	订单类别
        this.order_currency	=	param.getOrder_currency()	;//	订单货币
        this.order_status	=	param.getOrder_status()	;//	订单状态
        this.modify_date	=	param.getModify_date()	;//	订单主数据的更改日期
        this.order_flag	=	param.getOrder_flag()	;//	有关统计订单标志
        this.reference_order_no	=	param.getReference_order_no()	;//	参考订单号
        this.enterer	=	param.getEnterer()	;//	输入者
        this.applicant	=	param.getApplicant()	;//	申请人
        this.applicant_phone	=	param.getApplicant_phone()	;//	申请人的电话号
        this.relevant_person_phone	=	param.getRelevant_person_phone()	;//	有关人员的电话号
        this.responsible_person	=	param.getResponsible_person()	;//	负责人
        this.last_modifier	=	param.getLast_modifier()	;//	最后更改人
        this.order_estimated_cost	=	param.getOrder_estimated_cost()	;//	订单估算成本
        this.depart	=	param.getDepart()	;//	部门
        this.object_no	=	param.getObject_no()	;//	对象号
        this.profit_center_code	=	param.getProfit_center_code()	;//	利润中心
        this.release_date	=	param.getRelease_date()	;//	发行日期
        this.technical_complete_date	=	param.getTechnical_complete_date()	;//	技术完成日期
        this.settlement_date	=	param.getSettlement_date()	;//	结算日期
        this.modify_time	=	param.getModify_time()	;//	更改时间
        this.create_time	=	param.getCreate_time()	;//	创建的时间
        this.timestamp	=	param.getTimestamp()	;//	时戳
        this.descc	=	param.getDescc()	;//	描述
        this.long_text	=	param.getLong_text()	;//	长文本存在
        this.factory1_code	=	param.getFactory1_code()	;//	工厂1
        this.factory2_code	=	param.getFactory2_code()	;//	工厂2
        this.business_range	=	param.getBusiness_range()	;//	业务范围
        this.control_range	=	param.getControl_range()	;//	控制范围
        this.collector_code	=	param.getCollector_code()	;//	成本汇集器代码
        this.position	=	param.getPosition()	;//	位置
        this.condition_table_use	=	param.getCondition_table_use()	;//	条件表的用途
        this.application	=	param.getApplication()	;//	应用程序
        this.cost_calculation_table	=	param.getCost_calculation_table()	;//	成本核算表
        this.distribute_group	=	param.getDistribute_group()	;//	分配组
        this.settlement_cost_element	=	param.getSettlement_cost_element()	;//	结算成本要素
        this.company_code	=	param.getCompany_code()	;//	公司代码
        this.request_company_code	=	param.getRequest_company_code()	;//	请求公司代码
        this.request_cost_center_code	=	param.getRequest_cost_center_code()	;//	请求成本中心
        this.cost_center_code	=	param.getCost_center_code()	;//	负责的成本中心
        this.basic_list_cost_center	=	param.getBasic_list_cost_center()	;//	基本清单的成本中心
        this.posted_cost_center	=	param.getPosted_cost_center()	;//	实际过帐成本的成本中心
        this.maintain_task_work_center	=	param.getMaintain_task_work_center()	;//	维护任务的工作中心
        this.wbs	=	param.getWbs()	;//	工作分解结构要素
        this.difference_code	=	param.getDifference_code()	;//	差异码
        this.result_analysis_code	=	param.getResult_analysis_code()	;//	结果分析码
        this.functional_scope	=	param.getFunctional_scope()	;//	功能范围
        this.object_class	=	param.getObject_class()	;//	对象类
        this.external_order_number	=	param.getExternal_order_number()	;//	外部订单号
        this.product_process	=	param.getProduct_process()	;//	生产过程
        this.process_category	=	param.getProcess_category()	;//	处理类别
        this.responsibilities_relate_factory	=	param.getResponsibilities_relate_factory()	;//	与工作中心职责相关联的工厂
        this.regulatory	=	param.getRegulatory()	;//	Regulatory
        this.maintenance_reason	=	param.getMaintenance_reason()	;//	维修原因
        this.production_line	=	param.getProduction_line()	;//	产线
        this.supplier_account_number	=	param.getSupplier_account_number()	;//	供应商帐户号
        this.storage_location	=	param.getStorage_location()	;//	存储地点
        this.quality_nation_alloca_proj	=	param.getQuality_nation_alloca_proj()	;//	质量/国拨项目
        this.zhtbh	=	param.getZhtbh()	;//	备用字段2
    }

    public JTGKNBDDResult() {

    }
}
