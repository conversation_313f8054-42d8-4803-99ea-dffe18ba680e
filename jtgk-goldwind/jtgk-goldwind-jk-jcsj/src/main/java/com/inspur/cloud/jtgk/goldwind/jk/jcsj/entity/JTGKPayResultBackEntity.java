package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "JTGKPayresultRESULT")
public class JTGKPayResultBackEntity {
    @Id
    private String	id;
    private String	PAY_ARRANGE	;//	付款安排编号
    private String	APPROVAL_RESULT	;//	审批结果
    private String	PAY_REQ	;//	付款申请编号
    private String	PAY_AMOUNT	;//	本次支付金额
    private String	PAY_METHOD	;//	付款方式编码
    private String	BANKK	;//	开票银行
    private String	BANKN	;//	银行账号
    private String	BOE_TYPE	;//	付款方式
    private String	BOE_HKONT	;//	科目编码
    private String	ZFBDT	;//	到期日
    private String	BILL_NUMBER	;//	承兑汇票号
    private String	BOE_PROVIDER	;//	出票方
    private String	BOE_RECEIVER	;//	受票方
    private String	indorser	;//	背书人
    private String	LIFNR_TEXT	;//	供应商名称
    private String	LIFNR	;//	供应商代码
    private String	BLART	;//	凭证类型（ZP）
    private String	PAY_DATE	;//	付款日期
    private String	WAERS	;//	币种
    private String	INVOICE_NUMBER	;//	发票编号
    private String	DISCOUNT_AMOUNT	;//	折扣支付金额
    private String	BANKK_RECEIVE	;//	联行号
    private String	KURSF	;//	汇率
    private String	KURSF_DATE	;//	汇率时间
    private String	REMARK	;//	备注
    private String	RETURN_FLAG	;//	标识符：是否退回剩余付款
    private String	ZRESERVE_I_F1	;//	备用字段1
    private String	ZRESERVE_I_F2	;//	备用字段2
    private String	ZRESERVE_I_F3	;//	备用字段3
    private String	ZRESERVE_I_F4	;//	备用字段4
    private String	ZRESERVE_I_F5	;//	备用字段5
    private String pushdate;//推送时间
    private String pushr;//推送人
    private	String		status	;//司库同步状态	0未同步、1已同步
    private	String		msg	;//司库同步备注
    private	String		proxy_id	;

}
