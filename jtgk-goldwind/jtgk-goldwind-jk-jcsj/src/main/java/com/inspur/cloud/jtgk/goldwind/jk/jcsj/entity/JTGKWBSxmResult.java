package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "JTGKWBSXMRESULT")
public class JTGKWBSxmResult {
    @Id
    private String	id;
    private String	mandt;//	集团
    private String	wbs_factor	;//	WBS要素
    private String	wbs_element;//	WBS元素
    private String	wbs_short_desc	;//	WBS短描述
    private String	wbs_short_upper_desc	;//	WBS短描述大写
    private String	wbs_identifier	;//	WBS元素简明标识
    private String	wbs_company_code	;//	WBS元素的公司代码
    private String	wbs_control_range	;//	WBS元素的控制范围
    private String	wbs_currency	;//	WBS元素货币
    private String	t_wbs_element;//	统计
    private String	standard_wbs	;//	标准WBS:
    private String	project_in_code	;//	项目编码（内码）
    private String	project_type	;//	项目类型
    private String	project_hierarchy_level	;//	项目层次的等级
    private String	profit_center_code	;//	利润中心编码
    private String	actual_posting_cost_center_code	;//	实际过账成本的成本中心编码
    private String	function_range	;//	功能范围
    private String	request_company_code	;//	请求公司代码
    private String	factory_code	;//	工厂编码
    private String	object_no	;//	对象号
    private String	object_class	;//	对象类
    private String	applicant_code	;//	申请人编码
    private String	applicant_name	;//	申请人姓名
    private String	responsible_person_code	;//	负责人编号（项目经理）
    private String	responsible_person_name	;//	负责人姓名（项目管理者）
    private String	creator_name	;//	创建对象的人员名称
    private String	create_date	;//	记录创建日期
    private String	modifier_name	;//	更改对象用户的名称
    private String	modify_date	;//	对象最后更改日期
    private String	plan_element_flag	;//	标志：计划元素
    private String	account_allocation_flag	;//	标志：科目分配元素
    private String	wbs_project_summary_flag	;//	标识：用于项目汇总的
    private String	keyword_id	;//	关键词
    private String	ps_progress	;//	PS


    public JTGKWBSxmResult(JTGKWBSxmResult param)
    {
        this.id=param.getMandt()+param.getWbs_factor();
        this.mandt=	param.getMandt()	;//	集团
        this.wbs_factor	=	param.getWbs_factor();//	WBS要素
        this.wbs_element=	param.getWbs_element()	;//	WBS元素
        this.wbs_short_desc	=	param.getWbs_short_desc();//	WBS短描述
        this.wbs_short_upper_desc	=	param.getWbs_short_upper_desc();//	WBS短描述大写
        this.wbs_identifier	=	param.getWbs_identifier();//	WBS元素简明标识
        this.wbs_company_code	=	param.getWbs_company_code();//	WBS元素的公司代码
        this.wbs_control_range	=	param.getWbs_control_range();//	WBS元素的控制范围
        this.wbs_currency	=	param.getWbs_currency();//	WBS元素货币
        this.t_wbs_element=	param.getT_wbs_element()	;//	统计
        this.standard_wbs	=	param.getStandard_wbs();//	标准WBS:
        this.project_in_code	=	param.getProject_in_code();//	项目编码（内码）
        this.project_type	=	param.getProject_type();//	项目类型
        this.project_hierarchy_level	=	param.getProject_hierarchy_level();//	项目层次的等级
        this.profit_center_code	=	param.getProfit_center_code();//	利润中心编码
        this.actual_posting_cost_center_code	=	param.getActual_posting_cost_center_code();//	实际过账成本的成本中心编码
        this.function_range	=	param.getFunction_range();//	功能范围
        this.request_company_code	=	param.getRequest_company_code();//	请求公司代码
        this.factory_code	=	param.getFactory_code();//	工厂编码
        this.object_no	=	param.getObject_no();//	对象号
        this.object_class	=	param.getObject_class();//	对象类
        this.applicant_code	=	param.getApplicant_code();//	申请人编码
        this.applicant_name	=	param.getApplicant_name();//	申请人姓名
        this.responsible_person_code	=	param.getResponsible_person_code();//	负责人编号（项目经理）
        this.responsible_person_name	=	param.getResponsible_person_name();//	负责人姓名（项目管理者）
        this.creator_name	=	param.getCreator_name();//	创建对象的人员名称
        this.create_date	=	param.getCreate_date();//	记录创建日期
        this.modifier_name	=	param.getModifier_name();//	更改对象用户的名称
        this.modify_date	=	param.getModify_date();//	对象最后更改日期
        this.plan_element_flag	=	param.getPlan_element_flag();//	标志：计划元素
        this.account_allocation_flag	=	param.getAccount_allocation_flag();//	标志：科目分配元素
        this.wbs_project_summary_flag	=	param.getWbs_project_summary_flag();//	标识：用于项目汇总的
        this.keyword_id	=	param.getKeyword_id();//	关键词
        this.ps_progress	=	param.getPs_progress();//	PS

    }

    public JTGKWBSxmResult() {

    }
}
