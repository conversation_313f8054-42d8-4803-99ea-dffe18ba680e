package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "JTGKJWHQZHYERESULT")
public class JwHqZhyeResultObject {
    @Id
    private String id;
    private String	MANDT	;//	集团
    private String	REFNBR	;//	付款ID
    private String	ACTNBR	;//	银行账号
    private String	ST_SEQNUM	;//	花旗银行账户对账单顺序号
    private String	WAERS	;//	货币码
    private String	ZOPEN_BAL	;//
    private String	ZCLOSE_BAL	;//金额
    private String	ZMT940_FNAME	;//	MT940文件名
    private String	ZMT940_FDAT	;//	对账单获取日期
    private String	BAL_DATE	;//
    private String	CDATE	;//
    private String	CTIEM	;//
    private String	USERNAME	;//
    private String	TCODE	;//
    private String	CHANGE_IND	;//
    private String	UUSERNAME	;//
    private String	UDATE	;//
    private String	UTIME	;//
    private String	UTCODE	;//
    private String	UCHANGE_IND	;//

    private String	flag	;//	                        //修改标识N新增M修改D删除

    private String updatedate ;//数据更新时间
    private String sksyncstatus;// 司库同步状态  0未同步、1已同步 2 异常
    private String sksyncmsg;// 司库同步备注
    public JwHqZhyeResultObject(JwHqZhyeResultObject param) {
        this.id = UUID.randomUUID().toString();
        this.MANDT=param.getMANDT();//集团
        this.REFNBR=param.getREFNBR();//付款ID
        this.ACTNBR=param.getACTNBR();//银行账号
        this.ST_SEQNUM=param.getST_SEQNUM();//花旗银行账户对账单顺序号
        this.WAERS=param.getWAERS();//货币码
        this.ZOPEN_BAL=param.getZOPEN_BAL();//
        this.ZCLOSE_BAL=param.getZCLOSE_BAL();//
        this.ZMT940_FNAME=param.getZMT940_FNAME();//MT940文件名
        this.ZMT940_FDAT=param.getZMT940_FDAT();//对账单获取日期
        this.BAL_DATE=param.getBAL_DATE();//
        this.CDATE=param.getCDATE();//
        this.CTIEM=param.getCTIEM();//
        this.USERNAME=param.getUSERNAME();//
        this.TCODE=param.getTCODE();//
        this.CHANGE_IND=param.getCHANGE_IND();//
        this.UUSERNAME=param.getUUSERNAME();//
        this.UDATE=param.getUDATE();//
        this.UTIME=param.getUTIME();//
        this.UTCODE=param.getUTCODE();//
        this.UCHANGE_IND=param.getUCHANGE_IND();//
        this.updatedate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());


    }



}
