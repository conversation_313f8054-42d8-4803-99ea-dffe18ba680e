package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "JTGKJWMGZHYERESULT")
public class JwMgZhyeResultObject {
    @Id
    private String id;
    private String	MANDT	;//	集团
    private String	GUID	;//
    private String	ZLINES	;//
    private String	FILE_TYPE	;//	30 个字符
    private String	FILENAME	;//	Char 60
    private String	MT94_20	;//	Char 60
    private String	MT94_21	;//	长度 16 字段
    private String	MT94_25	;//	长度为 40 的字符型字段
    private String	MT94_60	;//	长度为 40 的字符型字段
    private String	MT94_61	;//	字符100
    private String	MT94_86	;//	字符1024
    private String	MT94_62	;//	30 个字符
    private String	MT94_64	;//	30 个字符
    private String	MT94_65	;//	30 个字符
    private String	FINAL_86	;//	单字符标记
    private String	VALUE_DATE	;//	交易日期
    private String	ENTRY_DATE	;//
    private String	CREDIT_DEBIT	;//	付款方向：D付款，C收款
    private String	FOUNDS_D	;//	单字符标记
    private String	CURRENCY_CODE	;//	交易币种
    private String	AMOUNT	;//	交易金额
    private String	TRANS_TYPE	;//	字符字段长度为 10
    private String	REF_ID	;//	客户参考号
    private String	BANK_REF	;//	银行参考号
    private String	REFNBR	;//	付款ID
    private String	MSGID	;//	Char 60
    private String	CREDTTM	;//	Char 60
    private String	ORGNLMSGID	;//	文件ID
    private String	ORGNLNBOFTXS	;//	付款总笔数
    private String	ORGNLCTRLSUM	;//	付款总金额
    private String	GRPSTS	;//	JMP付款状态
    private String	ADDTLINF	;//	字符1024
    private String	ORGNLPMTINFID	;//	批次ID
    private String	ORGNLINSTRID	;//	文件中的顺序号
    private String	ORGNLENDTOENDID	;//	付款ID
    private String	TXSTS	;//	JMP付款状态
    private String	ACCTSVCRREF	;//	Char 60
    private String	AMT	;//	付款金额
    private String	AMT_CCY	;//	付款币种
    private String	REQDEXCTNDT	;//	付款指令的请求执行日期yyyy-mm-dd
    private String	AMT_BLANCE	;//	账号余额
    private String	AMT_CCY_BLANCE	;//	余额货币
    private String	DATE_BLANCE	;//	余额日期
    private String	INCLUDE	;//	通用修改更新结构
    private String	CDATE	;//	创建修改文档的数据
    private String	CTIEM	;//	时间已更改
    private String	USERNAME	;//	修改文档中的个人负责的用户名
    private String	TCODE	;//	ABAP 系统字段：当前事务代码
    private String	CHANGE_IND	;//	应用程序对象的更改类型
    private String	UUSERNAME	;//	修改文档中的个人负责的用户名
    private String	UDATE	;//	创建修改文档的数据
    private String	UTIME	;//	时间已更改
    private String	UTCODE	;//	已在其中进行更改的事务
    private String	UCHANGE_IND	;//	应用程序对象的更改类型
    private String	flag	;//	                        //修改标识N新增M修改D删除
    private String updatedate ;//数据更新时间
    private String sksyncstatus;// 司库同步状态  0未同步、1已同步 2 异常
    private String sksyncmsg;// 司库同步备注
    public JwMgZhyeResultObject(JwMgZhyeResultObject param) {
        this.id = UUID.randomUUID().toString();
        this.MANDT=param.getMANDT();//集团
        this.GUID=param.getGUID();//
        this.ZLINES=param.getZLINES();//
        this.FILE_TYPE=param.getFILE_TYPE();//30 个字符
        this.FILENAME=param.getFILENAME();//Char 60
        this.MT94_20=param.getMT94_20();//Char 60
        this.MT94_21=param.getMT94_21();//长度 16 字段
        this.MT94_25=param.getMT94_25();//长度为 40 的字符型字段
        this.MT94_60=param.getMT94_60();//长度为 40 的字符型字段
        this.MT94_61=param.getMT94_61();//字符100
        this.MT94_86=param.getMT94_86();//字符1024
        this.MT94_62=param.getMT94_62();//30 个字符
        this.MT94_64=param.getMT94_64();//30 个字符
        this.MT94_65=param.getMT94_65();//30 个字符
        this.FINAL_86=param.getFINAL_86();//单字符标记
        this.VALUE_DATE=param.getVALUE_DATE();//交易日期
        this.ENTRY_DATE=param.getENTRY_DATE();//
        this.CREDIT_DEBIT=param.getCREDIT_DEBIT();//付款方向：D付款，C收款
        this.FOUNDS_D=param.getFOUNDS_D();//单字符标记
        this.CURRENCY_CODE=param.getCURRENCY_CODE();//交易币种
        this.AMOUNT=param.getAMOUNT();//交易金额
        this.TRANS_TYPE=param.getTRANS_TYPE();//字符字段长度为 10
        this.REF_ID=param.getREF_ID();//客户参考号
        this.BANK_REF=param.getBANK_REF();//银行参考号
        this.REFNBR=param.getREFNBR();//付款ID
        this.MSGID=param.getMSGID();//Char 60
        this.CREDTTM=param.getCREDTTM();//Char 60
        this.ORGNLMSGID=param.getORGNLMSGID();//文件ID
        this.ORGNLNBOFTXS=param.getORGNLNBOFTXS();//付款总笔数
        this.ORGNLCTRLSUM=param.getORGNLCTRLSUM();//付款总金额
        this.GRPSTS=param.getGRPSTS();//JMP付款状态
        this.ADDTLINF=param.getADDTLINF();//字符1024
        this.ORGNLPMTINFID=param.getORGNLPMTINFID();//批次ID
        this.ORGNLINSTRID=param.getORGNLINSTRID();//文件中的顺序号
        this.ORGNLENDTOENDID=param.getORGNLENDTOENDID();//付款ID
        this.TXSTS=param.getTXSTS();//JMP付款状态
        this.ACCTSVCRREF=param.getACCTSVCRREF();//Char 60
        this.AMT=param.getAMT();//付款金额
        this.AMT_CCY=param.getAMT_CCY();//付款币种
        this.REQDEXCTNDT=param.getREQDEXCTNDT();//付款指令的请求执行日期yyyy-mm-dd
        this.AMT_BLANCE=param.getAMT_BLANCE();//账号余额
        this.AMT_CCY_BLANCE=param.getAMT_CCY_BLANCE();//余额货币
        this.DATE_BLANCE=param.getDATE_BLANCE();//余额日期
        //this.INCLUDE=param.getINCLUDE();//通用修改更新结构
        this.CDATE=param.getCDATE();//创建修改文档的数据
        this.CTIEM=param.getCTIEM();//时间已更改
        this.USERNAME=param.getUSERNAME();//修改文档中的个人负责的用户名
        this.TCODE=param.getTCODE();//ABAP 系统字段：当前事务代码
        this.CHANGE_IND=param.getCHANGE_IND();//应用程序对象的更改类型
        this.UUSERNAME=param.getUUSERNAME();//修改文档中的个人负责的用户名
        this.UDATE=param.getUDATE();//创建修改文档的数据
        this.UTIME=param.getUTIME();//时间已更改
        this.UTCODE=param.getUTCODE();//已在其中进行更改的事务
        this.UCHANGE_IND=param.getUCHANGE_IND();//应用程序对象的更改类型

        this.updatedate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
    }
}
