package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao.JxhResultObjectDto;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

@Data
@Entity
@Table(name = "JTGKJXHRESULT")
public class JxhResultObjectEntity {
    @Id
    private String	id	;//
    private String BUKRS;//公司编码
    private String HKONT;//会计科目
    private String BANKN;
    private String BUTXT;//公司名称
    private String BANKA;//银行名称
    private String PERNR;//账户负责人
    private String CNAME;//负责人名称
    private String MAIL;//负责人邮箱
    private String	flag	;//	                        //修改标识N新增M修改D删除
    private String updateDate ;//数据更新时间
    private String sksyncstatus;// 司库同步状态  0未同步、1已同步 2 异常
    private String sksyncmsg;// 司库同步备注

    public JxhResultObjectEntity(JxhResultObjectDto jxhResultObjectDto) {
        this.id = UUID.randomUUID().toString();
        this.BUKRS=jxhResultObjectDto.getBUKRS();
        this.HKONT=jxhResultObjectDto.getHKONT();
        this.BANKN=jxhResultObjectDto.getBANKN();
        this.BUTXT=jxhResultObjectDto.getBUTXT();
        this.BANKA=jxhResultObjectDto.getBANKA();
        this.PERNR=jxhResultObjectDto.getPERNR();
        this.CNAME=jxhResultObjectDto.getCNAME();
        this.MAIL=jxhResultObjectDto.getMAIL();
        this.updateDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
    }

    public JxhResultObjectEntity() {

    }
}
