package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * 数据库表名称：	formmain_0378
 */
@Getter
@Setter
public class OaSpResultObjectDto {

    private String	id	;//
    private String	state	;//
    private String	start_member_id	;//
    private String	start_date	;//
    private String	approve_member_id	;//
    private String	approve_date	;//
    private String	finishedflag	;//
    private String	ratifyflag	;//
    private String	ratify_member_id	;//
    private String	ratify_date	;//
    private String	sort	;//
    private String	modify_member_id	;//
    private String	modify_date	;//
    private String	field0001	;//	公司代码
    private String	field0002	;//	所属分公司
    private String	field0003	;//	公司名称
    private String	field0004	;//	部门名称
    private String	field0005	;//	公司法人
    private String	field0006	;//	水厂厂长
    private String	field0007	;//	水厂出纳
    private String	field0008	;//	水厂会计
    private String	field0009	;//	公章管理员
    private String	field0010	;//	文件管理员
    private String	field0011	;//	综合行政专员
    private String	field0012	;//	安全工程师
    private String	field0013	;//	分公司总经理
    private String	field0014	;//	分公司总工程师
    private String	field0015	;//	分公司财务总监
    private String	field0016	;//	分公司人力BP
    private String	field0017	;//	财务BP
    private String	field0018	;//	法务BP
    private String	field0019	;//	分公司增值副总
    private String	field0020	;//	采购BP
    private String	field0021	;//	分公司分管
    private String	rybh	;//	人员编号


}
