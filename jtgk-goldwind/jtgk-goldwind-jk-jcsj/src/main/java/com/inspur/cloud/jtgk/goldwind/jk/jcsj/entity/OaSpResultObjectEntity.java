package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@Entity
@Table(name = "JTGKOASPRESULT")
public class OaSpResultObjectEntity {
    @Id
    private String	id	;//
    private String	state	;//
    private String	start_member_id	;//
    private String	start_date	;//
    private String	approve_member_id	;//
    private String	approve_date	;//
    private String	finishedflag	;//
    private String	ratifyflag	;//
    private String	ratify_member_id	;//
    private String	ratify_date	;//
    private String	sort	;//
    private String	modify_member_id	;//
    private String	modify_date	;//
    private String	field0001	;//	公司代码
    private String	field0002	;//	所属分公司
    private String	field0003	;//	公司名称
    private String	field0004	;//	部门名称
    private String	field0005	;//	公司法人
    private String	field0006	;//	水厂厂长
    private String	field0007	;//	水厂出纳
    private String	field0008	;//	水厂会计
    private String	field0009	;//	公章管理员
    private String	field0010	;//	文件管理员
    private String	field0011	;//	综合行政专员
    private String	field0012	;//	安全工程师
    private String	field0013	;//	分公司总经理
    private String	field0014	;//	分公司总工程师
    private String	field0015	;//	分公司财务总监
    private String	field0016	;//	分公司人力BP
    private String	field0017	;//	财务BP
    private String	field0018	;//	法务BP
    private String	field0019	;//	分公司增值副总
    private String	field0020	;//	采购BP
    private String	field0021	;//	分公司分管
    private String	rybh	;//	人员编号
    private String	flag	;//	                        //修改标识N新增M修改D删除
    private String updateDate ;//数据更新时间
    private String sksyncstatus;// 司库同步状态  0未同步、1已同步 2 异常
    private String sksyncmsg;// 司库同步备注

    public OaSpResultObjectEntity(OaSpResultObjectDto param) {
        this.id=param.getId();//
        this.state=param.getState();//
        this.start_member_id=param.getStart_member_id();//
        this.start_date=param.getStart_date();//
        this.approve_member_id=param.getApprove_member_id();//
        this.approve_date=param.getApprove_date();//
        this.finishedflag=param.getFinishedflag();//
        this.ratifyflag=param.getRatifyflag();//
        this.ratify_member_id=param.getRatify_member_id();//
        this.ratify_date=param.getRatify_date();//
        this.sort=param.getSort();//
        this.modify_member_id=param.getModify_member_id();//
        this.modify_date=param.getModify_date();//
        this.field0001=param.getField0001();//公司代码
        this.field0002=param.getField0002();//所属分公司
        this.field0003=param.getField0003();//公司名称
        this.field0004=param.getField0004();//部门名称
        this.field0005=param.getField0005();//公司法人
        this.field0006=param.getField0006();//水厂厂长
        this.field0007=param.getField0007();//水厂出纳
        this.field0008=param.getField0008();//水厂会计
        this.field0009=param.getField0009();//公章管理员
        this.field0010=param.getField0010();//文件管理员
        this.field0011=param.getField0011();//综合行政专员
        this.field0012=param.getField0012();//安全工程师
        this.field0013=param.getField0013();//分公司总经理
        this.field0014=param.getField0014();//分公司总工程师
        this.field0015=param.getField0015();//分公司财务总监
        this.field0016=param.getField0016();//分公司人力BP
        this.field0017=param.getField0017();//财务BP
        this.field0018=param.getField0018();//法务BP
        this.field0019=param.getField0019();//分公司增值副总
        this.field0020=param.getField0020();//采购BP
        this.field0021=param.getField0021();//分公司分管
        this.rybh=param.getRybh();
        this.updateDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
    }

    public OaSpResultObjectEntity() {

    }
}
