package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 交易明细生单入参
 *
 * <AUTHOR>
 * @date 2021/09/08.
 */
@Data
public class OfflineTransDetailsEntity {
    /**
     * 交易流水号
     */
    private String bankFlowNo;
    /**
     * 收支属性必填 1付款 2收款
     */
    private Integer incomeOrExpenditure;
    /**
     * 账户开户单位ID-必填
     */
    private String accountUnit;
    /**
     * 账户ID 必填
     * 如没有用账号查账户表，找到传过来：BFBANKACCOUNTS
     */
    private String bankAccountID;
    /**
     * 银行账号-必填
     */
    private String bankAccountNo;
    /**
     * 币种ID 必填
     */
    private String currency;
    /**
     * 交易金额 必填
     */
    private BigDecimal settlementAmount;

    /**
     * 对方账号-划拨类必填
     * 业务类型BIZTYPE不是01,02,11,12,17,21,22，就属于划拨类
     * 如果是划拨类的流水，产品会根据对方账号，给对方单位ID（ReciprocalUnit）和对方账户ID（reciprocalAccount）赋值，不用传参
     */
    private String reciprocalAccountNo;
    /**
     * 对方账号名称
     */
    private String reciprocalAccName;
    /**
     * 对方开户行名
     *      */
    private String bankCodeOfReciprocalAccount;
    /**
     * 对方省
     */
    private String provice;
    /**
     * 对方市
     */
    private String city;
    /**
     * 摘要 必填
     */
    private String summary;
    /**
     * 交易状态必填 1未生成2已生成3已核销4无需生成5作废
     */
    private Integer transStatus;
    /**
     * 交易日期 必填
     */
    private Date transactionDate;
    /**
     * 交易时间
     */
    private Date transactionTime;
    /**
     * 即时金额（当前余额），这笔交易明细发生之后，账户的实时余额，一般为直联调度使用
     */
    private BigDecimal immediateAmount;
    /**
     * 单据来源必填 01: 直联程序 02: 手工导入03: 财务公司采集 04：手工录入
     */
    private String dataSrc;
    // 银行原始流水号
    private String OrigBankFlowNo;
    // 扩展字段
    private String TXT01;
    private String TXT02;
    private String TXT03;
    private String TXT04;
    private String TXT05;
    private String TXT06;
    private String TXT07;
    private String FK01;
    private String FK02;
    private String FK03;
    private String FK04;
    private String FK05;
    private Integer NUM01;
    private Integer NUM02;
    private Integer NUM03;
    private Integer NUM04;
    private Integer NUM05;
    private BigDecimal AMT01;
    private BigDecimal AMT02;
    private BigDecimal AMT03;
    private BigDecimal AMT04;
    private BigDecimal AMT05;
    private Date DATE01;
    private Date DATE02;
    private Date TIME01;
    private Date TIME02;
}
