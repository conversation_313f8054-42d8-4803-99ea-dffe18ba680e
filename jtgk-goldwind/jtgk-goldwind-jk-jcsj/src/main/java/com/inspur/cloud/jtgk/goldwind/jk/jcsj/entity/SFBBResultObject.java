package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "JTGKSFBBRESULT")
public class SFBBResultObject {
    @Id
    private  String id;
    @Column(name = "MANDT")
    private String mandt; // 集团

    @Column(name = "C_SCOPE")
    private String cscope; // 合并组

    @Column(name = "C_ENTITY")
    private String centity; // 公司

    @Column(name = "C_TIME")
    private String ctime; // 期间

    @Column(name = "C_SCOPE_T")
    private String cscopet; // 合并组描述

    @Column(name = "C_ENTITY_T")
    private String centityt; // 子公司描述

    @Column(name = "C_SCOPE_PARENT")
    private String cscopeparent; // 父级合并组

    @Column(name = "C_SCOPE_TYPE")
    private String cscopetype; // 合并组类型

    @Column(name = "SIGNEDDATA")
    private String signeddata; // SIGNDAT

    @Column(name = "flag")
    private String flag; // 修改标识N新增M修改D删除

    @Column(name = "updateDate")
    private String updatedate; // 数据更新时间

    @Column(name = "sksyncstatus")
    private String sksyncstatus; // 司库同步状态  0未同步、1已同步 2 异常

    @Column(name = "sksyncmsg")
    private String sksyncmsg; // 司库同步备注
    public SFBBResultObject(SFBBResultObjectDto param) {
        this.id= param.getC_ENTITY()+param.getC_TIME()+param.getC_SCOPE_TYPE()+param.getC_SCOPE();
        this.mandt=param.getMANDT();//集团
        this.cscope=param.getC_SCOPE();//合并组
        this.centity=param.getC_ENTITY();//公司
        this.ctime=param.getC_TIME();//期间
        this.cscopet=param.getC_SCOPE_T();//合并组描述
        this.centityt=param.getC_ENTITY_T();//子公司描述
        this.cscopeparent=param.getC_SCOPE_PARENT();//父级合并组
        this.cscopetype=param.getC_SCOPE_TYPE();//合并组类型
        this.signeddata=param.getSIGNEDDATA();//SIGNDATA
        this.updatedate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
    }
}
