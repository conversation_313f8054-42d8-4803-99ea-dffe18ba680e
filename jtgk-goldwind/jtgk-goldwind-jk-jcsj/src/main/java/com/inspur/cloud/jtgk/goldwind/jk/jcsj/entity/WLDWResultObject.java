package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "JTGKWLDWRESULT")
public class WLDWResultObject {
    @Id
    private String id;
    private String	PARTNER	;//	  客户编码
    private String NAME_ORG1 ;//客户名称
    private String VBUND ;//客户性质  1、2、3、4枚举提供  内部单位存在公司主数据
    private String	flag	;//	                        //修改标识N新增M修改D删除
    private String updateDate ;//数据更新时间
    private String sksyncstatus;// 司库同步状态  0未同步、1已同步 2 异常
    private String sksyncmsg;// 司库同步备注

    public WLDWResultObject(WLDWResultObjectDto param) {
            this.id=param.getPARTNER();
            this.PARTNER=param.getPARTNER();
            this.NAME_ORG1=param.getNAME_ORG1();
            this.VBUND=param.getVBUND();
            this.updateDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());
    }
}
