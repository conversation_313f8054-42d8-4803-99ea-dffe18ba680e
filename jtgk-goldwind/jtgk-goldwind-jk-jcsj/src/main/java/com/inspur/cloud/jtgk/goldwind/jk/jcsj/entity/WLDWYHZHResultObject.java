package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "JTGKWLDWYHZHRESULT")
public class WLDWYHZHResultObject {
    @Id
    private String id;
    private String	KOINH	;//	银行账号
    private String	BANKL	;//	银行代码 银行SAP编码
    private String	BANKS	;//	银行国家代码
    private String	LIFNR	;//	业务伙伴编号
    private String	NAME1	;//	贸易伙伴名称
    private String	EBPP_ACCNAME	;//	账户持有人
    private String	flag	;//	                        //修改标识N新增M修改D删除
    private String updateDate ;//数据更新时间
    private String sksyncstatus;// 司库同步状态  0未同步、1已同步 2 异常
    private String sksyncmsg;// 司库同步备注
    public WLDWYHZHResultObject(WLDWYHZHResultObjectDto param) {
        this.id=param.getKOINH();
        this.KOINH=param.getKOINH();//银行账号
        this.BANKL=param.getBANKL();//银行代码 银行-开户行 SAP编码
        this.BANKS=param.getBANKS();//银行国家代码
        this.LIFNR=param.getLIFNR();//业务伙伴编号
        this.NAME1=param.getNAME1();//贸易伙伴名称
        this.EBPP_ACCNAME=param.getEBPP_ACCNAME();//账户持有人
        this.updateDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());


    }
}
