package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import com.itextpdf.text.pdf.PRIndirectReference;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "JTGKXJLLXMRESULT")
public class XJLLXMResultObject {
    @Id
    private  String id;
    private String BUKRS;//公司代码
    private String RSTGR;//原因代码
    private String TXT40;//长文本
    private String	flag	;//	                        //修改标识N新增M修改D删除
    private String updateDate ;//数据更新时间
    private String sksyncstatus;// 司库同步状态  0未同步、1已同步 2 异常
    private String sksyncmsg;// 司库同步备注
    public XJLLXMResultObject(XJLLXMResultObjectDto param) {
        this.id=param.getRSTGR();
        this.BUKRS=param.getBUKRS();
        this.RSTGR=param.getRSTGR();
        this.TXT40=param.getTXT40();
        this.updateDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

    }
}
