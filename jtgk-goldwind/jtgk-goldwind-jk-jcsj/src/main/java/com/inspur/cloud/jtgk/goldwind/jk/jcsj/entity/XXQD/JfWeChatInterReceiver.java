package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.XXQD;

import io.iec.edp.caf.message.api.receiver.MessageReceiver;

import java.util.ArrayList;
import java.util.List;

public class JfWeChatInterReceiver extends MessageReceiver {

    public String toString() {
        return "JfWeChatInterReceiver(ygh=" + getUserCode() + ")";
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1; // 初始化 result
        List<String> $userid = getUserCode(); // 将 $usercode 定义为 List<String>
        return result * PRIME + ((usercode == null) ? 43 : $userid.hashCode());
    }

    protected boolean canEqual(Object other) {
        return other instanceof JfWeChatInterReceiver;
    }

    public boolean equals(Object o) {
        if (o == this) return true;
        if (!(o instanceof JfWeChatInterReceiver)) return false;

        JfWeChatInterReceiver other = (JfWeChatInterReceiver) o;
        if (!other.canEqual(this)) return false;

        List<String> this$usercode = getUserCode();
        List<String> other$usercode = other.getUserCode();

        return (this$usercode == null) ? (other$usercode == null) : this$usercode.equals(other$usercode);
    }

    public void setUsercode(List<String> usercode) {
        this.usercode = usercode;
    }

    /**接收者用户id，多个接收者以逗号分隔*/
    private List<String> usercode = new ArrayList<>();

    public List<String> getUserCode() {
        return this.usercode;
    }
}
