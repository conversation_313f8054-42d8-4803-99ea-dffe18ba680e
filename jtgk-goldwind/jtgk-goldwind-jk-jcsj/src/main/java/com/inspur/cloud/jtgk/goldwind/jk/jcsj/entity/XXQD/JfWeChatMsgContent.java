package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.XXQD;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao.JfTextContent;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao.JfTextcardContent;
import io.iec.edp.caf.message.api.dingtalk.content.TextContent;
import io.iec.edp.caf.message.api.msgcontent.MessageContent;
import io.iec.edp.caf.message.api.qywechat.content.TextcardContent;
import lombok.Getter;
import lombok.Setter;


@Getter
@Setter
public class JfWeChatMsgContent extends MessageContent {
    private TextcardContent textcardContent;

    public void setTextcardContent(TextcardContent textcardContent) {
        this.textcardContent = textcardContent;
    }

    private TextContent textContent;

    public void setTextContent(TextContent textContent) {
        this.textContent = textContent;
    }

    public boolean equals(Object o) {
        if (o == this) return true;
        if (!(o instanceof JfWeChatMsgContent)) return false;
        JfWeChatMsgContent other = (JfWeChatMsgContent) o;
        if (!other.canEqual(this)) return false;
        Object this$textcardContent = getTextcardContent(), other$textcardContent = other.getTextcardContent();
        if ((this$textcardContent == null) ? (other$textcardContent != null) : !this$textcardContent.equals(other$textcardContent))
            return false;
        Object this$textContent = getTextContent(), other$textContent = other.getTextContent();
        return !((this$textContent == null) ? (other$textContent != null) : !this$textContent.equals(other$textContent));
    }

    protected boolean canEqual(Object other) {
        return other instanceof JfWeChatMsgContent;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $textcardContent = getTextcardContent();
        result = result * 59 + (($textcardContent == null) ? 43 : $textcardContent.hashCode());
        Object $textContent = getTextContent();
        return result * 59 + (($textContent == null) ? 43 : $textContent.hashCode());
    }

    public String toString() {
        return "JfWeChatMsgContent(textcardContent=" + getTextcardContent() + ", textContent=" + getTextContent() + ")";
    }

    public TextcardContent getTextcardContent() {
        return this.textcardContent;
    }

    public TextContent getTextContent() {
        return this.textContent;
    }
}
