package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.text.SimpleDateFormat;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "JTGKYHRESULT")
public class YHResultObject {
    @Id
    private String id;
    private String	MANDT	;//	集团
    private String	BANKS	;//	银行国家代码
    private String	BANKL	;//	银行代码
    private String	BANKA	;//	银行名称
    private String	PROVZ	;//	地区（省/自治区/直辖市、市、县）
    private String	STRAS	;//	街道和房屋号
    private String	ORT01	;//	城市
    private String	SWIFT	;//	国际付款的 SWIFT/BIC	如果国家是非CN， 用这个找到SAP的银行代码， 作为唯一标识
    private String	LOEVM	;//	删除标识符
    private String	BNKLZ	;//	银行编号  联行号	如果国家是CN， 用这个找到SAP的银行代码， 作为唯一标识
    private String	BRNCH	;//	分行	描述，具体到哪个支行上

    private String zzbranch_code;
    private String zzrouting_code;
    private String	flag	;//	                        //修改标识N新增M修改D删除
    private String updateDate ;//数据更新时间
    private String sksyncstatus;// 司库同步状态  0未同步、1已同步 2 异常
    private String sksyncmsg;// 司库同步备注
    public YHResultObject(YHResultObjectDto param) {
        this.id=param.getBANKL()+param.getBANKS();//银行代码
        this.MANDT=param.getMANDT();//集团
        this.BANKS=param.getBANKS();//银行国家代码
        this.BANKL=param.getBANKL();//银行代码
        this.BANKA=param.getBANKA();//银行名称
        this.PROVZ=param.getPROVZ();//地区（省/自治区/直辖市、市、县） 
        this.STRAS=param.getSTRAS();//街道和房屋号
        this.ORT01=param.getORT01();//城市
        this.SWIFT=param.getSWIFT();//国际付款的 SWIFT/BIC
        this.LOEVM=param.getLOEVM();//删除标识符 
        this.BNKLZ=param.getBNKLZ();//银行编号  联行号
        this.BRNCH=param.getBRNCH();//分行
        this.zzbranch_code=param.getZzbranch_code();
        this.zzrouting_code=param.getZzrouting_code();
        this.updateDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

    }
}
