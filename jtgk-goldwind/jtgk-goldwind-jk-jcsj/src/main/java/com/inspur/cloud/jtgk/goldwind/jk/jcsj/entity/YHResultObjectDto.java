package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class YHResultObjectDto {
    private String	MANDT	;//	集团
    private String	BANKS	;//	银行国家代码
    private String	BANKL	;//	银行代码
    private String	BANKA	;//	银行名称
    private String	PROVZ	;//	地区（省/自治区/直辖市、市、县）
    private String	STRAS	;//	街道和房屋号
    private String	ORT01	;//	城市
    private String	SWIFT	;//	国际付款的 SWIFT/BIC	如果国家是非CN， 用这个找到SAP的银行代码， 作为唯一标识
    private String	LOEVM	;//	删除标识符
    private String	BNKLZ	;//	银行编号  联行号	如果国家是CN， 用这个找到SAP的银行代码， 作为唯一标识
    private String	BRNCH	;//	分行	描述，具体到哪个支行上
    private String zzbranch_code;
    private String zzrouting_code;

}
