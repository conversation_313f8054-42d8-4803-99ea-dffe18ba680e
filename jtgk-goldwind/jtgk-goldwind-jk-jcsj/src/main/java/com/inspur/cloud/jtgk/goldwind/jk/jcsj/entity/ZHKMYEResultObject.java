package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.*;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "JTGKZHKMYEB")
public class ZHKMYEResultObject {
    @Id
    private String id;
    private String	BUKRS	;//	公司代码
    private String	HKONT	;//	会计科目
    private BigDecimal BALANCE_CUM	;//	科目余额
    private String	WAERS	;//	币种
    private String  CURTYPE;//调取FAGLB03,取货币类型 00凭证货币 10公司代码货币 30集团货币
    private String	ZRESERVE_F1	;//	预留字段1
    private String	ZRESERVE_F2	;//	预留字段2
    private String	ZRESERVE_F3	;//	预留字段3
    private String	year	;//	年份
    private String	MOUTH	;//	月份
    private String	RQ	;//	日期
    private String	accountno	;//	账号
    private String	account	;//	账户id
    private String updateDate ;//数据更新时间
    public ZHKMYEResultObject(ZHKMYEResultObject param,String currentYear,String currentMonth,String rq) {
        this.id = UUID.randomUUID().toString();
        this.BUKRS=param.getBUKRS();//公司代码
        this.HKONT=param.getHKONT();//会计科目
        this.BALANCE_CUM=param.getBALANCE_CUM();//科目余额
        this.WAERS=param.getWAERS();//币种
        this.CURTYPE=param.getCURTYPE();//货币类型
        this.ZRESERVE_F1=param.getZRESERVE_F1();//预留字段1
        this.ZRESERVE_F2=param.getZRESERVE_F2();//预留字段2
        this.ZRESERVE_F3=param.getZRESERVE_F3();//预留字段3
        this.year=currentYear;//年份
        this.MOUTH=currentMonth;//月份
        this.RQ= !StringUtils.isEmpty(rq)?rq:new SimpleDateFormat("yyyyMMdd").format(new Date());//日期
        this.accountno=param.getAccountno();//账号
        this.account=param.getAccount();//账户id
        this.updateDate= new SimpleDateFormat("yyyy-MM-dd").format(new Date());

    }
}
