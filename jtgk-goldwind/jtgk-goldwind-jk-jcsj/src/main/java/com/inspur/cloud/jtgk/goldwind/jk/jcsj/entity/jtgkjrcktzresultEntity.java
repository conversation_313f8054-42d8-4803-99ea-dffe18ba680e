package com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "JTGKJRCKTZRESULT")
public class jtgkjrcktzresultEntity {
    @Id
    private	String		id;
    private	String		serial;
    private	String		bank_name;
    private	String		product;
    private	String		product_name;
    private	String		low_amount;
    private	String		hign_amount;
    private	String		configprice;
    private	String		currentrate;
    private	String		configdate;
    private	String		enddate;
    private	String		deposit_day;
    private	String		expected_amonut;
    private	String		deposit_state;
    private String      amount;
    private String      clint_name;
    private String      payment_method;
}
