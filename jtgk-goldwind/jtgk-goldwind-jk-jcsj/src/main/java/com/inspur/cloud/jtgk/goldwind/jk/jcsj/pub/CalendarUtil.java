package com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub;

import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.scheduler.calendar.api.manager.CalendarBasicInfoManager;

import java.time.OffsetDateTime;

/**
 * <AUTHOR>
 * @date 2023/11/14 9:32
 */
public class CalendarUtil {
    public static boolean judgeDateIsWorkDate(String calendarId,OffsetDateTime date){
        if(date==null){
            date=OffsetDateTime.now();
        }
        CalendarBasicInfoManager calendarBasicInfoManager = SpringBeanUtils.getBean(CalendarBasicInfoManager.class);
        boolean workDate = calendarBasicInfoManager.isWorkDate(calendarId, date);
        return workDate;
    }
    public static boolean judgeDateIsRestDate(String calendarId,OffsetDateTime date){
        if(date==null){
            date=OffsetDateTime.now();
        }
        CalendarBasicInfoManager calendarBasicInfoManager = SpringBeanUtils.getBean(CalendarBasicInfoManager.class);
        boolean workDate = calendarBasicInfoManager.isRestDate(calendarId, date);
        return workDate;
    }
}