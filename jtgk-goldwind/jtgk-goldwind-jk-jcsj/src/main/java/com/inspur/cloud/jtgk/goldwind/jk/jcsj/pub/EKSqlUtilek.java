package com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub;

import io.iec.edp.caf.commons.utils.SpringBeanUtils;

import javax.persistence.EntityManager;
import javax.persistence.EntityTransaction;
import javax.persistence.Query;
import java.util.Map;

public class EKSqlUtilek {

    //region 获取数据方法
    static public String getStringValue(Map dt, String field) {
        Object ans = dt.get(field);
        return ans == null ? "" : ans.toString();
    }
    /**
     * 5.执行更新语句
     *
     * @param upSQL
     */
    public static int executeUpdateSQL(String upSQL, Object... params) {
        EntityManager entityManager = SpringBeanUtils.getBean(EntityManager.class);
        EntityManager tempManager = entityManager.getEntityManagerFactory().createEntityManager();
        EntityTransaction transaction = tempManager.getTransaction();
        try {
            transaction.begin();
            Query nativeQuery = tempManager.createNativeQuery(upSQL);
            for (int i = 0; i < params.length; i++) {
                nativeQuery.setParameter(i + 1, params[i]);
            }
            int ret = nativeQuery.executeUpdate();
            transaction.commit();
            return ret;
        } catch (Throwable e) {
            //e.printStackTrace();
            transaction.rollback();
            return 0;
        } finally {
            tempManager.close();
        }
    }
}
