package com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub;

import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Service
public class GUIDUtils {
    public static String appendCurrentDateTime() {
        // 获取当前日期和时间
        LocalDateTime now = LocalDateTime.now();

        // 提取月、日、时、分、秒（确保都是两位数）
        String month = String.format("%02d", now.getMonthValue());
        String day = String.format("%02d", now.getDayOfMonth());
        String hour = String.format("%02d", now.getHour());
        String minute = String.format("%02d", now.getMinute());
        String second = String.format("%02d", now.getSecond());

        // 将月、日、时、分、秒拼接到前缀字符串上
        return  month + day + hour + minute + second;
    }

    public static String appendCurrentDateTimeyear() {
        // 获取当前日期和时间
        LocalDateTime now = LocalDateTime.now();

        // 提取年、月、日、时、分、秒（确保都是两位数，年份除外但这里保持格式统一可加前缀00以凑齐位数，或直接使用）
        String year = String.format("%04d", now.getYear()); // 年份通常是4位数
        String month = String.format("%02d", now.getMonthValue());
        String day = String.format("%02d", now.getDayOfMonth());
        String hour = String.format("%02d", now.getHour());
        String minute = String.format("%02d", now.getMinute());
        String second = String.format("%02d", now.getSecond());

        // 注意：这里我没有添加前缀字符串，因为您的原始方法也没有。如果您需要添加，请在返回的字符串前加上。
        // 例如：return "Prefix_" + year + month + day + hour + minute + second;

        // 将年、月、日、时、分、秒拼接到一起
        return year + month + day + hour + minute + second;
    }
}
