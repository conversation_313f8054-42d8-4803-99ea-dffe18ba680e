package com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub;


import java.text.SimpleDateFormat;
import java.util.Date;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import cn.hutool.core.date.DateUtil;

public class JTGKStringUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(JTGKStringUtil.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static boolean isEmpty(String param) {
        if (StringUtils.isEmpty(param) || StringUtils.isEmpty(param.trim()))
            return true;
        return false;
    }

    public static Date stringToDate(String dateStr, String format) {
        if (isEmpty(dateStr))
            return null;
        try {
            if (format == null)
                return DateUtil.parse(dateStr);
            else
                return DateUtil.parse(dateStr, format);
        } catch (Exception e) {
            LOGGER.error("------>>>日期转换失败,{},{}", dateStr, format, e);
            return null;
        }
    }

    public static int getStringBytesLength(String msg, String encode) {
        if (encode == null)
            encode = "utf-8";
        try {
            byte[] bytes = msg.getBytes(encode);
            return bytes.length;
        } catch (Exception e) {
            return -1;
        }
    }

    public static Date stringToDateDefault(String dateStr) {
        int length = dateStr.length();
        String formatStr = "";
        if (length == 8) {
            formatStr = "yyyyMMdd";
        } else if (length == 14) {
            formatStr = "yyyyMMddHHmmss";
        } else if (length == 10) {
            formatStr = "yyyy-MM-dd";
        } else {
            formatStr = "yyyy-MM-dd HH:mm:ss";
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(formatStr);
            return sdf.parse(dateStr);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * byte截取
     *
     * @param content
     * @param len
     * @return
     */
    public static String getLenString(String content, int len) {
        if (content == null || content.length() == 0) {
            return content;
        }
        byte[] bytes = content.getBytes();
        if (bytes.length > len) {
            int tempLen = new String(bytes, 0, len).length();
            content = content.substring(0, tempLen);
            // 防止最后一个字符的长度不是一个字节数
            if (content.getBytes().length > len) {
                content = content.substring(0, tempLen - 1);
            }
        }
        return content;
    }

    public static <T> T jsonToBean(String jsonString, Class<T> clazz) {
        try {
            return objectMapper.readValue(jsonString, clazz);
        } catch (Exception e) {
            e.printStackTrace(); // 适当处理异常
            return null;
        }
    }
}