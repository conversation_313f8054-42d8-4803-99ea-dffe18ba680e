package com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class ResultParamGenerate {

    public Map<String, Object> getSuccess() {
        Map<String, Object> result = new HashMap<>();
        result.put("result", true);
        result.put("Message", "");
        result.put("Code", 1);
        Map<String,Object> valueVal = new HashMap<>();
        valueVal.put("RESULT", true);
        result.put("value", valueVal);
        return result;
    }

    public Map<String, Object> getFail(String msg) {
        Map<String, Object> result = new HashMap<>();
        result.put("result", false);
        result.put("Message", msg);
        result.put("Code", -1);
        Map<String,Object> valueVal = new HashMap<>();
        valueVal.put("RESULT", false);
        result.put("value", valueVal);
        return result;
    }
}
