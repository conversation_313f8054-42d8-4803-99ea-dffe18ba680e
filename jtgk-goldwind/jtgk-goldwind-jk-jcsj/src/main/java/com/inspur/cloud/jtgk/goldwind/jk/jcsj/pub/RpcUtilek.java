package com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub;

import io.iec.edp.caf.common.JSONSerializer;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.rpc.api.service.RpcClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
public class RpcUtilek {
    /**
     * 调用rpc接口
     *
     * @param serviceId 接口名
     * @param su 服务单元
     * @param param 参数
     * @param exceptionMap 错误信息
     * @param clazz 返回对象类型
     * @return 返回对象
     * <AUTHOR>
     * @date 2021/9/23
     */
    public static <T> T rpcCall(String serviceId, String su, LinkedHashMap<String, Object> param,
                                Map<String, String> exceptionMap, Class<T> clazz) {
        log.error("rpcCall [{}] begin, param is {}", serviceId, JSONSerializer.serialize(param));

        T result = null;

        try {
            RpcClient client = SpringBeanUtils.getBean(RpcClient.class);

            result = client.invoke(clazz, serviceId, su, param, null);

        } catch (Throwable e) {
            String message = getBizErrorInException(e);

            exceptionMap.put("message", message);

            log.error("rpcCall fail:", e);
        }

        log.info("rpcCall [{}] end, response is:{}", serviceId, JSONSerializer.serialize(result));

        return result;
    }

    /**
     * 获取捕获异常中的业务信息 在内部存在RPC调用时，扔出的异常会套两层“RPC调用异常”，不可直接拿外层的message。单独的web api请求可能前端处理了此种情况
     *
     * @param e 捕获的异常
     * @return Message
     */
    public static String getBizErrorInException(Throwable e) {
        String message = e.getMessage();
        Throwable innerException = e;

        if (innerException.getCause() != null) {
            do {
                innerException = innerException.getCause();
                message = innerException.getMessage();
            } while (innerException.getCause() != null);
        }

        if (StringUtils.isBlank(message)) {
            message = innerException.toString();
        }

        return message;
    }
    /**map给entity赋值
     * 来源为map，返回为entity
     * @param map
     * @param clazz
     * @return
     */
    public static Object mapToObject(Map<String, Object> map,Class<?> clazz) throws JTGKExtendException {
        if(map == null){
            return null;
        }
        Object entity = null;
        try {
            entity = clazz.newInstance();
            //获取到所有属性，不包括继承的属性
            Field[] fields = entity.getClass().getDeclaredFields();
            //获取到所有属性，不包括继承的属性
            Field[] supFields = entity.getClass().getSuperclass().getDeclaredFields();
            for(Field field : fields){
                //获取字段的修饰符
                int mod = field.getModifiers();
                if(Modifier.isStatic(mod) || Modifier.isFinal(mod)){
                    continue;
                }
                if(map.containsKey(field.getName().toUpperCase())){
                    field.setAccessible(true);
                    if(field.getType().equals(Integer.class)) {
                        //对于int类型特殊处理，因为oracle数据库下 idp返回的map内类型为BigDecimal，直接赋值给转实体会有问题
                        String intVaule=ObjectToBigDecimal(map.get(field.getName().toUpperCase())).toPlainString();
                        //根据属性名称去map获取value ,转换成大写
                        field.set(entity,Integer.valueOf(intVaule));
                    }else{
                        //根据属性名称去map获取value ,转换成大写
                        field.set(entity, map.get(field.getName().toUpperCase()));
                    }
                }
            }
        }catch (Throwable throwable){
            log.error("map转实体异常："+ throwable.getMessage(), throwable);
            throw new JTGKExtendException("mapToObject:"+throwable.getMessage());
        }
        return entity;
    }
    /** object转换为BigDecimal
     * 异常情况下，返回null
     * @param obj
     * @return
     */
    public static BigDecimal ObjectToBigDecimal(Object obj) {
        return (obj==null) ? BigDecimal.ZERO : new BigDecimal(String.valueOf(obj));
    }
}
