package com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub;

import cn.hutool.http.webservice.SoapClient;
import cn.hutool.json.JSONObject;
import cn.hutool.json.XML;
import com.inspur.edp.cdp.common.utils.spring.SpringUtil;
import com.inspur.fastdweb.util.StringUtil;
import com.inspur.idd.log.api.controller.LogService;
import org.springframework.stereotype.Service;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

@Service
public class WebServiceUtilek {
    private static final int TIMEOUT_SEND = 30000;


    /**
     * @Description:(循环遍历map节点和value,拼接成xml)
     * @param: Map<?, ?> map
     **/
    public static void mapToXML(Map<?, ?> map, StringBuffer sb) {
        Set<?> set = map.keySet();
        for (Iterator<?> it = set.iterator(); it.hasNext(); ) {
            String key = (String) it.next();
            Object value = map.get(key);

            if (value instanceof Map) {
                sb.append("<" + key + ">");
                mapToXML((Map<?, ?>) value, sb);
                sb.append("</" + key + ">");
            } else if (value instanceof List) {
                List<?> list = (List<?>) map.get(key);
                sb.append("<" + key + ">");
                for (int i = 0; i < list.size(); i++) {
                    Map<?, ?> hm = (Map<?, ?>) list.get(i);
                    mapToXML(hm, sb);
                }
                sb.append("</" + key + ">");
            } else {
                sb.append("<" + key + ">" + value + "</" + key + ">");
            }
        }
    }

    // 这边进行调用定制化xml的根节点
    public static String getXmlByMap(Map<?, ?> map) {
        StringBuffer sb = new StringBuffer();
        sb.append("<![CDATA[");
        sb.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>");
        mapToXML(map, sb);
        sb.append("]]>");
        return sb.toString();
    }

    public static String getXmlByMap2(Map<?, ?> map) {
        StringBuffer sb = new StringBuffer();
        mapToXML2(map, sb);
        return sb.toString();
    }

    /**
     * 将null处理成""
     * @param map
     * @param sb
     */
    public static void mapToXML2(Map<?, ?> map, StringBuffer sb) {
        Set<?> set = map.keySet();
        for (Iterator<?> it = set.iterator(); it.hasNext(); ) {
            String key = (String) it.next();
            Object value = map.get(key);

            if (value instanceof Map) {
                sb.append("<" + key + ">");
                mapToXML2((Map<?, ?>) value, sb);
                sb.append("</" + key + ">");
            } else if (value instanceof List) {
                List<?> list = (List<?>) map.get(key);
                sb.append("<" + key + ">");
                for (int i = 0; i < list.size(); i++) {
                    Map<?, ?> hm = (Map<?, ?>) list.get(i);
                    mapToXML2(hm, sb);
                }
                sb.append("</" + key + ">");
            } else {
                if(null==value){
                    value="";
                }
                sb.append("<" + key + ">" + value + "</" + key + ">");
            }
        }
    }

    /**
     * ListMap根据map中的一个列做分类形成分组
     * @param list 需要形成树状图的listmap
     * @param key 需要做分类的map的key
     * @return 树状map
     */
    public static Map<String, Object> listMapClassification(List<HashMap> list, String key) {
        //创建一个空的新map
        Map<String, Object> newMap = new HashMap<>();
        //循环listmap
        for (int s = 0; s<list.size(); s++){
            //创建一个暂存数据的list
            List<Map<String, Object>> mapList = new ArrayList<>();
            //取出当前list循环下的Map
            Map<String, Object> objectMap = list.get(s);
            //取出map中按key分类好类的值列表
            //第一次循环因为没有插入过值，所以会为空
            Object get = newMap.get((String) objectMap.get(key));
            //判断分好类的当前类的名字下的值是否为空
            if(get!=null){
                //不为空就将泛型转为list
                mapList=(List<Map<String, Object>>) get;
            }
            //将当前循环的下的那个map插入，分好类的list中
            mapList.add(objectMap);
            //将当前循环插入好的list，重新给分类赋值
            newMap.put((String) objectMap.get(key),mapList);
        }
        return newMap;
    }

    @SuppressWarnings("unchecked")
    public static <T> T convertToJavaBean(String xml, Class<T> c) {
        T t = null;
        try {
            JAXBContext context = JAXBContext.newInstance(c);
            Unmarshaller unmarshaller = context.createUnmarshaller();
            t = (T) unmarshaller.unmarshal(new StringReader(xml));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return t;
    }

    public static String webServiceRequest(LogService logService,String djbh,String url,String soapAction,String data,String username,String password,boolean authFlag){
        String xmlString=null;
        try{
            URL realURL = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) realURL.openConnection();
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setRequestProperty("Content-Type", "text/xml; charset=UTF-8");
//                connection.setRequestProperty("Content-Type", "application/soap+xml;charset=UTF-8");
            //connection.setRequestProperty("content-length",String.valueOf(xmlData.length));
            connection.setRequestProperty("SOAPAction",soapAction);
            connection.setRequestMethod("POST");
            if(authFlag){
                String authString = username + ":" + password;
                String authStringEncoded = Base64.getEncoder().encodeToString(authString.getBytes());
                String basicAuth = "Basic " + authStringEncoded;
                connection.setRequestProperty("Authorization", basicAuth);
            }
            DataOutputStream printOut = new DataOutputStream(connection.getOutputStream());
            printOut.write(data.getBytes("UTF-8"));
            printOut.flush();
            printOut.close();

            // 从连接的输入流中取得回执信息
            InputStream inputStream = connection.getInputStream();
            InputStreamReader isr = new InputStreamReader(inputStream,"UTF-8");
            BufferedReader bufreader = new BufferedReader(isr);

            StringBuilder xmlStringBuilder = new StringBuilder();
            int c;
            while ((c = bufreader.read()) != -1) {
                xmlStringBuilder.append((char) c);
            }
             xmlString = xmlStringBuilder.toString();
            logService.error(djbh,"请求"+url+"返回结果：{}",xmlString);
//            log.error("请求"+url+"返回结果：{}",xmlString);
            isr.close();
        } catch (Throwable ex){
            ex.printStackTrace();
            logService.error(djbh,"请求"+url+"返回结果：{}",xmlString);
        }
        finally {
            return xmlString;
        }
    }
    /**
     * WebService接口调用
     *
     * @param url        地址
     * @param methodName 方法名
     * @param header     header部
     * @param params     参数
     * @return 返回结果（XML）
     */
    public static JSONObject webApiRequest(String url, String nameSpaceUrl, String methodName, Map header, Map params) {
        // log.info("****************** WebService Api调用。地址：" + url + ", 参数：" + JSON.toJSONString(params));
        try {
            SoapClient client = SoapClient.create(url)
                    .charset("UTF-8")
                    .setConnectionTimeout(TIMEOUT_SEND)
                    .setReadTimeout(TIMEOUT_SEND);


            if (StringUtil.isNullOrEmpty(nameSpaceUrl)) {
                client.setMethod(methodName);
            } else {
                client.setMethod(methodName, nameSpaceUrl);
            }

            if (header == null) {
                header = new HashMap();
//                header.put("Content-Type", "text/xml");
                header.put("Content-Type", "application/soap+xml;charset=UTF-8");
                header.put("SOAPAction", "http://tempuri.org/RPCSKWC");
            } else {
//                header.put("Content-Type", "text/xml");
                header.put("Content-Type", "application/soap+xml;charset=UTF-8");
                header.put("SOAPAction", "http://tempuri.org/RPCSKWC");
            }
            client.addHeaders(header);

            if (params != null) {
                client.setParams(params, false);
            }

            //  log.info("****************** WebService Api送信内容：" + client.getMsgStr(false));

            String xmlResponse = client.send();

            // log.info("****************** WebService Api调用结束。结果：" + xmlResponse);

            return XML.toJSONObject(xmlResponse);
        } catch (Exception ex) {
            //  log.error("****************** WebService Api调用结束。结果异常：" + ex.getMessage(), ex);

            return null;
        }
    }
}
