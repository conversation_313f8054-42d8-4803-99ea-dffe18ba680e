package com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub;

import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.service.JtgkEnvironmentService;
import com.inspur.fastdweb.core.FastdwebSqlSession;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
public class WechatUtilek {


    public static String getToken(){
//        JtgkEnvironmentService jtgkEnvironmentService = SpringBeanUtils.getBean(JtgkEnvironmentService.class);
//        String url=jtgkEnvironmentService.GetCacheValue("wechatUrl", 1, TimeUnit.DAYS);
//        String sendurl=jtgkEnvironmentService.GetCacheValue("wechatSendUrl", 1, TimeUnit.DAYS);
//        String appid=jtgkEnvironmentService.GetCacheValue("wechatAppid", 1, TimeUnit.DAYS);
//        String appsecret =jtgkEnvironmentService.GetCacheValue("wechatAppsecret", 1, TimeUnit.DAYS);
//        String wechatkey=jtgkEnvironmentService.GetCacheValue("wechatkey", 1, TimeUnit.DAYS);
        FastdwebSqlSession sqlSession=SpringBeanUtils.getBean(FastdwebSqlSession.class);
        String url="";//jtgkEnvironmentService.GetCacheValue("wechatUrl", 1, TimeUnit.DAYS);
        String sendurl="";//jtgkEnvironmentService.GetCacheValue("wechatSendUrl", 1, TimeUnit.DAYS);
        String appid="";//jtgkEnvironmentService.GetCacheValue("wechatAppid", 1, TimeUnit.DAYS);
        String appsecret ="";//jtgkEnvironmentService.GetCacheValue("wechatAppsecret", 1, TimeUnit.DAYS);
        String agentId ="";//jtgkEnvironmentService.GetCacheValue("agentId", 1, TimeUnit.DAYS);
        String wechatkey="";//jtgkEnvironmentService.GetCacheValue("wechatkey", 1, TimeUnit.DAYS);
        List<Map> cofigList =sqlSession.selectList(Map.class,"select ISENABLE , KEYVALUE,CODE from JTGKINTERFACECONFIG  " +
                "where CODE IN ('wechatUrl','wechatSendUrl','wechatAppid','wechatAppsecret','agentId','wechatkey','debugger')  AND ISENABLE='1'") ;
        for(Map cofigmap:cofigList){
            if("wechatUrl".equals(cofigmap.get("CODE"))){
                url= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
            }
            else if("wechatSendUrl".equals(cofigmap.get("CODE"))){
                sendurl= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
            }
            else if("wechatAppid".equals(cofigmap.get("CODE"))){
                appid= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
            }
            else if("wechatAppsecret".equals(cofigmap.get("CODE"))){
                appsecret= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
            }
            else if("agentId".equals(cofigmap.get("CODE"))){
                agentId= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
            }
            else if("wechatkey".equals(cofigmap.get("CODE"))){
                wechatkey= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
            }
        }
        String encode = Base64.encode(wechatkey);
        StringRedisTemplate stringRedisTemplate = SpringBeanUtils.getBean(StringRedisTemplate.class);
        String token =  stringRedisTemplate.opsForValue().get("JtgkWeChatConfig:WeChatToken");
        log.error("企微预警："+url+",sendurl:"+sendurl+",appid:"+appid+",appsecret:"+appsecret);
        log.error("地址："+url+"?secret="+appsecret+";encode:"+encode);
        if(StringUtils.isEmpty(token)){
            //获取企微token  corpid="+appid+"&corpsecret="+appsecret
//            HttpResponse restoken = HttpRequest.get(url+"?secret="+appsecret)
//                    .timeout(5000)
//                    .execute();
            HttpResponse restoken = HttpRequest.post(url+"?secret="+appsecret)
                    .header("Authorization", "Basic " + encode).timeout(5000)
                    .execute();
            if(restoken.getStatus()==200) {
                log.error("获取token企微返回数据：" + restoken.body());
                JSONObject tokenObject = JSONObject.parseObject(restoken.body());
                if(!StringUtils.isEmpty(tokenObject.getString("access_token"))){
                    Long expires_in = tokenObject.getLong("expires_in");
                    if(expires_in>100)
                        expires_in=expires_in-100;
                    stringRedisTemplate.opsForValue().set("JtgkWeChatConfig:WeChatToken", tokenObject.getString("access_token"),expires_in, TimeUnit.SECONDS);
                    token = tokenObject.getString("access_token");
                }else{

                }
            }else{
                log.error("获取token企微返回状态码："+restoken.getStatus());
            }
        }

        return token;
    }
}
