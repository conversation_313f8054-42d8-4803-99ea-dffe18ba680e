package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.BMResultObject;
import io.iec.edp.caf.data.orm.DataRepository;

import java.util.List;

public interface BMResultObjectRepository  extends DataRepository<BMResultObject, String>  {
    BMResultObject findByOrgCode(String orgcode);

    List<BMResultObject> findTop500BySksyncstatusOrderByJglb(String sksyncstatus);

    List<BMResultObject> findTop100BySksyncstatusOrderByJglb(String sksyncstatus);
}
