package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.EmployeeQlResultObject;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.EmployeeResultObject;
import io.iec.edp.caf.data.orm.DataRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface EmployeeQlResultObjectRepository  extends DataRepository<EmployeeQlResultObject,String> {
    List<EmployeeQlResultObject> findTop1000BySksyncstatus(String sksyncstatus);

    EmployeeQlResultObject findByUserId(String userid);


    @Query(value = " select * from jtgkemployeeqlresult where not EXISTS(select 1 from JTGKEMPLOYEERESULT WHERE userid=JTGKEMPLOYEERESULT.pernr)  and  not EXISTS(select 1 from JTGKEMPLOYEEBCRESULT WHERE JTGKEMPLOYEEBCRESULT.userid=jtgkemployeeqlresult.userid)      ORDER BY userid  LIMIT 1000  ",nativeQuery = true)
    List<EmployeeQlResultObject> findbyEmployeelist();
}
