package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.EmployeeResultObject;
import io.iec.edp.caf.data.orm.DataRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface EmployeeResultObjectRepository  extends DataRepository<EmployeeResultObject,String> {
    EmployeeResultObject findByPernr(String pernr);

    List<EmployeeResultObject> findTop500BySksyncstatus(String sksyncstatus);

    List<EmployeeResultObject> findTop1000BySksyncstatus(String sksyncstatus);

    List<EmployeeResultObject> findTop1000ByYgzhstatusAndYgstatus(String ygzhstatus,String ygstatus);
    List<EmployeeResultObject> findTop1000ByYgstatus(String ygstatus);

    @Query(value = " select * from JTGKEMPLOYEERESULT where      not  EXISTS (select 1 from BFEMPLOYEEBANKACCOUNTS WHERE BFEMPLOYEEBANKACCOUNTS.BANKACCOUNT=ZhrAccount) " +
            " and EXISTS (select 1 from BFEMPLOYEE WHERE BFEMPLOYEE.CODE=pernr)  and ZhrAccount !=''  ORDER BY ZhrAccount  LIMIT 1000  ",nativeQuery = true)
    List<EmployeeResultObject> findByEmployee();
}
