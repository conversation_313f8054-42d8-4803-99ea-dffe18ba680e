package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.EmployeebcResult;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface EmployeebcResultRepository extends JpaRepository<EmployeebcResult,String> {
    EmployeebcResult findByUserId(String userId);

    List<EmployeebcResult> findByYgstatus(String ygstatus);

    List<EmployeebcResult> findTop1000ByYgzhstatus(String number);
}
