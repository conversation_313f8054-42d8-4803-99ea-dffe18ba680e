package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.GSResultObject;
import io.iec.edp.caf.data.orm.DataRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface GSResultObjectRepository extends DataRepository<GSResultObject,String> {
    GSResultObject findByBUKRS(String BUKRS);

    List<GSResultObject> findTop500BySksyncstatus(String sksyncstatus);

    List<GSResultObject> findTop1000BySksyncstatus(String sksyncstatus);

    @Query(value = "select * from JTGKGSRESULT where sksyncstatus='0' and (trim(ZZ_INVALID)!='X' or  trim(ZZ_INVALID) is null) ORDER BY BUKRS  LIMIT 100  ",nativeQuery = true)
    List<GSResultObject> findBygslist();
}
