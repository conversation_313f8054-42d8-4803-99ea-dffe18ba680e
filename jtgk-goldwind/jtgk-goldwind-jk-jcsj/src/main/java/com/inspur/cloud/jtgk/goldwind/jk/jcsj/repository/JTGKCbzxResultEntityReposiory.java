package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.JTGKCbzxResultEntity;
import io.iec.edp.caf.data.orm.DataRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface JTGKCbzxResultEntityReposiory extends DataRepository<JTGKCbzxResultEntity,String> {
    @Query(value = "select * from JTGKCBZXRESULT where group_id=:group_id and cost_center_code=:cost_center_code and control_area=:control_area and end_date=:end_date  and language_code=:language_code ",nativeQuery = true)
    JTGKCbzxResultEntity findByCostlist(@Param("group_id")String group_id,   @Param("cost_center_code") String cost_center_code,@Param("control_area")  String control_area,@Param("end_date")  String end_date,@Param("language_code")  String language_code);

}
