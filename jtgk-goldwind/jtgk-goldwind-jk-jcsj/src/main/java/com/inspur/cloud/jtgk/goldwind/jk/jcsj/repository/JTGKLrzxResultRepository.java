package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.JTGKLrzxResultEntity;
import io.iec.edp.caf.data.orm.DataRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface JTGKLrzxResultRepository extends DataRepository<JTGKLrzxResultEntity,String> {

    @Query(value = "select * from JTGKLRZXRESULT where group_id=:group_id and language_code=:language_code and control_area=:control_area and  profit_center_code=:profit_center_code and start_date=:start_date and end_date=:end_date ",nativeQuery = true)
    JTGKLrzxResultEntity findByProfitlist(@Param("group_id") String group_id,@Param("language_code") String language_code,@Param("control_area") String control_area,  @Param("profit_center_code") String profit_center_code, @Param("start_date") String start_date,@Param("end_date")  String end_date);
}
