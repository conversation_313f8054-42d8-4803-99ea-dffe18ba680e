package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.JTGKNBDDResult;
import io.iec.edp.caf.data.orm.DataRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface JTGKNBDDResultRepository extends DataRepository<JTGKNBDDResult,String> {
    @Query(value = "select * from JTGKNBDDRESULT where order_no=:order_no and mandt=:mandt ",nativeQuery = true)
    JTGKNBDDResult findByNbddlist(@Param("order_no") String order_no, @Param("mandt") String mandt);

    @Query(value = "select * from JTGKNBDDRESULT where order_no=:order_no and mandt=:mandt and modify_time=:modify_time and modify_date:modify_date ",nativeQuery = true)
    JTGKNBDDResult findByNbddlists(@Param("order_no") String order_no, @Param("mandt") String mandt,@Param("modify_time") String modify_time,@Param("modify_date") String modify_date);
}
