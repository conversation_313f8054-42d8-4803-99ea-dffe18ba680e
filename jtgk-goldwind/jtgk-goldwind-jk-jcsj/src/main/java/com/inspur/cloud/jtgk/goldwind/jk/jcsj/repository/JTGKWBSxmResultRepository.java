package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.JTGKWBSxmResult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface JTGKWBSxmResultRepository extends JpaRepository<JTGKWBSxmResult,String> {
    @Query(value = "select * from JTGKWBSXMRESULT where mandt=:mandt and wbs_element=:wbs_element ",nativeQuery = true)
    JTGKWBSxmResult findByWbslist(@Param("mandt") String mandt, @Param("wbs_element") String wbs_element);


    @Query(value = "select * from JTGKWBSXMRESULT where mandt=:mandt and wbs_element=:wbs_element and modify_date=:modify_date ",nativeQuery = true)
    JTGKWBSxmResult findByWbslists(@Param("mandt") String mandt, @Param("wbs_element") String wbs_element ,@Param("modify_date") String modify_date);
}
