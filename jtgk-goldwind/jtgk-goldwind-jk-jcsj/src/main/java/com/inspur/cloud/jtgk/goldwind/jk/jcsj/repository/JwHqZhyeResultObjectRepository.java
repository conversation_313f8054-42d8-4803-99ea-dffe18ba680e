package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.JwHqZhyeResultObject;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.JwMgZhyeResultObject;
import io.iec.edp.caf.data.orm.DataRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface JwHqZhyeResultObjectRepository extends DataRepository<JwHqZhyeResultObject,String> {

//    @Query(value = "SELECT * FROM JTGKJWHQZHYERESULT WHERE ACTNBR =?1  and WAERS=?2  and ZMT940_FDAT=?3 ", nativeQuery = true)
    JwHqZhyeResultObject findByACTNBRAndWAERSAndREFNBR(String ACTNBR, String WAERS, String REFNBR);

    @Query(value = "SELECT * FROM JTGKJWHQZHYERESULT WHERE Sksyncstatus='0' and BAL_DATE IS NOT NULL and BAL_DATE<>''  order by BAL_DATE limit 500", nativeQuery = true)
    List<JwHqZhyeResultObject> findByhqye();

}
