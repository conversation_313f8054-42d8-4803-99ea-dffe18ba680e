package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.JwMgZhyeResultObject;
import io.iec.edp.caf.data.orm.DataRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface JwMgZhyeResultObjectRepository   extends DataRepository<JwMgZhyeResultObject,String> {

    @Query(value = "SELECT * FROM JTGKJWMGZHYERESULT WHERE Sksyncstatus='0' and AMT_BLANCE is not null and AMT_BLANCE<>''   order by DATE_BLANCE limit 500 ", nativeQuery = true)
    List<JwMgZhyeResultObject> findByMgye();

   // @Query(value = "SELECT * FROM JTGKJWMGZHYERESULT WHERE GUID =?1  and VALUE_DATE=?2  and CREDIT_DEBIT=?3 and AMT_CCY_BLANCE=?4 and DATE_BLANCE=?5 ", nativeQuery = true)
    JwMgZhyeResultObject findByGUIDAndZLINES(String GUID,String ZLINES);

    @Query(value = "SELECT * FROM JTGKJWMGZHYERESULT WHERE Sksyncstatus='0'    order by DATE_BLANCE limit 500 ", nativeQuery = true)
    List<JwMgZhyeResultObject> findByMgyelist();
}
