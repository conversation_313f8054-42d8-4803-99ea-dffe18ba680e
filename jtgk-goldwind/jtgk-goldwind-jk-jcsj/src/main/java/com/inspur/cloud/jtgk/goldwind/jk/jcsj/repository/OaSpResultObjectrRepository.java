package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.OaSpResultObjectEntity;
import io.iec.edp.caf.data.orm.DataRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface OaSpResultObjectrRepository extends DataRepository<OaSpResultObjectEntity,String> {

    Optional<OaSpResultObjectEntity> findById(String Id);

    List<OaSpResultObjectEntity> findTop1000BySksyncstatus(String sksyncstatus);

    //流程状态，finishedflag ='0'--未结束， '3' --终止， '1'--结束
    @Query(value = " select * from JTGKOASPRESULT where  Sksyncstatus='0'   ORDER BY ZhrAccount  LIMIT 1000  ",nativeQuery = true)
    List<OaSpResultObjectEntity> findOalist(String sksyncstatus);
}
