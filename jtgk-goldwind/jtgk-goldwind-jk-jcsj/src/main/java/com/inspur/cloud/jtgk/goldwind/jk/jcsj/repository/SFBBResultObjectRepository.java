package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.SFBBResultObject;
import io.iec.edp.caf.data.orm.DataRepository;
import io.lettuce.core.dynamic.annotation.Param;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import javax.transaction.Transactional;
import java.util.List;

public interface SFBBResultObjectRepository  extends DataRepository<SFBBResultObject,String> {

    SFBBResultObject findByCtimeAndCentityAndCscopeAndCscopetype( String ctime,String centity,String Cscope,String Cscopetype);

    List<SFBBResultObject> findByCtime(String ctime);

    @Transactional
    @Modifying
    @Query("DELETE FROM SFBBResultObject s WHERE s.ctime = :ctime")
    void deleteByCtime(@Param("ctime") String ctime);
}
