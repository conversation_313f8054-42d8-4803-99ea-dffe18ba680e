package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.WLDWResultObject;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.YHResultObject;
import io.iec.edp.caf.data.orm.DataRepository;

import java.util.List;

public interface WLDWResultObjectRepository extends DataRepository<WLDWResultObject,String> {
    WLDWResultObject findByPARTNER(String PARTNER);

    List<WLDWResultObject> findTop500BySksyncstatus(String sksyncstatus);

    List<WLDWResultObject> findTop100BySksyncstatus(String sksyncstatus);
}
