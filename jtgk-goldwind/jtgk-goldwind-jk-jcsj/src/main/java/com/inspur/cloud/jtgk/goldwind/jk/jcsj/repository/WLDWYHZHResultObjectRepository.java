package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.WLDWYHZHResultObject;
import io.iec.edp.caf.data.orm.DataRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface WLDWYHZHResultObjectRepository extends DataRepository<WLDWYHZHResultObject,String> {
    WLDWYHZHResultObject findByKOINH(String KOINH);

    List<WLDWYHZHResultObject> findTop500BySksyncstatus(String sksyncstatus);

    List<WLDWYHZHResultObject> findTop100BySksyncstatus(String sksyncstatus);

    @Query(value = "select * from JTGKWLDWYHZHRESULT where sksyncstatus='0' and exists(select 1 from BFPARTNER where code=JTGK<PERSON>LD<PERSON>YHZHRESULT.LIFNR)  ORDER BY LIFNR  LIMIT 500  ",nativeQuery = true)
    List<WLDWYHZHResultObject> findBywldwyhzhlist();
}
