package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.XJLLXMResultObject;
import io.iec.edp.caf.data.orm.DataRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

public interface XJLLXMResultObjectRepository  extends DataRepository<XJLLXMResultObject,String> {
    XJLLXMResultObject findByRSTGRAndBUKRS(String RSTGR,String BUKRS);

    List<XJLLXMResultObject> findTop500BySksyncstatus(String sksyncstatus);

    List<XJLLXMResultObject> findTop2BySksyncstatus(String sksyncstatus);

    @Query(value = " select * from JTGKXJLLXMRESULT where  Sksyncstatus='0' and   not  EXISTS (select 1 from CASHFLOWITEMSNEW WHERE CASHFLOWITEMSNEW.code=rstgr)   and rstgr !=''  ORDER BY rstgr  LIMIT 2  ",nativeQuery = true)
    List<XJLLXMResultObject> findByXJLLXM();
}
