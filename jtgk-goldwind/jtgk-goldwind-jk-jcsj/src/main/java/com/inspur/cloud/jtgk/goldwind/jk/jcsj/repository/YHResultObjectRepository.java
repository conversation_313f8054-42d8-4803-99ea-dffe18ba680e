package com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.YHResultObject;
import io.iec.edp.caf.data.orm.DataRepository;

import java.util.List;

public interface YHResultObjectRepository extends DataRepository<YHResultObject,String> {
    YHResultObject findByBANKLAndBANKS(String BANKL,String BANKS);

    List<YHResultObject> findTop500BySksyncstatus(String sksyncstatus);

    List<YHResultObject> findTop1000BySksyncstatus(String sksyncstatus);
}
