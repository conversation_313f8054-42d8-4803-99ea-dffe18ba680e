//package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service;
//
//
//import com.inspur.fastdweb.bean.SqlField;
//import com.inspur.fastdweb.context.FContext;
//import com.inspur.fastdweb.core.FastdwebSqlSession;
//import com.inspur.fastdweb.mgrlist.ListEvent;
//import com.inspur.fastdweb.mgrlist.request.ListPageRequest;
//import com.inspur.fastdweb.mgrlist.request.TreeChildrenRequest;
//import com.inspur.fastdweb.mgrlist.request.TreeFilterRequest;
//import com.inspur.fastdweb.mgrlist.request.TreeLevelRequest;
//import com.inspur.fastdweb.util.DateUtil;
//import com.inspur.gs.bf.bc.commonservice.api.IBusinessConfigKVResultService;
//import com.inspur.gs.tm.am.bankaccountinfoquery.api.service.BankAccountInfoQueryService;
//import com.inspur.gs.tm.am.bankaccountinfoquery.core.common.BankAccountInfoQueryCommon;
//import com.inspur.gs.tm.am.bankaccountinfoquery.core.common.BankAccountInfoQueryException;
//import com.inspur.gs.tm.am.bankaccountinfoquery.core.entity.IhcInnerAccountBalanceEntity;
//import com.inspur.gs.tm.am.bankaccountinfoquery.core.entity.TMBalanceOfBAEntity;
//import com.inspur.gs.tm.am.bankaccountinfoquery.core.repository.AccQryIhcInnerAccountBalanceRepository;
//import com.inspur.gs.tm.am.bankaccountinfoquery.core.repository.AccQryTMBalanceOfBARepository;
//import com.inspur.gs.tm.tmfnd.tmpub.common.util.permission.TMPubPermissionService;
//import io.iec.edp.caf.boot.context.CAFContext;
//import io.iec.edp.caf.common.JSONSerializer;
//import io.iec.edp.caf.commons.utils.SpringBeanUtils;
//import io.iec.edp.caf.databaseobject.api.entity.DbType;
//import io.iec.edp.caf.permission.api.manager.designtime.datapermission.AuthEntryManager;
//import java.math.BigDecimal;
//import java.text.DateFormat;
//import java.text.SimpleDateFormat;
//import java.util.ArrayList;
//import java.util.Calendar;
//import java.util.Comparator;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.Iterator;
//import java.util.List;
//import java.util.Map;
//import java.util.concurrent.CopyOnWriteArrayList;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//import java.util.stream.Collectors;
//import javax.persistence.EntityManager;
//import javax.persistence.Query;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//public class BankAccountekQueryServiceImpl implements ListEvent {
//    private static final Logger log = LoggerFactory.getLogger(BankAccountInfoQueryServiceImpl.class);
//
//
//    @Autowired
//    private AccQryTMBalanceOfBARepository tmBalanceOfBARepository;
//
//    @Autowired
//    private AccQryIhcInnerAccountBalanceRepository ihcInnerAccountBalanceRepository;
//
//    @Autowired
//    private TMPubPermissionService tmPubPermissionService;
//
//    @Autowired
//    private EntityManager entityManager;
//
//
//    public void beforeGetListPage(ListPageRequest pageRequest) {
//        HashMap<String, Object> fContext = (HashMap<String, Object>)FContext.getContext().getProperties().get("com.inspur.fastdweb.formparam");
//        if (fContext == null) {
//            log.error("fContext);
//            return;
//        }
//        String su = BankAccountInfoQueryCommon.objToStr(fContext.get("srcsu"));
//        if (StringUtils.isEmpty(su)) {
//            throw new BankAccountInfoQueryException("srcsu is null");
//        }
//
//
//
//        int replaceCount = 0;
//        List<SqlField> unitSqlFields = (List<SqlField>)pageRequest.fields.stream().filter(sqlField -> "BFADMINORGANIZATION.ID".equals(sqlField.field)).collect(Collectors.toList());
//        if (unitSqlFields != null && unitSqlFields.size() > 0) {
//            String field = ((SqlField)unitSqlFields.get(0)).field;
//            String value = BankAccountInfoQueryCommon.objToStr(((SqlField)unitSqlFields.get(0)).value);
//            if (StringUtils.isNotEmpty(value)) {
//                log.info("value:" + value);
//                value = value.replace("(", "").replace(")", "");
//                String[] idArray = value.split(",");
//                if (idArray.length > 1000) {
//                    String newValue = BankAccountInfoQueryCommon.arrayToInSqlString(field, idArray);
//                    String pattern = "BFADMINORGANIZATION.ID(.*?)\\)";
//                    Pattern r = Pattern.compile(pattern);
//                    Matcher matcher = r.matcher(pageRequest.sqlWhere);
//                    if (matcher.find()) {
//                        String reeStr = matcher.replaceAll(newValue);
//                        pageRequest.sqlWhere = reeStr;
//                    }
//
//                    replaceCount++;
//                }
//            }
//        }
//
//        List<SqlField> accSqlFields = (List<SqlField>)pageRequest.fields.stream().filter(sqlField -> "BFBANKACCOUNTS.ID".equals(sqlField.field)).collect(Collectors.toList());
//        if (accSqlFields != null && accSqlFields.size() > 0) {
//            String field = ((SqlField)accSqlFields.get(0)).field;
//            String value = BankAccountInfoQueryCommon.objToStr(((SqlField)accSqlFields.get(0)).value);
//            if (StringUtils.isNotEmpty(value)) {
//                log.info("value:" + value);
//                value = value.replace("(", "").replace(")", "");
//                String[] idArray = value.split(",");
//                if (idArray.length > 1000) {
//                    String newValue = BankAccountInfoQueryCommon.arrayToInSqlString(field, idArray);
//                    String pattern = "BFBANKACCOUNTS.ID(.*?)\\)";
//                    Pattern r = Pattern.compile(pattern);
//                    Matcher matcher = r.matcher(pageRequest.sqlWhere);
//                    if (matcher.find()) {
//                        String reeStr = matcher.replaceAll(newValue);
//                        pageRequest.sqlWhere = reeStr;
//                    }
//
//                    replaceCount++;
//                }
//            }
//        }
//        if (StringUtils.isNotEmpty(pageRequest.sqlWhere) && replaceCount > 0) {
//            String pattern = "1=1[\\s\\S]*";
//            Pattern r = Pattern.compile(pattern);
//            Matcher matcher = r.matcher(pageRequest.statement);
//            if (matcher.find()) {
//                String strWhere = "1=1 ".concat(pageRequest.sqlWhere);
//                pageRequest.statement = matcher.replaceAll(strWhere);
//            }
//        }
//
//
//
//
//
//        if (su.equals("TA")) {
//
//            String authorizationId = "IhcBankAccountInfoQuery";
//
//            String authOpId = "002";
//            String authSql = this.tmPubPermissionService.getTreasureOrgAuthorisedSql(authorizationId, authOpId);
//            if (StringUtils.isNotEmpty(authSql)) {
//                pageRequest.statement += " and ((BFBANKACCOUNTS.INNEROROUTER = 1 and BFBANKACCOUNTS.BANK in (" + authSql + "))";
//                pageRequest.statement += " or (BFBANKACCOUNTS.INNEROROUTER <> 1 and BFBANKACCOUNTS.OPENACCOUNTUNIT in (SELECT memberunit FROM bftreaorgserviceunit WHERE parentid in (" + authSql + "))))";
//            } else {
//                pageRequest.statement += " and (BFBANKACCOUNTS.INNEROROUTER = 1";
//                pageRequest.statement += " or (BFBANKACCOUNTS.INNEROROUTER <> 1 and BFBANKACCOUNTS.OPENACCOUNTUNIT in (SELECT memberunit FROM bftreaorgserviceunit)))";
//            }
//        } else {
//
//            String authorizationId = "AM_ACCOUNTINFOQUERY";
//            if (su.equals("CM")) {
//
//                AuthEntryManager authEntryManager1 = (AuthEntryManager)SpringBeanUtils.getBean(AuthEntryManager.class);
//
//                Boolean enable = authEntryManager1.getEnable("CM_BANKACCOUNTQUERY", "TM_AdminOrg");
//                if (enable.booleanValue()) {
//                    authorizationId = "CM_BANKACCOUNTQUERY";
//                }
//            }
//            String authOpId = "002";
//            String authSql = this.tmPubPermissionService.getAdminOrgAuthorisedSql(authorizationId, authOpId);
//            if (StringUtils.isNotEmpty(authSql)) {
//                if (pageRequest.originStatement.indexOf(" WHERE 1=1 ") > -1) {
//                    String replacement = " INNER JOIN (".concat(authSql) + ") authorg on authorg.aodata=BFBANKACCOUNTS.OPENACCOUNTUNIT ".concat("WHERE 1=1 ").concat(pageRequest.sqlWhere);
//                    pageRequest.statement = pageRequest.originStatement.replaceFirst("WHERE 1=1", Matcher.quoteReplacement(replacement));
//                } else {
//                    pageRequest.statement += " and BFBANKACCOUNTS.OPENACCOUNTUNIT in (" + authSql + ")";
//                }
//            }
//
//            pageRequest
//                    .statement = pageRequest.statement.concat(" AND NOT EXISTS (SELECT 1 FROM BFTREASUREORG WHERE TRANSTRANSITACCOUNT=BFBANKACCOUNTS.ID AND TRANSTRANSITACCOUNT IS NOT NULL) ").concat(" AND NOT EXISTS (SELECT 1 FROM BFTREAORGTRANSITACC WHERE TRANSITACCOUNTID=BFBANKACCOUNTS.ID)");
//        }
//
//
//
//        switch (su) {
//
//
//
//
//            case "Gls":
//                pageRequest.statement = pageRequest.statement.concat(" AND (BFMASTERORGANIZATION.CountryOrRegion IS NOT NULL AND BFMASTERORGANIZATION.CountryOrRegion <> '7daf073a-30fd-4c91-8e11-8c20d8d87d85'");
//                if (CAFContext.current.getDbType().name().equals(DbType.PgSQL.name())) {
//                    pageRequest.statement = pageRequest.statement.concat(" AND BFMASTERORGANIZATION.CountryOrRegion<>''");
//                }
//                pageRequest.statement = pageRequest.statement.concat(" ) ");
//                break;
//        }
//
//
//
//
//
//
//        boolean innerLoanAccountShow = BankAccountInfoQueryCommon.getInnerLoanAccountShow();
//
//        if (!innerLoanAccountShow) {
//            pageRequest.statement = pageRequest.statement.concat(" AND NOT EXISTS(SELECT acc.id FROM bfbankaccounts acc LEFT JOIN bfBankAccountItems ON bfBankAccountItems.parentid = acc.id WHERE acc.innerorouter = 1 AND bfBankAccountItems.accounttype = '5' and acc.id=BFBANKACCOUNTS.ID)");
//        }
//
//
//
//
//        String onlyShowLongUnUseAcc = BankAccountInfoQueryCommon.objToStr(fContext.get("onlyShowLongUnUseAcc"));
//        if ("1".equals(onlyShowLongUnUseAcc)) {
//            Map<String, Object> longUnUseAccMap = getLongUnusedAccountSql();
//            if ("success".equals(longUnUseAccMap.get("result"))) {
//                List<String> accountIds = (List<String>)longUnUseAccMap.get("data");
//                if ("1".equals(BankAccountInfoQueryCommon.objToStr(longUnUseAccMap.get("hasExtend"))) && accountIds != null) {
//
//                    pageRequest.statement = pageRequest.statement.concat(" AND ").concat(BankAccountInfoQueryCommon.listToInSqlString("BFBANKACCOUNTS.ID", accountIds));
//                } else {
//                    String longNoUseAccSql = BankAccountInfoQueryCommon.objToStr(longUnUseAccMap.get("longNoUseAccSql"));
//                    int paramSize = (pageRequest.paramObjects != null) ? pageRequest.paramObjects.size() : 0;
//                    longNoUseAccSql = longNoUseAccSql.replaceFirst("#BizDate#", "#{ sqlWhereParam" + paramSize + " }");
//                    longNoUseAccSql = longNoUseAccSql.replaceFirst("#BizDate#", "#{ sqlWhereParam" + (paramSize + 1) + " }");
//                    longNoUseAccSql = longNoUseAccSql.replaceFirst("#BizDate#", "#{ sqlWhereParam" + (paramSize + 2) + " }");
//                    longNoUseAccSql = longNoUseAccSql.replaceFirst("#BizDate#", "#{ sqlWhereParam" + (paramSize + 3) + " }");
//                    longNoUseAccSql = longNoUseAccSql.replaceFirst("#transactNum#", "#{ sqlWhereParam" + (paramSize + 4) + " }");
//                    pageRequest.paramObjects.put("sqlWhereParam" + paramSize, longUnUseAccMap.get("BizDate"));
//                    pageRequest.paramObjects.put("sqlWhereParam" + (paramSize + 1), longUnUseAccMap.get("BizDate"));
//                    pageRequest.paramObjects.put("sqlWhereParam" + (paramSize + 2), longUnUseAccMap.get("BizDate"));
//                    pageRequest.paramObjects.put("sqlWhereParam" + (paramSize + 3), longUnUseAccMap.get("BizDate"));
//                    pageRequest.paramObjects.put("sqlWhereParam" + (paramSize + 4), longUnUseAccMap.get("transactNum"));
//                    pageRequest.statement = pageRequest.statement.concat(" AND EXISTS (").concat(longNoUseAccSql).concat(")");
//                }
//            } else {
//                throw new BankAccountInfoQueryException(BankAccountInfoQueryCommon.objToStr(longUnUseAccMap.get("err")));
//            }
//        }
//
//
//        log.info("listsql+ pageRequest.statement);
//    }
//
//
//    public void afterGetListPage(ListPageRequest pageRequest) {
//        HashMap<String, Object> fContext = (HashMap<String, Object>)FContext.getContext().getProperties().get("com.inspur.fastdweb.formparam");
//        if (fContext == null) {
//            log.error("fContext);
//            return;
//        }
//        String su = (String)fContext.get("srcsu");
//        if (StringUtils.isEmpty(su)) {
//            log.error(");
//            throw new BankAccountInfoQueryException("srcsu is null");
//        }
//        String showBalance = BankAccountInfoQueryCommon.objToStr(fContext.get("showBalance"));
//        String showSelfCount = BankAccountInfoQueryCommon.objToStr(fContext.get("showSelfCount"));
//
//
//        List<String> accountIds = (List<String>)pageRequest.result.stream().map(v -> BankAccountInfoQueryCommon.objToStr(v.get("ID"))).collect(Collectors.toList());
//        Map<String, Object> balanceOfBAList = new HashMap<>();
//        Map<String, Object> balanceOfInnerList = new HashMap<>();
//        Map<String, Object> balanceOnBankList = new HashMap<>();
//        if ("1".equals(showBalance)) {
//            if ("TA".equals(su)) {
//                log.info(");
//                        balanceOfInnerList = BankAccountInfoQueryCommon.getNewestInnerAccBalnce(accountIds);
//            } else {
//                log.info(");
//                        balanceOfBAList = BankAccountInfoQueryCommon.getNewestBalnceOfBA(accountIds);
//            }
//            log.info(");
//                    balanceOnBankList = BankAccountInfoQueryCommon.getNewestBankBalnce(accountIds);
//        }
//
//        List<Map<String, Object>> unitList = new CopyOnWriteArrayList<>();
//        log.info(");
//        for (Map<String, String> map : (Iterable<Map<String, String>>)pageRequest.result) {
//
//
//            List<Map<String, Object>> unitList2 = (List<Map<String, Object>>)unitList.stream().filter(m -> (map.get("ADMINORGCODE") != null && map.get("ADMINORGCODE").equals(String.valueOf(m.get("ADMINORGCODE"))))).collect(Collectors.toList());
//
//            if (unitList2 == null || unitList2.size() == 0) {
//                Map<String, Object> newMap = new HashMap<>();
//                newMap.put("ID", "*");
//                newMap.put("ADMINORGID", map.get("ADMINORGID"));
//                newMap.put("PNTHRINFO_PARENTELEMENT", map.get("PNTHRINFO_PARENTELEMENT"));
//                newMap.put("ADMINORGCODE", map.get("ADMINORGCODE"));
//                newMap.put("ADMINORGNAME", map.get("ADMINORGNAME"));
//                newMap.put("PNTHRINFO_ISDETAIL", map.get("PNTHRINFO_ISDETAIL"));
//                newMap.put("TREEINFO_PATH", map.get("TREEINFO_PATH"));
//                newMap.put("PNTHRINFO_LAYER", map.get("PNTHRINFO_LAYER"));
//                newMap.put("ACCOUNTCOUNTS", Integer.valueOf(1));
//                newMap.put("ACCOUNTTYPE", null);
//                newMap.put("CURRENCYNAME", null);
//                newMap.put("ONLINEBANKOPENSTATUS", null);
//                newMap.put("ACCOUNTPROPERTYNAME", null);
//                newMap.put("BALANCEOFBA", null);
//                newMap.put("OPENDATE", null);
//                newMap.put("CURRENCY", null);
//                newMap.put("BANKNAME", null);
//                newMap.put("INNEROROUTER", null);
//                newMap.put("ACCOUNTNAME", null);
//                newMap.put("BALANCEONBANK", null);
//                newMap.put("BANKTYPENAME", null);
//                newMap.put("ACCOUNTSTATUS", null);
//                newMap.put("ACCOUNTUNITNAME", null);
//                newMap.put("OPENACCOUNTUNIT", null);
//                newMap.put("ACCOUNTNO", null);
//                unitList.add(newMap);
//                unitList2.add(newMap);
//            }
//            map.put("PNTHRINFO_PARENTELEMENT", ((Map)unitList2.get(0)).get("ADMINORGID"));
//            map.put("ADMINORGID", "*");
//            String accountId = BankAccountInfoQueryCommon.objToStr(map.get("ID"));
//            String currency = BankAccountInfoQueryCommon.objToStr(map.get("CURRENCY"));
//            String accountType = BankAccountInfoQueryCommon.objToStr(map.get("ACCOUNTTYPE"));
//
//            if ("1".equals(BankAccountInfoQueryCommon.objToStr(map.get("ISCENTRALBUDGETZEROBALANCE")))) {
//                currency = BankAccountInfoQueryCommon.objToStr(map.get("DEFAULTCURRENCY"));
//                accountType = "1";
//                map.put("CURRENCYNAME", BankAccountInfoQueryCommon.getLang("RMB"));
//                map.put("ACCOUNTTYPENAME", BankAccountInfoQueryCommon.getLang("HQ"));
//            }
//
//
//
//            map.put("ACCOUNTCOUNTS", null);
//
//            if ("1".equals(showBalance)) {
//                if ("TA".equals(su) && "1".equals(BankAccountInfoQueryCommon.objToStr(map.get("INNEROROUTER")))) {
//
//                    String strBalance = BankAccountInfoQueryCommon.objToStr(balanceOfInnerList.get(accountId));
//                    BigDecimal balance = null;
//                    if (StringUtils.isNotEmpty(strBalance)) {
//                        balance = new BigDecimal(strBalance);
//                    }
//                    map.put("BALANCEOFBA", balance);
//                } else {
//
//                    String strBalance = BankAccountInfoQueryCommon.objToStr(balanceOfBAList.get(accountId + currency + accountType));
//                    BigDecimal balance = null;
//                    if (StringUtils.isNotEmpty(strBalance)) {
//                        balance = new BigDecimal(strBalance);
//                    }
//                    map.put("BALANCEOFBA", balance);
//                }
//
//                String strBalance = BankAccountInfoQueryCommon.objToStr(balanceOnBankList.get(accountId + currency + accountType));
//                String strAvailableBalance = BankAccountInfoQueryCommon.objToStr(balanceOnBankList.get("available" + accountId + currency + accountType));
//                String strBalanceDate = BankAccountInfoQueryCommon.objToStr(balanceOnBankList.get("date" + accountId + currency + accountType));
//                BigDecimal balance = null;
//                BigDecimal availableBalance = null;
//                if (StringUtils.isNotEmpty(strBalance)) {
//                    balance = new BigDecimal(strBalance);
//                }
//                if (StringUtils.isNotEmpty(strAvailableBalance)) {
//                    availableBalance = new BigDecimal(strAvailableBalance);
//                }
//                map.put("BALANCEONBANK", balance);
//                map.put("BALANCEBANKAVAILABLE", availableBalance);
//                map.put("BALANCEONBANKDATE", strBalanceDate);
//            }
//        }
//
//
//        int[] count = { 0 };
//        Iterator<Map<String, Object>> iterator = unitList.iterator();
//        log.info(");
//
//        while (iterator.hasNext()) {
//            Map<String, Object> map = iterator.next();
//
//            if (count[0] > 500) {
//                break;
//            }
//            count[0] = count[0] + 1;
//
//            String path = BankAccountInfoQueryCommon.objToStr(map.get("TREEINFO_PATH"));
//            int unitLength = path.length();
//
//            if (unitLength <= 4) {
//                continue;
//            }
//
//            String pUnitId = BankAccountInfoQueryCommon.objToStr(map.get("PNTHRINFO_PARENTELEMENT"));
//
//            String pPath = path.substring(0, unitLength - 4);
//            int layer = Integer.parseInt(BankAccountInfoQueryCommon.objToStr(map.get("PNTHRINFO_LAYER")));
//            List<Map<String, Object>> newList = BankAccountInfoQueryCommon.getAllParentUnitList(pUnitId, pPath, layer);
//
//            List<String> adminIds = (List<String>)unitList.stream().map(v -> BankAccountInfoQueryCommon.objToStr(v.get("ADMINORGID"))).collect(Collectors.toList());
//            for (int i = newList.size() - 1; i >= 0; i--) {
//                if (!adminIds.contains(BankAccountInfoQueryCommon.objToStr(((Map)newList.get(i)).get("ADMINORGID")))) {
//                    unitList.add(newList.get(i));
//                }
//            }
//        }
//        log.info(");
//
//
//
//
//
//
//
//
//
//
//
//                String sql2 = "SELECT BFBANKACCOUNTITEMS2.ID, BFADMINORGANIZATION.ID AS ORGID,BFADMINORGANIZATION.TREEINFO_PATH,BFBANKACCOUNTITEMS2.PARENTID,BFBANKACCOUNTITEMS2.CURRENCY FROM BFBANKACCOUNTS LEFT JOIN BFBANKACCOUNTITEMS BFBANKACCOUNTITEMS2 ON BFBANKACCOUNTS.ID = BFBANKACCOUNTITEMS2.PARENTID AND BFBANKACCOUNTS.ISCENTRALBUDGETZEROBALANCE='0' LEFT JOIN BFBANKACCOUNTPROPERTY BFBANKACCOUNTPROPERTY ON BFBANKACCOUNTS.ACCOUNTPROPERTY = BFBANKACCOUNTPROPERTY.ID LEFT JOIN BFBANK BFBANK ON BFBANKACCOUNTS.BANK = BFBANK.ID LEFT JOIN BFBANKTYPE BFBANKTYPE ON BFBANK.BANKTYPE = BFBANKTYPE.ID LEFT JOIN BFADMINORGANIZATION BFADMINORGANIZATION ON BFBANKACCOUNTS.OPENACCOUNTUNIT = BFADMINORGANIZATION.ID LEFT JOIN BFMASTERORGANIZATION BFMASTERORGANIZATION ON BFADMINORGANIZATION.MASTERORGID = BFMASTERORGANIZATION.ID " + "LEFT JOIN BFTREAORGSERVICEUNIT BFTREAORGSERVICEUNIT ON BFADMINORGANIZATION.ID = BFTREAORGSERVICEUNIT.MEMBERUNIT ".concat(pageRequest.statement.substring(pageRequest.statement.indexOf("WHERE ")));
//
//        if (unitList.size() > 0) {
//
//            List<String> rootPathList = new ArrayList<>();
//            for (Map<String, Object> map : unitList) {
//                if (String.valueOf(map.get("TREEINFO_PATH")).length() == 4) {
//                    rootPathList.add(String.valueOf(map.get("TREEINFO_PATH")));
//                }
//            }
//            String columnName = "BFADMINORGANIZATION.TREEINFO_PATH";
//            String unitSql = BankAccountInfoQueryCommon.listToLikeSql(columnName, rootPathList);
//            sql2 = sql2.concat(" AND (").concat(unitSql).concat(")");
//        }
//        FastdwebSqlSession idpSqlSession = (FastdwebSqlSession)SpringBeanUtils.getBean(FastdwebSqlSession.class);
//        log.info(");
//                List<Map> resultList = idpSqlSession.selectList(Map.class, sql2, pageRequest.paramObjects);
//        log.info(");
//
//        for (Map<String, Object> map : unitList) {
//            String curPath = String.valueOf(map.get("TREEINFO_PATH"));
//
//
//
//
//
//            List<Map> hasLower_result = (List<Map>)resultList.stream().filter(item -> (String.valueOf(item.get("TREEINFO_PATH")).length() >= curPath.length() && String.valueOf(item.get("TREEINFO_PATH")).substring(0, curPath.length()).equals(curPath))).collect(Collectors.toList());
//
//            Map<String, List<Map>> hasLower_accList = (Map<String, List<Map>>)hasLower_result.stream().collect(Collectors.groupingBy(c -> BankAccountInfoQueryCommon.objToStr(c.get("PARENTID"))));
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//            List<Map> self_result = (List<Map>)resultList.stream().filter(item -> String.valueOf(item.get("ORGID")).equals(String.valueOf(map.get("ADMINORGID")))).collect(Collectors.toList());
//
//            Map<String, List<Map>> self_accList = (Map<String, List<Map>>)self_result.stream().collect(Collectors.groupingBy(c -> BankAccountInfoQueryCommon.objToStr(c.get("PARENTID"))));
//
//
//            int countOfTotalAcc = (hasLower_accList != null) ? hasLower_accList.size() : 0;
//
//            int countOfSelfAcc = (self_accList != null) ? self_accList.size() : 0;
//
//
//            if ("1".equals(showSelfCount)) {
//                map.put("ACCOUNTCOUNTS", countOfTotalAcc + BankAccountInfoQueryCommon.getLang("orgSelfAccCount") + countOfSelfAcc); continue;
//            }
//            map.put("ACCOUNTCOUNTS", Integer.valueOf(countOfTotalAcc));
//        }
//
//        log.info(");
//
//                unitList = (List<Map<String, Object>>)unitList.stream().sorted(Comparator.comparing(item -> String.valueOf(item.get("TREEINFO_PATH")))).collect(Collectors.toList());
//        int lastIndex = -1;
//        log.info(");
//
//        for (int i = unitList.size() - 1; i >= 0; i--) {
//            int index = -1;
//
//            for (int j = 0; j < pageRequest.result.size(); j++) {
//                if (String.valueOf(((Map)unitList.get(i)).get("TREEINFO_PATH")).equals(((Map)pageRequest.result.get(j)).get("TREEINFO_PATH"))) {
//                    index = j;
//                    lastIndex = j;
//                    break;
//                }
//            }
//            if (index == -1 && lastIndex != -1 && i < unitList.size() - 1) {
//                String lastTreeInfoPath = String.valueOf(((Map)unitList.get(i + 1)).get("TREEINFO_PATH"));
//
//                int lastLength = lastTreeInfoPath.length();
//                String path = lastTreeInfoPath.substring(0, lastLength - 4);
//                if (lastLength > 4 && String.valueOf(((Map)unitList.get(i)).get("TREEINFO_PATH")).equals(path)) {
//                    index = lastIndex;
//                }
//            }
//            if (index > -1) {
//                pageRequest.result.add(index, unitList.get(i));
//            }
//        }
//        log.info(");
//        for (int i = 0; i < pageRequest.result.size(); i++) {
//
//            String balanceDirection = BankAccountInfoQueryCommon.objToStr(((Map)pageRequest.result.get(i)).get("BALANCEDIRECTION"));
//            String innerOrOuter = BankAccountInfoQueryCommon.objToStr(((Map)pageRequest.result.get(i)).get("INNEROROUTER"));
//            if ("TA".equals(su) && "1".equals(innerOrOuter)) {
//                if ("1".equals(balanceDirection)) {
//                    ((Map<String, Integer>)pageRequest.result.get(i)).put("BALANCEDIRECTION", Integer.valueOf(2));
//                } else if ("2".equals(balanceDirection)) {
//                    ((Map<String, Integer>)pageRequest.result.get(i)).put("BALANCEDIRECTION", Integer.valueOf(1));
//                }
//            }
//
//            String isDisable = BankAccountInfoQueryCommon.objToStr(((Map)pageRequest.result.get(i)).get("ID_ISDISABLE"));
//
//            String id = BankAccountInfoQueryCommon.objToStr(((Map)pageRequest.result.get(i)).get("ID"));
//            if (!"*".equals(id) && !"1".equals(isDisable)) {
//                ((Map<String, String>)pageRequest.result.get(i)).put("ID_ISDISABLE", "0");
//            }
//        }
//        log.info(");
//    }
//
//
//
//
//    public void beforeGetLevelData(TreeLevelRequest levelRequest) {}
//
//
//
//
//    public void afterGetLevelData(TreeLevelRequest levelRequest) {}
//
//
//
//
//    public void beforeGetChildData(TreeChildrenRequest childrenRequest) {}
//
//
//
//
//    public void afterGetChildData(TreeChildrenRequest childrenRequest) {}
//
//
//
//
//    public void beforeGetFilterData(TreeFilterRequest filterRequest) {}
//
//
//
//
//    public void afterGetFilterData(TreeFilterRequest filterRequest) {}
//
//
//
//
//    public BigDecimal getAccBeginBalance(String account, String currency, String accountType, String su) {
//        String sql = "select innerorouter from bfbankaccounts where id=?1";
//        Query query = this.entityManager.createNativeQuery(sql);
//        query.setParameter(1, account);
//        List result = query.getResultList();
//        if (result != null && result.size() > 0) {
//            String innerOrOuter = BankAccountInfoQueryCommon.objToStr(result.get(0));
//
//            if ("TA".equals(su) && "1".equals(innerOrOuter)) {
//
//
//                List<IhcInnerAccountBalanceEntity> ihcInnerAccountBalanceEntitieList = this.ihcInnerAccountBalanceRepository.findFirstByInnerAccountIdAndCurrencyAndAccountTypeOrderByBalanceDateAsc(account, currency, accountType);
//                if (ihcInnerAccountBalanceEntitieList != null && ihcInnerAccountBalanceEntitieList.size() > 0) {
//                    return ((IhcInnerAccountBalanceEntity)ihcInnerAccountBalanceEntitieList.get(0)).getBeginningBalance();
//                }
//            } else {
//
//                List<TMBalanceOfBAEntity> tmBalanceOfBAEntityList = this.tmBalanceOfBARepository.findFirstByAccountIdAndCurrencyAndAccountTypeOrderByBalanceDateAsc(account, currency, accountType);
//                if (tmBalanceOfBAEntityList != null && tmBalanceOfBAEntityList.size() > 0) {
//                    return ((TMBalanceOfBAEntity)tmBalanceOfBAEntityList.get(0)).getBeginningBalance();
//                }
//            }
//        }
//        return BigDecimal.ZERO;
//    }
//
//
//
//
//
//
//    public Object queryApplyDoc(String account) {
//        Map<String, Object> res = new HashMap<>();
//        res.put("success", Boolean.valueOf(false));
//        String sql = "select innerorouter,relatedid from bfbankaccounts where id=?1";
//        Query query = this.entityManager.createNativeQuery(sql);
//        query.setParameter(1, account);
//        List<Object[]> result = query.getResultList();
//        if (result == null || result.size() == 0) {
//
//            res.put("msg", BankAccountInfoQueryCommon.getLang("notFindAccInfo"));
//            return res;
//        }
//        Object[] row = result.get(0);
//        String innerOrOuter = BankAccountInfoQueryCommon.objToStr(row[0]);
//        String relatedid = BankAccountInfoQueryCommon.objToStr(row[1]);
//        res.put("innerOrOuter", innerOrOuter);
//        if ("1".equals(innerOrOuter)) {
//
//            String sql2 = "select ihcinneraccount.id,ihcinneraccount.sourcesign,ihcinneraccount.relatedid,ihciaopeningprocess.srcdocid from ihcinneraccount left join ihciaopeningprocess on ihciaopeningprocess.id=ihcinneraccount.relatedid where ihcinneraccount.id=?1";
//
//
//            Query query2 = this.entityManager.createNativeQuery(sql2);
//            query2.setParameter(1, account);
//            List<Object[]> result2 = query2.getResultList();
//            if (result2 == null || result2.size() == 0) {
//
//                res.put("msg", BankAccountInfoQueryCommon.getLang("notFindInnerAccInfo"));
//                return res;
//            }
//            Object[] row2 = result2.get(0);
//            if (!"2".equals(BankAccountInfoQueryCommon.objToStr(row2[1]))) {
//
//                res.put("msg", BankAccountInfoQueryCommon.getLang("notFindOpenAccApply"));
//                return res;
//            }
//            String srcdocid = BankAccountInfoQueryCommon.objToStr(row2[3]);
//            res.put("success", Boolean.valueOf(true));
//            res.put("srcdocid", srcdocid);
//        } else {
//
//            if (StringUtils.isEmpty(relatedid)) {
//
//                res.put("msg", BankAccountInfoQueryCommon.getLang("notFindOpenAccApply"));
//                return res;
//            }
//            res.put("success", Boolean.valueOf(true));
//            res.put("srcdocid", relatedid);
//        }
//        return res;
//    }
//
//
//
//
//
//
//
//
//    public String AccInformationBalance(Map<String, Object> map) {
//        Map<String, Object> resultmap = new HashMap<>();
//        String sql = "select innerorouter from bfbankaccounts where id=?1";
//        String bizdate = String.valueOf(map.getOrDefault("BizDate", ""));
//        String bizdate1 = String.valueOf(map.getOrDefault("BizDate1", ""));
//        String bizdate2 = String.valueOf(map.getOrDefault("BizDate2", ""));
//        String account = String.valueOf(map.getOrDefault("Account", ""));
//        String accounttype = String.valueOf(map.getOrDefault("AccountType", ""));
//        String currency = String.valueOf(map.getOrDefault("Currency", ""));
//        BigDecimal beginningbalance = BigDecimal.ZERO;
//        BigDecimal currentbalance = BigDecimal.ZERO;
//        String accountid = account;
//        String inneraccountid = account;
//        String bankaccount = account;
//        String sql_accountType = "";
//        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//        Date startbizdate = new Date();
//        Date endbizdate = new Date();
//        if (!"".equals(bizdate)) {
//            try {
//                startbizdate = dateFormat.parse(bizdate1);
//                endbizdate = dateFormat.parse(bizdate2);
//            } catch (Throwable throwable) {
//                log.error(", throwable.getMessage(), throwable);
//            }
//            log.info("+ startbizdate + "+ endbizdate);
//        }
//        Query query = this.entityManager.createNativeQuery(sql);
//        query.setParameter(1, account);
//        List result = query.getResultList();
//
//        if (result != null && result.size() > 0) {
//            String innerOrOuter = BankAccountInfoQueryCommon.objToStr(result.get(0));
//            String sql_qm = " SELECT(A .settlementamount - B.settlementamount) AS currentbalance FROM(SELECT(case when SUM(settlementamount) is null then 0 else SUM(settlementamount) end) settlementamount FROM TMUNITJOURNAL LEFT JOIN BFCURRENCY BFCURRENCY ON TMUNITJOURNAL.CURRENCY = BFCURRENCY. ID LEFT JOIN bfbankaccounts bfbankaccounts ON TMUNITJOURNAL.bankaccount = bfbankaccounts. ID %s WHERE 1 = 1 AND BANKACCOUNT =?1  AND BFCURRENCY.ID =?2 %s AND settlementorientation = 1 AND (((BANKACCOUNT in (select id from bfbankaccounts where innerorouter =2)) OR (isincbiz = '1' AND BANKACCOUNT in (select id from bfbankaccounts where innerorouter <>2) )))) A, (SELECT (case when SUM(settlementamount) is null then 0 else SUM(settlementamount) end) settlementamount FROM TMUNITJOURNAL LEFT JOIN BFCURRENCY BFCURRENCY ON TMUNITJOURNAL.CURRENCY = BFCURRENCY. ID LEFT JOIN bfbankaccounts bfbankaccounts ON TMUNITJOURNAL.bankaccount = bfbankaccounts.ID %s WHERE 1 = 1 AND BANKACCOUNT =?1  AND BFCURRENCY. ID =?2 %s AND settlementorientation = 2 AND (((BANKACCOUNT in (select id from bfbankaccounts where innerorouter =2)) OR (isincbiz = '1' AND BANKACCOUNT in (select id from bfbankaccounts where innerorouter <>2) )))) B ";
//            if ("1".equals(innerOrOuter)) {
//
//                sql = " select beginningbalance FROM ihcinneraccountbalance WHERE (balancedate= ";
//                if (!"".equals(accounttype)) {
//                    sql_accountType = " and accounttype = ?3 ";
//                }
//                if ("".equals(bizdate)) {
//
//                    sql = sql + " (SELECT MIN(balancedate) FROM ihcinneraccountbalance where inneraccountid=?1  and currency=?2 " + sql_accountType + ")) and inneraccountid=?1  and currency=?2  " + sql_accountType;
//
//                }
//                else if (!"".equals(accounttype)) {
//                    sql = sql + " ?4  or balancedate=(SELECT MIN(balancedate) FROM ihcinneraccountbalance where inneraccountid=?1  and currency=?2 " + sql_accountType + ")) and inneraccountid=?1  and currency=?2 " + sql_accountType + " ORDER BY balancedate ASC ";
//                } else {
//                    sql = sql + " ?3  or balancedate=(SELECT MIN(balancedate) FROM ihcinneraccountbalance where inneraccountid=?1  and currency=?2 " + sql_accountType + ")) and inneraccountid=?1  and currency=?2 " + sql_accountType + " ORDER BY balancedate ASC ";
//                }
//
//                Query query1 = this.entityManager.createNativeQuery(sql);
//                if (!"".equals(accounttype)) {
//                    query1.setParameter(1, inneraccountid);
//                    query1.setParameter(2, currency);
//                    query1.setParameter(3, accounttype);
//                    if (!"".equals(bizdate)) {
//                        query1.setParameter(4, startbizdate);
//                    }
//                } else {
//                    query1.setParameter(1, inneraccountid);
//                    query1.setParameter(2, currency);
//                    if (!"".equals(bizdate)) {
//                        query1.setParameter(3, startbizdate);
//                    }
//                }
//                List<Object[]> result1 = query1.getResultList();
//                if (result1 != null && result1.size() > 0) {
//                    beginningbalance = new BigDecimal(String.valueOf(result1.get(result1.size() - 1)));
//                } else {
//                    beginningbalance = BigDecimal.valueOf(0L);
//                }
//            } else {
//
//                sql = " select beginningbalance FROM tmbalanceofba WHERE (balancedate= ";
//                if (!"".equals(accounttype)) {
//                    sql_accountType = " and accounttype = ?3 ";
//                }
//                if ("".equals(bizdate)) {
//
//                    sql = sql + " (SELECT MIN(balancedate) FROM tmbalanceofba where accountid=?1 and currency=?2 " + sql_accountType + "))  and accountid=?1 and currency=?2 " + sql_accountType;
//
//                }
//                else if (!"".equals(accounttype)) {
//                    sql = sql + " ?4  or balancedate=(SELECT MIN(balancedate) FROM tmbalanceofba where accountid=?1  and currency=?2 " + sql_accountType + ")) and accountid=?1  and currency=?2 " + sql_accountType + " ORDER BY balancedate ASC ";
//                } else {
//                    sql = sql + " ?3  or balancedate=(SELECT MIN(balancedate) FROM tmbalanceofba where accountid=?1  and currency=?2 " + sql_accountType + ")) and accountid=?1  and currency=?2 " + sql_accountType + " ORDER BY balancedate ASC ";
//                }
//
//                Query query1 = this.entityManager.createNativeQuery(sql);
//                if (!"".equals(accounttype)) {
//                    query1.setParameter(1, inneraccountid);
//                    query1.setParameter(2, currency);
//                    query1.setParameter(3, accounttype);
//                    if (!"".equals(bizdate)) {
//                        query1.setParameter(4, startbizdate);
//                    }
//                } else {
//                    query1.setParameter(1, inneraccountid);
//                    query1.setParameter(2, currency);
//                    if (!"".equals(bizdate)) {
//                        query1.setParameter(3, startbizdate);
//                    }
//                }
//                List<Object[]> result1 = query1.getResultList();
//                if (result1 != null && result1.size() > 0) {
//                    beginningbalance = new BigDecimal(String.valueOf(result1.get(result1.size() - 1)));
//                } else {
//                    beginningbalance = BigDecimal.valueOf(0L);
//                }
//            }
//
//            String sql_join1 = "", sql_join2 = "";
//            if (!"".equals(bizdate)) {
//                if ("1".equals(innerOrOuter)) {
//                    sql_join1 = " LEFT JOIN ihcinneraccountbalance ihcinneraccountbalance ON TMUNITJOURNAL.bankaccount = ihcinneraccountbalance.INNERACCOUNTID AND TMUNITJOURNAL.CURRENCY = ihcinneraccountbalance.CURRENCY AND TMUNITJOURNAL.bizdate = ihcinneraccountbalance.balancedate ";
//                    if (!"".equals(accounttype)) {
//                        sql_accountType = "AND ihcinneraccountbalance.accounttype=?3 ";
//                        sql_join2 = sql_accountType + " AND bizdate>=?4 AND bizdate<=?5 ";
//                    } else {
//                        sql_join2 = sql_accountType + " AND bizdate>=?3 AND bizdate<=?4 ";
//                    }
//                    sql_qm = String.format(sql_qm, new Object[] { sql_join1, sql_join2, sql_join1, sql_join2 });
//                } else {
//                    sql_join1 = " LEFT JOIN tmbalanceofba ON TMUNITJOURNAL.bizdate = tmbalanceofba.balancedate AND TMUNITJOURNAL.bankaccount = tmbalanceofba.accountid AND TMUNITJOURNAL.CURRENCY = tmbalanceofba.CURRENCY ";
//                    if (!"".equals(accounttype)) {
//                        sql_accountType = "AND tmbalanceofba.accounttype=?3 ";
//                        sql_join2 = sql_accountType + " AND bizdate>=?4 AND bizdate<=?5 ";
//                    } else {
//                        sql_join2 = sql_accountType + " AND bizdate>=?3 AND bizdate<=?4 ";
//                    }
//                    sql_qm = String.format(sql_qm, new Object[] { sql_join1, sql_join2, sql_join1, sql_join2 });
//                }
//                Query query2 = this.entityManager.createNativeQuery(sql_qm);
//                if (!"".equals(accounttype)) {
//                    query2.setParameter(1, bankaccount);
//                    query2.setParameter(2, currency);
//                    query2.setParameter(3, accounttype);
//                    query2.setParameter(4, startbizdate);
//                    query2.setParameter(5, endbizdate);
//                } else {
//                    query2.setParameter(1, bankaccount);
//                    query2.setParameter(2, currency);
//                    query2.setParameter(3, startbizdate);
//                    query2.setParameter(4, endbizdate);
//                }
//                List<Object[]> result2 = query2.getResultList();
//                if (result2 != null && result2.size() > 0) {
//                    currentbalance = new BigDecimal(String.valueOf(result2.get(0)));
//                } else {
//                    currentbalance = BigDecimal.valueOf(0L);
//                }
//            } else {
//                if ("1".equals(innerOrOuter)) {
//                    if (!"".equals(accounttype)) {
//                        sql_accountType = "AND ihcinneraccountbalance.accounttype=?3 ";
//                    }
//                    sql_join1 = " LEFT JOIN ihcinneraccountbalance ihcinneraccountbalance ON TMUNITJOURNAL.bankaccount = ihcinneraccountbalance.INNERACCOUNTID AND TMUNITJOURNAL.CURRENCY = ihcinneraccountbalance.CURRENCY ";
//                    sql_join2 = sql_accountType;
//                    sql_qm = String.format(sql_qm, new Object[] { sql_join1, sql_join2, sql_join1, sql_join2 });
//                } else {
//                    if (!"".equals(accounttype)) {
//                        sql_accountType = "AND tmbalanceofba.accounttype=?3 ";
//                    }
//                    sql_join1 = " LEFT JOIN tmbalanceofba ON TMUNITJOURNAL.bizdate = tmbalanceofba.balancedate AND TMUNITJOURNAL.bankaccount = tmbalanceofba.accountid AND TMUNITJOURNAL.CURRENCY = tmbalanceofba.CURRENCY ";
//                    sql_join2 = sql_accountType;
//                    sql_qm = String.format(sql_qm, new Object[] { sql_join1, sql_join2, sql_join1, sql_join2 });
//                }
//                Query query2 = this.entityManager.createNativeQuery(sql_qm);
//                query2.setParameter(1, bankaccount);
//                query2.setParameter(2, currency);
//                if (!"".equals(accounttype)) {
//                    query2.setParameter(3, accounttype);
//                }
//                List<Object[]> result2 = query2.getResultList();
//                if (result2 != null && result2.size() > 0) {
//                    currentbalance = new BigDecimal(String.valueOf(result2.get(0)));
//                } else {
//                    currentbalance = BigDecimal.valueOf(0L);
//                }
//            }
//        }
//        resultmap.put("BeginningBalance", beginningbalance);
//        resultmap.put("CurrentBalance", currentbalance.add(beginningbalance));
//        return JSONSerializer.serialize(resultmap);
//    }
//
//
//
//
//
//
//    public Map<String, Object> getLongUnusedAccountSql() {
//        Map<String, Object> res = new HashMap<>();
//        try {
//            IBusinessConfigKVResultService kvResultService = (IBusinessConfigKVResultService)SpringBeanUtils.getBean(IBusinessConfigKVResultService.class);
//
//
//            Object[] keys = { "AM_LongNotUsedAccountTerm", "AM_LongNotUsedAccountTransacteNum" };
//            Map<String, Object> settingResult = kvResultService.GetKvResult(keys, new HashMap<>(), "AM");
//            if (settingResult == null) {
//                res.put("result", "fail");
//
//                res.put("err", BankAccountInfoQueryCommon.getLang("globalParamSetErr"));
//                return res;
//            }  if (!settingResult.containsKey("AM_LongNotUsedAccountTerm") || settingResult.get("AM_LongNotUsedAccountTerm") == null) {
//                res.put("result", "fail");
//
//                res.put("err", BankAccountInfoQueryCommon.getLang("globalParamSetErr"));
//                return res;
//            }  if (!settingResult.containsKey("AM_LongNotUsedAccountTransacteNum") || settingResult.get("AM_LongNotUsedAccountTransacteNum") == null) {
//                res.put("result", "fail");
//
//                res.put("err", BankAccountInfoQueryCommon.getLang("globalParamSetErr"));
//                return res;
//            }
//
//
//            int term = Integer.parseInt(settingResult.get("AM_LongNotUsedAccountTerm").toString());
//            if (term <= 0) {
//                res.put("result", "fail");
//
//                res.put("err", BankAccountInfoQueryCommon.getLang("globalParamSetErr"));
//                return res;
//            }
//
//            int transacteNum = Integer.parseInt(settingResult.get("AM_LongNotUsedAccountTransacteNum").toString());
//
//            Date systemDate = new Date();
//            Calendar cal = Calendar.getInstance();
//            cal.setTime(systemDate);
//            cal.add(2, -term);
//            String targetDate = DateUtil.getDateString(cal.getTime(), "yyyy-MM-dd");
//
//
//            StringBuffer filterSql = new StringBuffer();
//            filterSql.append("SELECT 1 FROM BFBANKACCOUNTS ACCOUNTS ");
//            filterSql.append("LEFT JOIN ( ");
//
//            filterSql.append(" SELECT DETAILS.BANKACCOUNT, COUNT(DETAILS.BANKACCOUNT ) AS PAYCOUNT ");
//            filterSql.append(" FROM BPBANKTRANSCATIONDETAILS DETAILS ");
//            filterSql.append(" WHERE DETAILS.TRANSACTIONDATE >= ?1");
//            filterSql.append(" and DETAILS.TransStatus <> 5 ");
//            filterSql.append(" GROUP BY DETAILS.BANKACCOUNT ");
//            filterSql.append(" UNION ALL ");
//
//            filterSql.append(" SELECT journal.BankAccount AS BANKACCOUNT, COUNT( journal.BankAccount ) AS PAYCOUNT ");
//            filterSql.append(" FROM tmunitjournal journal ");
//            filterSql.append(" LEFT JOIN ( SELECT DETAILS.BANKACCOUNT  FROM BPBANKTRANSCATIONDETAILS DETAILS ");
//            filterSql.append(" WHERE DETAILS.TRANSACTIONDATE >= ?1");
//            filterSql.append(" GROUP BY DETAILS.BANKACCOUNT  ");
//            filterSql.append(" ) TRANSACCOUNT ON TRANSACCOUNT.BANKACCOUNT = journal.BankAccount ");
//            filterSql.append(" WHERE journal.BizDate >= ?1 AND journal.journalstatus = 1 AND journal.IsRedDoc <> '1' AND TRANSACCOUNT.BANKACCOUNT IS NULL");
//            filterSql.append(" GROUP BY journal.BankAccount ");
//            filterSql.append(") PAYHIS ON ACCOUNTS.ID = PAYHIS.BANKACCOUNT ");
//
//
//            filterSql.append(" WHERE ACCOUNTS.OPENDATE < ?1");
//            filterSql.append(" AND ACCOUNTS.ID=BFBANKACCOUNTS.ID");
//            filterSql.append(" AND (PAYHIS.BANKACCOUNT is null OR PAYHIS.PAYCOUNT <= ?2");
//            filterSql.append(" ) AND ACCOUNTS.INNEROROUTER = 2 ");
//
//            List<String> accountIds = new ArrayList<>();
//
//            Map<String, Object> map = new HashMap<>();
//            map.put("term", Integer.valueOf(term));
//            map.put("transactNum", Integer.valueOf(transacteNum));
//            String retSql = filterSql.toString();
//            Map<String, Object> resultMap = BankAccountInfoQueryCommon.executeExtend(map);
//            if (resultMap != null && "1".equals(resultMap.get("hasExtend"))) {
//                accountIds = (List<String>)resultMap.get("accountIds");
//                retSql = BankAccountInfoQueryCommon.objToStr(resultMap.get("querySql"));
//                res.put("hasExtend", "1");
//            } else {
//                retSql = retSql.replace("?1", "#BizDate#");
//                retSql = retSql.replace("?2", "#transactNum#");
//            }
//            res.put("result", "success");
//            res.put("data", accountIds);
//            res.put("longNoUseAccSql", retSql);
//            res.put("BizDate", DateUtil.getDate(targetDate, "yyyy-MM-dd"));
//            res.put("transactNum", Integer.valueOf(transacteNum));
//            return res;
//        } catch (Exception ex) {
//            log.error(", ex);
//            throw new BankAccountInfoQueryException(ex.getMessage(), ex);
//        }
//    }
//
//
//
//
//
//
//
//    public Map<String, Object> getCancellationInfo(Map<String, Object> map) {
//        Map<String, Object> res = new HashMap<>();
//        try {
//            String accountID = (String)map.get("ACCOUNTID");
//            String accountClass = (map.get("INNEROROUTER") == null) ? "" : String.valueOf(map.get("INNEROROUTER"));
//            String sql = "SELECT ID FROM TMBANKACCOUNTCANCELLATION WHERE ACCOUNT=? order by docstatus desc";
//            if (accountClass.equals("1")) {
//                sql = "SELECT ID FROM TMIACANCELLATIONAPPLY WHERE ACCOUNT=?  ";
//            }
//            Query query = this.entityManager.createNativeQuery(sql);
//            query.setParameter(1, accountID);
//            List<String> resultList = query.getResultList();
//            if (resultList.isEmpty()) {
//                res.put("result", "success");
//                res.put("data", "0");
//            } else if (resultList.size() >= 1) {
//                String cancellationId = resultList.get(0);
//                res.put("result", "success");
//                res.put("data", cancellationId);
//            }
//            return res;
//        }
//        catch (Exception ex) {
//            res.put("result", "fail");
//            res.put("message", ex.getMessage());
//            return res;
//        }
//    }
//
//
//
//
//    public Map<String, Object> getIsQxdxEnabled() {
//        Map<String, Object> res = new HashMap<>();
//        AuthEntryManager authEntryManager1 = (AuthEntryManager)SpringBeanUtils.getBean(AuthEntryManager.class);
//
//        Boolean enable = authEntryManager1.getEnable("CM_BANKACCOUNTQUERY", "TM_AdminOrg");
//        res.put("IsQxdxEnabled", enable);
//        return res;
//    }
//}
