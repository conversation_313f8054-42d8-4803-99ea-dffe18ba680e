package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service.CKGL;

import cn.hutool.core.codec.Base64;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.JTGKDepositPZEntity;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub.GUIDUtils;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub.WebServiceUtilek;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository.JTGKDepositPZRepository;
import com.inspur.fastdweb.core.FastdwebSqlSession;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.rpc.api.service.RpcClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class automaticDepositService {
    @Autowired
    private RpcClient rpcClient;
    @Autowired
    private FastdwebSqlSession sqlSession;
    @Autowired
    private LogService logService;
    @Autowired
    private JTGKDepositPZRepository jtgkDepositPZRepository;

    /**
     * 推送sap 存款台账 生成凭证
     */
    public void automaticDeposit()
    {
        String name="automaticDeposit";
        logService.init(name);
        String djbh=name;
        try{
//            Map<String,Object> map = sqlSession.selectOne(Map.class, "SELECT KEYVALUE,URL,username,PASSWORD,MANDT  FROM JTGKINTERFACECONFIG WHERE CODE = 'CAZDRY'");
            List<Map> pzlist=sqlSession.selectList(Map.class," select IDD_DATADICTIONARY.bigtxt01,IDD_DATADICTIONARY.code,IDD_DATADICTIONARY.txt01,IDD_DATADICTIONARY.txt02 from IDD_DATADICTIONARY  join  IDD_DATADICCATE on categoryid=IDD_DATADICCATE.id  " +
                    "where IDD_DATADICCATE.code='sap-ckpz'   ");
            if(pzlist==null||pzlist.size()<=0){
                logService.error(name,"未配置接口路径");
                return;
            }
            Map<String,Object>  map=pzlist.get(0);
            String url=String.valueOf(map.get("BIGTXT01"));//更新日期，不传此参数，则获取全量数据    scc/123456   geam/123456
            String username=String.valueOf(map.get("TXT01"));
            String pwd=String.valueOf(map.get("TXT02"));
            String mandt=String.valueOf(map.get("CODE"));
            //String encode = Base64.encode(String.valueOf(map.get("KEYVALUE")));
            logService.info( djbh, "map:"+map);
            //logService.info( djbh, "encode:"+encode);

            List<JTGKDepositPZEntity> jtgkDepositPZEntityList=sqlSession.selectList(JTGKDepositPZEntity.class,"select * from VW_JTGKCKSQPUSHSAP");
            if(jtgkDepositPZEntityList!=null&&jtgkDepositPZEntityList.size()>0){
                for(JTGKDepositPZEntity jtgkDepositPZEntity:jtgkDepositPZEntityList){
                    try {
//                        (case when TMBANKDEPOSIT.DEPOSITTYPE='2' then '4' else TMBANKDEPOSIT.CDLX end)  as  CDLX_CODE,
//                            (case when TMBANKDEPOSIT.DEPOSITTYPE='2' then '4-通知存款' when TMBANKDEPOSIT.CDLX='1' then '1-定期存款'
//                            when TMBANKDEPOSIT.CDLX='2' then '2-结构性存款' when TMBANKDEPOSIT.CDLX='3' then '3-大额存单'  end) as CDLX_NAME
                      if("2".equals(jtgkDepositPZEntity.getDEPOSITTYPE())){
                            jtgkDepositPZEntity.setCdlx_code("4");
                            jtgkDepositPZEntity.setCdlx_name("4-通知存款");
                        }
                        else if("1".equals(jtgkDepositPZEntity.getCdlx())){
                            jtgkDepositPZEntity.setCdlx_code(jtgkDepositPZEntity.getCdlx());
                            jtgkDepositPZEntity.setCdlx_name("1-定期存款");
                        }
                        else if("2".equals(jtgkDepositPZEntity.getCdlx())){
                            jtgkDepositPZEntity.setCdlx_code(jtgkDepositPZEntity.getCdlx());
                            jtgkDepositPZEntity.setCdlx_name("2-结构性存款");
                        }
                        else if("3".equals(jtgkDepositPZEntity.getCdlx())){
                            jtgkDepositPZEntity.setCdlx_code(jtgkDepositPZEntity.getCdlx());
                            jtgkDepositPZEntity.setCdlx_name("3-大额存单");
                        }
                        cktzpushsap(djbh,logService,jtgkDepositPZEntity,url,username,pwd,mandt);
                    }
                    catch (Throwable e){
                        e.printStackTrace();
                        logService.error( djbh, "推送sap存款台账异常",e);
                        jtgkDepositPZEntity.setStatus("2");//推送失败
                        jtgkDepositPZRepository.save(jtgkDepositPZEntity);
                    }
                }

            }else{
                logService.info(djbh,"无数据！");
            }

        }catch (Throwable ex){
            logService.error( name, "推送sap存款台账异常",ex);
        }finally {
            logService.flush();
        }
    }

    public boolean cktzpushsap(String djbh,LogService logService,JTGKDepositPZEntity jtgkDepositPZEntity,String url,String username,String pwd,String mandt){
        String GUID = (jtgkDepositPZEntity.getDJBH() + GUIDUtils.appendCurrentDateTime()).toUpperCase();
        String proxy_id = (jtgkDepositPZEntity.getDJBH() + GUIDUtils.appendCurrentDateTime()).toUpperCase();
        djbh = jtgkDepositPZEntity.getDJBH();
        jtgkDepositPZEntity.setId(GUID);
        jtgkDepositPZEntity.setProxy_id(proxy_id);
        String data = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:urn=\"urn:goldwind.com:i_gsc:gsc_deposit_data\">\n" +
                "   <soapenv:Header/>\n" +
                "   <soapenv:Body>\n" +
                "      <urn:mt_gsc_deposit_data>\n" +
                "         <iv_msg_flag>GSCLOUD</iv_msg_flag>\n" +
                "         <is_msg_head>\n" +
                "            <!--Optional:-->\n" +
                "            <MANDT>" + mandt + "</MANDT>\n" +
                "            <!--Optional:-->\n" +
                "            <GUID>" + GUID + "</GUID>\n" +
                "            <!--Optional:-->\n" +
                "            <PROXY_ID>" + proxy_id + "</PROXY_ID>\n" +
                "            <!--Optional:-->\n" +
                "            <SYSTEM_ID>GSC司库</SYSTEM_ID>\n" +
                "            <!--Optional:-->\n" +
                "            <OPERATOR>" + jtgkDepositPZEntity.getZsqr() + "</OPERATOR>\n" +
                "            <!--Optional:-->\n" +
                "            <SPRAS>?</SPRAS>\n" +
                "            <!--Optional:-->\n" +
                "            <INTERFACE_ID>?</INTERFACE_ID>\n" +
                "            <!--Optional:-->\n" +
                "            <SENDER>?</SENDER>\n" +
                "            <!--Optional:-->\n" +
                "            <RECIVER>?</RECIVER>\n" +
                "            <!--Optional:-->\n" +
                "            <SENDTIME>?</SENDTIME>\n" +
                "         </is_msg_head>\n" +
                "         <!--Zero or more repetitions:-->\n" +
                "         <it_row>\n" +
                "            <!--Optional:-->\n" +
                "            <id>" + Optional.ofNullable(jtgkDepositPZEntity.getDJBH()).orElse("")  + "</id>\n" +
                "            <!--Optional:-->\n" +
                "            <skcjbh>" + Optional.ofNullable(jtgkDepositPZEntity.getSkcjbh()).orElse("") + "</skcjbh>\n" +
                "            <!--Optional:-->\n" +
                "            <skcjmc>" + Optional.ofNullable(jtgkDepositPZEntity.getSkcjmc()).orElse("") + "</skcjmc>\n" +
                "            <!--Optional:-->\n" +
                "            <cdlx_code>" + Optional.ofNullable(jtgkDepositPZEntity.getCdlx_code()).orElse("") + "</cdlx_code>\n" +
                "            <!--Optional:-->\n" +
                "            <cdlx_name>" + Optional.ofNullable(jtgkDepositPZEntity.getCdlx_name()).orElse("") + "</cdlx_name>\n" +
                "            <!--Optional:-->\n" +
                "            <bukrs>" + Optional.ofNullable(jtgkDepositPZEntity.getBukrs()).orElse("") + "</bukrs>\n" +
                "            <!--Optional:-->\n" +
                "            <butxt>" + Optional.ofNullable(jtgkDepositPZEntity.getButxt()).orElse("") + "</butxt>\n" +
                "            <!--Optional:-->\n" +
                "            <actnbr>" + Optional.ofNullable(jtgkDepositPZEntity.getActnbr()).orElse("") + "</actnbr>\n" +
                "            <!--Optional:-->\n" +
                "            <zszfs>" + Optional.ofNullable(jtgkDepositPZEntity.getZszfs()).orElse("") + "</zszfs>\n" +
                "            <!--Optional:-->\n" +
                "            <waers>" + Optional.ofNullable(jtgkDepositPZEntity.getWaers()).orElse("") + "</waers>\n" +
                "            <!--Optional:-->\n" +
                "            <waers_nm>" + Optional.ofNullable(jtgkDepositPZEntity.getWaers_nm()).orElse("") + "</waers_nm>\n" +
                "            <!--Optional:-->\n" +
                "            <amount>" + Optional.ofNullable(jtgkDepositPZEntity.getAmount()).orElse("") + "</amount>\n" +
                "            <!--Optional:-->\n" +
                "            <interest>" + Optional.ofNullable(jtgkDepositPZEntity.getInterest()).orElse("") + "</interest>\n" +
                "            <!--Optional:-->\n" +
                "            <bnklz>" + Optional.ofNullable(jtgkDepositPZEntity.getBnklz()).orElse("") + "</bnklz>\n" +
                "            <!--Optional:-->\n" +
                "            <banka>" + Optional.ofNullable(jtgkDepositPZEntity.getBanka()).orElse("") + "</banka>\n" +
                "            <!--Optional:-->\n" +
                "            <kunnr>" + Optional.ofNullable(jtgkDepositPZEntity.getKunnr()).orElse("") + "</kunnr>\n" +
                "            <!--Optional:-->\n" +
                "            <prctr>" + Optional.ofNullable(jtgkDepositPZEntity.getPrctr()).orElse("") + "</prctr>\n" +
                "            <!--Optional:-->\n" +
                "            <ltext>" + Optional.ofNullable(jtgkDepositPZEntity.getLtext()).orElse("") + "</ltext>\n" +
                "            <!--Optional:-->\n" +
                "            <kostl>" + Optional.ofNullable(jtgkDepositPZEntity.getKostl()).orElse("") + "</kostl>\n" +
                "            <!--Optional:-->\n" +
                "            <ltext_cb>" + Optional.ofNullable(jtgkDepositPZEntity.getLtext_cb()).orElse("") + "</ltext_cb>\n" +
                "            <!--Optional:-->\n" +
                "            <zsgtx>" + Optional.ofNullable(jtgkDepositPZEntity.getZsgtx()).orElse("") + "</zsgtx>\n" +
                "            <!--Optional:-->\n" +
                "            <zsqr>" + Optional.ofNullable(jtgkDepositPZEntity.getZsqr()).orElse("") + "</zsqr>\n" +
                "            <!--Optional:-->\n" +
                "            <zblr>" + Optional.ofNullable(jtgkDepositPZEntity.getZblr()).orElse("") + "</zblr>\n" +
                "            <!--Optional:-->\n" +
                "            <zdate>" + Optional.ofNullable(jtgkDepositPZEntity.getZdate()).orElse("") + "</zdate>\n" +
                "            <!--Optional:-->\n" +
                "            <reserve_f1></reserve_f1>\n" +
                "            <!--Optional:-->\n" +
                "            <reserve_f2></reserve_f2>\n" +
                "            <!--Optional:-->\n" +
                "            <reserve_f3></reserve_f3>\n" +
                "            <!--Optional:-->\n" +
                "            <reserve_f4></reserve_f4>\n" +
                "            <!--Optional:-->\n" +
                "            <reserve_f5></reserve_f5>\n" +
                "            <!--Optional:-->\n" +
                "            <reserve_f6></reserve_f6>\n" +
                "         </it_row>\n" +
                "      </urn:mt_gsc_deposit_data>\n" +
                "   </soapenv:Body>\n" +
                "</soapenv:Envelope>";

        String soapAction = "";
        logService.info(djbh, "入参：" + data);
        String xmlString = WebServiceUtilek.webServiceRequest(logService, djbh, url, soapAction, data, username, pwd, true);
        logService.info(djbh, "出参：" + xmlString);
        jtgkDepositPZEntity.setStatus("1");//已推送
        jtgkDepositPZRepository.save(jtgkDepositPZEntity);
        return true;
    }

}
