package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service;

import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class JtgkEnvironmentService {

    public String GetCacheValue(String key, long timeout, TimeUnit unit) {
        StringRedisTemplate stringRedisTemplate = SpringBeanUtils.getBean(StringRedisTemplate.class);
        String keyValue = stringRedisTemplate.opsForValue().get("JtgkConfig:" + key);
        if (keyValue == null) {
            try {
                EntityManager manager = SpringBeanUtils.getBean(EntityManager.class);
                Query nativeQuery = manager.createNativeQuery(
                        "select ISENABLE , KEYVALUE from JTGKINTERFACECONFIG where code = '" + key + "'");
                nativeQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
                List<Map<String, Object>> queryList = nativeQuery.getResultList();

                List<Map<String, Object>> resultList = new ArrayList<>();
                for (Map<String, Object> singleResult : queryList) {
                    Map<String, Object> single = new HashMap<>();
                    for (String s : new HashSet<>(singleResult.keySet())) {
                        String lowerCase = s.toLowerCase();
                        if (!single.containsKey(lowerCase)) {
                            single.put(lowerCase, singleResult.get(s));
                        }
                    }
                    resultList.add(single);
                }
                if (CollectionUtils.isEmpty(resultList)) {
                    return null;

                } else {
                    if (resultList.size() > 0) {
                        if (resultList.get(0).get("isenable").toString().equals("1"))
                            keyValue = resultList.get(0).get("keyvalue").toString();
                    }
                    stringRedisTemplate.opsForValue().set("JtgkConfig:" + key, keyValue, timeout, unit);
                    return keyValue;
                }

            } catch (Exception e) {
                return null;
            }
        } else {
            return keyValue;
        }
    }

}
