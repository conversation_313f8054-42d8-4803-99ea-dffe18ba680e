package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.fasterxml.jackson.databind.JsonSerializable;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao.PayResultBackDto;
import com.inspur.fastdweb.core.FastdwebSqlSession;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.common.JSONSerializer;
import io.iec.edp.caf.rpc.api.service.RpcClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 付款结果反馈  调度
 */
@Slf4j
public class PayResultFeedbackService {
    @Autowired
    private RpcClient rpcClient;
    @Autowired
    private FastdwebSqlSession sqlSession;
    @Autowired
    private LogService logService;

    public void PayResultFeedback()
    {
        String name="付款结果回传";//付款结果回传";
        logService.init(name);
        try {
            //#region 待付池字段解析
            //DOCSTATUS： 0,制单 X 外部系统传入司库待付池;1,待付款安排 X 需要付款安排还未安排得 ;-1,接收失败 X;
            // 2,已完成 安排完成 ;-2,已退回[这里是司库待付池手工点击退回后调用我的退回接口【SFS场景，其他异构系统得走这个调度反馈】
            //部分终止付款【付款安排+结算办理】、全部终止付款【待付池】手工退回前端业务系统【这里终止付款后不退回还是可以再次安排，退回后不能安排了】  ;
            // -3,已作废 X 共享 组合支付 拆分之后 再次回传司库场景【共享拆分后再次
            // 推送司库时会返回原单将原单作废掉，这个退回接口不处理】 共享链接还没给 TODO：
            //RESULTBACKSTATUS 付款结果回传状态(null未回传、1付款完成待回传、2回传对方接收成功、3回传对方接收失败)   回传后回写
            //REDDOCRESPONSE 退汇回传结果说明
            //REQUESTDOCNO 业务支付申请单号
            //REQUESTDETAILID 业务支付申请明细ID
            //REFDOCID 付款安排单ID
            //DOCSTATUS 付款结果(1,付款成功;2,付款终止;3,退回;4,银行交易失败退汇;5,票据签收)
            //REDDOCSTATUS 退汇回传状态(null未退汇、1已退汇待回传、2回传对方接收成功、3回传对方接收失败)
            //REFDETAILID 付款安排单明细ID
            //REDDOCNO 退汇单据编号
            //RESULTBACKRESPSONSE 付款结果回传说明
            //PAYDOCID 付款结算单ID
            //PAYDOCTYPE 付款结算单类型
            //REDDOCPROCESSON 退汇回传时间   ---是疑似退汇流水认领成功回传前端业务系统的时间
            //REQUESTDOCID 业务支付申请ID
            //#endregion
//            select JTGKPAYMENTINFO.DOCSTATUS,JTGKPAYMENTDETAIL.* from JTGKPAYMENTDETAIL    join JTGKPAYMENTINFO on JTGKPAYMENTINFO.id=JTGKPAYMENTDETAIL.parentid   where JTGKPAYMENTINFO.DOCSTATUS ='2'
            List<Map> groptype=sqlSession.selectList(Map.class,"select distinct srcBizSys from vw_jtgkfkjgfk ");
            if(groptype!=null&&groptype.size()>0){
                for(Map map:groptype){
                    List<PayResultBackDto>  payLists=sqlSession.selectList(PayResultBackDto.class,"  select * from vw_jtgkfkjgfk where srcBizSys=#{srcBizSys} ",map.get("SRCBIZSYS"));
                    goldInterfaceService goldInterfaceService=new goldInterfaceService();
                    if(payLists!=null&&payLists.size()>0){
                        goldInterfaceService.paymentResultPush(name,payLists,"",logService);
                    }

                }
            }
        }
        catch (Throwable ex){
            ex.printStackTrace();
            logService.error( name, "付款结果回传异常",ex);
        }finally {
            logService.flush();
        }
    }

}
