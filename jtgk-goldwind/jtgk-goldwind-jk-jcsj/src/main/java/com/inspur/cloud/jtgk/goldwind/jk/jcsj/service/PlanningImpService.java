package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.inspur.fastdweb.model.excel.ExcelObject;
import com.inspur.fastdweb.model.excel.ExcelResult;
import com.inspur.fastdweb.model.excel.IDPExcelImportMx;
import com.inspur.fastdweb.service.excel.IExcelImportEvent;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import io.iec.edp.caf.common.JSONSerializer;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 资金计划导入excel扩展
 * <AUTHOR>
 */
@Slf4j
@Service
public class PlanningImpService implements IExcelImportEvent {

    /**
     * 插入数据库前
     */
    @Override
    public ExcelObject beforeInsertImport(ExcelObject excelObject) {
        log.error("插入数据库之前");
        List<ExcelResult> rows  = excelObject.rows;
        List<IDPExcelImportMx> exiMx  = excelObject.exiMx;
        int rowStart = Integer.parseInt(excelObject.rowStart) - 1;
        Map<String, Object> valMap = excelObject.valMap;
        log.error("valMap:"+JSONSerializer.serialize(valMap));
        BigDecimal JHJHDATA_ZDYJE = ObjectUtils.isEmpty(valMap.get("JHJHDATA_ZDYJE"))?BigDecimal.ZERO:new BigDecimal(valMap.get("JHJHDATA_ZDYJE").toString()).multiply(new BigDecimal(10000));//
        BigDecimal JHJHDATA_ZDYJE1 = ObjectUtils.isEmpty(valMap.get("JHJHDATA_ZDYJE1"))?BigDecimal.ZERO:new BigDecimal(valMap.get("JHJHDATA_ZDYJE1").toString()).multiply(new BigDecimal(10000));//
        BigDecimal JHJHDATA_ZDYJE2 = ObjectUtils.isEmpty(valMap.get("JHJHDATA_ZDYJE2"))?BigDecimal.ZERO:new BigDecimal(valMap.get("JHJHDATA_ZDYJE2").toString()).multiply(new BigDecimal(10000));//
        BigDecimal JHJHDATA_ZDYJE3 = ObjectUtils.isEmpty(valMap.get("JHJHDATA_ZDYJE3"))?BigDecimal.ZERO:new BigDecimal(valMap.get("JHJHDATA_ZDYJE3").toString()).multiply(new BigDecimal(10000));//
        BigDecimal JHJHDATA_ZDYJE4 = ObjectUtils.isEmpty(valMap.get("JHJHDATA_ZDYJE4"))?BigDecimal.ZERO:new BigDecimal(valMap.get("JHJHDATA_ZDYJE4").toString()).multiply(new BigDecimal(10000));//
        BigDecimal JHJHDATA_ZDYJE5 = ObjectUtils.isEmpty(valMap.get("JHJHDATA_ZDYJE5"))?BigDecimal.ZERO:new BigDecimal(valMap.get("JHJHDATA_ZDYJE5").toString()).multiply(new BigDecimal(10000));//
        BigDecimal JHJHDATA_ZDYJE6 = ObjectUtils.isEmpty(valMap.get("JHJHDATA_ZDYJE6"))?BigDecimal.ZERO:new BigDecimal(valMap.get("JHJHDATA_ZDYJE6").toString()).multiply(new BigDecimal(10000));//
        BigDecimal JHJHDATA_ZDYJE7 = ObjectUtils.isEmpty(valMap.get("JHJHDATA_ZDYJE7"))?BigDecimal.ZERO:new BigDecimal(valMap.get("JHJHDATA_ZDYJE7").toString()).multiply(new BigDecimal(10000));//
        BigDecimal JHJHDATA_ZDYJE8 = ObjectUtils.isEmpty(valMap.get("JHJHDATA_ZDYJE8"))?BigDecimal.ZERO:new BigDecimal(valMap.get("JHJHDATA_ZDYJE8").toString()).multiply(new BigDecimal(10000));//
        BigDecimal JHJHDATA_ZDYJE9 = ObjectUtils.isEmpty(valMap.get("JHJHDATA_ZDYJE9"))?BigDecimal.ZERO:new BigDecimal(valMap.get("JHJHDATA_ZDYJE9").toString()).multiply(new BigDecimal(10000));//
//        BigDecimal JHJHDATA_ZDYJE10 = ObjectUtils.isEmpty(valMap.get("JHJHDATA_ZDYJE10"))?BigDecimal.ZERO:new BigDecimal(valMap.get("JHJHDATA_ZDYJE10").toString()).multiply(new BigDecimal(10000));//
//        BigDecimal JHJHDATA_ZDYJE11 = ObjectUtils.isEmpty(valMap.get("JHJHDATA_ZDYJE11"))?BigDecimal.ZERO:new BigDecimal(valMap.get("JHJHDATA_ZDYJE11").toString()).multiply(new BigDecimal(10000));//
//        BigDecimal JHJHDATA_ZDYJE12 = ObjectUtils.isEmpty(valMap.get("JHJHDATA_ZDYJE12"))?BigDecimal.ZERO:new BigDecimal(valMap.get("JHJHDATA_ZDYJE12").toString()).multiply(new BigDecimal(10000));//
        log.error("JHJHDATA_ZDYJE:"+JHJHDATA_ZDYJE+"JHJHDATA_ZDYJE2:"+JHJHDATA_ZDYJE2);

        valMap.put("JHJHDATA_ZDYJE", JHJHDATA_ZDYJE);
        valMap.put("JHJHDATA_ZDYJE1",JHJHDATA_ZDYJE1);
        valMap.put("JHJHDATA_ZDYJE2", JHJHDATA_ZDYJE2);
        valMap.put("JHJHDATA_ZDYJE3",JHJHDATA_ZDYJE3);
        valMap.put("JHJHDATA_ZDYJE4", JHJHDATA_ZDYJE4);
        valMap.put("JHJHDATA_ZDYJE5", JHJHDATA_ZDYJE5);
        valMap.put("JHJHDATA_ZDYJE6", JHJHDATA_ZDYJE6);
        valMap.put("JHJHDATA_ZDYJE7", JHJHDATA_ZDYJE7);
        valMap.put("JHJHDATA_ZDYJE8", JHJHDATA_ZDYJE8);
        valMap.put("JHJHDATA_ZDYJE9", JHJHDATA_ZDYJE9);
//        valMap.put("JHJHDATA_ZDYJE10", JHJHDATA_ZDYJE10);
//        valMap.put("JHJHDATA_ZDYJE11", JHJHDATA_ZDYJE11);
//        valMap.put("JHJHDATA_ZDYJE12", JHJHDATA_ZDYJE12);
        log.error(String.format("资金计划导入前事件重新组织入参:%s", JSONSerializer.serialize(valMap)));


        return excelObject;

    }


}
