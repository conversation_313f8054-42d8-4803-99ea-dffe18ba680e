package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service;

import com.alibaba.dubbo.config.annotation.Service;
import com.inspur.fastdweb.model.excel.ExcelObject;
import com.inspur.fastdweb.model.excel.ExcelResult;
import com.inspur.fastdweb.model.excel.IDPExcelImportMx;
import com.inspur.fastdweb.service.excel.IExcelImportEvent;
import io.iec.edp.caf.common.JSONSerializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class PlanningImpServicejhxh implements IExcelImportEvent {

    /**
     * 插入数据库前
     */
    @Override
    public ExcelObject beforeInsertImport(ExcelObject excelObject) {
        log.error("插入数据库之前");
        List<ExcelResult> rows  = excelObject.rows;
        List<IDPExcelImportMx> exiMx  = excelObject.exiMx;
        int rowStart = Integer.parseInt(excelObject.rowStart) - 1;
        Map<String, Object> valMap = excelObject.valMap;
        log.error("valMap:"+ JSONSerializer.serialize(valMap));
        BigDecimal JHJHDATA_JHXH = ObjectUtils.isEmpty(valMap.get("JHJHDATA_JHXH"))?BigDecimal.ZERO:new BigDecimal(valMap.get("JHJHDATA_JHXH").toString()).multiply(new BigDecimal(10000));//
//        log.error("JHJHDATA_JHXH:"+JHJHDATA_JHXH+"JHJHDATA_ZDYJE2:"+JHJHDATA_ZDYJE2);

        valMap.put("JHJHDATA_JHXH", JHJHDATA_JHXH); 
        log.error(String.format("资金计划导入前事件重新组织入参:%s", JSONSerializer.serialize(valMap)));


        return excelObject;

    }


}