package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service;


import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.controller.ProcessComponentsController;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao.MonitorInitParams;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub.JTGKExtendException;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub.ResultParamGenerate;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository.ZHKMYEResultObjectRepository;
import com.inspur.fastdweb.core.FastdwebSqlSession;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.common.JSONSerializer;
import io.iec.edp.caf.commons.exception.CAFRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class ProcessComponentsService implements ProcessComponentsController {
    @Autowired
    private LogService logService;
    @Autowired
    private ResultParamGenerate resultParamGenerate;
    @Autowired
    private FastdwebSqlSession sqlSession;

    /**
     * @Description: 账户科目维护节点前一节点的 执行后事件，执行后修改账户信息FK01=1  可修改
     * @Param:
     * @return:
     * @Author: lq
     * @Date: 2022/6/21
     */
    @Override
    public Map<String, Object> UpdateZhkmState_before(Map<String, Object> data) {
        String name="账户科目维护节点前";
        logService.init(name);
        String billID = "";
        try {
//            logService.info(name,"入参解析：" + JSON.toJSONString(data));
            Map<String, Object> cotextParam = (Map<String, Object>) data.get("contextParam");
            billID = String.valueOf(cotextParam.get("BILLID")) ;
            logService.info(name,billID);
            int i= sqlSession.update("update BFBANKACCOUNTS SET FK01='1' WHERE RELATEDID=#{ID} ",billID);
            logService.info(name,"修改条数：" + i);
            return resultParamGenerate.getSuccess();
        } catch (Throwable ex) {
            logService.error(name, ex);
            return resultParamGenerate.getFail(ex.toString());
        } finally {
            logService.flush();
        }
    }
    /**
     * @Description: 账户科目维护通过后 修改账户自定义字段 FK01=2  不可修改
     * @Param:
     * @return:
     * @Author: lq
     * @Date: 2022/6/21
     */
    @Override
    public Map<String, Object> UpdateZhkmState_after(Map<String, Object> data) {
        String name="账户科目维护节点后";
        logService.init(name);
        String billID = "";
        try {
//            logService.info(name,"入参解析：" + JSON.toJSONString(data));
            Map<String, Object> cotextParam = (Map<String, Object>) data.get("contextParam");
            billID = String.valueOf(cotextParam.get("BILLID")) ;
            logService.info(name,billID);
            Map<String,Object> parameterMap = new HashMap<String,Object>();
            parameterMap.put("billID",billID);
            List<Map> getData = sqlSession.selectList(Map.class," select bfbankaccounts.accountno,bfcurrency.code,bfcurrency.name_chs  from bfbankaccountitems left join bfbankaccounts on bfbankaccountitems.parentid=bfbankaccounts.id  left join bfcurrency on bfbankaccountitems.currency=bfcurrency.id " +
                    "where bfbankaccounts.RELATEDID=#{billID} and (bfbankaccountitems.FK01 IS NULL  or trim(bfbankaccountitems.FK01)='')  ",parameterMap);
            if(getData!=null&&getData.size()>0){
                logService.error(name,"请先维护会计科目!");
                //throw new goldWindException("请先维护会计科目!");
                return resultParamGenerate.getFail("请先维护会计科目");
            }
            int i= sqlSession.update("update BFBANKACCOUNTS SET FK01='2' WHERE RELATEDID=#{ID} ",billID);
            logService.info(name,"修改条数：" + i);
            return resultParamGenerate.getSuccess();
        } catch (Throwable ex) {
            logService.error(name, ex);
            Map info=new HashMap();
            String errorMessage =  ex.getMessage();
            if(ex instanceof CAFRuntimeException){
                //如果为CAFRuntimeException或者它的继承类则
                if(((CAFRuntimeException)ex).isBizException()){
                    info.put("Error",errorMessage);
                }
            }
            //throw new goldWindException(JSONSerializer.serialize(info) );
            return resultParamGenerate.getFail(JSONSerializer.serialize(info) );
        } finally {
            logService.flush();
            //return resultParamGenerate.getSuccess();
        }
    }

    /**
     * @Description: 付款成功修改待付池状态
     * @Param:
     * @return:
     * @Author: lq
     * @Date: 2022/6/21
     */
    @Override
    public Map<String, Object> UpdateStatesuccess(Map<String, Object> data) {
        String filename="付款成功回写待付池状态";
        logService.init(filename);
        String billID="";
        try {
            //#region 这里作废，振龙老师那边使用了调度回写待付池执行记录表
//            logService.info(filename, "UpdateStatesuccess入参解析："+JSON.toJSONString(data));
//            MonitorInitParams zx_cmp_param = JSON.parseObject(JSON.toJSONString(data), MonitorInitParams.class);
//            billID = zx_cmp_param.getContextParam().getBILLID();
//            String FORMTYPE= zx_cmp_param.getContextParam().getFORMTYPE();
//            logService.info(billID, "【UpdateStatesuccess入参解析FORMTYPE】：" + FORMTYPE+",billID:"+billID);
//            String zt="K";
//            Map<String,Object> parameterMap = new HashMap<>();
//            parameterMap.put("ID",billID);
//            parameterMap.put("ZT",zt);
//            if("TM_QYFKD".equals(FORMTYPE))
//            {
//                List<Map> JHBHList =sqlSession.selectList(Map.class,"SELECT ID,SRCDOCID,DOCSTATUS,docno from  TMPAYMENTSETTLEMENT   WHERE TMPAYMENTSETTLEMENT.ID=#{ID} ",parameterMap);
//                logService.info(billID,"UpdateStatesuccess-JHBHList.size():"+JHBHList.size());
//                if(JHBHList!=null &&JHBHList.size()>0) {
//                    parameterMap.put("SRCDOCID",JHBHList.get(0).get("SRCDOCID"));
//                    String updateSQL = "update 待付池 set 待付池_ZDY4=?1    where  待付池_NM in (SELECT 待付池HBFKMX.待付池_NM FROM 待付池HBFKMX left join 待付池HBFK ON  待付池HBFK.待付池_NM=待付池HBFKMX.PARENTID  LEFT JOIN TMPAYMENTSETTLEMENT ON TMPAYMENTSETTLEMENT.Srcdocno=待付池HBFK.待付池_JHBH  WHERE TMPAYMENTSETTLEMENT.ID=#{ID}) AND 待付池.待付池_TZDNM IS NULL ";
//                    int num = sqlSession.update(updateSQL, parameterMap);
//                    logService.info(billID,"UpdateStatesuccess-修改待付池状态num：" + num);
//                }
//            }
//            else if("BM_ZGBS".equals(FORMTYPE))
//            {
//                List<Map> JHBHList =sqlSession.selectList(Map.class,"SELECT * from  TMBILLENDORSEREQUEST   WHERE TMBILLENDORSEREQUEST.ID=#{ID} ",parameterMap);
//                logService.info(billID,"UpdateStatesuccess-JHBHList.size():"+JHBHList.size());
//                String updateSQL = "";
//                int  num = sqlSession.update(updateSQL, parameterMap);
//                logService.info(billID,"UpdateStatesuccess-修改待付池状态num：" + num);
//            }
//            else
//            {//开票申请 BM_ZGKP
//                List<Map> JHBHList =sqlSession.selectList(Map.class,"SELECT * from  TMBILLINGREQUEST   WHERE TMBILLINGREQUEST.ID=#{ID} ",parameterMap);
//                logService.info(billID,"UpdateStatesuccess-JHBHList.size():"+JHBHList.size());
//                String updateSQL = "";
//                int num = sqlSession.update(updateSQL, parameterMap);
//                logService.info(billID,"UpdateStatesuccess-修改待付池状态num：" + num);
//            }
            //#endregion
        }
        catch (Throwable e)
        {
            log.error("UpdateStatesuccess-修改待付池状态报错",e);
            return resultParamGenerate.getFail(e.toString());
        }
        finally {
            log.error("UpdateStatesuccess-修改待付池状态结束！");
            return resultParamGenerate.getSuccess();

        }

    }

}
