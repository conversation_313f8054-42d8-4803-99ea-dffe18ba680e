package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service.XXQD;

import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONObject;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.XXQD.JfWeChatInterReceiver;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.XXQD.JfWeChatMsgContent;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub.WechatUtilek;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.service.JtgkEnvironmentService;
import com.inspur.fastdweb.core.FastdwebSqlSession;
import io.iec.edp.caf.common.JSONSerializer;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.message.api.MessageSender;
import io.iec.edp.caf.message.api.msgcontent.MessageContent;
import io.iec.edp.caf.message.api.receiver.MessageReceiver;
import io.iec.edp.caf.message.api.response.MessageResponse;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;

//消息发送器(MessageSender)：用于消息发送的实际操作，包含业务逻辑、第三方接口调用等；
@Slf4j
public class JfWeChatInterSender extends MessageSender {

    @Override
    public String getParamConfigName() {
        return "JfEnterpriseWechatInter";
    }

    @Override
    public String getMsgContentBuilderName() {
        return "JfEnterpriseWechatInter";
    }
    /**
     * @description 消息发送方法
     * <AUTHOR> Lin
     * @param receiver 消息接收者
     * @param content 消息体
     * @param channel 消息通道编码(可选)
     * @return MessageResponse 消息响应
     */
    @Override
    public MessageResponse send(MessageReceiver receiver, MessageContent content, @Nullable String channel) throws MalformedURLException {
        MessageResponse response = new MessageResponse();
        //receiver为上一步组织好的接收人渠道账号
        //content为组织好的消息内容体
        //通过调用阿里的短息服务，将所需参数传入即可发送短信
        log.error("企业微信消息渠道receiver："+JSONSerializer.serialize(receiver));
        log.error("企业微信消息渠道content："+JSONSerializer.serialize(content));
        JfWeChatInterReceiver weChatInterReceiver = (JfWeChatInterReceiver)receiver;
        JfWeChatMsgContent weChatMsgContent = (JfWeChatMsgContent)content;
         FastdwebSqlSession sqlSession=SpringBeanUtils.getBean(FastdwebSqlSession.class);

//        JtgkEnvironmentService jtgkEnvironmentService = SpringBeanUtils.getBean(JtgkEnvironmentService.class);
        String tokenurl="";//jtgkEnvironmentService.GetCacheValue("wechatUrl", 1, TimeUnit.DAYS);
        String sendurl="";//jtgkEnvironmentService.GetCacheValue("wechatSendUrl", 1, TimeUnit.DAYS);
        String corpId="";//jtgkEnvironmentService.GetCacheValue("wechatAppid", 1, TimeUnit.DAYS);
        String corpSecret ="";//jtgkEnvironmentService.GetCacheValue("wechatAppsecret", 1, TimeUnit.DAYS);
        String agentId ="";//jtgkEnvironmentService.GetCacheValue("agentId", 1, TimeUnit.DAYS);
        String wechatkey="";//jtgkEnvironmentService.GetCacheValue("wechatkey", 1, TimeUnit.DAYS);
        String ts="";//jtgkEnvironmentService.GetCacheValue("debugger", 1, TimeUnit.DAYS);
        List<Map> cofigList =sqlSession.selectList(Map.class,"select ISENABLE , KEYVALUE,CODE from JTGKINTERFACECONFIG  " +
                "where CODE IN ('wechatUrl','wechatSendUrl','wechatAppid','wechatAppsecret','agentId','wechatkey','debugger')  AND ISENABLE='1' ") ;
        for(Map cofigmap:cofigList){
            if("wechatUrl".equals(cofigmap.get("CODE"))){
                tokenurl= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
            }
            else if("wechatSendUrl".equals(cofigmap.get("CODE"))){
                sendurl= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
            }
            else if("wechatAppid".equals(cofigmap.get("CODE"))){
                corpId= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
            }
            else if("wechatAppsecret".equals(cofigmap.get("CODE"))){
                corpSecret= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
            }
            else if("agentId".equals(cofigmap.get("CODE"))){
                agentId= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
            }
            else if("wechatkey".equals(cofigmap.get("CODE"))){
                wechatkey= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
            }
            else if("debugger".equals(cofigmap.get("CODE"))){
                ts= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
            }
        }
        String encode = Base64.encode(wechatkey);

        log.error("corpId:"+corpId+",corpSecret:"+corpSecret+",agentId:"+agentId+",tokenurl:"+tokenurl+",sendurl:"+sendurl+",wechatkey:"+wechatkey);
//        JfWeChatInterServiceImpl enterpriseWeChatInterProxyService = (JfWeChatInterServiceImpl)SpringBeanUtils.getBean(JfWeChatInterServiceImpl.class);
        List<String> userIdsList = new ArrayList<>();
         // FastdwebSqlSession sqlSession=SpringBeanUtils.getBean(FastdwebSqlSession.class);
        for(String UserCode:weChatInterReceiver.getUserCode()){
            userIdsList.add(UserCode);
        }
        String userIds = "";
        if (userIdsList.size() > 0) {
            userIds = userIdsList.stream().collect(Collectors.joining("|"));
        }
        String qwToken = WechatUtilek.getToken();
        if(!StringUtils.isEmpty(qwToken)){
            // 解析 JSON
            org.json.JSONObject jsonObject = null;
            try {
                jsonObject = new org.json.JSONObject(JSONSerializer.serialize(content));
                log.error("jsonObject:"+jsonObject);
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
            String contentWithHtml="";
            try {
                  contentWithHtml = jsonObject.getJSONObject("textContent").getString("content");
                log.error("contentWithHtml:"+contentWithHtml);
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
            if(StringUtils.isEmpty(contentWithHtml)){
                log.error("contentWithHtml为空！");
                return response;
            }
            // 将 HTML 内容解析为 Document
            Document document = Jsoup.parse(contentWithHtml);
            Map<String,Object> aa=new HashMap<>();
            aa.put("content",document.text());
            JSONObject msgBody=new JSONObject();
            msgBody.put("touser",userIds);
            msgBody.put("msgtype","text");
            msgBody.put("agentid",agentId);
            msgBody.put("safe",0);
            msgBody.put("text",aa);
            log.error("发送企微消息报文："+msgBody.toJSONString());
//            log.error("发送企微消息地址："+sendurl+ "?access_token=" +qwToken+ts);
            //发送企微消息
            HttpResponse resmsg= HttpRequest.post(sendurl+ "?access_token=" +qwToken+ts)
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Basic " + encode)
                    .body(msgBody.toJSONString())
                    .timeout(5000)
                    .execute();
            log.error("企微推送消息返回数据2：" + JSONSerializer.serialize(resmsg));
            if(resmsg.getStatus()==200){
                JSONObject resmsgO = JSONObject.parseObject(resmsg.body());
                if(resmsgO.getInteger("errcode").equals(0)){
                    //处理成功

                }else{
                    //微信失败

                }
            }
            else{
                log.error("获取resmsg企微返回数据失败："+resmsg.getStatus());
            }
        }else{
            log.error("获取token为空");
        }
        return response;
    }

}
