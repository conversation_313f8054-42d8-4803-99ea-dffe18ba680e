//package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service.XXQD;
//
//import lombok.extern.slf4j.Slf4j;
//
//@Slf4j
//public class JfWeChatInterServiceImpl {
//
//    private Object lockObject = new Object();
//
//
//
//    public void sendMessage(String corpId, String corpSecret, Long agentId, String userIds, EpWeChatMsgType msgType, WeChatMsgContent weChatMsgContent) {
//        try {
//            EpWeChatInterMessageVO message = EpWeChatInterMessageVO.builder().touser(userIds).msgtype(msgType.toString()).agentid(agentId).textcard(weChatMsgContent.getTextcardContent()).text(weChatMsgContent.getTextContent()).build();
//
//            updateAccess_token(corpId, corpSecret);
//            String result = this.service.sendMessage(access_token.getAccess_token(), message);
//            if (log.isDebugEnabled()) {
//                log.debug("+ result);
//            }
//            SendResultResponse sendResultResponse = (SendResultResponse)JSONSerializer.deserialize(result, SendResultResponse.class);
//            if (sendResultResponse.getErrcode() != 0) {
//                throw new MsgException("msg", "GSP_Svc_Msg_0503", "Failed to send EpWeChatInter message. failure reason=" + sendResultResponse.getErrcode() + ":" + sendResultResponse.getErrmsg(), null, ExceptionLevel.Error, false);
//            }
//            if (sendResultResponse.getInvaliduser() != null && sendResultResponse.getInvaliduser().length() > 0 &&
//                    log.isDebugEnabled()) {
//                log.debug("+ sendResultResponse.getInvaliduser());
//            }
//
//            if (sendResultResponse.getUnlicenseduser() != null && sendResultResponse.getUnlicenseduser().length() > 0 &&
//                    log.isDebugEnabled()) {
//                log.debug("+ sendResultResponse.getUnlicenseduser());
//
//            }
//        }
//        catch (Exception e) {
//            throw new MsgException("msg", "GSP_Svc_Msg_0503", e.getMessage(), e, ExceptionLevel.Error, false);
//        }
//    }
//
//
//    public String getUser(String corpId, String corpSecret, String mobile) {
//        updateAccess_token(corpId, corpSecret);
//        EpWeChatInterUserVO userVO = new EpWeChatInterUserVO();
//        userVO.setMobile(mobile);
//        String result = this.getUserService.getUser(access_token.getAccess_token(), userVO);
//        GetUserResponse userResponse = (GetUserResponse)JSONSerializer.deserialize(result, GetUserResponse.class);
//        String userId = "";
//        if (userResponse.getErrcode() == 0) {
//            userId = userResponse.getUserid();
//        } else {
//            throw new MsgException("msg", "GSP_Svc_Msg_0505", "Failed to obtain EpWeChatInter user information. User=" + mobile + " failure reason=" + userResponse.getErrcode() + ":" + userResponse.getErrmsg(), null, ExceptionLevel.Error, false);
//        }
//        return userId;
//    }
//
//
//
//    private void updateAccess_token(String corpId, String corpSecret) {
//        if (access_token == null) {
//            synchronized (this.lockObject) {
//                if (access_token == null) {
//                    String token = this.tokenService.getToken(corpId, corpSecret);
//                    if (log.isDebugEnabled()) {
//                        log.debug("" + token);
//                    }
//                    access_token = (AccessTokenResponse)JSONSerializer.deserialize(token, AccessTokenResponse.class);
//                    if (access_token.getErrcode() != 0) {
//                        throw new MsgException("msg", "GSP_Svc_Msg_0504", "Failed to obtain EpWeChatInter accessToken. failure reason=" + access_token.getErrcode() + ":" + access_token.getErrmsg(), null, ExceptionLevel.Error, false);
//                    }
//                    access_token.setLastUpdateTimeMillis(System.currentTimeMillis());
//                }
//            }
//        } else {
//
//            synchronized (this.lockObject) {
//                long currentTimeMillis = System.currentTimeMillis();
//                long LastUpdateTimeMillis = access_token.getLastUpdateTimeMillis();
//                if (currentTimeMillis - LastUpdateTimeMillis > 6600000L) {
//                    String token = this.tokenService.getToken(corpId, corpSecret);
//                    if (log.isDebugEnabled()) {
//                        log.debug("" + token);
//                    }
//                    access_token = (AccessTokenResponse)JSONSerializer.deserialize(token, AccessTokenResponse.class);
//                    if (access_token.getErrcode() != 0) {
//                        throw new MsgException("msg", "GSP_Svc_Msg_0504", "Failed to obtain EpWeChatInter accessToken. failure reason=" + access_token.getErrcode() + ":" + access_token.getErrmsg(), null, ExceptionLevel.Error, false);
//                    }
//                    access_token.setLastUpdateTimeMillis(currentTimeMillis);
//                }
//            }
//        }
//    }
//}
//
