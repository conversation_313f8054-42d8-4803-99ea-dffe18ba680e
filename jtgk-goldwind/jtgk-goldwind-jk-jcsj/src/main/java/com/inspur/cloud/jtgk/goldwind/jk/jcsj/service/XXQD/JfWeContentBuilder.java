package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service.XXQD;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.XXQD.JfWeChatMsgContent;
import io.iec.edp.caf.common.JSONSerializer;
import io.iec.edp.caf.message.api.CAFMessage;
import io.iec.edp.caf.message.api.GspMessage;
import io.iec.edp.caf.message.api.IMsgContentBuilder;
import io.iec.edp.caf.message.api.dingtalk.content.TextContent;
import io.iec.edp.caf.message.api.msgcontent.MessageContent;
import io.iec.edp.caf.message.api.qywechat.content.TextcardContent;
import lombok.extern.slf4j.Slf4j;

//内容构造器(ContentBuilder):用于消息实际发送内容的构造、渲染；
@Slf4j
public class JfWeContentBuilder  implements IMsgContentBuilder {
    /**
     * @description 消息发送方式
     * @param message 消息内容
     * @return MessageContent 构造好的消息体
     */
    @Deprecated
    public MessageContent build(GspMessage message) {
        log.error("企微："+ JSONSerializer.serialize(message));
        JfWeChatMsgContent weChatMsgContent=new JfWeChatMsgContent();
        TextcardContent textcardContent = new TextcardContent();
        if (message.getWeChatMsg() != null && message.getWeChatMsg().getTextcardContent() != null && !message.getWeChatMsg().getTextcardContent().getUrl().equals("")) {
            textcardContent = message.getWeChatMsg().getTextcardContent();
            textcardContent.setTitle(message.getSubject());
            textcardContent.setDescription(message.getMsgText());
        }
        weChatMsgContent.setTextcardContent(textcardContent);

        TextContent textContent = new TextContent();
        textContent.setContent(message.getMsgText());
        weChatMsgContent.setTextContent(textContent);
        return weChatMsgContent;
    }
    /**
     * @description 消息发送方式
     * @param message 消息内容
     * @return MessageContent 构造完成的消息体
     */
    public MessageContent build(CAFMessage message) {
        log.error("企微01："+ JSONSerializer.serialize(message));
        JfWeChatMsgContent weChatMsgContent=new JfWeChatMsgContent();
        TextcardContent textcardContent = new TextcardContent();
        if (message.getWeChatMsg() != null && message.getWeChatMsg().getTextcardContent() != null && !message.getWeChatMsg().getTextcardContent().getUrl().equals("")) {
            textcardContent = message.getWeChatMsg().getTextcardContent();
            textcardContent.setTitle(message.getSubject());
            textcardContent.setDescription(message.getMsgText());
        }
        weChatMsgContent.setTextcardContent(textcardContent);

        TextContent textContent = new TextContent();
        textContent.setContent(message.getMsgText());
        weChatMsgContent.setTextContent(textContent);
        return weChatMsgContent;
    }
}
