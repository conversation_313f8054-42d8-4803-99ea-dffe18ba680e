package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service.XXQD;

import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.XXQD.JfWeChatInterReceiver;
import com.inspur.fastdweb.core.FastdwebSqlSession;
import com.inspur.fastdweb.util.StringUtil;
import io.iec.edp.caf.common.JSONSerializer;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.message.api.GspMessage;
import io.iec.edp.caf.message.api.IMsgUserMapper;
import io.iec.edp.caf.message.api.receiver.MessageReceiver;
import io.iec.edp.caf.sysmanager.api.data.user.UserExtend;
import io.iec.edp.caf.sysmanager.api.manager.user.UserExtendManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//用户映射器(UserMapper):用于系统用户ID与实际发送所需用户参数的映射；
@Slf4j
public class JfWeUserMapper implements IMsgUserMapper {
//    @Autowired
//    private FastdwebSqlSession sqlSession;
    @Override
    public List<MessageReceiver> map(GspMessage message, String sendType) {
        log.error("企微消息message："+message+",SENDTYPE:"+sendType);
        List<MessageReceiver> list = new ArrayList<>();
        //转调另外一个map方法
        list = map(message.getSender(), message.getReceivers(), message.getCcReceivers(), message.getBccReceivers(), message.getParameters(), sendType);
        log.error("企微消息list:"+ JSONSerializer.serialize(list));
        return list;
    }
    @Override
    public List<MessageReceiver> map(String sender, List<String> receiverList, List<String> ccReceivers, List<String> bccReceivers, HashMap<String,Object> parameters, String sendType) {
        List<MessageReceiver> receivers=  new ArrayList<>() ;
        JfWeChatInterReceiver jfWeChatInterReceiver = new JfWeChatInterReceiver();
        //根据传参映射接收人的真正账号
        //比如，根据传入接受人的系统用户usercode（receivers），映射为接收人短信接收的工号列表,这些参数中，大部分只需要receivers即可
        log.error("企业微信入参receiverList:"+ JSONSerializer.serialize(receiverList));
        log.error("企微消息sender："+sender+",SENDTYPE:"+sendType+",parameters:"+JSONSerializer.serialize(parameters));
        FastdwebSqlSession sqlSession=SpringBeanUtils.getBean(FastdwebSqlSession.class);
        List<String> usercodelist=new ArrayList<>();
        for (String item : receiverList) {
            try {
                Map<String, Object> maps = new HashMap<>();
                maps.put("userid", item);
                Map<String, Object> usercode = sqlSession.selectOne(Map.class, "SELECT code FROM GSPUSER WHERE id = #{userid}", maps);
                log.error("企业微信usercode:" + JSONSerializer.serialize(usercode));
                if (usercode != null && !usercode.isEmpty()) {
                    usercodelist.add(!ObjectUtils.isEmpty(usercode.get("code")) ? usercode.get("code").toString() : (!ObjectUtils.isEmpty(usercode.get("CODE")) ? usercode.get("CODE").toString() : item));
                }
            }catch (Throwable e) {
                e.printStackTrace();
                usercodelist.add(item);
            }
        }
        log.error("企业微信usercode:"+ JSONSerializer.serialize(usercodelist));
        if(usercodelist!=null&&usercodelist.size()>0){
            jfWeChatInterReceiver.setUsercode(usercodelist);
        }
        receivers.add(jfWeChatInterReceiver);
        log.error("企微消息工号列表list:"+ JSONSerializer.serialize(receivers));
        return receivers;
    }
}
