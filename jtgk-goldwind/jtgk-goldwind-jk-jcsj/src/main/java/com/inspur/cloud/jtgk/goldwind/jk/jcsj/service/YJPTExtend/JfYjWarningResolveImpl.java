package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service.YJPTExtend;


import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.JSONSerializable;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub.WechatUtilek;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.service.JtgkEnvironmentService;
import com.inspur.edp.svc.earlywarning.api.entity.GspWarningSub;
import com.inspur.edp.svc.earlywarning.api.spi.IWarningDataProvider;
import com.inspur.fastdweb.core.FastdwebSqlSession;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.common.JSONSerializer;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.TimeUnit;

//IWarningReceiverProvider
@Slf4j
public class JfYjWarningResolveImpl implements IWarningDataProvider{
    /*需预制sql
         insert into gspwarningexmapping(id,Defid,Extendcode,extendid,extendtype,serviceunit,Subid)
        values('ZHYQYJ', 'ZHYQYJ', 'ZHYQYJ', 'ZHYQYJ', 0, 'goldwind','' );--TODO:

         insert into gspwarningextend (ID, CODE, DESCRIBE, NAME, SERVICEUNIT, TYPE)
        values ('ZHYQYJ', 'ZHYQYJ', '账户久悬户提醒', '账户久悬户提醒', 'goldwind', 0);
        type: 预警收件人的拓展，请填写1。

        select * from gspwarningsub

        Id 为不会重复的字符串即可；
        Defid 为预警定义的id，非必填；
        Extendcode、extendid、extendtype、serviceunit 分别对应gspwarningextend中的
        code、id、type、serviceunit；
        Subid为需要配置的预警订阅的预警订阅id，可从gspwarningsub中获取。
    * */
//    @Autowired
//    private LogService logService;

//    @Autowired
//    private FastdwebSqlSession sqlSession;

    private final String identity = "ZHYQYJ";

    /**
     *
     * @return 最终的消息数据
     */
    @Override
    public void solveWarningData(GspWarningSub gspWarningSub, Map map) {
        String name="预警提醒";
        log.error("solveWarningData进入:");
        try{
            log.error("solveWarningData进入:");
            String mapString = JSONObject.toJSONString(map);
            log.error("平台入参map:" + mapString);

            JSONObject yjxxOb = JSONObject.parseObject(mapString);
            JSONObject dataOb = yjxxOb.getJSONObject("data");
            JSONArray dataArray = dataOb.getJSONArray("data");
            int zs = dataArray.size();
            //预警订阅中使用用户变量 --关联出的行政人员编号
            JSONArray userArray = dataOb.getJSONArray("sysUsers") ;
            String wxuserid="";String sqbh="";String sqbhs="";String sqlx="";

           // sqbh = sqbh.substring(0, sqbh.length() - 1);
            FastdwebSqlSession sqlSession=SpringBeanUtils.getBean(FastdwebSqlSession.class);
            for (Object item : userArray) {
                sqbh="";wxuserid="";sqbhs="";sqlx="";
                Map<String, Object> maps = new HashMap<>();
                maps.put("ID", item.toString());
                String ygbh = sqlSession.selectOne(String.class, " select bfemployee.code from bfemployee join bfEmployeeSysUser on bfEmployeeSysUser.parentid=bfemployee.id " +
                        " join gspuser on gspuser.ID=bfEmployeeSysUser.sysuser where gspuser.ID =#{ID} ", maps);
                wxuserid = ygbh ;
                for(int i =0;i<zs;i++) {
                    JSONObject object = (JSONObject) dataArray.get(i);
                    if(item.toString().equals(object.getString("applicantid"))) {
                        sqbh+=object.getString("sqbh")+",";
                    }
                    sqbhs+=object.getString("sqbh")+",";
                    sqlx=object.getString("sqlx");
                }
                if(sqbh.length()>0){
                     sqbh = sqbh.substring(0, sqbh.length() - 1);
                }
                else {
                    sqbh = sqbhs.substring(0, sqbhs.length() - 1);
                }
                //wxuserid = wxuserid.substring(0, wxuserid.length() - 1);
//                JtgkEnvironmentService jtgkEnvironmentService = SpringBeanUtils.getBean(JtgkEnvironmentService.class);
//                String sendurl = jtgkEnvironmentService.GetCacheValue("wechatSendUrl", 1, TimeUnit.DAYS);
//                String wechatkey = jtgkEnvironmentService.GetCacheValue("wechatkey", 1, TimeUnit.DAYS);
//                String agentid = jtgkEnvironmentService.GetCacheValue("agentid", 1, TimeUnit.DAYS);
//                String sqlxmc = jtgkEnvironmentService.GetCacheValue(sqlx, 1, TimeUnit.DAYS);
//                String ts=jtgkEnvironmentService.GetCacheValue("debugger", 1, TimeUnit.DAYS);
                //        JtgkEnvironmentService jtgkEnvironmentService = SpringBeanUtils.getBean(JtgkEnvironmentService.class);
                String sqlxmc="";//jtgkEnvironmentService.GetCacheValue("wechatUrl", 1, TimeUnit.DAYS);
                String sendurl="";//jtgkEnvironmentService.GetCacheValue("wechatSendUrl", 1, TimeUnit.DAYS);
                String corpId="";//jtgkEnvironmentService.GetCacheValue("wechatAppid", 1, TimeUnit.DAYS);
                String corpSecret ="";//jtgkEnvironmentService.GetCacheValue("wechatAppsecret", 1, TimeUnit.DAYS);
                String agentid ="";//jtgkEnvironmentService.GetCacheValue("agentId", 1, TimeUnit.DAYS);
                String wechatkey="";//jtgkEnvironmentService.GetCacheValue("wechatkey", 1, TimeUnit.DAYS);
                String ts="";//jtgkEnvironmentService.GetCacheValue("debugger", 1, TimeUnit.DAYS);
                List<Map> cofigList =sqlSession.selectList(Map.class,"select ISENABLE , KEYVALUE,CODE from JTGKINTERFACECONFIG  " +
                        "where CODE IN ('wechatUrl','wechatSendUrl','wechatAppid','wechatAppsecret','agentId','wechatkey','debugger','"+sqlx+"')  AND ISENABLE='1' ") ;
                for(Map cofigmap:cofigList){
                    if(sqlx.equals(cofigmap.get("CODE"))){
                        sqlxmc= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
                    }
                    else if("wechatSendUrl".equals(cofigmap.get("CODE"))){
                        sendurl= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
                    }
                    else if("wechatAppid".equals(cofigmap.get("CODE"))){
                        corpId= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
                    }
                    else if("wechatAppsecret".equals(cofigmap.get("CODE"))){
                        corpSecret= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
                    }
                    else if("agentId".equals(cofigmap.get("CODE"))){
                        agentid= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
                    }
                    else if("wechatkey".equals(cofigmap.get("CODE"))){
                        wechatkey= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
                    }
                    else if("debugger".equals(cofigmap.get("CODE"))){
                        ts= !ObjectUtils.isEmpty(cofigmap.get("KEYVALUE"))? cofigmap.get("KEYVALUE").toString():"";
                    }
                }
                String encode = Base64.encode(wechatkey);
                String qwToken = WechatUtilek.getToken();
                if (!StringUtils.isEmpty(qwToken)) {
                    JSONObject msgBody = new JSONObject();
                    msgBody.put("touser", wxuserid);
                    msgBody.put("msgtype", "text");
                    msgBody.put("agentid", agentid);
                    JSONObject content = new JSONObject();
//                content.put("content",txtmsg+"<a href=\""+skip+"/api/jtgk/coscoshipping/sso/appwf?taskParam="+workItem.getId()+"&userInfo="+encodeStr+"\">点击查看详情</a>");
                    content.put("content", sqlxmc.replace("{}",sqbh));
                    msgBody.put("text", content);
                    log.error("发送企微消息报文：" + msgBody.toJSONString());
                    //发送企微消息
                    HttpResponse resmsg = HttpRequest.post(sendurl + "?access_token=" + qwToken+ts)
                            .header("Content-Type", "application/json")
                            .header("Authorization", "Basic " + encode)
                            .body(msgBody.toJSONString())
                            .timeout(5000)
                            .execute();
                    log.error("企微推送消息返回数据1：" + JSONSerializer.serialize(resmsg));
                    if (resmsg.getStatus() == 200) {
                        JSONObject resmsgO = JSONObject.parseObject(resmsg.body());
                        if (resmsgO.getInteger("errcode").equals(0)) {
                            //处理成功

                        } else {
                            //微信失败

                        }
                    } else {
                        log.error("获取resmsg企微返回数据失败：" + resmsg.getStatus());
                    }
                } else {
                    log.error("获取token为空");
                }
            }

        }catch (Exception ex){
            log.error("预警定义扩展异常：",ex);
        }

    }

    /**
     * 获得标识的函数getIdentity
     * 标识 是自定义拓展的 唯一标识
     * 与自定义拓展一一对应且 不可重复 ，存储传输均用 string 字符串
     * @return
     */
    @Override
    public String getIdentity() {
        return identity;
    }
}
