//package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service.YJPTExtend;
//
//
//import cn.hutool.core.codec.Base64;
//import cn.hutool.http.HttpRequest;
//import cn.hutool.http.HttpResponse;
//import com.alibaba.fastjson.JSONObject;
//import com.inspur.edp.svc.earlywarning.api.entity.GspWarningSub;
//import com.inspur.edp.svc.earlywarning.api.entity.UserInfo;
//import com.inspur.edp.svc.earlywarning.api.spi.IWarningReceiverProvider;
//import com.inspur.edp.svc.earlywarning.api.stucture.MsgDataWrapper;
//import com.inspur.fastdweb.core.FastdwebSqlSession;
//import com.inspur.idd.log.api.controller.LogService;
//import org.apache.commons.codec.digest.DigestUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.util.CollectionUtils;
//
//import java.util.*;
//
//public class JfYjWarningServiceImpl implements IWarningReceiverProvider {
//
//    @Autowired
//    private LogService logService;
//
//    @Autowired
//    private FastdwebSqlSession sqlSession;
//    private final String identity = "ZHYQYJ";
//
//    /**
//     * @param rawData 原始数据
//     * @param userInfo 用户信息
//     * @param sub 预警订阅
//     * @param dataMap 后续提供更多扩展用的map
//     * @return 最终的消息数据
//     */
//
//    @Override
//    public MsgDataWrapper receiverSolve(MsgDataWrapper rawData, UserInfo userInfo, GspWarningSub sub, Map dataMap) {
//        String name="企微预警";
//        MsgDataWrapper result = new MsgDataWrapper();
//        logService.init(name);
//        try{
//            logService.error(name,"开始");
//            //#region
//            List<Map> dataList = (List) rawData.getData();
//            result.setData(dataList);
//            //数据源的名称，一般不做任何改变
//            result.setMasterName(rawData.getMasterName());
//
//            //获取预警订阅的描述，可以在描述中设置一些用于自身识别的东西
//            //String descrb = sub.getDescription();
//
//            //设置为false将视为无数据不再发送预警
//            result.setHasData(true);
//
//            //这个是在前端选取的系统用户
//            List<String> users = userInfo.getUsers();
//
//            //这个是在前端选取的用户变量，用户变量是设置数据分组的主要依据
//            String variable = userInfo.getUserVariable();
//
//            //当在勾选了只发送自身数据时，需要设置groupData
//            if(sub.isSelfData()){
//
//                //groupData的key和value分别是收件人的id和收件人的数据(Object存list类型)
//                Map<String,Object> groupData = new HashMap<>();
//
//                //遍历获取用户变量对应值的一种方式
//                for(Map<String,Object> map:dataList){
//                    //用户变量对应的值可以不是用户id，取决于自己想做什么
//                    String userPlus = map.get(variable).toString();
//                    if(!users.contains(userPlus)) users.add(userPlus);
//                }
//
//                //实现自己的逻辑
//                for(String user:users){
//                    List list = new ArrayList();
//                    for(Map<String,Object> map:dataList){
//                        if(!map.get(variable).equals(user)) list.add(map);
//                    }
//                    groupData.put(user,list);
//                }
//                //这里设置最终的收件人
//                result.setSysUsers(users);
//
//                //这里设置每个收件人对应的数据
//                result.setGroupData(groupData);
//            }
//            logService.error(name,"groupData：");
//            //#endregion
//            //#region 企微消息推送
//            Map<String,Object> parameterMap = new HashMap<String,Object>();
//            parameterMap.put("id",userInfo.getUsers());
//            List<Map>  userList = sqlSession.selectList(Map.class,"select code from gspuser where id=#{id}",parameterMap);
//            if(CollectionUtils.isEmpty(userList)){
//                logService.error(name,"*****当前用户【"+userInfo.getUserNames()+"】不是司库企微用户，不发送消息*****");
//                return null;
//            }
//            String wxuserid=userList.get(0).get("code").toString();
//            String gscode=userList.get(0).get("code").toString();
//                JSONObject msgBody=new JSONObject();
//                msgBody.put("touser",wxuserid);
//                msgBody.put("msgtype","text");
//               // msgBody.put("agentid","1000475");//TODO:
//                JSONObject content =new JSONObject();
//                String data = gscode+"_" + new Date().getTime();
//                String md5 = DigestUtils.md5Hex(data);
//                data = md5+"_"+data;
//                //URLEncoder并加密
//                String encodeStr = "";
//                String txtmsg = "司库消息预警：请检查"+result;
////                try{
////                    //encodeStr = URLEncoder.encode(DesUtil.encrypt(data,jtgkEnvironmentService.GetCacheValue("OASSOPWD",1, TimeUnit.DAYS)), "utf-8");TODO:
////                }catch (Exception e) {
////                    logService.error(name,"加密异常",e);
////                   // DBUtil.execute("insert into JTGKWECHATMSGLOG(id,WxUserid,GSCode,txtmsg,state)values('"+workItem.getId()+"','"+wxuserid+"','"+gscode+"','"+txtmsg+"',0)");
////                    return null;
////                }
//                content.put("content",txtmsg);//+"<a href=\""+skip+"/api/jtgk/coscoshipping/sso/appwf?taskParam="+workItem.getId()+"&userInfo="+encodeStr+"\">点击查看详情</a>
//
//
//                msgBody.put("text",content);
//                logService.error(name,"发送企微消息报文："+msgBody.toJSONString());
//                Map<String,Object> map = sqlSession.selectOne(Map.class, "SELECT KEYVALUE,URL FROM JTGKINTERFACECONFIG WHERE CODE = 'QYWX'");
//                String url=String.valueOf(map.get("URL"));//更新日期，不传此参数，则获取全量数据   测试环境先用这个  std/123456        scc/123456   geam/123456
//                String encode = Base64.encode(String.valueOf(map.get("KEYVALUE")));
//                logService.info( name, "map:"+map);
//                logService.info( name, "encode:"+encode);
//                //发送企微消息
//                HttpResponse resmsg = HttpRequest.post(url).header("Content-Type", "application/json").header("Authorization", "Basic " + encode).body(msgBody.toJSONString()
//                        ).timeout(50000)
//                        .execute();
//                if(resmsg.getStatus()==200){
//                    logService.error(name,"企微推送消息返回数据："+resmsg.body());
//                    JSONObject resmsgO = JSONObject.parseObject(resmsg.body());
//                    if(resmsgO.getInteger("errcode").equals(0)){
//                        logService.error(name,"企微推送消息返回数据成功");
//                        //处理成功
//                       // DBUtil.execute("insert into JTGKWECHATMSGLOG(id,WxUserid,GSCode,txtmsg,state,QWMSGID)values('"+workItem.getId()+"','"+wxuserid+"','"+gscode+"','"+txtmsg+"',1,'"+resmsgO.get("msgid").toString()+"')");
//                    }else{
//                        //微信失败
//                        //DBUtil.execute("insert into JTGKWECHATMSGLOG(id,WxUserid,GSCode,txtmsg,state)values('"+workItem.getId()+"','"+wxuserid+"','"+gscode+"','"+txtmsg+"',-1)");
//                    }
//                }
//                else{
//                    logService.error(name,"获取resmsg企微返回数据失败："+resmsg.getStatus());
//                }
//
//
//            //#endregion
//
//        }catch (Exception ex){
//            logService.error(name,"预警定义扩展异常："+ex.getMessage()+ex.getStackTrace());
//        }finally {
//            logService.flush();
//            //返回的result将用于数据的发送
//            return result;
//        }
//    }
//
//    /**
//     * 获得标识的函数getIdentity
//     * 标识 是自定义拓展的 唯一标识
//     * 与自定义拓展一一对应且 不可重复 ，存储传输均用 string 字符串
//     * @return
//     */
//    @Override
//    public String getIdentity() {
//        return identity;
//    }
//}
