package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.*;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository.*;
import com.inspur.edp.data.connectors.exec.ExectorFactory;
import com.inspur.edp.data.connectors.exec.RelationalDbExector;
import com.inspur.fastdweb.core.FastdwebSqlSession;
import com.inspur.fastdweb.util.StringUtil;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.common.JSONSerializer;
import io.iec.edp.caf.commons.utils.CollectionUtils;
import io.iec.edp.caf.rpc.api.service.RpcClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.swing.tree.RowMapper;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public class automaticProcesService {
    @Autowired
    private RpcClient rpcClient;
    @Autowired
    private FastdwebSqlSession sqlSession;
    @Autowired
    private EmployeeResultObjectRepository employeeResultObjectRepository;
    @Autowired
    private LogService logService;
    @Autowired
    private JTGKjrcktzresultRepository jtgkjrcktzresultRepository;
    @Autowired
    private JTGKBZHLRESULTRepository jtgkbzhlresultRepository;

    @Autowired
    private JTGKCbzxResultEntityReposiory jtgkCbzxResultEntityReposiory;
    @Autowired
    private JTGKCbzxgsgxresultRepository jtgkCbzxgsgxresultRepository;
    @Autowired
    private JTGKLrzxResultRepository jtgkLrzxResultRepository;
    @Autowired
    private EmployeeQlResultObjectRepository employeeQlResultObjectRepository;
    @Autowired
    private EmployeebcResultRepository employeebcResultRepository;

    @Autowired
    private JTGKCkzhyebResultRepository jtgkCkzhyebResultRepository;
    @Autowired
    private SFBBResultObjectRepository sfbbResultObjectRepository;

    @Autowired
    private JTGKKSZSJResultRepository jtgkkszsjResultRepository;
    @Autowired
    private JTGKNBDDResultRepository jtgknbddResultRepository;
    @Autowired
    private JTGKWBSxmResultRepository jtgkWBSxmResultRepository;

    /*
    获取往来单位 员工支付信息 CA人员信息 二开中间表 同步司库   增量数据
     */
    public void automaticGenerationPartner_yg()
    {
        String name="automaticGenerationPartner_yg";//获取SAP往来单位同步司库";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationPartner_yg");
            List<EmployeeResultObject> resultList = employeeResultObjectRepository.findTop1000ByYgstatus("0");
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }
//            logService.info( name, "resultList:{}", JSONSerializer.serialize(resultList));
           // String nbdw =sqlSession.selectOne(String.class,"select ID  FROM  BFPARTNERTYPE where code= '01'");//内部单位
            String wbdw =sqlSession.selectOne(String.class,"select ID  FROM  BFPARTNERTYPE where code= '0201'");//外部单位--个人
            for(EmployeeResultObject result:resultList) {
                try {
                    LinkedHashMap<String, Object> orgparam = new LinkedHashMap<String, Object>();
                    Map<String, Object> hashMap = new HashMap<>();
                    Map<String, Object> mapsb = new HashMap<>();
                    mapsb.put("code", result.getPernr());
                    Map<String,Object> myhb = sqlSession.selectOne(Map.class, "select ID,state_isenabled  from  BFPARTNER  where code= #{code}", mapsb);
                    //若往来单位中不存在，则为新增
                    if(myhb!=null&&myhb.size()>0){
                        if("D".equals(result.getFlag()))
                        {
                            if("0".equals(ObjectUtils.isNotEmpty(myhb.get("state_isenabled"))?myhb.get("state_isenabled").toString()
                                    :(ObjectUtils.isNotEmpty(myhb.get("STATE_ISENABLED"))?myhb.get("STATE_ISENABLED").toString():"") ))
                            {//删除并且已停用不处理
                                continue;
                            }
                            result.setFlag("D");
                        }
                        else {
                            if("0".equals(ObjectUtils.isNotEmpty(myhb.get("state_isenabled"))?myhb.get("state_isenabled").toString()
                                    :(ObjectUtils.isNotEmpty(myhb.get("STATE_ISENABLED"))?myhb.get("STATE_ISENABLED").toString():"") ))
                            {//不删除并且是停用状态  则启用
                                result.setFlag("QY");
                            }
                            result.setFlag("M");
                        }
                    }else{
                        if("D".equals(result.getFlag())){
                            continue;
                        }
                        result.setFlag("A");
                    }

                    hashMap.put("operation", "A".equals(result.getFlag()) ? "add" : ("M".equals(result.getFlag()) ? "modify" : ("QY".equals(result.getFlag()) ? "enable" : "disable")));//操作类型
                    Map<String, Object> orgData = new HashMap<>();
                    Map<String, Object> mapsa = new HashMap<>();
                    mapsa.put("code", result.getPernr());
                    String YHID = sqlSession.selectOne(String.class, "select ID from  BFPARTNER  where code= #{code}", mapsa);
                    orgData.put("ID", YHID);
                    orgData.put("CODE", result.getPernr());//编号
                    orgData.put("NAME", result.getEname());//名称
                    orgData.put("TYPE", wbdw);//往来单位类别  -个人
                    orgData.put("BILLSTATUS","2");// "审批状态，0制单、1审批中、2通过、3驳回",
                    Map<String, Object> maps = new HashMap<>();
                    maps.put("name", result.getZhrCity());
                    String dqid=sqlSession.selectOne(String.class,"select id from BFPARTNERAREA where name_chs=#{name}  ORDER BY code  LIMIT 1",maps);
                    orgData.put("AREA",dqid); // 往来单位地区，引用往来单位地区
//                orgData.put("DOMAINTYPE",""); // 使用范围，新增默认公有，0公有1组织私有2组织范围，若为1则所属行政组织必填，若为2则所属组织范围必填
//                orgData.put("OWNERORG",""); // 所属行政组织ID，引用行政组织
//                orgData.put("OWNERDOMAIN",result.getBNKLZ()); // 所属组织范围id，引用组织范围
                    maps = new HashMap<>();
                    maps.put("code", result.getLandx50());
                    String gjid=sqlSession.selectOne(String.class,"select id from BFNATIONALANDREGIONALDICT where TWOCHARCODE= #{code}  ORDER BY code  LIMIT 1 ",maps);
                    orgData.put("COUNTRYORREGION",gjid); // 国家或地区，引用国家地区字典
//                orgData.put("DEFAULTCURRENCYID",""); // 默认交易币种，引用币种
//                orgData.put("SUPERIORPARTNER",""); // 上级往来单位，引用往来单位
//                orgData.put("ACTUALCONTROLLERPARTNER",""); // 实际控制单位，引用往来单位
//                orgData.put("INDUSTRY",""); // 行业，引用标准代码
//                orgData.put("SHORTNAME",result.getBANKS()); // 简称
//                orgData.put("UNITNATURE",result.getPROVZ()); // 单位性质，引用标准代码单位性质
//                orgData.put("PROVINCE",""); // 省
//                orgData.put("CITY",dqid); // 市
//                orgData.put("REMARK", ""); // 备注
                    hashMap.put("data", orgData);
                    orgparam.put("param", hashMap);//hashmap内容为所需参数
                    String su = "df";
                    String serviceID = "com.inspur.gs.bf.df.commonservice.api.IPartnerService.synchronous";
                    logService.info(name, "automaticGenerationPartner_yg-产品入参：" + JSONSerializer.serialize(orgparam));
                    HashMap results = rpcClient.invoke(HashMap.class, serviceID, su, orgparam, null);
                    logService.info(name, "automaticGenerationPartner_yg-产品返回：" + results);
                    if ("success".equals(String.valueOf(results.get("status")))) {
                        log.info("创建成功");
                        result.setYgstatus("1");
                        result.setYgmsg("");
                        employeeResultObjectRepository.save(result);
                    } else {
                        result.setYgstatus("2");
                        result.setYgmsg(String.valueOf(results.get("message")));
                        employeeResultObjectRepository.save(result);
                    }
                }catch (Throwable ex){
                    logService.error( name, "同步个人往来单位异常：",ex);
                }

            }

        }catch (Throwable ex){
            logService.error( name, "同步个人往来单位异常",ex);
        }finally {
            // logService.info( name, "结束：");
            logService.flush();
        }
    }

    /*
    获取往来单位银行账号 员工支付信息 CA人员信息  二开中间表同步司库 增量数据
     */
    public void automaticGenerationPartner_yhzh()
    {
        String name="automaticGenerationPartner_yhzh";//获取SAP往来单位银行账号同步司库";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationPartner_yhzh");
            List<EmployeeResultObject> resultList = employeeResultObjectRepository.findTop1000ByYgzhstatusAndYgstatus("0","1");
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }
//            logService.info( name, "resultList:{}",JSONSerializer.serialize(resultList));
            for(EmployeeResultObject result:resultList) {
                try {
                    LinkedHashMap<String, Object> orgparam = new LinkedHashMap<String, Object>();
                    Map<String, Object> hashMap = new HashMap<>();
                    Map<String, Object> mapsa = new HashMap<>();
                    mapsa.put("code", result.getZhrAccount());
                    String myhb = sqlSession.selectOne(String.class, "select ID from  BFPARTNERBANKACCOUNTS  where ACCOUNTCODE= #{code}", mapsa);
                    //若往来单位中不存在，则为新增
                    if (StringUtil.isNullOrEmpty(myhb)) {
                        if("D".equals(result.getFlag())){ continue; }
                        result.setFlag("A");
                    }else if("D".equals(result.getFlag())){result.setFlag("D"); }
                    else {
                        result.setFlag("M");
                    }
                    hashMap.put("operation", "A".equals(result.getFlag()) ? "add" : ("M".equals(result.getFlag()) ? "modify" : "modify"));//操作类型
                    Map<String, Object> orgData = new HashMap<>();
                    orgData.put("ID", myhb);
                    orgData.put("ACCOUNTCODE", result.getZhrAccount());//银行编号
                    orgData.put("ACCOUNTNAME", result.getEname());//账户名称  账户持有人
                    Map<String, Object> mapss = new HashMap<>();
                    mapss.put("wldwbh", result.getPernr());
                    mapss.put("khhlhh", result.getZzLhh());
                    mapss.put("khhmc", result.getZzKhh());
                    String WLDWID = sqlSession.selectOne(String.class, "select ID from  BFPARTNER  where code= #{wldwbh}", mapss);
                    orgData.put("PARTNERID", WLDWID); // 往来单位ID，引用往来单位  TODO:
                    String khhid = sqlSession.selectOne(String.class, "select ID from  BFBANK  where BANKIDENTIFIER= #{khhlhh} " +
                            " and name_chs= #{khhmc} ", mapss);
                    orgData.put("INBANK", khhid); // 所属银行ID，引用银行定义
                    orgData.put("ACCOUNTSTATE", ("D".equals(result.getFlag()) ? "1" : "0")); // 账号状态，0,正常;1,冻结;2,其他，默认为0
                    orgData.put("PRIVATEORPUBLIC","2"); // 账号性质，1,对公账号;2,对私账号，默认为1  TODO: 付款不在司库发起，无影响;
                    Map<String,Object> maps = new HashMap<>();
                    maps.put("code", result.getLandx50());
                    String gjid=sqlSession.selectOne(String.class,"select id from BFNATIONALANDREGIONALDICT where TWOCHARCODE= #{code}  ORDER BY code  LIMIT 1 ",maps);
                    orgData.put("COUNTRY",gjid); // 国家地区ID，引用国家地区字典
//                orgData.put("PROVINCE",result.getSWIFT()); // 省份，取行政区划省份名称
//                orgData.put("CITY",""); //  城市，取行政区划城市名称
//                orgData.put("CURRENCYLIST",""); // 币种ID，引用币种定义字典
//                  orgData.put("ISMAIN","1"); // 是否默认账户，0,否;1,是，默认为0
//                orgData.put("ISMAINFORINVOICING",""); // 是否默认开票账号，0,否;1,是，默认为0
//                orgData.put("ISPUBLIC",result.getBANKS()); // 是否公共，0,否;1,是，默认为1
//                orgData.put("REMARK",result.getPROVZ()); // 备注
//                orgData.put("PLAINTEXT1",""); // 扩展字段
//                orgData.put("PLAINTEXT2",result.getORT01()); // 扩展字段
//                orgData.put("PLAINTEXT3", ""); // 扩展字段
                    hashMap.put("data", orgData);
                    orgparam.put("param", hashMap);//hashmap内容为所需参数
                    String su = "df";
                    String serviceID = "com.inspur.gs.bf.df.commonservice.api.IPartnerBankAccountService.synchronous";
                    logService.info(name, "产品入参：" + JSONSerializer.serialize(orgparam));
                    HashMap results = rpcClient.invoke(HashMap.class, serviceID, su, orgparam, null);
                    logService.info(name, "产品返回：" + results);
                    if ("success".equals(String.valueOf(results.get("status")))) {
                        result.setYgzhstatus("1");
                        result.setYgzhmsg("");
                        employeeResultObjectRepository.save(result);
                    } else {
                        result.setYgzhstatus("2");
                        result.setYgzhmsg(String.valueOf(results.get("message")));
                        employeeResultObjectRepository.save(result);
                    }
                }catch (Throwable ex){
                    logService.error( name, "同步员工支付信息异常：",ex);
                }
            }

        }catch (Throwable ex){
            logService.error( name, "同步员工支付信息异常",ex);
        }finally {
            //logService.info( name, "结束：");
            logService.flush();
        }
    }

    /*
       获取行政人员 CA 全量人员与增量人员差异 获取增量表中缺失得人员信息，同步二开表
        */
    public void automaticEmployees_bc()
    {
        String name="automaticEmployees_bc";//获取CA行政人员";
        logService.init(name);
        try{
            logService.info( name, "automaticEmployees_bc");
            List<EmployeeQlResultObject> resultLists = employeeQlResultObjectRepository.findbyEmployeelist();
            if (CollectionUtils.isEmpty(resultLists)) {
                logService.info(name,"无数据");
                return;
            }
            Map<String,Object> map = sqlSession.selectOne(Map.class, "SELECT KEYVALUE,URL,cxdate FROM JTGKINTERFACECONFIG WHERE CODE = 'CAZDRY'");
            //#region
            String url=String.valueOf(map.get("URL"));//更新日期，不传此参数，则获取全量数据    scc/123456   geam/123456
//            String s = "pitims:ACvpfiw2A9GgB-HNwPfY";
            String encode = Base64.encode(String.valueOf(map.get("KEYVALUE")));
            logService.info( name, "map:"+map);
            logService.info( name, "encode:"+encode);
            List<EmployeebcResult> resultList=new ArrayList<>();
            EmployeebcResult result = null;
            Map<String, Object> maps = new HashMap<>();
            for(EmployeeQlResultObject eo:resultLists) {
                resultList = new ArrayList<>();
                result = null;
                maps = new HashMap<>();
                maps.put("param", eo.getUserId());//页码  (必填)
                String mapls = JSONSerializer.serialize(maps);
                HttpResponse response = HttpRequest.post(url).header("Content-Type", "application/json").header("Authorization", "Basic " + encode).body(mapls).timeout(50000)
                        .execute();
                logService.info(name, "response:" + response);
                if (response.getStatus() == 200) {
                    JSONObject resultJson = JSONObject.parseObject(response.body());
                    logService.info(name, "CA人员接口状态值:{}", response.getStatus());
                    if (resultJson != null && "000".equals(resultJson.getString("code")) && !"[]".equals(resultJson.getString("employees"))) {
                        resultList = JSON.parseArray(resultJson.getString("employees"), EmployeebcResult.class);
                    } else {
                        continue;
                    }
                    if (!CollectionUtils.isEmpty(resultList) && resultList.size() > 0) {
                        //存入二开中间表
                        for (EmployeebcResult item : resultList) {
                            try {
                                boolean infoChange = false;
                                //存入二开中间表，根据code  实时更新中间表
                                EmployeebcResult bmResultObject = new EmployeebcResult(item);
                                EmployeebcResult infoInDB = employeebcResultRepository.findByUserId(item.getUserId());//查询是否有相同编号的
                                if (infoInDB == null) {
                                    infoChange = true;
                                    bmResultObject.setFlag("D".equals(bmResultObject.getFlag()) ? "D" : "A");//A新增，M修改，D删除
                                } else {
                                    if (Objects.equals(infoInDB.getUserId(),bmResultObject.getUserId())&&
                                            Objects.equals(infoInDB.getUserName(),bmResultObject.getUserName())&&
                                            Objects.equals(infoInDB.getUnitId(),bmResultObject.getUnitId())&&
                                            Objects.equals(infoInDB.getUnitTxt(),bmResultObject.getUnitTxt())&&
                                            Objects.equals(infoInDB.getSystemId(),bmResultObject.getSystemId())&&
                                            Objects.equals(infoInDB.getSystemTxt(),bmResultObject.getSystemTxt())&&
                                            Objects.equals(infoInDB.getSystemId(),bmResultObject.getSystemId())&&
                                            Objects.equals(infoInDB.getSystemTxt(),bmResultObject.getSystemTxt())&&
                                            Objects.equals(infoInDB.getStell(),bmResultObject.getStell())&&
                                            Objects.equals(infoInDB.getStext(),bmResultObject.getStext())&&
                                            Objects.equals(infoInDB.getDeptId(),bmResultObject.getDeptId())&&
                                            Objects.equals(infoInDB.getDeptName(),bmResultObject.getDeptName())&&
                                            Objects.equals(infoInDB.getZhrOtext(),bmResultObject.getZhrOtext())&&
                                            Objects.equals(infoInDB.getPhoneNumber(),bmResultObject.getPhoneNumber())&&
                                            Objects.equals(infoInDB.getEmail(),bmResultObject.getEmail())&&
                                            Objects.equals(infoInDB.getCenterId(),bmResultObject.getCenterId())&&
                                            Objects.equals(infoInDB.getCenterTxt(),bmResultObject.getCenterTxt())&&
                                            Objects.equals(infoInDB.getZhrTime1(),bmResultObject.getZhrTime1())&&
                                            Objects.equals(infoInDB.getZhrRzrq(),bmResultObject.getZhrRzrq())&&
                                            Objects.equals(infoInDB.getOrgeh(),bmResultObject.getOrgeh())&&
                                            Objects.equals(infoInDB.getOfficeId(),bmResultObject.getOfficeId())&&
                                            Objects.equals(infoInDB.getOfficeTxt(),bmResultObject.getOfficeTxt())&&
                                            Objects.equals(infoInDB.getDirectorCode(),bmResultObject.getDirectorCode())&&
                                            Objects.equals(infoInDB.getBranchCode(),bmResultObject.getBranchCode())&&
                                            Objects.equals(infoInDB.getZhrCost(),bmResultObject.getZhrCost())&&
                                            Objects.equals(infoInDB.getZhrCosttxt(),bmResultObject.getZhrCosttxt())&&
                                            Objects.equals(infoInDB.getWerks(),bmResultObject.getWerks())&&
                                            Objects.equals(infoInDB.getWerksTxt(),bmResultObject.getWerksTxt())&&
                                            Objects.equals(infoInDB.getInst(),bmResultObject.getInst())&&
                                            Objects.equals(infoInDB.getGesc(),bmResultObject.getGesc())&&
                                            Objects.equals(infoInDB.getPersg(),bmResultObject.getPersg())&&
                                            Objects.equals(infoInDB.getZhrLoca(),bmResultObject.getZhrLoca())&&
                                            Objects.equals(infoInDB.getLevelpk(),bmResultObject.getLevelpk())&&
                                            Objects.equals(infoInDB.getZhrPtext(),bmResultObject.getZhrPtext())&&
                                            Objects.equals(infoInDB.getStatus(),bmResultObject.getStatus())&&
                                            Objects.equals(infoInDB.getCompany(),bmResultObject.getCompany())&&
                                            Objects.equals(infoInDB.getCompanyName(),bmResultObject.getCompanyName())&&
                                            Objects.equals(infoInDB.getZhrBank(),bmResultObject.getZhrBank())&&
                                            Objects.equals(infoInDB.getZhrAccount(),bmResultObject.getZhrAccount())&&
                                            Objects.equals(infoInDB.getZzKhhs(),bmResultObject.getZzKhhs())&&
                                            Objects.equals(infoInDB.getZzKhhd(),bmResultObject.getZzKhhd())&&
                                            Objects.equals(infoInDB.getZzKhh(),bmResultObject.getZzKhh())&&
                                            Objects.equals(infoInDB.getZzYhh(),bmResultObject.getZzYhh())&&
                                            Objects.equals(infoInDB.getZzLhh(),bmResultObject.getZzLhh())&&
                                            Objects.equals(infoInDB.getPlans(),bmResultObject.getPlans())
                                    ) {
                                        // 如果更新时间完全相同，此中情况不再进行更新操作
                                    } else {
                                        infoChange = true;
                                        bmResultObject.setFlag("D".equals(bmResultObject.getFlag()) ? "D" : "M");
                                    }
                                }
                                if (infoChange) {
                                    bmResultObject.setYgmsg("");
                                    bmResultObject.setYgstatus("0");
                                    bmResultObject.setYgzhmsg("");
                                    bmResultObject.setYgzhstatus("0");
                                    employeebcResultRepository.save(bmResultObject);
                                }
                            } catch (Throwable ex) {
                                logService.error(name, "获取员工支付信息异常：", ex);
                            }
                        }
                    }


                }

                //#endregion
            }

        }catch (Throwable ex){
            logService.error( name, "获取员工支付信息异常",ex);
        }finally {
            // logService.info( name, "结束：");
            logService.flush();
        }
    }
    /*
       获取行政人员 CA 全量人员与增量人员差异 获取增量表中缺失得人员信息，同步二开表 后，同步 往来单位
       全量数据表中若有数据没有存在与增量表中则根据没有的员工拉取新的员工支付信息 TODO:
    */
    public void automaticGenerationPartner_ygbc()
    {
        String name="automaticGenerationPartner_ygbc";//获取SAP往来单位同步司库";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationPartner_ygbc");
            List<EmployeebcResult> resultList = employeebcResultRepository.findByYgstatus("0");
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }
            logService.info( name, "resultList:{}", JSONSerializer.serialize(resultList));
            // String nbdw =sqlSession.selectOne(String.class,"select ID  FROM  BFPARTNERTYPE where code= '01'");//内部单位
            String wbdw =sqlSession.selectOne(String.class,"select ID  FROM  BFPARTNERTYPE where code= '03'");//外部单位--个人
            for(EmployeebcResult result:resultList) {
                try {
                    LinkedHashMap<String, Object> orgparam = new LinkedHashMap<String, Object>();
                    Map<String, Object> hashMap = new HashMap<>();
                    Map<String, Object> mapsb = new HashMap<>();
                    mapsb.put("code", result.getUserId());
                    Map<String,Object> myhb = sqlSession.selectOne(Map.class, "select ID,state_isenabled  from  BFPARTNER  where code= #{code}", mapsb);
                    //若往来单位中不存在，则为新增
                    if(myhb!=null&&myhb.size()>0){
                        if("D".equals(result.getFlag()))
                        {
                            if("0".equals(ObjectUtils.isNotEmpty(myhb.get("state_isenabled"))?myhb.get("state_isenabled").toString()
                                    :(ObjectUtils.isNotEmpty(myhb.get("STATE_ISENABLED"))?myhb.get("STATE_ISENABLED").toString():"") ))
                            {//删除并且已停用不处理
                                continue;
                            }
                            result.setFlag("D");
                        }
                        else {
                            if("0".equals(ObjectUtils.isNotEmpty(myhb.get("state_isenabled"))?myhb.get("state_isenabled").toString()
                                    :(ObjectUtils.isNotEmpty(myhb.get("STATE_ISENABLED"))?myhb.get("STATE_ISENABLED").toString():"") ))
                            {//不删除并且是停用状态  则启用
                                result.setFlag("QY");
                            }
                            result.setFlag("M");
                        }
                    }else{
                        if("D".equals(result.getFlag())){
                            continue;
                        }
                        result.setFlag("A");
                    }

                    hashMap.put("operation", "A".equals(result.getFlag()) ? "add" : ("M".equals(result.getFlag()) ? "modify" : ("QY".equals(result.getFlag()) ? "enable" : "disable")));//操作类型
                    Map<String, Object> orgData = new HashMap<>();
                    Map<String, Object> mapsa = new HashMap<>();
                    mapsa.put("code", result.getUserId());
                    String YHID = sqlSession.selectOne(String.class, "select ID from  BFPARTNER  where code= #{code}", mapsa);
                    orgData.put("ID", YHID);
                    orgData.put("CODE", result.getUserId());//编号
                    orgData.put("NAME", result.getUserName());//名称
                    orgData.put("TYPE", wbdw);//往来单位类别  -个人
                    orgData.put("BILLSTATUS","2");// "审批状态，0制单、1审批中、2通过、3驳回",
//                orgData.put("AREA",""); // 往来单位地区，引用往来单位地区
//                orgData.put("DOMAINTYPE",""); // 使用范围，新增默认公有，0公有1组织私有2组织范围，若为1则所属行政组织必填，若为2则所属组织范围必填
//                orgData.put("OWNERORG",""); // 所属行政组织ID，引用行政组织
//                orgData.put("OWNERDOMAIN",result.getBNKLZ()); // 所属组织范围id，引用组织范围
//                orgData.put("COUNTRYORREGION",result.getSWIFT()); // 国家或地区，引用国家地区字典
//                orgData.put("DEFAULTCURRENCYID",""); // 默认交易币种，引用币种
//                orgData.put("SUPERIORPARTNER",""); // 上级往来单位，引用往来单位
//                orgData.put("ACTUALCONTROLLERPARTNER",""); // 实际控制单位，引用往来单位
//                orgData.put("INDUSTRY",""); // 行业，引用标准代码
//                orgData.put("SHORTNAME",result.getBANKS()); // 简称
//                orgData.put("UNITNATURE",result.getPROVZ()); // 单位性质，引用标准代码单位性质
//                orgData.put("PROVINCE",""); // 省
//                orgData.put("CITY",result.getORT01()); // 市
//                orgData.put("REMARK", ""); // 备注
                    orgData.put("EMAIL", result.getEmail()); // 备注
                    orgData.put("OFFICEPHONE", result.getPhoneNumber()); // 备注
                    hashMap.put("data", orgData);
                    orgparam.put("param", hashMap);//hashmap内容为所需参数
                    String su = "df";
                    String serviceID = "com.inspur.gs.bf.df.commonservice.api.IPartnerService.synchronous";
                    logService.info(name, "automaticGenerationPartner_yg-产品入参：" + JSONSerializer.serialize(orgparam));
                    HashMap results = rpcClient.invoke(HashMap.class, serviceID, su, orgparam, null);
                    logService.info(name, "automaticGenerationPartner_yg-产品返回：" + results);
                    if ("success".equals(String.valueOf(results.get("status")))) {
                        result.setYgstatus("1");
                        result.setYgmsg("");
                        employeebcResultRepository.save(result);
                    } else {
                        result.setYgstatus("2");
                        result.setYgmsg(String.valueOf(results.get("message")));
                        employeebcResultRepository.save(result);
                    }
                }catch (Throwable ex){
                    logService.error( name, "同步个人往来单位异常：",ex);
                }

            }

        }catch (Throwable ex){
            logService.error( name, "同步个人往来单位异常",ex);
        }finally {
            // logService.info( name, "结束：");
            logService.flush();
        }
    }

    /*
    获取行政人员 CA 全量人员与增量人员差异 获取增量表中缺失得人员信息，同步二开表 后，同步 往来单位银行账号 TODO:
     */
    public void automaticGenerationPartner_yhbczh()
    {
        String name="automaticGenerationPartner_yhzh";//获取SAP往来单位银行账号同步司库";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationPartner_yhzh");
            List<EmployeebcResult> resultList = employeebcResultRepository.findTop1000ByYgzhstatus("0");
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }
            logService.info( name, "resultList:{}",JSONSerializer.serialize(resultList));
            for(EmployeebcResult result:resultList) {
                try {
                    LinkedHashMap<String, Object> orgparam = new LinkedHashMap<String, Object>();
                    Map<String, Object> hashMap = new HashMap<>();
                    Map<String, Object> mapsa = new HashMap<>();
                    mapsa.put("code", result.getZhrAccount());
                    String myhb = sqlSession.selectOne(String.class, "select ID from  BFPARTNERBANKACCOUNTS  where ACCOUNTCODE= #{code}", mapsa);
                    //若往来单位中不存在，则为新增
                    if (StringUtil.isNullOrEmpty(myhb)) {
                        if("D".equals(result.getFlag())){ continue; }
                        result.setFlag("A");
                    }else if("D".equals(result.getFlag())){result.setFlag("D"); }
                    else {
                        result.setFlag("M");
                    }
                    hashMap.put("operation", "A".equals(result.getFlag()) ? "add" : ("M".equals(result.getFlag()) ? "modify" : "modify"));//操作类型
                    Map<String, Object> orgData = new HashMap<>();
                    orgData.put("ID", myhb);
                    orgData.put("ACCOUNTCODE", result.getZhrAccount());//银行编号
                    orgData.put("ACCOUNTNAME", result.getUserName());//账户名称  账户持有人
                    Map<String, Object> mapss = new HashMap<>();
                    mapss.put("wldwbh", result.getUserId());
                    mapss.put("khhlhh", result.getZzLhh());
                    mapss.put("khhmc", result.getZzKhh());
                    String WLDWID = sqlSession.selectOne(String.class, "select ID from  BFPARTNER  where code= #{wldwbh}", mapss);
                    orgData.put("PARTNERID", WLDWID); // 往来单位ID，引用往来单位  TODO:
                    String khhid = sqlSession.selectOne(String.class, "select ID from  BFBANK  where BANKIDENTIFIER= #{khhlhh} " +
                            " and name_chs= #{khhmc} ", mapss);
                    orgData.put("INBANK", khhid); // 所属银行ID，引用银行定义
                    orgData.put("ACCOUNTSTATE", ("D".equals(result.getFlag()) ? "1" : "0")); // 账号状态，0,正常;1,冻结;2,其他，默认为0
                    orgData.put("PRIVATEORPUBLIC","2"); // 账号性质，1,对公账号;2,对私账号，默认为1  TODO: 付款不在司库发起，无影响;
//                orgData.put("COUNTRY",result.getBNKLZ()); // 国家地区ID，引用国家地区字典
//                orgData.put("PROVINCE",result.getSWIFT()); // 省份，取行政区划省份名称
//                orgData.put("CITY",""); //  城市，取行政区划城市名称
//                orgData.put("CURRENCYLIST",""); // 币种ID，引用币种定义字典
//                    orgData.put("ISMAIN","1"); // 是否默认账户，0,否;1,是，默认为0
//                orgData.put("ISMAINFORINVOICING",""); // 是否默认开票账号，0,否;1,是，默认为0
//                orgData.put("ISPUBLIC",result.getBANKS()); // 是否公共，0,否;1,是，默认为1
//                orgData.put("REMARK",result.getPROVZ()); // 备注
//                orgData.put("PLAINTEXT1",""); // 扩展字段
//                orgData.put("PLAINTEXT2",result.getORT01()); // 扩展字段
//                orgData.put("PLAINTEXT3", ""); // 扩展字段
                    hashMap.put("data", orgData);
                    orgparam.put("param", hashMap);//hashmap内容为所需参数
                    String su = "df";
                    String serviceID = "com.inspur.gs.bf.df.commonservice.api.IPartnerBankAccountService.synchronous";
                    logService.info(name, "产品入参：" + JSONSerializer.serialize(orgparam));
                    HashMap results = rpcClient.invoke(HashMap.class, serviceID, su, orgparam, null);
                    logService.info(name, "产品返回：" + results);
                    if ("success".equals(String.valueOf(results.get("status")))) {
                        result.setYgzhstatus("1");
                        result.setYgzhmsg("");
                        employeebcResultRepository.save(result);
                    } else {
                        result.setYgzhstatus("2");
                        result.setYgzhmsg(String.valueOf(results.get("message")));
                        employeebcResultRepository.save(result);
                    }
                }catch (Throwable ex){
                    logService.error( name, "同步员工支付信息异常：",ex);
                }
            }

        }catch (Throwable ex){
            logService.error( name, "同步员工支付信息异常",ex);
        }finally {
            //logService.info( name, "结束：");
            logService.flush();
        }
    }



    /**
     * 获取成本中心
     */
    public void automaticGenerationCBZX() throws Exception {
        String name="automaticGenerationCBZX";
        logService.init(name);
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
        if (StringUtil.isNullOrEmpty(dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        RelationalDbExector exector = ExectorFactory.create(dataSourceId);
        try{
            logService.info( name, "automaticGenerationCBZX");
            //#region
            List<JTGKCbzxResultEntity> resultList=new ArrayList<>();
            logService.info( name, "automaticGenerationCBZX-dataSourceId:"+dataSourceId);
            ResultSet gsOfOut = exector.query(" select group_id, cost_center_code, general_text, long_text, match_text, cost_center_type, create_date, start_date, end_date, change_date, control_area, function_area, user_name, cost_center_person, cost_center_user, language_code, company_code, profit_center_code, region_code, organization_code, organization_type, dept_code, dept_name, dept2_name, object_identifier, object_number, currency, tax_jurisdiction, record_complete_flag, complete_description, statistical_object_flag, budgeted_cost_center, budget_availability_profile,  budget_availability_active_flag as budget_availab_active_flag, actual_primary_freeze_flag, actual_secondary_freeze_flag, actual_income_freeze_flag, plann_primary_freeze_flag, plann_secondary_freeze_flag, plann_income_freeze_flag, open_item_freeze_flag, standard_hierarchy_area, cost_aggregator_code from dm_dim_pub_cost_center_master_data_f_h ", null);
            logService.info( name, "automaticGenerationCBZX-ResultSet:" );
            while (gsOfOut.next()) {
                JTGKCbzxResultEntity entity = new JTGKCbzxResultEntity();
                entity.setGroup_id(gsOfOut.getString(1) );//
                entity.setCost_center_code(gsOfOut.getString(2) );//
                entity.setGeneral_text(gsOfOut.getString(3) );//
                entity.setLong_text(gsOfOut.getString(4) );//
                entity.setMatch_text(gsOfOut.getString(5) );//
                entity.setCost_center_type(gsOfOut.getString(6) );//
                entity.setCreate_date(gsOfOut.getString(7) );//
                entity.setStart_date(gsOfOut.getString(8) );//
                entity.setEnd_date(gsOfOut.getString(9) );//
                entity.setChange_date(gsOfOut.getString(10) );//
                entity.setControl_area(gsOfOut.getString(11) );//
                entity.setFunction_area(gsOfOut.getString(12) );//
                entity.setUser_name(gsOfOut.getString(13) );//
                entity.setCost_center_person(gsOfOut.getString(14) );//
                entity.setCost_center_user(gsOfOut.getString(15) );//
                entity.setLanguage_code(gsOfOut.getString(16) );//
                entity.setCompany_code(gsOfOut.getString(17) );//
                entity.setProfit_center_code(gsOfOut.getString(18) );//
                entity.setRegion_code(gsOfOut.getString(19) );//
                entity.setOrganization_code(gsOfOut.getString(20) );//
                entity.setOrganization_type(gsOfOut.getString(21) );//
                entity.setDept_code(gsOfOut.getString(22) );//
                entity.setDept_name(gsOfOut.getString(23) );//
                entity.setDept2_name(gsOfOut.getString(24) );//
                entity.setObject_identifier(gsOfOut.getString(25) );//
                entity.setObject_number(gsOfOut.getString(26) );//
                entity.setCurrency(gsOfOut.getString(27) );//
                entity.setTax_jurisdiction(gsOfOut.getString(28) );//
                entity.setRecord_complete_flag(gsOfOut.getString(29) );//
                entity.setComplete_description(gsOfOut.getString(30) );//
                entity.setStatistical_object_flag(gsOfOut.getString(31) );//
                entity.setBudgeted_cost_center(gsOfOut.getString(32) );//
                entity.setBudget_availability_profile(gsOfOut.getString(33) );//
                entity.setBudget_availab_active_flag(gsOfOut.getString(34) );//
                entity.setActual_primary_freeze_flag(gsOfOut.getString(35) );//
                entity.setActual_secondary_freeze_flag(gsOfOut.getString(36) );//
                entity.setActual_income_freeze_flag(gsOfOut.getString(37) );//
                entity.setPlann_primary_freeze_flag(gsOfOut.getString(38) );//
                entity.setPlann_secondary_freeze_flag(gsOfOut.getString(39) );//
                entity.setPlann_income_freeze_flag(gsOfOut.getString(40) );//
                entity.setOpen_item_freeze_flag(gsOfOut.getString(41) );//
                entity.setStandard_hierarchy_area(gsOfOut.getString(42) );//
                entity.setCost_aggregator_code(gsOfOut.getString(43) );//
                resultList.add(entity);
            }
            logService.info( name, "automaticGenerationCBZX-ResultSet11:" );
//            jtgkCbzxResultEntityReposiory.saveAll(resultList);
//            jtgkCbzxResultEntityReposiory.flush();
            for(JTGKCbzxResultEntity item:resultList){
                try {
                    boolean infoChange = false;
                    //存入二开中间表，根据code  实时更新中间表
                    JTGKCbzxResultEntity jtgkCbzxResultEntity = new JTGKCbzxResultEntity(item);
                    JTGKCbzxResultEntity infoInDB = jtgkCbzxResultEntityReposiory.findByCostlist(item.getGroup_id(),item.getCost_center_code(),item.getControl_area(),item.getEnd_date(),item.getLanguage_code());//查询是否有相同编号的
                    if (infoInDB == null) {
                        infoChange = true;
                    } else {
                        if (Objects.equals(infoInDB.getChange_date(),jtgkCbzxResultEntity.getChange_date()))
//                                Objects.equals(infoInDB.getGroup_id(),jtgkCbzxResultEntity.getGroup_id())&&
//                                Objects.equals(infoInDB.getCost_center_code(),jtgkCbzxResultEntity.getCost_center_code())&&
//                                Objects.equals(infoInDB.getGeneral_text(),jtgkCbzxResultEntity.getGeneral_text())&&
//                                Objects.equals(infoInDB.getLong_text(),jtgkCbzxResultEntity.getLong_text())&&
//                                Objects.equals(infoInDB.getMatch_text(),jtgkCbzxResultEntity.getMatch_text())&&
//                                Objects.equals(infoInDB.getCost_center_type(),jtgkCbzxResultEntity.getCost_center_type())&&
//                                Objects.equals(infoInDB.getCreate_date(),jtgkCbzxResultEntity.getCreate_date())&&
//                                Objects.equals(infoInDB.getStart_date(),jtgkCbzxResultEntity.getStart_date())&&
//                                Objects.equals(infoInDB.getEnd_date(),jtgkCbzxResultEntity.getEnd_date())&&
//                                Objects.equals(infoInDB.getChange_date(),jtgkCbzxResultEntity.getChange_date())&&
//                                Objects.equals(infoInDB.getControl_area(),jtgkCbzxResultEntity.getControl_area())&&
//                                Objects.equals(infoInDB.getFunction_area(),jtgkCbzxResultEntity.getFunction_area())&&
//                                Objects.equals(infoInDB.getUser_name(),jtgkCbzxResultEntity.getUser_name())&&
//                                Objects.equals(infoInDB.getCost_center_person(),jtgkCbzxResultEntity.getCost_center_person())&&
//                                Objects.equals(infoInDB.getCost_center_user(),jtgkCbzxResultEntity.getCost_center_user())&&
//                                Objects.equals(infoInDB.getLanguage_code(),jtgkCbzxResultEntity.getLanguage_code())&&
//                                Objects.equals(infoInDB.getCompany_code(),jtgkCbzxResultEntity.getCompany_code())&&
//                                Objects.equals(infoInDB.getProfit_center_code(),jtgkCbzxResultEntity.getProfit_center_code())&&
//                                Objects.equals(infoInDB.getRegion_code(),jtgkCbzxResultEntity.getRegion_code())&&
//                                Objects.equals(infoInDB.getOrganization_code(),jtgkCbzxResultEntity.getOrganization_code())&&
//                                Objects.equals(infoInDB.getOrganization_type(),jtgkCbzxResultEntity.getOrganization_type())&&
//                                Objects.equals(infoInDB.getDept_code(),jtgkCbzxResultEntity.getDept_code())&&
//                                Objects.equals(infoInDB.getDept2_name(),jtgkCbzxResultEntity.getDept_name())&&
//                                Objects.equals(infoInDB.getDept2_name(),jtgkCbzxResultEntity.getDept2_name())&&
//                                Objects.equals(infoInDB.getObject_identifier(),jtgkCbzxResultEntity.getObject_identifier())&&
//                                Objects.equals(infoInDB.getObject_number(),jtgkCbzxResultEntity.getObject_number())&&
//                                Objects.equals(infoInDB.getCurrency(),jtgkCbzxResultEntity.getCurrency())&&
//                                Objects.equals(infoInDB.getTax_jurisdiction(),jtgkCbzxResultEntity.getTax_jurisdiction())&&
//                                Objects.equals(infoInDB.getRecord_complete_flag(),jtgkCbzxResultEntity.getRecord_complete_flag())&&
//                                Objects.equals(infoInDB.getComplete_description(),jtgkCbzxResultEntity.getComplete_description())&&
//                                Objects.equals(infoInDB.getStatistical_object_flag(),jtgkCbzxResultEntity.getStatistical_object_flag())&&
//                                Objects.equals(infoInDB.getBudgeted_cost_center(),jtgkCbzxResultEntity.getBudgeted_cost_center())&&
//                                Objects.equals(infoInDB.getBudget_availability_profile(),jtgkCbzxResultEntity.getBudget_availability_profile())&&
//                                Objects.equals(infoInDB.getBudget_availability_active_flag(),jtgkCbzxResultEntity.getBudget_availability_active_flag())&&
//                                Objects.equals(infoInDB.getActual_primary_freeze_flag(),jtgkCbzxResultEntity.getActual_primary_freeze_flag())&&
//                                Objects.equals(infoInDB.getActual_secondary_freeze_flag(),jtgkCbzxResultEntity.getActual_secondary_freeze_flag())&&
//                                Objects.equals(infoInDB.getActual_income_freeze_flag(),jtgkCbzxResultEntity.getActual_income_freeze_flag())&&
//                                Objects.equals(infoInDB.getPlann_primary_freeze_flag(),jtgkCbzxResultEntity.getPlann_primary_freeze_flag())&&
//                                Objects.equals(infoInDB.getPlann_secondary_freeze_flag(),jtgkCbzxResultEntity.getPlann_secondary_freeze_flag())&&
//                                Objects.equals(infoInDB.getPlann_income_freeze_flag(),jtgkCbzxResultEntity.getPlann_income_freeze_flag())&&
//                                Objects.equals(infoInDB.getOpen_item_freeze_flag(),jtgkCbzxResultEntity.getOpen_item_freeze_flag())&&
//                                Objects.equals(infoInDB.getStandard_hierarchy_area(),jtgkCbzxResultEntity.getStandard_hierarchy_area())&&
//                                Objects.equals(infoInDB.getCost_aggregator_code(),jtgkCbzxResultEntity.getCost_aggregator_code())
                        {//如果更新日期一致，不再更新

                        } else {
                            infoChange = true;
                        }
                    }
                    if (infoChange) {
                        jtgkCbzxResultEntity.setSksyncstatus("0");
                        jtgkCbzxResultEntity.setSksyncmsg("");
                        jtgkCbzxResultEntityReposiory.save(jtgkCbzxResultEntity);
                    }
                }catch (Throwable ex){
                    logService.error( name, "获取成本中心异常：",ex);
                }
            }

        }catch (Throwable ex){
            logService.error( name, "获取成本中心异常",ex);
        }finally {
            //logService.info( name, "结束：");
            logService.flush();
            //关闭连接
            if(exector!=null)exector.close();
        }
    }

    /**
     * 获取利润中心与公司关系
     */
    public void automaticGenerationCBZXgsgx() throws Exception {
        String name="automaticGenerationCBZXgsgx";
        logService.init(name);
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
        if (StringUtil.isNullOrEmpty(dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        RelationalDbExector exector = ExectorFactory.create(dataSourceId);
        try{
            logService.info( name, "automaticGenerationCBZXgsgx");
            //#region
            List<JTGKCbzxgsgxresultEntity> resultList=new ArrayList<>();
            logService.info( name, "dataSourceId:"+dataSourceId);
            ResultSet gsOfOut = exector.query(" select group_id, control_area, profit_center_code, company_code from  dm_dim_pub_profit_center_company_f_h ", null);
            logService.info( name, "ResultSet:" );
            while (gsOfOut.next()) {
                JTGKCbzxgsgxresultEntity entity = new JTGKCbzxgsgxresultEntity();
                entity.setId(gsOfOut.getString(1)+gsOfOut.getString(2)+gsOfOut.getString(3)+gsOfOut.getString(4));
                entity.setGroup_id(gsOfOut.getString(1) );//
                entity.setControl_area(gsOfOut.getString(2) );//
                entity.setProfit_center_code(gsOfOut.getString(3) );
                entity.setCompany_code(gsOfOut.getString(4) );
                resultList.add(entity);
            }
            //判断resultListDB数据表中存在，resultList中不存在，这种情况将resultListDB存在的数据删掉
            List<JTGKCbzxgsgxresultEntity> resultListDB=jtgkCbzxgsgxresultRepository.findAll();
            if(resultListDB!=null&&resultListDB.size()>0){
                Set<String> resultListIds = resultList.stream()
                        .map(item -> item.getGroup_id() + item.getControl_area() + item.getProfit_center_code() + item.getCompany_code())
                        .collect(Collectors.toSet());

                // 从 resultListDB 中查找需要删除的项目
                List<JTGKCbzxgsgxresultEntity> itemsToDelete = resultListDB.stream()
                        .filter(itemss -> !resultListIds.contains(itemss.getId()))
                        .collect(Collectors.toList());
                // 使用 JPA Repository 删除这些项目
                if (!itemsToDelete.isEmpty()) {
                    jtgkCbzxgsgxresultRepository.deleteAll(itemsToDelete);
                }
            }
            logService.info( name, "ResultSet11:" );
            jtgkCbzxgsgxresultRepository.saveAll(resultList);
            jtgkCbzxgsgxresultRepository.flush();


        }catch (Throwable ex){
            logService.error( name, "获取利润中心关联关系异常",ex);
        }finally {
            //logService.info( name, "结束：");
            logService.flush();
            //关闭连接
            if(exector!=null)exector.close();
        }
    }

    /**
     * 获取利润中心
     */
    public void automaticGenerationLRZX() throws Exception {
        String name="automaticGenerationLRZX";
        logService.init(name);
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
        if (StringUtil.isNullOrEmpty(dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        RelationalDbExector exector = ExectorFactory.create(dataSourceId);
        try{
            logService.info( name, "automaticGenerationLRZX");
            //#region
            List<JTGKLrzxResultEntity> resultList=new ArrayList<>();
            logService.info( name, "dataSourceId:"+dataSourceId);
            ResultSet gsOfOut = exector.query(" select group_id, profit_center_code, general_text, long_text, match_text, control_area, create_date, start_date, end_date, user_name, profit_center_person, profit_center_group_code, unit_code, language_code from dm_dim_pub_profit_center_master_data_f_h ", null);
            logService.info( name, "ResultSet:" );
            while (gsOfOut.next()) {
                JTGKLrzxResultEntity entity = new JTGKLrzxResultEntity();
                entity.setGroup_id(gsOfOut.getString(1) );//
                entity.setProfit_center_code(gsOfOut.getString(2) );//
                entity.setGeneral_text(gsOfOut.getString(3) );//
                entity.setLong_text(gsOfOut.getString(4) );//
                entity.setMatch_text(gsOfOut.getString(5) );//
                entity.setControl_area(gsOfOut.getString(6) );//
                entity.setCreate_date(gsOfOut.getString(7) );//
                entity.setStart_date(gsOfOut.getString(8) );//
                entity.setEnd_date(gsOfOut.getString(9) );//
                entity.setUser_name(gsOfOut.getString(10) );//
                entity.setProfit_center_person(gsOfOut.getString(11) );//
                entity.setProfit_center_group_code(gsOfOut.getString(12) );//
                entity.setUnit_code(gsOfOut.getString(13) );//
                entity.setLanguage_code(gsOfOut.getString(14) );//
                resultList.add(entity);
            }
            logService.info( name, "ResultSet11:" );
            for(JTGKLrzxResultEntity item:resultList){
                try {
                    boolean infoChange = false;
                    //存入二开中间表，根据code  实时更新中间表
                    JTGKLrzxResultEntity jtgkLrzxResultEntity = new JTGKLrzxResultEntity(item);

                    JTGKLrzxResultEntity infoInDB = jtgkLrzxResultRepository.findByProfitlist(item.getGroup_id(),item.getLanguage_code(),item.getControl_area(),item.getProfit_center_code(),item.getStart_date(),item.getEnd_date());//查询是否有相同编号的
                    if (infoInDB == null) {
                        infoChange = true;
                    } else {
                        if (Objects.equals(infoInDB.getGroup_id(),jtgkLrzxResultEntity.getGroup_id())&&
                                Objects.equals(infoInDB.getProfit_center_code(),jtgkLrzxResultEntity.getProfit_center_code())&&
                                Objects.equals(infoInDB.getGeneral_text(),jtgkLrzxResultEntity.getGeneral_text())&&
                                Objects.equals(infoInDB.getLong_text(),jtgkLrzxResultEntity.getLong_text())&&
                                Objects.equals(infoInDB.getMatch_text(),jtgkLrzxResultEntity.getMatch_text())&&
                                Objects.equals(infoInDB.getControl_area(),jtgkLrzxResultEntity.getControl_area())&&
                                Objects.equals(infoInDB.getCreate_date(),jtgkLrzxResultEntity.getCreate_date())&&
                                Objects.equals(infoInDB.getStart_date(),jtgkLrzxResultEntity.getStart_date())&&
                                Objects.equals(infoInDB.getEnd_date(),jtgkLrzxResultEntity.getEnd_date())&&
                                Objects.equals(infoInDB.getUser_name(),jtgkLrzxResultEntity.getUser_name())&&
                                Objects.equals(infoInDB.getProfit_center_person(),jtgkLrzxResultEntity.getProfit_center_person())&&
                                Objects.equals(infoInDB.getProfit_center_group_code(),jtgkLrzxResultEntity.getProfit_center_group_code())&&
                                Objects.equals(infoInDB.getUnit_code(),jtgkLrzxResultEntity.getUnit_code())&&
                                Objects.equals(infoInDB.getLanguage_code(),jtgkLrzxResultEntity.getLanguage_code())
                        ) {//如果基本信息一致，不再更新

                        } else {
                            infoChange = true;
                        }
                    }
                    if (infoChange) {
                        jtgkLrzxResultEntity.setSksyncstatus("0");
                        jtgkLrzxResultEntity.setSksyncmsg("");
                        jtgkLrzxResultRepository.save(jtgkLrzxResultEntity);
                    }
                }catch (Throwable ex){
                    logService.error( name, "获取利润中心异常：",ex);
                }
            }

        }catch (Throwable ex){
            logService.error( name, "获取利润中心异常",ex);
        }finally {
            //logService.info( name, "结束：");
            logService.flush();
            //关闭连接
            if(exector!=null)exector.close();
        }
    }



    /**
     * 获取财司数仓数据湖  存款账户余额+准备金余额
     */
    public void automaticcCKZHYE() throws Exception {
        String name="automaticGenerationCKZHYE";
        logService.init(name);
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'GFS'");
        if (StringUtil.isNullOrEmpty(dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        RelationalDbExector exector = ExectorFactory.create(dataSourceId);
        try{
            logService.info( name, "automaticGenerationCKZHYE");
            //#region
            List<JTGKCkzhyebResult> resultList=new ArrayList<>();
            logService.info( name, "dataSourceId:"+dataSourceId);
            ResultSet gsOfOut = exector.query(" select id, clientname, clientcode, accountno, accountstate, opendate, enddate , balance , accounttype  from dm.dm_sk_acnt_deposit_balance_f_d ", null);
            logService.info( name, "ResultSet:" );
            List<Map> resultList2=sqlSession.selectList(Map.class,"select bfmasterorganization.CODE,ywdy.code YWDYBH,ywdy.name_chs ywdymc,bfmasterorganization.treeinfo_path " +
                    "   from bfmasterorganization  " +
                    "left join BFMASTERORGANIZATIONTYPE on BFMASTERORGANIZATIONTYPE.id=bfmasterorganization.orgtype " +
                    "left join bfmasterorganization ywdy on ywdy.treeinfo_path= SUBSTRING(bfmasterorganization.treeinfo_path FROM 1 FOR 8) " +
                    "where  BFMASTERORGANIZATIONTYPE.name_chs ='单位' ");

            List<SFBBResultObject> sfbblist=sfbbResultObjectRepository.findAll();
            while (gsOfOut.next()) {
                //select accountno 账号,unitname 单位名称,unitno 单位编号,ywdybh 业务单元编号,cdlx 存款类型,ckdjbh 存款单据编号,ckdjbh_old 旧存款单据编号,startdate 开始日期,enddate 结束日期,qx 期限/通知存款品种,klje 开立金额,dqye 当前余额,status 状态,ll 利率,yjtlx 已计提利息   from jtgkckzhyeb
                JTGKCkzhyebResult entity = new JTGKCkzhyebResult();
                entity.setId(gsOfOut.getString(1) );//
                entity.setUnitname(gsOfOut.getString(2) );//
                entity.setUnitno(gsOfOut.getString(3) );//
                entity.setAccountno(gsOfOut.getString(4) );
                entity.setStatus(gsOfOut.getString(5) );
                entity.setStartdate(gsOfOut.getString(6) );
                entity.setEnddate(gsOfOut.getString(7) );
                entity.setDqye(gsOfOut.getString(8) );
                entity.setCdlx(gsOfOut.getString(9) );
                List<Map> resultLists=resultList2.stream().filter(sqlField -> entity.getUnitno().equals(sqlField.get("CODE"))).collect(Collectors.toList());
                if(resultLists!=null&&resultLists.size()>0){
                    entity.setYwdybh( !ObjectUtils.isEmpty(resultLists.get(0).get("YWDYBH"))? resultLists.get(0).get("YWDYBH").toString():"");
                }
                entity.setSfbb( "0");
                List<SFBBResultObject> sfbblists=sfbblist.stream().filter(sqlField -> entity.getStartdate().substring(0,7).equals(sqlField.getCtime())&&entity.getUnitno().equals(sqlField.getCentity())).collect(Collectors.toList());
                if(resultLists!=null&&resultLists.size()>0){
                    entity.setSfbb( "1");
                }
                resultList.add(entity);
            }
            logService.info( name, "ResultSet11:" );
            jtgkCkzhyebResultRepository.saveAll(resultList);
            jtgkCkzhyebResultRepository.flush();

        }catch (Throwable ex){
            logService.error( name, "获取存款账户科目余额异常",ex);
        }finally {
            //logService.info( name, "结束：");
            logService.flush();
            //关闭连接
            if(exector!=null)exector.close();
        }
    }

    /**
     * 往来单位-客户、供应商拉取数据 同步二开表
     */
    public void automaticPenternew() throws Exception {
        String name="automaticPenternew";
        logService.init(name);
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
        if (StringUtil.isNullOrEmpty(dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        RelationalDbExector exector = ExectorFactory.create(dataSourceId);
        try{
            logService.info( name, "automaticPenternew");
            //#region
            List<JTGKKSZSJResultEntity> resultList=new ArrayList<>();
            logService.info( name, "dataSourceId:"+dataSourceId);
            ResultSet gsOfOut = exector.query(" select id,category_code,category_name,code,assistant_code1,assistant_code2,assistant_code3,assistant_code4,mnemonic_code,parent_id,recorder_code,recorder_name,recorder_time,pretrial_code,pretrial_name,pretrial_time,auditor_code,auditor_name,audit_time,audit_level,audit_flag,retrieve_flag,retriever_code,retriever_name,retrieve_time,freeze_flag,freezer_code,freezer_name,freeze_time,workflow_id,recorder_corp,mdm_code,audit_node_name,master_data_version,physical_model_version,filing_flag,filing_user_code,filing_user_name,filing_date,lucence_flag,lucence_time,submit_corp,desc_long,desc_short,partner_type,merchant_code,merchant_name,search_term,language,trade_partner,country_region,province_code,city,postal_code,remark,validate_msg,modify_group_label_code,category_version,mdm_code_createable,last_modify_recorder_code,last_modify_recorder_name,last_modify_record_time,submit_time,flow_para,file_count,temp_save_flag,task_flag,uuid,error_msg,auditing_flag,release_flag,data_source_flag,last_modify_submit_corp,workflow,stand_info,security_level_code,submitter_name,address,tax_category,partner_nature,tax_number,Billing_phone,mobile_phone,email,file,freeze_customer,delete_customer,freeze_client_companies,freeze_sales_organizations,freeze_supplier,delete_supplier,freeze_supplier_companies,freeze_purchasing_organizations,supply_range,legal_nature,crt_number,icms_taxpayer,industry_main_type,tax_declaration_type,customer_group_abbreviation,second_level_company,affiliated_group,employee_code,is_finance,data_source,merchant_type,supplier_type,customer_classification,transportation_supplier,is_portal_supplier,reason,tax_jurisdiction,house_number,province_name,gw_resource_dev_Engineer,wind_turbine_supply_chain,contact_person from dm_dim_pub_merchant_master_data_f_1d   ", null);
            logService.info( name, "ResultSet:" );
            while (gsOfOut.next()) {
                JTGKKSZSJResultEntity param = new JTGKKSZSJResultEntity();
                param.setId(gsOfOut.getString(1) );
                param.setCategory_code(gsOfOut.getString(2) )	;//类别编码
                param.setCategory_name(gsOfOut.getString(3) )	;//类别名称
                param.setCode(gsOfOut.getString(4) )	;//编码
                param.setAssistant_code1(gsOfOut.getString(5) )	;//辅助编码1
                param.setAssistant_code2(gsOfOut.getString(6) )	;//辅助编码2
                param.setAssistant_code3(gsOfOut.getString(7) )	;//辅助编码3
                param.setAssistant_code4(gsOfOut.getString(8) )	;//辅助编码4
                param.setMnemonic_code(gsOfOut.getString(9) )	;//助记码
                param.setParent_id(gsOfOut.getString(10) )	;//父ID
                param.setRecorder_code(gsOfOut.getString(11) )	;//制单人编码
                param.setRecorder_name(gsOfOut.getString(12) )	;//制单人名称
                param.setRecorder_time(gsOfOut.getString(13) )	;//制单人时间
                param.setPretrial_code(gsOfOut.getString(14) )	;//预审人编码
                param.setPretrial_name(gsOfOut.getString(15) )	;//预审人名称
                param.setPretrial_time(gsOfOut.getString(16) )	;//预审时间
                param.setAuditor_code(gsOfOut.getString(17) )	;//审核人编码
                param.setAuditor_name(gsOfOut.getString(18) )	;//审核人名称
                param.setAudit_time(gsOfOut.getString(19) )	;//审核时间
                param.setAudit_level(gsOfOut.getString(20) )	;//审核级别
                param.setAudit_flag(gsOfOut.getString(21) )	;//审核标志 0 未提交 1 待审核  2 审核通过
                param.setRetrieve_flag(gsOfOut.getString(22) )	;//回退标志
                param.setRetriever_code(gsOfOut.getString(23) )	;//回退人编码
                param.setRetriever_name(gsOfOut.getString(24) )	;//回退人名称
                param.setRetrieve_time(gsOfOut.getString(25) )	;//回退时间
                param.setFreeze_flag(gsOfOut.getString(26) )	;//注销标志：0:正常 1:冻结 2:删除标记 3:物理删除
                param.setFreezer_code(gsOfOut.getString(27) )	;//注销人编码
                param.setFreezer_name(gsOfOut.getString(28) )	;//注销人名称
                param.setFreeze_time(gsOfOut.getString(29) )	;//注销时间
                param.setWorkflow_id(gsOfOut.getString(30) )	;//工作流ID
                param.setRecorder_corp(gsOfOut.getString(31) )	;//制单人单位
                param.setMdm_code(gsOfOut.getString(32) )	;//MDMCODE
                param.setAudit_node_name(gsOfOut.getString(33) )	;//审核节点名称
                param.setMaster_data_version(gsOfOut.getString(34) )	;//主数据版本
                param.setPhysical_model_version(gsOfOut.getString(35) )	;//实体模型版本
                param.setFiling_flag(gsOfOut.getString(36) )	;//归档标志
                param.setFiling_user_code(gsOfOut.getString(37) )	;//归档人编码
                param.setFiling_user_name(gsOfOut.getString(38) )	;//归档人名称
                param.setFiling_date(gsOfOut.getString(39) )	;//归档时间
                param.setLucence_flag(gsOfOut.getString(40) )	;//检索标志
                param.setLucence_time(gsOfOut.getString(41) )	;//创建检索时间
                param.setSubmit_corp(gsOfOut.getString(42) )	;//提报单位
                param.setDesc_long(gsOfOut.getString(43) )	;//长描述
                param.setDesc_short(gsOfOut.getString(44) )	;//短描述
                param.setPartner_type(gsOfOut.getString(45) )	;//合作伙伴类型
                param.setMerchant_code(gsOfOut.getString(46) )	;//客商编码
                param.setMerchant_name(gsOfOut.getString(47) )	;//客商名称
                param.setSearch_term(gsOfOut.getString(48) )	;//搜索词（简称）
                param.setLanguage(gsOfOut.getString(49) )	;//语言
                param.setTrade_partner(gsOfOut.getString(50) )	;//贸易伙伴
                param.setCountry_region(gsOfOut.getString(51) )	;//国家/地区
                param.setProvince_code(gsOfOut.getString(52) )	;//省份代码
                param.setCity(gsOfOut.getString(53) )	;//城市
                param.setPostal_code(gsOfOut.getString(54) )	;//邮政编码
                param.setRemark(gsOfOut.getString(55) )	;//备注
                param.setValidate_msg(gsOfOut.getString(56) )	;//校验信息和校验级别
                param.setModify_group_label_code(gsOfOut.getString(57) )	;//修改分组标签编码
                param.setCategory_version(gsOfOut.getString(58) )	;//分类数据模型版本
                param.setMdm_code_createable(gsOfOut.getString(59) )	;//是否可建立主数据 0:不可以建立 1:可以建立
                param.setLast_modify_recorder_code(gsOfOut.getString(60) )	;//上一次变更人编码
                param.setLast_modify_recorder_name(gsOfOut.getString(61) )	;//上一次变更人名称
                param.setLast_modify_record_time(gsOfOut.getString(62) )	;//上一次变更时间
                param.setSubmit_time(gsOfOut.getString(63) )	;//提报时间
                param.setFlow_para(gsOfOut.getString(64) )	;//工作流附加参数
                param.setFile_count(gsOfOut.getString(65) )	;//附件个数
                param.setTemp_save_flag(gsOfOut.getString(66) )	;//暂存标志 0 否 1 是
                param.setTask_flag(gsOfOut.getString(67) )	;//0:排队中 1:执行完毕
                param.setUuid(gsOfOut.getString(68) )	;//uuid
                param.setError_msg(gsOfOut.getString(69) )	;//报错提醒
                param.setAuditing_flag(gsOfOut.getString(70) )	;//0:未在最后一级审核中 1:正在最后一级审核中
                param.setRelease_flag(gsOfOut.getString(71) )	;//0:该节点的一下节点不是发布节点 1:该节点的一下节点是发布节点
                param.setData_source_flag(gsOfOut.getString(72) )	;//数据来源标识
                param.setLast_modify_submit_corp(gsOfOut.getString(73) )	;//最后修改组织
                param.setWorkflow(gsOfOut.getString(74) )	;//工作流
                param.setStand_info(gsOfOut.getString(75) )	;//数据标准信息
                param.setSecurity_level_code(gsOfOut.getString(76) )	;//密级
                param.setSubmitter_name(gsOfOut.getString(77) )	;//提报人名称
                param.setAddress(gsOfOut.getString(78) )	;//地址
                param.setTax_category(gsOfOut.getString(79) )	;//税类别
                param.setPartner_nature(gsOfOut.getString(80) )	;//合作伙伴性质
                param.setTax_number(gsOfOut.getString(81) )	;//统一社会信用代码（税号）
                param.setBilling_phone(gsOfOut.getString(82) )	;//开票电话
                param.setMobile_phone(gsOfOut.getString(83) )	;//移动电话
                param.setEmail(gsOfOut.getString(84) )	;//E-MAIL
                param.setFile(gsOfOut.getString(85) )	;//归档_XDELE
                param.setFreeze_customer(gsOfOut.getString(86) )	;//冻结客户
                param.setDelete_customer(gsOfOut.getString(87) )	;//删除客户
                param.setFreeze_client_companies(gsOfOut.getString(88) )	;//冻结所有客户公司
                param.setFreeze_sales_organizations(gsOfOut.getString(89) )	;//冻结所有销售组织（预留）
                param.setFreeze_supplier(gsOfOut.getString(90) )	;//冻结供应商
                param.setDelete_supplier(gsOfOut.getString(91) )	;//删除供应商
                param.setFreeze_supplier_companies(gsOfOut.getString(92) )	;//冻结所有供应商公司
                param.setFreeze_purchasing_organizations(gsOfOut.getString(93) )	;//冻结所有采购组织
                param.setSupply_range(gsOfOut.getString(94) )	;//供应范围
                param.setLegal_nature(gsOfOut.getString(95) )	;//Legal Nature
                param.setCrt_number(gsOfOut.getString(96) )	;//CRT Number
                param.setIcms_taxpayer(gsOfOut.getString(97) )	;//ICMS Taxpayer
                param.setIndustry_main_type(gsOfOut.getString(98) )	;//Industry Main Type
                param.setTax_declaration_type(gsOfOut.getString(99) )	;//Tax Declaration Type
                param.setCustomer_group_abbreviation(gsOfOut.getString(100) )	;//客户集团简称
                param.setSecond_level_company(gsOfOut.getString(101) )	;//二级公司
                param.setAffiliated_group(gsOfOut.getString(102) )	;//所属集团简称(天眼查
                param.setEmployee_code(gsOfOut.getString(103) )	;//工号
                param.setIs_finance(gsOfOut.getString(104) )	;//是否金融
                param.setData_source(gsOfOut.getString(105) )	;//数据来源
                param.setMerchant_type(gsOfOut.getString(106) )	;//客商类型
                param.setSupplier_type(gsOfOut.getString(107) )	;//供应商类型
                param.setCustomer_classification(gsOfOut.getString(108) )	;//客户分类
                param.setTransportation_supplier(gsOfOut.getString(109) )	;//运输供应商
                param.setIs_portal_supplier(gsOfOut.getString(110) )	;//是否门户供应商
                param.setReason(gsOfOut.getString(111) )	;//不是门户供应商的原因
                param.setTax_jurisdiction(gsOfOut.getString(112) )	;//税管辖区
                param.setHouse_number(gsOfOut.getString(113) )	;//门牌号
                param.setProvince_name(gsOfOut.getString(114) )	;//省份名称
                param.setGw_resource_dev_Engineer(gsOfOut.getString(115) )	;//金风资源开发工程师
                param.setWind_turbine_supply_chain(gsOfOut.getString(116) )	;//是否风机供应链
                param.setContact_person(gsOfOut.getString(117) )	;//联系人
                resultList.add(param);
            }
            for(JTGKKSZSJResultEntity item:resultList){
                try {
                    boolean infoChange = false;
                    //存入二开中间表，根据code  实时更新中间表
                    JTGKKSZSJResultEntity ResultObject = new JTGKKSZSJResultEntity(item);
                    Optional<JTGKKSZSJResultEntity> infoInDBop = jtgkkszsjResultRepository.findById(item.getId());//查询是否存在数据
                    if (!infoInDBop.isPresent()) {
                        infoChange = true;
                        ResultObject.setFlag("A");//A新增，M修改，D删除
                    } else {
                        JTGKKSZSJResultEntity infoInDB=infoInDBop.get();
                        if (Objects.equals(infoInDB.getCategory_code(),ResultObject.getCategory_code()) &
                                Objects.equals(infoInDB.getCategory_name(),ResultObject.getCategory_name()) &
                                Objects.equals(infoInDB.getCode(),ResultObject.getCode()) &
                                Objects.equals(infoInDB.getAssistant_code1(),ResultObject.getAssistant_code1()) &
                                Objects.equals(infoInDB.getAssistant_code2(),ResultObject.getAssistant_code2()) &
                                Objects.equals(infoInDB.getAssistant_code3(),ResultObject.getAssistant_code3()) &
                                Objects.equals(infoInDB.getAssistant_code4(),ResultObject.getAssistant_code4()) &
                                Objects.equals(infoInDB.getMnemonic_code(),ResultObject.getMnemonic_code()) &
                                Objects.equals(infoInDB.getParent_id(),ResultObject.getParent_id()) &
                                Objects.equals(infoInDB.getRecorder_code(),ResultObject.getRecorder_code()) &
                                Objects.equals(infoInDB.getRecorder_name(),ResultObject.getRecorder_name()) &
                                Objects.equals(infoInDB.getRecorder_time(),ResultObject.getRecorder_time()) &
                                Objects.equals(infoInDB.getPretrial_code(),ResultObject.getPretrial_code()) &
                                Objects.equals(infoInDB.getPretrial_name(),ResultObject.getPretrial_name()) &
                                Objects.equals(infoInDB.getPretrial_time(),ResultObject.getPretrial_time()) &
                                Objects.equals(infoInDB.getAuditor_code(),ResultObject.getAuditor_code()) &
                                Objects.equals(infoInDB.getAuditor_name(),ResultObject.getAuditor_name()) &
                                Objects.equals(infoInDB.getAudit_time(),ResultObject.getAudit_time()) &
                                Objects.equals(infoInDB.getAudit_level(),ResultObject.getAudit_level()) &
                                Objects.equals(infoInDB.getAudit_flag(),ResultObject.getAudit_flag()) &
                                Objects.equals(infoInDB.getRetrieve_flag(),ResultObject.getRetrieve_flag()) &
                                Objects.equals(infoInDB.getRetriever_code(),ResultObject.getRetriever_code()) &
                                Objects.equals(infoInDB.getRetriever_name(),ResultObject.getRetriever_name()) &
                                Objects.equals(infoInDB.getRetrieve_time(),ResultObject.getRetrieve_time()) &
                                Objects.equals(infoInDB.getFreeze_flag(),ResultObject.getFreeze_flag()) &
                                Objects.equals(infoInDB.getFreezer_code(),ResultObject.getFreezer_code()) &
                                Objects.equals(infoInDB.getFreezer_name(),ResultObject.getFreezer_name()) &
                                Objects.equals(infoInDB.getFreeze_time(),ResultObject.getFreeze_time()) &
                                Objects.equals(infoInDB.getWorkflow_id(),ResultObject.getWorkflow_id()) &
                                Objects.equals(infoInDB.getRecorder_corp(),ResultObject.getRecorder_corp()) &
                                Objects.equals(infoInDB.getMdm_code(),ResultObject.getMdm_code()) &
                                Objects.equals(infoInDB.getAudit_node_name(),ResultObject.getAudit_node_name()) &
                                Objects.equals(infoInDB.getMaster_data_version(),ResultObject.getMaster_data_version()) &
                                Objects.equals(infoInDB.getPhysical_model_version(),ResultObject.getPhysical_model_version()) &
                                Objects.equals(infoInDB.getFiling_flag(),ResultObject.getFiling_flag()) &
                                Objects.equals(infoInDB.getFiling_user_code(),ResultObject.getFiling_user_code()) &
                                Objects.equals(infoInDB.getFiling_user_name(),ResultObject.getFiling_user_name()) &
                                Objects.equals(infoInDB.getFiling_date(),ResultObject.getFiling_date()) &
                                Objects.equals(infoInDB.getLucence_flag(),ResultObject.getLucence_flag()) &
                                Objects.equals(infoInDB.getLucence_time(),ResultObject.getLucence_time()) &
                                Objects.equals(infoInDB.getSubmit_corp(),ResultObject.getSubmit_corp()) &
                                Objects.equals(infoInDB.getDesc_long(),ResultObject.getDesc_long()) &
                                Objects.equals(infoInDB.getDesc_short(),ResultObject.getDesc_short()) &
                                Objects.equals(infoInDB.getPartner_type(),ResultObject.getPartner_type()) &
                                Objects.equals(infoInDB.getMerchant_code(),ResultObject.getMerchant_code()) &
                                Objects.equals(infoInDB.getMerchant_name(),ResultObject.getMerchant_name()) &
                                Objects.equals(infoInDB.getSearch_term(),ResultObject.getSearch_term()) &
                                Objects.equals(infoInDB.getLanguage(),ResultObject.getLanguage()) &
                                Objects.equals(infoInDB.getTrade_partner(),ResultObject.getTrade_partner()) &
                                Objects.equals(infoInDB.getCountry_region(),ResultObject.getCountry_region()) &
                                Objects.equals(infoInDB.getProvince_code(),ResultObject.getProvince_code()) &
                                Objects.equals(infoInDB.getCity(),ResultObject.getCity()) &
                                Objects.equals(infoInDB.getPostal_code(),ResultObject.getPostal_code()) &
                                Objects.equals(infoInDB.getRemark(),ResultObject.getRemark()) &
                                Objects.equals(infoInDB.getValidate_msg(),ResultObject.getValidate_msg()) &
                                Objects.equals(infoInDB.getModify_group_label_code(),ResultObject.getModify_group_label_code()) &
                                Objects.equals(infoInDB.getCategory_version(),ResultObject.getCategory_version()) &
                                Objects.equals(infoInDB.getMdm_code_createable(),ResultObject.getMdm_code_createable()) &
                                Objects.equals(infoInDB.getLast_modify_recorder_code(),ResultObject.getLast_modify_recorder_code()) &
                                Objects.equals(infoInDB.getLast_modify_recorder_name(),ResultObject.getLast_modify_recorder_name()) &
                                Objects.equals(infoInDB.getLast_modify_record_time(),ResultObject.getLast_modify_record_time()) &
                                Objects.equals(infoInDB.getSubmit_time(),ResultObject.getSubmit_time()) &
                                Objects.equals(infoInDB.getFlow_para(),ResultObject.getFlow_para()) &
                                Objects.equals(infoInDB.getFile_count(),ResultObject.getFile_count()) &
                                Objects.equals(infoInDB.getTemp_save_flag(),ResultObject.getTemp_save_flag()) &
                                Objects.equals(infoInDB.getTask_flag(),ResultObject.getTask_flag()) &
                                Objects.equals(infoInDB.getUuid(),ResultObject.getUuid()) &
                                Objects.equals(infoInDB.getError_msg(),ResultObject.getError_msg()) &
                                Objects.equals(infoInDB.getAuditing_flag(),ResultObject.getAuditing_flag()) &
                                Objects.equals(infoInDB.getRelease_flag(),ResultObject.getRelease_flag()) &
                                Objects.equals(infoInDB.getData_source_flag(),ResultObject.getData_source_flag()) &
                                Objects.equals(infoInDB.getLast_modify_submit_corp(),ResultObject.getLast_modify_submit_corp()) &
                                Objects.equals(infoInDB.getWorkflow(),ResultObject.getWorkflow()) &
                                Objects.equals(infoInDB.getStand_info(),ResultObject.getStand_info()) &
                                Objects.equals(infoInDB.getSecurity_level_code(),ResultObject.getSecurity_level_code()) &
                                Objects.equals(infoInDB.getSubmitter_name(),ResultObject.getSubmitter_name()) &
                                Objects.equals(infoInDB.getAddress(),ResultObject.getAddress()) &
                                Objects.equals(infoInDB.getTax_category(),ResultObject.getTax_category()) &
                                Objects.equals(infoInDB.getPartner_nature(),ResultObject.getPartner_nature()) &
                                Objects.equals(infoInDB.getTax_number(),ResultObject.getTax_number()) &
                                Objects.equals(infoInDB.getBilling_phone(),ResultObject.getBilling_phone()) &
                                Objects.equals(infoInDB.getMobile_phone(),ResultObject.getMobile_phone()) &
                                Objects.equals(infoInDB.getEmail(),ResultObject.getEmail()) &
                                Objects.equals(infoInDB.getFile(),ResultObject.getFile()) &
                                Objects.equals(infoInDB.getFreeze_customer(),ResultObject.getFreeze_customer()) &
                                Objects.equals(infoInDB.getDelete_customer(),ResultObject.getDelete_customer()) &
                                Objects.equals(infoInDB.getFreeze_client_companies(),ResultObject.getFreeze_client_companies()) &
                                Objects.equals(infoInDB.getFreeze_sales_organizations(),ResultObject.getFreeze_sales_organizations()) &
                                Objects.equals(infoInDB.getFreeze_supplier(),ResultObject.getFreeze_supplier()) &
                                Objects.equals(infoInDB.getDelete_supplier(),ResultObject.getDelete_supplier()) &
                                Objects.equals(infoInDB.getFreeze_supplier_companies(),ResultObject.getFreeze_supplier_companies()) &
                                Objects.equals(infoInDB.getFreeze_purchasing_organizations(),ResultObject.getFreeze_purchasing_organizations()) &
                                Objects.equals(infoInDB.getSupply_range(),ResultObject.getSupply_range()) &
                                Objects.equals(infoInDB.getLegal_nature(),ResultObject.getLegal_nature()) &
                                Objects.equals(infoInDB.getCrt_number(),ResultObject.getCrt_number()) &
                                Objects.equals(infoInDB.getIcms_taxpayer(),ResultObject.getIcms_taxpayer()) &
                                Objects.equals(infoInDB.getIndustry_main_type(),ResultObject.getIndustry_main_type()) &
                                Objects.equals(infoInDB.getTax_declaration_type(),ResultObject.getTax_declaration_type()) &
                                Objects.equals(infoInDB.getCustomer_group_abbreviation(),ResultObject.getCustomer_group_abbreviation()) &
                                Objects.equals(infoInDB.getSecond_level_company(),ResultObject.getSecond_level_company()) &
                                Objects.equals(infoInDB.getAffiliated_group(),ResultObject.getAffiliated_group()) &
                                Objects.equals(infoInDB.getEmployee_code(),ResultObject.getEmployee_code()) &
                                Objects.equals(infoInDB.getIs_finance(),ResultObject.getIs_finance()) &
                                Objects.equals(infoInDB.getData_source(),ResultObject.getData_source()) &
                                Objects.equals(infoInDB.getMerchant_type(),ResultObject.getMerchant_type()) &
                                Objects.equals(infoInDB.getSupplier_type(),ResultObject.getSupplier_type()) &
                                Objects.equals(infoInDB.getCustomer_classification(),ResultObject.getCustomer_classification()) &
                                Objects.equals(infoInDB.getTransportation_supplier(),ResultObject.getTransportation_supplier()) &
                                Objects.equals(infoInDB.getIs_portal_supplier(),ResultObject.getIs_portal_supplier()) &
                                Objects.equals(infoInDB.getReason(),ResultObject.getReason()) &
                                Objects.equals(infoInDB.getTax_jurisdiction(),ResultObject.getTax_jurisdiction()) &
                                Objects.equals(infoInDB.getHouse_number(),ResultObject.getHouse_number()) &
                                Objects.equals(infoInDB.getProvince_name(),ResultObject.getProvince_name()) &
                                Objects.equals(infoInDB.getGw_resource_dev_Engineer(),ResultObject.getGw_resource_dev_Engineer()) &
                                Objects.equals(infoInDB.getWind_turbine_supply_chain(),ResultObject.getWind_turbine_supply_chain()) &
                                Objects.equals(infoInDB.getContact_person(),ResultObject.getContact_person())) {
                            // 如果更新时间完全相同，此中情况不再进行更新操作
                        } else {
                            infoChange = true;
                            ResultObject.setFlag("M");
                        }
                    }
                    if (infoChange) {
                        ResultObject.setSksyncstatus("0");
                        ResultObject.setSksyncmsg("");
                        jtgkkszsjResultRepository.save(ResultObject);
                    }
                }catch (Throwable ex){
                    logService.error( name, "获取客商主数据异常：",ex);
                }
            }

        }catch (Throwable ex){
            logService.error( name, "获取客商主数据异常",ex);
        }finally {
            logService.flush();
            //关闭连接
            if(exector!=null)exector.close();
        }
    }

    /**
     * 往来单位-客户、供应商拉取数据 二开表同步司库
     */
    public void automaticGenerationPartnernew()
    {
        String name="automaticGenerationPartnernew";//获取SAP往来单位同步司库";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationPartnernew");
            List<JTGKKSZSJResultEntity> resultList = jtgkkszsjResultRepository.findTop1000BySksyncstatus("0");
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }
            //logService.info( name, "automaticGenerationPartnernew-resultList:{}",JSONSerializer.serialize(resultList));
            List<Map> wlswlb =sqlSession.selectList(Map.class,"select ID,code,name_chs  FROM  BFPARTNERTYPE ");//内部单位
            List<Map> zzjg =sqlSession.selectList(Map.class,"select ID,code,name_chs  FROM  BFMASTERORGANIZATION ");//内部单位
            List<Map> gjids=sqlSession.selectList(Map.class,"select id,code,name_chs,TWOCHARCODE from BFNATIONALANDREGIONALDICT  ");
            List<Map> sfcsids=sqlSession.selectList(Map.class,"select id,code,name_chs from BFADMINDIVISION  ");
            for(JTGKKSZSJResultEntity result:resultList) {
                try {
                    LinkedHashMap<String, Object> orgparam = new LinkedHashMap<String, Object>();
                    Map<String, Object> hashMap = new HashMap<>();
                    Map<String, Object> mapsb = new HashMap<>();
                    mapsb.put("code", result.getMerchant_code());
                    //STATE_ISENABLED  是否可用 1 启用 0停用   STATE_ASYNCDELETESTATUS 异步删除状态 为空或者为0是正常；
                    Map wldw = sqlSession.selectOne(Map.class, "select ID,code,STATE_ISENABLED,STATE_ASYNCDELETESTATUS   from  BFPARTNER  where code= #{code}", mapsb);
                    //若往来单位中存在，判断是否可用
                    String operation="";
                    if (wldw!=null&&!wldw.isEmpty()) {
                        if("0".equals(String.valueOf(wldw.get("STATE_ISENABLED")))&&"0".equals(result.getFreeze_flag())){
                            //若往来单位停用但数据湖里是正常，则启用 freeze_flag 注销标志：0:正常 1:冻结 2:删除标记 3:物理删除
                            operation="enable";
                        }else if("0".equals(String.valueOf(wldw.get("STATE_ISENABLED")))&&!"0".equals(result.getFreeze_flag())){
                            continue;
                        }else if("1".equals(String.valueOf(wldw.get("STATE_ISENABLED")))&&!"0".equals(result.getFreeze_flag())){
                            operation="disable";
                        }else  if("1".equals(String.valueOf(wldw.get("STATE_ISENABLED")))&&"0".equals(result.getFreeze_flag())){
                            operation="modify";
                        }

                    } else if("0".equals(result.getFreeze_flag())){//若往来单位中不存在，则为新增
                        operation="add";
                    }
                    hashMap.put("operation", operation);//操作类型
                    Map<String, Object> orgData = new HashMap<>();
                    Map<String, Object> mapsa = new HashMap<>();
                    mapsa.put("code", result.getMerchant_code());
                    String YHID = sqlSession.selectOne(String.class, "select ID from  BFPARTNER  where code= #{code}", mapsa);
                    orgData.put("ID", YHID);
                    orgData.put("CODE", result.getMerchant_code());//编号
                    orgData.put("NAME", result.getMerchant_name());//名称
                    //贸易伙伴 trade_partner 存在公司代码，内部单位；不存在公司代码，外部单位
                    List<Map> resultLists=zzjg.stream().filter(sqlField -> result.getTrade_partner().equals(sqlField.get("CODE"))).collect(Collectors.toList());
                    if(resultLists!=null&&resultLists.size()>0){
                        //内部单位
//                        查询客户数据时： 过滤merchant_type = 01 或者 merchant_type = 03的数据
//                        查询供应商数据时：过滤 merchant_type = 02 或者merchant_type = 03的数据
                        if("01".equals(result.getMerchant_type())){
                            List<Map> khids=wlswlb.stream().filter(sqlField -> "0102".equals(sqlField.get("CODE"))).collect(Collectors.toList());
                            if(khids!=null&&khids.size()>0){
                                orgData.put("TYPE", khids.get(0).get("ID"));
                            }
                        }else if("02".equals(result.getMerchant_type())){
                            List<Map> khids=wlswlb.stream().filter(sqlField -> "0103".equals(sqlField.get("CODE"))).collect(Collectors.toList());
                            if(khids!=null&&khids.size()>0){
                                orgData.put("TYPE", khids.get(0).get("ID"));
                            }
                        }
                        else if("03".equals(result.getMerchant_type())){
                            List<Map> khids=wlswlb.stream().filter(sqlField -> "0101".equals(sqlField.get("CODE"))).collect(Collectors.toList());
                            if(khids!=null&&khids.size()>0){
                                orgData.put("TYPE", khids.get(0).get("ID"));
                            }
                        }

                    }else{
                        //外部单位
                        if("01".equals(result.getMerchant_type())){
                            List<Map> khids=wlswlb.stream().filter(sqlField -> "0202".equals(sqlField.get("CODE"))).collect(Collectors.toList());
                            if(khids!=null&&khids.size()>0){
                                orgData.put("TYPE", khids.get(0).get("ID"));
                            }
                        }else if("02".equals(result.getMerchant_type())){
                            List<Map> khids=wlswlb.stream().filter(sqlField -> "0203".equals(sqlField.get("CODE"))).collect(Collectors.toList());
                            if(khids!=null&&khids.size()>0){
                                orgData.put("TYPE", khids.get(0).get("ID"));
                            }
                        }
                        else if("03".equals(result.getMerchant_type())){
                            List<Map> khids=wlswlb.stream().filter(sqlField -> "0201".equals(sqlField.get("CODE"))).collect(Collectors.toList());
                            if(khids!=null&&khids.size()>0){
                                orgData.put("TYPE", khids.get(0).get("ID"));
                            }
                        }
                    }
                    String gj="";String s="";String citys="";
                    if(!StringUtil.isNullOrEmpty(result.getCountry_region())) {
                        List<Map> gjs=gjids.stream().filter(sqlField -> result.getCountry_region().equals(sqlField.get("TWOCHARCODE"))).collect(Collectors.toList());
                        if(gjs!=null&&gjs.size()>0){
                            gj= !ObjectUtils.isEmpty(gjs.get(0).get("ID"))?gjs.get(0).get("ID").toString():"" ;
                            orgData.put("COUNTRYORREGION",gjs.get(0).get("ID"));
                        }
                    }
                    if(!StringUtil.isNullOrEmpty(result.getProvince_name())) {
                        List<Map> sf = sfcsids.stream().filter(sqlField -> result.getProvince_name().equals(sqlField.get("NAME_CHS"))).collect(Collectors.toList());
                        if (sf != null && sf.size() > 0) {
                            s = !ObjectUtils.isEmpty(sf.get(0).get("ID")) ? sf.get(0).get("ID").toString() : "";
                            orgData.put("PROVINCE", sf.get(0).get("ID"));
                        } else {
                            sf = sfcsids.stream().filter(sqlField -> (result.getProvince_name() + "省").equals(sqlField.get("NAME_CHS"))).collect(Collectors.toList());
                            if (sf != null && sf.size() > 0) {
                                s = !ObjectUtils.isEmpty(sf.get(0).get("ID")) ? sf.get(0).get("ID").toString() : "";
                                orgData.put("PROVINCE", sf.get(0).get("ID"));
                            } else {
                                sf = sfcsids.stream().filter(sqlField -> (result.getProvince_name() + "市").equals(sqlField.get("NAME_CHS"))).collect(Collectors.toList());
                                if (sf != null && sf.size() > 0) {
                                    s = !ObjectUtils.isEmpty(sf.get(0).get("ID")) ? sf.get(0).get("ID").toString() : "";
                                    orgData.put("PROVINCE", sf.get(0).get("ID"));
                                }
                            }
                        }
                    }
                    if(!StringUtil.isNullOrEmpty(result.getCity())) {
                        List<Map> CS = sfcsids.stream().filter(sqlField -> result.getCity().equals(sqlField.get("NAME_CHS"))).collect(Collectors.toList());
                        if (CS != null && CS.size() > 0) {
                            citys = !ObjectUtils.isEmpty(CS.get(0).get("ID")) ? CS.get(0).get("ID").toString() : "";
                            orgData.put("CITY", CS.get(0).get("ID"));
                        }
                    }
                    orgData.put("BILLSTATUS","2");// "审批状态，0制单、1审批中、2通过、3驳回",
                    Map<String, Object> maps = new HashMap<>();
                    maps.put("name", result.getProvince_name());
                    String dqid=sqlSession.selectOne(String.class,"select id from BFPARTNERAREA where name_chs=#{name}  ORDER BY code  LIMIT 1",maps);
                    orgData.put("AREA",dqid); // 往来单位地区，引用往来单位地区
                    //orgData.put("ORGANIZATIONCODE",result.getTax_number()); // "ORGANIZATIONCODE": "社会信用代码",
                    orgData.put("EMAIL",result.getEmail());// "EMAIL": "电子邮箱",
                    orgData.put("CONTACTINFO",result.getMobile_phone());// "CONTACTINFO": "联系电话",
                    orgData.put("REMARK",result.getRemark()); // "REMARK": "备注",
                    List<Map> addresslist = new ArrayList<>();
                    if (wldw!=null&&!wldw.isEmpty()) {
                        if (!ObjectUtils.isEmpty(wldw.get("ID"))) {
                            Map<String, Object> addmap = new HashMap<>();
                            addmap.put("parentid", wldw.get("ID"));
                            addresslist = sqlSession.selectList(Map.class, "select id  from BFPARTNERADDRESS where partnerid=#{parentid}", addmap);
                        }
                    }
                    ArrayList<Map> ADDADDRESS=new ArrayList<>();
                    Map<String,Object> address=new HashMap<>();

                    if(addresslist!=null&&addresslist.size()>0){
                        //走修改
                        address.put("ID",addresslist.get(0).get("ID"));
                        address.put("COUNTRYORREGION",gj);//国家或地区id，取自国家地区
//                        address.put("ADDRTYPE",);//地址类型id，取自标准代码项
//                        address.put("ISMAIN",);//是否默认：0-否，1-是
                        address.put("STREETNO",result.getAddress());//详细地址
                        address.put("CONTACTNUM",result.getMobile_phone());//联系电话
                        address.put("REMARK",result.getRemark());//备注
                        ADDADDRESS.add(address);
                        orgData.put("MODIFYADDRESS",ADDADDRESS);
                    }else{
                        //走新增
                        address.put("CODE","001");
                        address.put("COUNTRYORREGION",gj);//国家或地区id，取自国家地区
//                        address.put("ADDRTYPE",);//地址类型id，取自标准代码项
//                        address.put("ISMAIN",);//是否默认：0-否，1-是
                        address.put("STREETNO",result.getAddress());//详细地址
                        address.put("CONTACTNUM",result.getMobile_phone());//联系电话
                        address.put("REMARK",result.getRemark());//备注
                        ADDADDRESS.add(address);
                        orgData.put("ADDADDRESS",ADDADDRESS);
                    }
                    hashMap.put("data", orgData);
                    orgparam.put("param", hashMap);//hashmap内容为所需参数
                    String su = "df";
                    String serviceID = "com.inspur.gs.bf.df.commonservice.api.IPartnerService.synchronous";
                    logService.info(name, "automaticGenerationPartnernew-产品入参：" + JSONSerializer.serialize(orgparam));
                    HashMap results = rpcClient.invoke(HashMap.class, serviceID, su, orgparam, null);
                    logService.info(name, "automaticGenerationPartnernew-产品返回：" + results);
                    if ("success".equals(String.valueOf(results.get("status")))) {
                        result.setSksyncstatus("1");
                        result.setSksyncmsg("");
                        jtgkkszsjResultRepository.save(result);
                    } else {
                        result.setSksyncstatus("2");
                        result.setSksyncmsg(String.valueOf(results.get("message")));
                        jtgkkszsjResultRepository.save(result);
                    }
                }catch (Throwable ex){
                    ex.printStackTrace();
                    log.error( "同步客商异常：",ex);
                    logService.error( name, "同步客商异常：",ex);
                }

            }

        }catch (Throwable ex){
            ex.printStackTrace();
            log.error( "同步客商异常：",ex);
            logService.error( name, "同步客商异常",ex);
        }finally {
            // logService.info( name, "结束：");
            logService.flush();
        }
    }

    /**
     * 同业存款拉取数据 dm_event_bd_deposit_details_f_1d
     * @throws Exception
     */
    public void automaticGenerationtyck() throws Exception {
        String name = "automaticGenerationtyck";
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'GFS'");
        if (StringUtil.isNullOrEmpty(dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        RelationalDbExector exector = ExectorFactory.create(dataSourceId);
        try{
            logService.info( name, "automaticPenternew");
            //#region
            List<jtgkjrcktzresultEntity> resultList=new ArrayList<>();
            logService.info( name, "dataSourceId:"+dataSourceId);
            ResultSet gsOfOut = exector.query(" select serial, bank_name, product, product_name, low_amount, hign_amount, configprice," +
                    " currentrate, configdate, enddate, deposit_day, expected_amonut , deposit_state,amount,clint_name,payment_method from dm.dm_event_bd_deposit_details_f_1d   ", null);
            logService.info( name, "ResultSet:" );
            while (gsOfOut.next()) {
                jtgkjrcktzresultEntity param = new jtgkjrcktzresultEntity();
                param.setId(UUID.randomUUID().toString());
                param.setSerial(gsOfOut.getString(1) );
                param.setBank_name(gsOfOut.getString(2) );	;//类别编码
                param.setProduct(gsOfOut.getString(3) );	;//类别名称
                param.setProduct_name(gsOfOut.getString(4) );
                param.setLow_amount(gsOfOut.getString(5) );
                param.setHign_amount(gsOfOut.getString(6) );
                param.setConfigprice(gsOfOut.getString(7) );
                param.setCurrentrate(gsOfOut.getString(8) );
                param.setConfigdate(gsOfOut.getString(9) );
                param.setEnddate(gsOfOut.getString(10) );
                param.setDeposit_day(gsOfOut.getString(11) );
                param.setExpected_amonut(gsOfOut.getString(12) );
                param.setDeposit_state(gsOfOut.getString(13) );
                param.setAmount(gsOfOut.getString(14));
                param.setClint_name(gsOfOut.getString(15));
                param.setPayment_method(gsOfOut.getString(16));
                resultList.add(param);
            }
            jtgkjrcktzresultRepository.deleteAll();
            jtgkjrcktzresultRepository.saveAll(resultList);
            jtgkjrcktzresultRepository.flush();
        }catch (Throwable ex){
            logService.error( name, "获取同业存款主数据异常",ex);
        }finally {
            logService.flush();
            //关闭连接
            if(exector!=null)exector.close();
        }
    }

    /**
     * 汇率拉取数据
     * @throws Exception
     */
    public void automaticGenerationhl() throws Exception {
        String name = "automaticGenerationhl";
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
        if (StringUtil.isNullOrEmpty(dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
//        Long longstart=System.currentTimeMillis();
        RelationalDbExector exector = ExectorFactory.create(dataSourceId);
        try{
            logService.info( name, "automaticGenerationhl");
            //#region
            List<JTGKBZHLRESULTEntity> resultList=new ArrayList<>();
            logService.info( name, "dataSourceId:"+dataSourceId);
            //TODO: 获取汇率数据
            ResultSet gsOfOut = exector.query(" select mandt, version as gw_category, foreign_currency as gw_inputcurrency, type as gw_r_account, company_entity_code as gw_r_entity,year_period as  gw_time,rmb_value as  bpc_signdata from  dm_dim_prv_fm_exchange_rate_model_f_1d  ", null);
            logService.info( name, "ResultSet:" );
            List<JTGKBZHLRESULTEntity> results = jtgkbzhlresultRepository.findAll();
            Set<String> existingIds = results.stream().map(JTGKBZHLRESULTEntity::getId).collect(Collectors.toSet());
            while (gsOfOut.next()) {
                if (existingIds.contains(gsOfOut.getString(1)+gsOfOut.getString(2)+gsOfOut.getString(3)+gsOfOut.getString(4)+gsOfOut.getString(5)+gsOfOut.getString(6)+gsOfOut.getString(7))) {
                    continue;
                }
                JTGKBZHLRESULTEntity param = new JTGKBZHLRESULTEntity();
                param.setId(gsOfOut.getString(1)+gsOfOut.getString(2)+gsOfOut.getString(3)+gsOfOut.getString(4)+gsOfOut.getString(5)+gsOfOut.getString(6)+gsOfOut.getString(7));
                param.setMandt(gsOfOut.getString(1) );
                param.setGw_category(gsOfOut.getString(2) );
                param.setGw_inputcurrency(gsOfOut.getString(3) );
                param.setGw_r_account(gsOfOut.getString(4) );
                param.setGw_r_entity(gsOfOut.getString(5) );
                param.setGw_time(gsOfOut.getString(6) );
                param.setBpc_signdata(gsOfOut.getString(7) );
                resultList.add(param);
            }
            jtgkbzhlresultRepository.saveAll(resultList);
            jtgkbzhlresultRepository.flush();
        }catch (Throwable ex){
            logService.error( name, "获取汇率主数据异常",ex);
        }finally {
            logService.flush();
            //关闭连接
            if(exector!=null)exector.close();
        }
    }

    /**
     * dm_isc_manuf_product_order_d_f_1d 拉取内部订单
     * @throws Exception
     */
    public void automaticNbdd() throws Exception {
        String name = "automaticNbdd";
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
        if (StringUtil.isNullOrEmpty(dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        Long longstart=System.currentTimeMillis();
        int batchSize = 10000; // 每次处理的行数
        int offset = 0;
        RelationalDbExector exector = ExectorFactory.create(dataSourceId);
        ResultSet gsOfOut=null;
        try{
            logService.info( name, "automaticNbdd");
            List<JTGKNBDDResult> infoInDB = jtgknbddResultRepository.findAll();
            Set<String> existingIds = infoInDB.stream().map(JTGKNBDDResult::getId).collect(Collectors.toSet());
            //#region
            List<JTGKNBDDResult> resultList=new ArrayList<>();
            logService.info( name, "dataSourceId:"+dataSourceId);
            while (true){
                exector = ExectorFactory.create(dataSourceId);
                String sql="  select  mandt,order_no,order_type,order_category,order_currency,order_status,modify_date,order_flag,reference_order_no,enterer,applicant,applicant_phone,relevant_person_phone,responsible_person,last_modifier,order_estimated_cost,depart,object_no,profit_center_code,release_date,technical_complete_date,settlement_date,modify_time,create_time,timestamp,`DESC`,long_text,factory1_code,factory2_code,business_range,control_range,collector_code,position,condition_table_use,application,cost_calculation_table,distribute_group,settlement_cost_element,company_code,request_company_code,request_cost_center_code,cost_center_code, " +
                        " basic_list_cost_center,posted_cost_center,maintain_task_work_center,wbs,difference_code,result_analysis_code,functional_scope,object_class,external_order_number,product_process,process_category,responsibilities_relate_factory,regulatory,maintenance_reason,production_line,supplier_account_number,storage_location,quality_nation_alloca_proj,zhtbh from  dm_isc_manuf_product_order_d_f_1d LIMIT "+batchSize+" OFFSET   "+offset ;// Arrays.asList(new Object[]{batchSize, offset})
                  gsOfOut = exector.query(sql,null);
                // 4. 检查是否还有数据
                if (!gsOfOut.next()) {
                    break; // 没有更多的行，退出循环
                }
                do {
                    if (existingIds.contains(gsOfOut.getString(1)+gsOfOut.getString(2)+
                            gsOfOut.getString(3)+gsOfOut.getString(4)+
                            gsOfOut.getString(5)+gsOfOut.getString(7)+gsOfOut.getString(23))) {
                        continue;
                    }
                    JTGKNBDDResult param = new JTGKNBDDResult();
                    param.setMandt(gsOfOut.getString(1));//
                    param.setOrder_no(gsOfOut.getString(2));//
                    param.setOrder_type(gsOfOut.getString(3));//
                    param.setOrder_category(gsOfOut.getString(4));//
                    param.setOrder_currency(gsOfOut.getString(5));//
                    param.setOrder_status(gsOfOut.getString(6));//
                    param.setModify_date(gsOfOut.getString(7));//
                    param.setOrder_flag(gsOfOut.getString(8));//
                    param.setReference_order_no(gsOfOut.getString(9));//
                    param.setEnterer(gsOfOut.getString(10));//
                    param.setApplicant(gsOfOut.getString(11));//
                    param.setApplicant_phone(gsOfOut.getString(12));//
                    param.setRelevant_person_phone(gsOfOut.getString(13));//
                    param.setResponsible_person(gsOfOut.getString(14));//
                    param.setLast_modifier(gsOfOut.getString(15));//
                    param.setOrder_estimated_cost(gsOfOut.getString(16));//
                    param.setDepart(gsOfOut.getString(17));//
                    param.setObject_no(gsOfOut.getString(18));//
                    param.setProfit_center_code(gsOfOut.getString(19));//
                    param.setRelease_date(gsOfOut.getString(20));//
                    param.setTechnical_complete_date(gsOfOut.getString(21));//
                    param.setSettlement_date(gsOfOut.getString(22));//
                    param.setModify_time(gsOfOut.getString(23));//
                    param.setCreate_time(gsOfOut.getString(24));//
                    param.setTimestamp(gsOfOut.getString(25));//
                    param.setDescc(gsOfOut.getString(26));//
                    param.setLong_text(gsOfOut.getString(27));//
                    param.setFactory1_code(gsOfOut.getString(28));//
                    param.setFactory2_code(gsOfOut.getString(29));//
                    param.setBusiness_range(gsOfOut.getString(30));//
                    param.setControl_range(gsOfOut.getString(31));//
                    param.setCollector_code(gsOfOut.getString(32));//
                    param.setPosition(gsOfOut.getString(33));//
                    param.setCondition_table_use(gsOfOut.getString(34));//
                    param.setApplication(gsOfOut.getString(35));//
                    param.setCost_calculation_table(gsOfOut.getString(36));//
                    param.setDistribute_group(gsOfOut.getString(37));//
                    param.setSettlement_cost_element(gsOfOut.getString(38));//
                    param.setCompany_code(gsOfOut.getString(39));//
                    param.setRequest_company_code(gsOfOut.getString(40));//
                    param.setRequest_cost_center_code(gsOfOut.getString(41));//
                    param.setCost_center_code(gsOfOut.getString(42));//
                    param.setBasic_list_cost_center(gsOfOut.getString(43));//
                    param.setPosted_cost_center(gsOfOut.getString(44));//
                    param.setMaintain_task_work_center(gsOfOut.getString(45));//
                    param.setWbs(gsOfOut.getString(46));//
                    param.setDifference_code(gsOfOut.getString(47));//
                    param.setResult_analysis_code(gsOfOut.getString(48));//
                    param.setFunctional_scope(gsOfOut.getString(49));//
                    param.setObject_class(gsOfOut.getString(50));//
                    param.setExternal_order_number(gsOfOut.getString(51));//
                    param.setProduct_process(gsOfOut.getString(52));//
                    param.setProcess_category(gsOfOut.getString(53));//
                    param.setResponsibilities_relate_factory(gsOfOut.getString(54));//
                    param.setRegulatory(gsOfOut.getString(55));//
                    param.setMaintenance_reason(gsOfOut.getString(56));//
                    param.setProduction_line(gsOfOut.getString(57));//
                    param.setSupplier_account_number(gsOfOut.getString(58));//
                    param.setStorage_location(gsOfOut.getString(59));//
                    param.setQuality_nation_alloca_proj(gsOfOut.getString(60));//
                    param.setZhtbh(gsOfOut.getString(61));//
                    param.setId((param.getMandt()+param.getOrder_no()+param.getOrder_type()+param.getOrder_category()+param.getOrder_currency()+param.getModify_date()+param.getModify_time()));
                    resultList.add(param);
                }while (gsOfOut.next());
                jtgknbddResultRepository.saveAll(resultList);
                jtgknbddResultRepository.flush();
                offset += batchSize; // 更新偏移量
                //关闭连接
                if(gsOfOut!=null)gsOfOut.close(); // 关闭结果集
                if(exector!=null)exector.close();// 关闭 statement
            }

        }catch (Throwable ex)
        {
            logService.error( name, "获取内部订单主数据异常",ex);
        }
        finally {
            logService.flush();
//            //关闭连接
//            if(gsOfOut!=null)gsOfOut.close(); // 关闭结果集
//            if(exector!=null)exector.close();// 关闭 statement
        }

    }

    /**
     *   拉取WBS项目
     * @throws Exception
     */
    public void automaticWBSxm() throws Exception {
        String name = "automaticWBSxm";
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
        if (StringUtil.isNullOrEmpty(dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        Long longstart=System.currentTimeMillis();
        int batchSize = 10000; // 每次处理的行数
        int offset = 0;
        RelationalDbExector exector = ExectorFactory.create(dataSourceId);
        ResultSet gsOfOut=null;
        try{
            logService.info( name, "automaticWBSxm");
            //#region
            List<JTGKWBSxmResult> resultList=new ArrayList<>();
            logService.info( name, "dataSourceId:"+dataSourceId);
            List<JTGKWBSxmResult> resultListDB= jtgkWBSxmResultRepository.findAll();
            Set<String> existingIds = resultListDB.stream().map(JTGKWBSxmResult::getId).collect(Collectors.toSet());
//            for(int i=0;i<=1000;i++)
            while (true) {
                exector = ExectorFactory.create(dataSourceId);
                String sql=" select mandt,wbs_factor,wbs_element,wbs_short_desc,wbs_short_upper_desc,wbs_identifier,wbs_company_code,wbs_control_range,wbs_currency,t_wbs_element,standard_wbs,project_in_code,project_type,project_hierarchy_level,profit_center_code,actual_posting_cost_center_code,function_range,request_company_code,factory_code,object_no,object_class,applicant_code,applicant_name,responsible_person_code,responsible_person_name,creator_name,create_date,modifier_name,modify_date,plan_element_flag,account_allocation_flag,wbs_project_summary_flag,keyword_id,ps_progress from dm_dim_pub_wbs_element_master_data_f_1d   LIMIT "+batchSize+" OFFSET  "+offset ;//"+batchSize+" OFFSET   "+offset ;
                  gsOfOut = exector.query(sql, null);
                if (!gsOfOut.next()) {
                    break; // 没有更多的行，退出循环
                }
               do {
                    if (existingIds.contains(gsOfOut.getString(1)+gsOfOut.getString(2)+gsOfOut.getString(3)+gsOfOut.getString(29))) {
                        continue; // 跳过当前循环
                    }
                    JTGKWBSxmResult param = new JTGKWBSxmResult();
                    param.setMandt(gsOfOut.getString(1));//	集团
                    param.setWbs_factor(gsOfOut.getString(2));//	WBS要素
                    param.setWbs_element(gsOfOut.getString(3));//	WBS元素
                    param.setWbs_short_desc(gsOfOut.getString(4));//	WBS短描述
                    param.setWbs_short_upper_desc(gsOfOut.getString(5));//	WBS短描述大写
                    param.setWbs_identifier(gsOfOut.getString(6));//	WBS元素简明标识
                    param.setWbs_company_code(gsOfOut.getString(7));//	WBS元素的公司代码
                    param.setWbs_control_range(gsOfOut.getString(8));//	WBS元素的控制范围
                    param.setWbs_currency(gsOfOut.getString(9));//	WBS元素货币
                    param.setT_wbs_element(gsOfOut.getString(10));//	统计
                    param.setStandard_wbs(gsOfOut.getString(11));//	标准WBS:
                    param.setProject_in_code(gsOfOut.getString(12));//	项目编码（内码）
                    param.setProject_type(gsOfOut.getString(13));//	项目类型
                    param.setProject_hierarchy_level(gsOfOut.getString(14));//	项目层次的等级
                    param.setProfit_center_code(gsOfOut.getString(15));//	利润中心编码
                    param.setActual_posting_cost_center_code(gsOfOut.getString(16));//	实际过账成本的成本中心编码
                    param.setFunction_range(gsOfOut.getString(17));//	功能范围
                    param.setRequest_company_code(gsOfOut.getString(18));//	请求公司代码
                    param.setFactory_code(gsOfOut.getString(19));//	工厂编码
                    param.setObject_no(gsOfOut.getString(20));//	对象号
                    param.setObject_class(gsOfOut.getString(21));//	对象类
                    param.setApplicant_code(gsOfOut.getString(22));//	申请人编码
                    param.setApplicant_name(gsOfOut.getString(23));//	申请人姓名
                    param.setResponsible_person_code(gsOfOut.getString(24));//	负责人编号（项目经理）
                    param.setResponsible_person_name(gsOfOut.getString(25));//	负责人姓名（项目管理者）
                    param.setCreator_name(gsOfOut.getString(26));//	创建对象的人员名称
                    param.setCreate_date(gsOfOut.getString(27));//	记录创建日期
                    param.setModifier_name(gsOfOut.getString(28));//	更改对象用户的名称
                    param.setModify_date(gsOfOut.getString(29));//	对象最后更改日期
                    param.setPlan_element_flag(gsOfOut.getString(30));//	标志：计划元素
                    param.setAccount_allocation_flag(gsOfOut.getString(31));//	标志：科目分配元素
                    param.setWbs_project_summary_flag(gsOfOut.getString(32));//	标识：用于项目汇总的
                    param.setKeyword_id(gsOfOut.getString(33));//	关键词
                    param.setPs_progress(gsOfOut.getString(34));//	PS
                    param.setId(param.getMandt()+param.getWbs_factor()+param.getWbs_element()+param.getModify_date());
                    resultList.add(param);
                }while (gsOfOut.next());
                jtgkWBSxmResultRepository.saveAll(resultList);
                jtgkWBSxmResultRepository.flush();
                offset += batchSize; // 更新偏移量
                //关闭连接
                if(gsOfOut!=null)gsOfOut.close(); // 关闭结果集
                if(exector!=null)exector.close();// 关闭 statement
            }

        }catch (Throwable ex)
        {
            logService.error( name, "获取WBS项目主数据异常",ex);
        }
        finally {
            logService.flush();
            //关闭连接
            if(gsOfOut!=null)gsOfOut.close(); // 关闭结果集
            if(exector!=null)exector.close();// 关闭 statement
        }

    }

}
