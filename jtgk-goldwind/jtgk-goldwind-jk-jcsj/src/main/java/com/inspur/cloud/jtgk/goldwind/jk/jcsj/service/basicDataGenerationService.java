package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonStreamContext;
import com.fasterxml.jackson.core.type.TypeReference;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao.JxhResultObjectDto;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.*;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub.JTGKStringUtil;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository.*;
import com.inspur.edp.data.connectors.exec.ExectorFactory;
import com.inspur.edp.data.connectors.exec.RelationalDbExector;
import com.inspur.edp.internalservice.api.proxy.InternalServiceProxy;
import com.inspur.fastdweb.util.StringUtil;
import com.inspur.gs.tm.tmfnd.fsjspub.api.dto.FsspResultRet;
import com.inspur.gs.tm.tmfnd.fsjspub.core.entity.TMProcessBillBase;
import com.inspur.gs.tm.tmfnd.fsjspub.core.service.TMTmFndFspfAndFsspCommon;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.common.JSONSerializer;
import io.iec.edp.caf.commons.utils.CollectionUtils;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.rpc.api.service.RpcClient;
import io.iec.edp.caf.scheduler.api.logger.RtfSchedulerLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import com.inspur.fastdweb.core.FastdwebSqlSession;
import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import org.springframework.util.StringUtils;
import io.iec.edp.caf.sysmanager.api.data.user.User;
import io.iec.edp.caf.sysmanager.api.data.user.UserExtend;
import io.iec.edp.caf.sysmanager.api.data.user.UserState;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
public class basicDataGenerationService {

    @Autowired
    private RpcClient rpcClient;
    @Autowired
    private   FastdwebSqlSession sqlSession;
    @Autowired
    private BMResultObjectRepository bmResultObjectRepository;
    @Autowired
    private OaSpResultObjectrRepository oaSpResultObjectrRepository;
    @Autowired
    private EmployeeResultObjectRepository  employeeResultObjectRepository;
    @Autowired
    private YHResultObjectRepository yhResultObjectRepository;
    @Autowired
    private WLDWResultObjectRepository wldwResultObjectRepository;
    @Autowired
    private WLDWYHZHResultObjectRepository wldwyhzhResultObjectRepository;
    @Autowired
    private XJLLXMResultObjectRepository xjllxmResultObjectRepository;

    @Autowired
    private SFBBResultObjectRepository sfbbResultObjectRepository;
    @Autowired
    private LogService logService;
    @Autowired
    private ZHKMYEResultObjectRepository zhkmyeResultObjectRepository;
    @Autowired
    private JxhResultObjectEntityRepository jxhResultObjectEntityRepository;
    @Autowired
    private GSResultObjectRepository gsResultObjectRepository;

    @Autowired
    private  EmployeeQlResultObjectRepository employeeQlResultObjectRepository;

    @Autowired
    private BankTransCationDetailsekRepository bankTransCationDetailsekRepository;

    @Autowired
    private JwHqZhyeResultObjectRepository jwHqZhyeResultObjectRepository;

    @Autowired
    private JwMgZhyeResultObjectRepository jwMgZhyeResultObjectRepository;


    SimpleDateFormat sdfymd = new SimpleDateFormat("yyyyMMdd");



    /*
    获取公司主数据  组织机构 SAP数据湖  存入中间表
     */
   public void automaticOrganization() throws Exception {
       String name="automaticOrganization";//SAP获取公司主数据";
       logService.init(name);
       String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
       if (StringUtil.isNullOrEmpty( dataSourceId)) {
           logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
           return;
       }
       RelationalDbExector exector = ExectorFactory.create( dataSourceId);
       try{
           //#region
           logService.info( name, "automaticOrganization-dataSourceId:"+ dataSourceId);
           ResultSet gsOfOut = exector.query(" select BUKRS,FULL_NAME,SPRAS,LAND1,WAERS,STCD5,PSTLZ,ORT01,JXGKBUSI,adrnr BUSI,ZZGXHS,ZZJXGKLW,ZZ_INVALID,is_discountspras IS_DISCOUNT from  v_sap_gsc_company_main_i_rt ", null);
           logService.info( name, "automaticOrganization-ResultSet:" );
           List<GSResultObjectDto> resultList=new ArrayList<>();
           while (gsOfOut.next()) {
               GSResultObjectDto entity = new GSResultObjectDto();
               entity.setBUKRS(gsOfOut.getString(1) );//公司代码
               entity.setFULL_NAME(gsOfOut.getString(2) );//公司代码或公司的名称
               entity.setSPRAS(gsOfOut.getString(3) );//语言代码
               entity.setLAND1(gsOfOut.getString(4) );//国家/地区代码
               entity.setWAERS(gsOfOut.getString(5) );//货币码
               entity.setSTCD5(gsOfOut.getString(6) );//税号5
               entity.setPSTLZ(gsOfOut.getString(7) );//邮编
              // entity.setString(gsOfOut.getString(8) );//地址
               entity.setORT01(gsOfOut.getString(8) );//城市
               entity.setJXGKBUSI(gsOfOut.getString(9) );//管理主体
               entity.setBUSI(gsOfOut.getString(10) );//地址  因为SAP表中没有业务单元这个字段，现用来存储地址
               entity.setZZGXHS(gsOfOut.getString(11) );//是否纳入共享
               entity.setZZJXGKLW(gsOfOut.getString(12) );//交叉管理例外
               entity.setZZ_INVALID(gsOfOut.getString(13) );//是否注销
               entity.setIS_DISCOUNT(gsOfOut.getString(14) );//计算抵扣
               resultList.add(entity);
           }
           logService.info( name, "automaticOrganization-ResultSet11:" );
           for(GSResultObjectDto item:resultList){
               try {
                   boolean infoChange = false;
                   //存入二开中间表，根据code  实时更新中间表
                   GSResultObject gsResultObject = new GSResultObject(item);
                   GSResultObject infoInDB = gsResultObjectRepository.findByBUKRS(item.getBUKRS());//查询是否有相同编号的
                   if (infoInDB == null) {
                       infoChange = true;
                       gsResultObject.setFlag("D".equals(gsResultObject.getFlag()) ? "D" : "A");//A新增，M修改，D删除
                   } else {
                       if (Objects.equals(infoInDB.getBUKRS(), gsResultObject.getBUKRS()) && Objects.equals(infoInDB.getFULL_NAME(), gsResultObject.getFULL_NAME())
                               && Objects.equals(infoInDB.getSTCD5(), gsResultObject.getSTCD5()) && Objects.equals(infoInDB.getLAND1(), gsResultObject.getLAND1())
                               && Objects.equals(infoInDB.getWAERS(), gsResultObject.getWAERS()) && Objects.equals(infoInDB.getZZJXGKLW(), gsResultObject.getZZJXGKLW())
                               && Objects.equals(infoInDB.getPSTLZ(), gsResultObject.getPSTLZ()) && Objects.equals(infoInDB.getJXGKBUSI(), gsResultObject.getJXGKBUSI())
                               && Objects.equals(infoInDB.getZZ_INVALID(), gsResultObject.getZZ_INVALID())) {//如果基本信息一致，不再更新

                       } else if (Objects.equals(infoInDB.getBUKRS(), gsResultObject.getBUKRS()) && Objects.equals(infoInDB.getFULL_NAME(), gsResultObject.getFULL_NAME())
                               && Objects.equals(infoInDB.getSTCD5(), gsResultObject.getSTCD5()) && Objects.equals(infoInDB.getLAND1(), gsResultObject.getLAND1())
                               && Objects.equals(infoInDB.getWAERS(), gsResultObject.getWAERS()) && Objects.equals(infoInDB.getZZJXGKLW(), gsResultObject.getZZJXGKLW())
                               && Objects.equals(infoInDB.getPSTLZ(), gsResultObject.getPSTLZ()) && !Objects.equals(infoInDB.getJXGKBUSI(), gsResultObject.getJXGKBUSI())
                               && Objects.equals(infoInDB.getZZ_INVALID(), gsResultObject.getZZ_INVALID())) {//如果基本信息一致，级别不一致
                           infoChange = true;
                           gsResultObject.setFlag("D".equals(gsResultObject.getFlag()) ? "D" : "M1");

                       } else {
                           infoChange = true;
                           gsResultObject.setFlag("D".equals(gsResultObject.getFlag()) ? "D" : "M");
                       }
                   }
                   if (infoChange) {
                       gsResultObject.setSksyncstatus("0");
                       gsResultObject.setSksyncmsg("");
                       gsResultObjectRepository.save(gsResultObject);
                   }
               }catch (Throwable ex){
                   logService.error( name, "获取公司异常：",ex);
               }
           }


           //#endregion

       }catch (Throwable ex){
           logService.error( name, "获取公司异常：",ex);
       }
       finally{
           //关闭连接
           if(exector!=null)exector.close();
          // logService.info( name, "结束：");
           logService.flush();
       }
   }
   /*
   获取公司主数据  组织机构 SAP数据湖  二开中间表同步司库  司库从SAP获取的公司按所属二级业务单元写入到组织机构
    */
    public void automaticGenerationOrganization()
    {
        String name="automaticGenerationOrganization";//公司主数据同步司库";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationOrganization");
            // 创建一个Calendar实例
            Calendar calendar = Calendar.getInstance();
            // 将月份减1，获取上个月
            calendar.add(Calendar.MONTH, -1);
            // 获取上个月的日期
            Date lastMonthDate = calendar.getTime();
            // 格式化上个月的日期
            String kjqj = new SimpleDateFormat("yyyy.MM").format(lastMonthDate);
            String sfbb="0";
            //#region
            List<GSResultObject> resultList = gsResultObjectRepository.findTop1000BySksyncstatus("0");
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }
//            logService.info( name, "automaticGenerationOrganization-resultList:{}",JSONSerializer.serialize(resultList));
            for(GSResultObject result:resultList){
                try {
                    logService.info(name,"result入参:"+result);
                    LinkedHashMap<String, Object> orgparam = new LinkedHashMap<String, Object>();
                    Map<String, Object> hashMap = new HashMap<>();
                    String jgid="";
                    Map<String,Object> JGID = sqlSession.selectOne(Map.class, "select ID,STATE_ISENABLED from  BFMASTERORGANIZATION  where code= #{code} ", result.getBUKRS());
                    //若是停用 且 操作类型不是删除  这块走修改？需要试试
                    if(JGID!=null&&"0".equals(String.valueOf(JGID.get("STATE_ISENABLED")))&& !"X".equals(result.getZZ_INVALID()) ){
                        //若组织机构已停用，但SAP为注销，则启用
                        result.setFlag("QY");
                        jgid= ObjectUtils.isEmpty(JGID.get("ID"))?"":JGID.get("ID").toString();
                    }
                    else if (JGID!=null&& !"X".equals(result.getZZ_INVALID()) && !"M1".equals(result.getFlag())) {
                        //若组织机构表中存在，且不是删除与组织层级发生改变 则修改
                        result.setFlag("M");
                        jgid= ObjectUtils.isEmpty(JGID.get("ID"))?"":JGID.get("ID").toString();
                    } else if (JGID!=null&& "X".equals(result.getZZ_INVALID()) && !"M1".equals(result.getFlag())) {
                        //若组织机构表中存在，且是删除但组织层级未发生改变 则停用
                        result.setFlag("D");
                        jgid= ObjectUtils.isEmpty(JGID.get("ID"))?"":JGID.get("ID").toString();
                    } else if (!"X".equals(result.getZZ_INVALID())&& !"M1".equals(result.getFlag())) {//不是删除且组织机构表中不存在则新增
                        result.setFlag("A");
                    } else if ("X".equals(result.getZZ_INVALID())) {//删除且组织机构表不存在不处理
                        continue;
                    }
                    if("1385".equals(result.getBUKRS())){
                        logService.error(name,"1385和6778是转签业务，1385不用");
                        continue;
                    }
//                if("1".equals(sfty)&&"M".equals(result.getType())){}
                    hashMap.put("operation", "A".equals(result.getFlag()) ? "add" : ("M".equals(result.getFlag()) ? "modifymaster" :("D".equals(result.getFlag())?"disablemaster":("QY".equals(result.getFlag())?"enablemaster":"disablemaster"))));//操作类型
                    Map<String, Object> orgData = new HashMap<>();
                    orgData.put("ID", jgid);
                    orgData.put("CODE", result.getBUKRS());//编码
                    orgData.put("NAME", result.getFULL_NAME());//名称
                    Map<String, Object> maps = new HashMap<>();
                    maps.put("kjqj",kjqj);
                    maps.put("zzbh",result.getBUKRS());
                    String sfbbsl=sqlSession.selectOne(String.class,"select count(1) from JTGKSFBBRESULT where c_time=#{kjqj} " +
                            " and c_entity=#{zzbh} ",maps);
                    if(new BigDecimal(sfbbsl).compareTo(BigDecimal.ZERO)>0){
                        sfbb="1";
                    }
                    String PARENTID = "root";
                    if (!StringUtils.isEmpty(result.getJXGKBUSI())) {//管报的管理主体，但是因为存在交叉的情况，最后以管理主体为准。
                        PARENTID = sqlSession.selectOne(String.class, "select id from  BFMASTERORGANIZATION  where code= #{code}", result.getJXGKBUSI());
                    }
                    if (StringUtil.isNullOrEmpty(PARENTID)) {
                        logService.info(name, "未找到上级，等下一批执行");
                        continue;
                    }
                    orgData.put("PARENTID", PARENTID);// 父节点ID，新增必填，顶级节点为"root"
                    orgData.put("ORGTYPE", "Company"); //组织层级，新增必填，Company: 单位; Department: 部门
                    orgData.put("ABBREVIATION", "");//简称
                    // orgData.put("ISLEGALPERSON","0");//是否法人（是否独立核算），0: 是; 1: 否
                    orgData.put("LEGALREPRESENTATIVE", "");//法定代表人
                    orgData.put("NAMEFORTAX", "");//纳税人名称
                    if(!StringUtils.isEmpty(result.getSTCD5())&&"内部转外部".equals(result.getSTCD5())){
                        orgData.put("ORGANIZATIONCODE","");//社会信用代码
                    }else {
                        orgData.put("ORGANIZATIONCODE", result.getSTCD5());//社会信用代码
                    }

                    String gj = sqlSession.selectOne(String.class, "select id from BFNATIONALANDREGIONALDICT where TWOCHARCODE= #{code}  ORDER BY code  LIMIT 1 ", result.getLAND1());
                    orgData.put("COUNTRYORREGION", gj);//国家或地区，引用国家地区字典
                    orgData.put("TEL", "");//联系方式
//                orgData.put("INCHARGE",result.getOrgDirectorLeader());//分管领导，引用行政人员
//                orgData.put("HEAD",result.getOrgDirectorLeader());//负责人
                    String bz = sqlSession.selectOne(String.class, "select id from BFCURRENCY where code= #{code}", result.getWAERS());
                    orgData.put("CURRENCY", bz);//货币
                    orgData.put("ACCOUNTINGORG", "0");//是否核算组织
                    orgData.put("TAXORG", "0");//是否税务组织
                    orgData.put("BUDGETORG", "1");//是否预算组织
                    orgData.put("PURCHASINGORG", "0");//是否采购组织
                    orgData.put("SALESORG", "0");//是否销售组织
                    orgData.put("INVENTORYORG", "0");//是否库存组织
                    orgData.put("EXTVARCHAR1", result.getZZJXGKLW());//是否纳入共享  可以按照这个判断付款流程走共享资金还是走业务单元的资金
                    orgData.put("POSTALCODEOFREGPLACE", result.getPSTLZ());
                    orgData.put("ORGANIZATIONCODE", result.getSTCD5());//纳税识别号  社会信用代码
                    orgData.put("EXTVARCHAR2", result.getBUSI());//业务单元
                    orgData.put("EXTVARCHAR3", sfbb);//是否并表
//                orgData.put("EXTVARCHAR4",result.getZZJXGKLW());//机构编码
//                orgData.put("EXTVARCHAR5",result.getZZ_INVALID());//是否注销
//                orgData.put("EXTVARCHAR6",result.getIS_DISCOUNT());//计算抵扣

                    if (!"M1".equals(result.getFlag())) {
                        hashMap.put("data", orgData);
                        orgparam.put("param", hashMap);//hashmap内容为所需参数
                        String su = "df";
                        String serviceID = "com.inspur.gs.bf.df.commonservice.api.IOrganizationService.synchronous";
                        logService.info(name, "automaticGenerationOrganization-产品入参：" + JSONSerializer.serialize(orgparam));
                        HashMap results = rpcClient.invoke(HashMap.class, serviceID, su, orgparam, null);
                        logService.info(name, "automaticGenerationOrganization-产品返回：" + results);
                        if ("success".equals(String.valueOf(results.get("status")))) {
                            result.setSksyncstatus("1");
                            result.setSksyncmsg("");
                            if(!"QY".equals(result.getFlag())) {
                                gsResultObjectRepository.save(result);
                            }
                        } else {
                            result.setSksyncstatus("2");
                            result.setSksyncmsg(String.valueOf(results.get("message")));
                            gsResultObjectRepository.save(result);
                        }
                    } else {
                        logService.info(name, "组织机构层级改变");
                        // 获取目标层级id
                        if (PARENTID != null) {
                            updateGSCOrgLevel(String.valueOf(JGID.get("ID")), PARENTID, name);
                        } else {
                            logService.error(name, "组织机构层级调整失败，上级组织不存在：" + PARENTID);
                            // logService.error(logId, "组织机构层级调整失败，上级组织不存在：" + JSON.toJSONString(mdmOrg));
                        }
                    }
                }catch (Throwable ex){
                    logService.error( name, "获取公司异常：",ex);
                }
            }
            //#endregion

        }catch (Throwable ex){
            logService.error( name, "获取公司异常",ex);
        }finally {
           // logService.info( name, "结束：");
            logService.flush();
        }
    }

    /*
       获取部门基础数据  组织机构 CA  存入二开中间表
       测试 { "username": "gsc", "password": "123456" }
      生产 { "username": "gsc", "password": "4x79|l9F[1)41jajz6g" }
    *
     */
    public void automaticDepartment()
    {
        String name="automaticDepartment";//获取CA部门基础数据";
        logService.init(name);
        try{
            logService.info( name, "automaticDepartment");
            //#region
            List<BMResultObject> resultList=new ArrayList<>();
            BMResultObject result = null;
            Map<String,Object> map = sqlSession.selectOne(Map.class, "SELECT KEYVALUE,URL FROM JTGKINTERFACECONFIG WHERE CODE = 'CABM'");
            String url=String.valueOf(map.get("URL"));//更新日期，不传此参数，则获取全量数据   测试环境先用这个  std/123456        scc/123456   geam/123456
//            String s = "pitims:ACvpfiw2A9GgB-HNwPfY";
            Map<String, Object> maps = new HashMap<>();
            if(!"http:///gateway/api-ca/rest/organizationApi/getOrgInfos".equals(url)){
                maps.put("date", new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
            }else{
                maps.put("date", "");
            }

            String mapls = JSONSerializer.serialize(maps);

            logService.info(name,"maps:"+mapls);
            String encode = Base64.encode(String.valueOf(map.get("KEYVALUE")));
            logService.info( name, "map:"+map);
            logService.info( name, "encode:"+encode);
            HttpResponse response = HttpRequest.post(url).header("Content-Type", "application/json").header("Authorization", "Basic " + encode).body(mapls).timeout(50000)
                    .execute();
//            logService.info( name, "response:"+response);
            if (response.getStatus() == 200) {
                JSONObject resultJson = JSONObject.parseObject(response.body());
                logService.info( name, "CA部门接口状态值:{}", response.getStatus());
                if (resultJson != null && "000".equals(resultJson.getString("code")))
                {
                     resultList = JSON.parseArray(resultJson.getString("resultOrgList"), BMResultObject.class);
                }
//                logService.info( name, "resultList:{}", JSONSerializer.serialize(resultList));
                if(!CollectionUtils.isEmpty(resultList)&&resultList.size()>0){
                    //存入二开中间表
                    for (BMResultObject item : resultList) {
                        try {
                            boolean infoChange = false;
                            //存入二开中间表，根据code  实时更新中间表
                            BMResultObject bmResultObject = new BMResultObject(item);
                            BMResultObject infoInDB = bmResultObjectRepository.findByOrgCode(item.getOrgCode());//查询是否有相同编号的
                            if (infoInDB == null) {
                                infoChange = true;
                                bmResultObject.setType("D".equals(bmResultObject.getType()) ? "D" : "A");//A新增，M修改，D删除
                            } else {
                                if (Objects.equals(infoInDB.getOrgCode(), bmResultObject.getOrgCode()) && Objects.equals(infoInDB.getParentOrgCode(), bmResultObject.getParentOrgCode())
                                        && Objects.equals(infoInDB.getJglb(), bmResultObject.getJglb()) && Objects.equals(infoInDB.getOrgName(), bmResultObject.getOrgName())
                                        && Objects.equals(infoInDB.getOrgDirectorLeader(), bmResultObject.getOrgDirectorLeader()) && Objects.equals(infoInDB.getType(), bmResultObject.getType())
                                ) {//如果基本信息完全相同，则不更新

                                } else if (Objects.equals(infoInDB.getOrgCode(), bmResultObject.getOrgCode()) && (!Objects.equals(infoInDB.getParentOrgCode(), bmResultObject.getParentOrgCode())
                                        || !Objects.equals(infoInDB.getJglb(), bmResultObject.getJglb())) && Objects.equals(infoInDB.getOrgName(), bmResultObject.getOrgName())
                                        && Objects.equals(infoInDB.getOrgDirectorLeader(), bmResultObject.getOrgDirectorLeader())) {//TODO:部门级别改变
                                    infoChange = true;
                                    bmResultObject.setType("M1");

                                } else {
                                    infoChange = true;
                                    bmResultObject.setType("D".equals(bmResultObject.getType()) ? "D" : "M");
                                }
                            }
                            if (infoChange) {
                                bmResultObject.setSksyncstatus("0");
                                bmResultObject.setSksyncmsg("");
                                bmResultObjectRepository.save(bmResultObject);
                            }
                        }catch (Throwable ex){
                            logService.error( name, "获取部门异常：",ex);
                        }
                    }
                }

            }

            //#endregion

        }catch (Throwable ex){
            logService.error( name, "获取部门异常",ex);
        }finally {
            //logService.info( name, "结束：");
            logService.flush();
        }
    }

     /*
     获取部门基础数据  组织机构 CA  存入二开中间表后，同步司库
        //司库从CA获取的组织写入到组织机构二级业务单元的下属各级部门
        //    "jglb": "80"   组
        //"jglb": "60"  部门
        //"jglb": "70"  科室
        //"jglb": "40"  中心
      */
    public void automaticGenerationDepartment(){
        String name="automaticGenerationDepartment";//获取CA部门基础数据同步司库";
        logService.init(name);
        try {
            //#region
            logService.info( name, "automaticGenerationDepartment");
            //根据机构类别排序，先存入中心的再存入部门....防止找不到上级
            List<BMResultObject> resultList = bmResultObjectRepository.findTop500BySksyncstatusOrderByJglb("0");
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }
            logService.info( name, "automaticGenerationDepartment-resultList:{}",JSONSerializer.serialize(resultList));
            for(BMResultObject result:resultList){
                try{
                LinkedHashMap<String,Object> orgparam=new LinkedHashMap<String,Object>();
                Map<String,Object> hashMap = new HashMap<>();
                String JGID="";
                String sfty="";
                String type=result.getType();
                String PARENTID ="";
                String ORGTYPE="Department";
                String code=result.getOrgCode();
                if(!StringUtil.isNullOrEmpty(result.getParentOrgCode())) {
                    //if ("40".equals(result.getJglb())) {//这里一定要把几个金融业务单元 先创建好 TODO:
                    PARENTID = sqlSession.selectOne(String.class, "select BFMASTERORGANIZATION.id from  BFMASTERORGANIZATION left join JTGKSAPCAYWDY on BFMASTERORGANIZATION.code=JTGKSAPCAYWDY.SAP  where JTGKSAPCAYWDY.CA= #{code}", result.getParentOrgCode());
                    // }
                    if("30".equals(result.getJglb())){
                        ORGTYPE="Company";
                        code=sqlSession.selectOne(String.class,"select SAP from JTGKSAPCAYWDY where CA= #{code} ",result.getOrgCode());
                    }
                    if(StringUtil.isNullOrEmpty(PARENTID)){
                        PARENTID = sqlSession.selectOne(String.class, "select BFMASTERORGANIZATION.id from  BFMASTERORGANIZATION   where code= #{code}", result.getParentOrgCode());
                    }
                }
                Map<String,Object> map=sqlSession.selectOne(Map.class,"select ID,STATE_ISENABLED from  BFMASTERORGANIZATION  where code= #{code}",code);
                 if(map!=null ) {
                     JGID = String.valueOf(map.get("ID"));
                     sfty = String.valueOf(map.get("STATE_ISENABLED"));
                 }
                 //若产品组织机构中不存在，则为新增
                if(StringUtil.isNullOrEmpty(JGID)&&"D".equals(result.getType())){
                    continue;
                }else if(!StringUtil.isNullOrEmpty(JGID)&&"0".equals(sfty)&&!"D".equals(result.getType())&&!"M1".equals(result.getType())){
                    //若存在组织机构，但停用，且ca未停用
                    result.setType("QY");
                }else if(!StringUtil.isNullOrEmpty(JGID)&&"1".equals(sfty)&&"D".equals(result.getType())){
                    //若存在组织机构，但启用，且ca停用
                    result.setType("D");
                }else if(!StringUtil.isNullOrEmpty(JGID)&&"0".equals(sfty)&&"D".equals(result.getType())){
                    //若存在组织机构，但停用，且ca停用
                    continue;
                }else   if(!"D".equals(result.getType())&&!"M1".equals(result.getType())){
                    if (StringUtil.isNullOrEmpty(JGID)) {
                        result.setType("A");
                    } else {
                        result.setType("M");
                    }
                }

//                    add：新增组织机构-同步行政组织；
//                    modify：修改行政组织；
//                    disable：停用行政组织；
//                    enable：启用行政组织；
//                    modifymaster：修改组织机构；
//                    disablemaster：停用组织机构；
//                    enablemaster：启用组织机构；
//                    delete：删除组织机构；
                hashMap.put("operation","A".equals(result.getType())?"add":("M".equals(result.getType())?"modifymaster":("D".equals(result.getType())?"disablemaster":("QY".equals(result.getType())?"enablemaster":"disablemaster"))));//操作类型
                Map<String,Object> orgData = new HashMap<>();


                if(StringUtil.isNullOrEmpty(PARENTID)){
                    logService.info( name, "未找到上级，等下一批执行");
                    continue;
                }
                if(!"M1".equals(type)) {
                    orgData.put("ID", JGID);
                    orgData.put("CODE", code);//编码
                    orgData.put("NAME", result.getOrgName());//名称
                    orgData.put("PARENTID", PARENTID);// 父节点ID，新增必填，顶级节点为"root"
                    orgData.put("ORGTYPE", ORGTYPE); //组织层级，新增必填，Company: 单位; Department: 部门
                    orgData.put("ABBREVIATION", result.getOrgName());//简称
                    orgData.put("ISLEGALPERSON", "1");//是否法人（是否独立核算），0: 是; 1: 否
                    orgData.put("LEGALREPRESENTATIVE", "");//法定代表人
                    orgData.put("NAMEFORTAX", "");//纳税人名称
                    orgData.put("ORGANIZATIONCODE", "");//社会信用代码
                    orgData.put("COUNTRYORREGION", "");//国家或地区，引用国家地区字典
                    orgData.put("TEL", "");//联系方式
                    if(!StringUtils.isEmpty(result.getOrgPostCode())) {
                        String ry = sqlSession.selectOne(String.class, "select id from  BFEMPLOYEE   where code= #{code}", result.getOrgPostCode().replaceAll("^0+", ""));
                        orgData.put("INCHARGE", ry);//分管领导，引用行政人员
                    }
                    if(!StringUtils.isEmpty(result.getOrgDirectorLeader())) {
                        String fzr = sqlSession.selectOne(String.class, "select id from  BFEMPLOYEE   where code= #{code}", result.getOrgDirectorLeader());
                        orgData.put("HEAD", fzr);//负责人
                    }
                    orgData.put("CURRENCY", "");
                    orgData.put("ACCOUNTINGORG", "0");//是否核算组织
                    orgData.put("TAXORG", "0");//是否税务组织
                    orgData.put("BUDGETORG",   "1" );//是否预算组织
                    orgData.put("PURCHASINGORG", "0");//是否采购组织
                    orgData.put("SALESORG", "0");//是否销售组织
                    orgData.put("INVENTORYORG", "0");//是否库存组织
                    orgData.put("EXTVARCHAR2", result.getJglb());//机构类别
                    orgData.put("EXTVARCHAR4", result.getJgbm());//机构编码

                    hashMap.put("data", orgData);
                    orgparam.put("param", hashMap);//hashmap内容为所需参数
                    String su = "df";
                    String serviceID = "com.inspur.gs.bf.df.commonservice.api.IOrganizationService.synchronous";
                    logService.info(name, "automaticGenerationDepartment-产品入参：" + JSONSerializer.serialize(orgparam));
                    HashMap results = rpcClient.invoke(HashMap.class, serviceID, su, orgparam, null);
                    logService.info(name, "automaticGenerationDepartment-产品返回：" + results);
                    if ("success".equals(String.valueOf(results.get("status")))) {
                        result.setSksyncstatus("1");
                        result.setSksyncmsg("");
                        if(!"QY".equals(result.getType())) {
                            bmResultObjectRepository.save(result);
                        }
                    } else {
                        result.setSksyncstatus("2");
                        result.setSksyncmsg(String.valueOf(results.get("message")));
                        bmResultObjectRepository.save(result);
                    }
                }else{//组织层级改变
                    logService.info( name,"组织机构层级改变" );
                    // 获取目标层级id
                    if (PARENTID != null) {
                        Boolean bln= updateGSCOrgLevel(JGID,PARENTID,name);
                        if(bln){
                            result.setSksyncstatus("1");
                            result.setSksyncmsg("");
                            bmResultObjectRepository.save(result);
                        }
                    } else {
                        logService.error( name,"组织机构层级调整失败，上级组织不存在：" + PARENTID);
                        result.setSksyncstatus("2");
                        result.setSksyncmsg("组织机构层级调整失败，上级组织不存在：" + PARENTID);
                        bmResultObjectRepository.save(result);
                        // logService.error(logId, "组织机构层级调整失败，上级组织不存在：" + JSON.toJSONString(mdmOrg));
                    }
                }
                }catch (Throwable ex){
                    logService.error( name, "获取公司异常：",ex);
                }
            }
            //#endregion


        }
        catch (Throwable ex){
            logService.error( name, "获取部门异常",ex);
        }finally {
            //logService.info( name, "结束：");
            logService.flush();
        }
    }
    private boolean updateGSCOrgLevel(String orgId, String targetParentOrgId, String name) {
        Map<String, Object> orgParam = new HashMap<>();
        orgParam.put("operation", "ToLowerLevel");
        orgParam.put("data", orgId);
        orgParam.put("target", targetParentOrgId);
        try {
            LinkedHashMap param1 = new LinkedHashMap<>();
            param1.put("param", orgParam);
            String serviceId = "com.inspur.gs.bf.df.commonservice.api.IOrganizationService.adjustMasterOrganization";
            Map<String, Object> result =
                    SpringBeanUtils.getBean(RpcClient.class).invoke(Map.class, serviceId, "df", param1, null);
            logService.info(name,"------>>>组织调整结果：{}", result);
            if (result != null && result.get("inspur_id") != null) {
                return true;
            } else {
                // logService.error(logId,
                // "组织机构层级调整失败,请求入参：" + JSON.toJSONString(orgParam) + ",返回值：" + JSON.toJSONString(result));
                return false;
            }
        } catch (Throwable e) {
            logService.error(name,"------>>>组织调整失败", e);
            Throwable cause = e.getCause();
            while (cause != null && cause.getCause() != null) {
                cause = cause.getCause();
            }
            // logService.error(logId, "组织机构层级调整失败,请求入参：" + JSON.toJSONString(orgParam) + ",返回值：" + cause.getMessage());
            return false;
        }
    }


    /*
    获取银行主数据 SAP数据湖 存入二开表
     */
    public void automaticBank() throws Exception {
        String name="automaticBank";//获取SAP银行主数据";
        logService.init(name);
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
        if (StringUtil.isNullOrEmpty(dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        RelationalDbExector exector = ExectorFactory.create(dataSourceId);
        try{
            logService.info( name, "automaticBank");
            //#region
            ResultSet gsOfOut = exector.query(" select  v_sap_gsc_bank_main_i_rt.MANDT,v_sap_gsc_bank_main_i_rt.BANKS,v_sap_gsc_bank_main_i_rt.BANKL,v_sap_gsc_bank_main_i_rt.BANKA,v_sap_gsc_bank_main_i_rt.PROVZ,v_sap_gsc_bank_main_i_rt.STRAS,v_sap_gsc_bank_main_i_rt.ORT01,v_sap_gsc_bank_main_i_rt.SWIFT,v_sap_gsc_bank_main_i_rt.LOEVM,v_sap_gsc_bank_main_i_rt.BNKLZ,v_sap_gsc_bank_main_i_rt.BRNCH,bnka.zzbranch_code,bnka.zzrouting_code from v_sap_gsc_bank_main_i_rt left join bnka on v_sap_gsc_bank_main_i_rt.bankl=bnka.bankl and v_sap_gsc_bank_main_i_rt.banks= bnka.banks  and bnka.loevm!='X'  ", null);
            logService.info( name, "ResultSet:" );
            List<YHResultObject> yhresults=new ArrayList<>();
            List<YHResultObjectDto> resultList=new ArrayList<>();
            while (gsOfOut.next()) {
                YHResultObjectDto entity = new YHResultObjectDto();
                entity.setMANDT(gsOfOut.getString(1) );//集团
                entity.setBANKS(gsOfOut.getString(2) );//银行国家代码
                entity.setBANKL(!StringUtil.isNullOrEmpty(gsOfOut.getString(3))?gsOfOut.getString(3).replace(" ",""):""  );//银行代码
                entity.setBANKA(gsOfOut.getString(4) );//银行名称
                entity.setPROVZ(gsOfOut.getString(5) );//地区（省/自治区/直辖市、市、县）
                entity.setSTRAS(gsOfOut.getString(6) );//街道和房屋号
                entity.setORT01(gsOfOut.getString(7) );//城市
                entity.setSWIFT(gsOfOut.getString(8) );//国际付款的
                entity.setLOEVM(gsOfOut.getString(9) );//删除标识符
                entity.setBNKLZ(gsOfOut.getString(10) );//银行编号
                entity.setBRNCH(gsOfOut.getString(11) );//分行
                entity.setZzbranch_code( !StringUtil.isNullOrEmpty(gsOfOut.getString(12))?gsOfOut.getString(12):""  );//银行编号
                entity.setZzrouting_code(!StringUtil.isNullOrEmpty(gsOfOut.getString(13))?gsOfOut.getString(13):"" );//分行
                resultList.add(entity);
            }
            logService.info( name, "ResultSet11:" );

            for(YHResultObjectDto item:resultList){
                try {
                    boolean infoChange = false;
                    //存入二开中间表，根据code  实时更新中间表
                    YHResultObject yhResultObject = new YHResultObject(item);
                    YHResultObject infoInDB = yhResultObjectRepository.findByBANKLAndBANKS(item.getBANKL(),item.getBANKS());//查询是否有相同编号的
                    if (infoInDB == null) {
                        infoChange = true;
                        yhResultObject.setFlag("D".equals(yhResultObject.getFlag()) ? "D" : "A");//A新增，M修改，D删除
                    } else {
                        if (Objects.equals(infoInDB.getBANKS(), yhResultObject.getBANKS()) &Objects.equals(infoInDB.getBANKL(), yhResultObject.getBANKL())
                                & Objects.equals(infoInDB.getBANKA(), yhResultObject.getBANKA()) &  Objects.equals(infoInDB.getPROVZ(), yhResultObject.getPROVZ())
                                &  Objects.equals(infoInDB.getSTRAS(), yhResultObject.getSTRAS())&  Objects.equals(infoInDB.getORT01(), yhResultObject.getORT01())
                                & Objects.equals(infoInDB.getSWIFT(), yhResultObject.getSWIFT())& Objects.equals(infoInDB.getLOEVM(), yhResultObject.getLOEVM())
                                &Objects.equals(infoInDB.getBNKLZ(), yhResultObject.getBNKLZ()) & Objects.equals(infoInDB.getBRNCH(), yhResultObject.getBRNCH())
                                &Objects.equals(infoInDB.getZzbranch_code(), yhResultObject.getZzbranch_code()) & Objects.equals(infoInDB.getZzrouting_code(), yhResultObject.getZzrouting_code())) {
                            // 如果更新时间完全相同，此中情况不再进行更新操作
                        }else {
                            infoChange = true;
                            yhResultObject.setFlag("D".equals(yhResultObject.getFlag()) ? "D" : "M");
                        }
                    }
                    if (infoChange) {
                        yhResultObject.setSksyncstatus("0");
                        yhResultObject.setSksyncmsg("");
                        yhResultObjectRepository.save(yhResultObject);
                    }
                }catch (Throwable ex){
                    logService.error( name, "获取银行异常：",ex);
                }
            }
        }catch (Throwable ex){
            logService.error( name, "获取银行异常",ex);
        }finally {
            //关闭连接
            if(exector!=null)exector.close();
           // logService.info( name, "结束：");
            logService.flush();
        }
    }
     /*
     获取银行主数据 SAP数据湖 二开中间表 同步司库
      */
    public void automaticGenerationBank(){
        String name="automaticGenerationBank";//获取SAP银行主数据同步司库";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationBank");
            List<YHResultObject> resultList = yhResultObjectRepository.findTop1000BySksyncstatus("0");
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }
            //logService.info( name, "automaticGenerationBank-resultList:{}",JSONSerializer.serialize(resultList));

            for(YHResultObject result:resultList) {
                try {
                    LinkedHashMap<String, Object> orgparam = new LinkedHashMap<String, Object>();
                    Map<String, Object> hashMap = new HashMap<>();
                    String yhid = result.getId().replace("-","").replace("_","")+result.getBANKS()+
                            (StringUtil.isNullOrEmpty(result.getZzbranch_code())?"":result.getZzbranch_code())+
                            (StringUtil.isNullOrEmpty(result.getZzrouting_code())?"":result.getZzrouting_code());
                    //用联行号查询银行定义
                    HashMap<String, Object> paramInfo = new HashMap<>();
                    paramInfo.put("LHH", StringUtil.isNullOrEmpty(result.getBNKLZ())?"":result.getBNKLZ());
                    paramInfo.put("SWIFT",  StringUtil.isNullOrEmpty(result.getSWIFT())?"":result.getSWIFT());
                    paramInfo.put("BANKA", result.getBRNCH());
                    paramInfo.put("PLAINTEXT2", StringUtil.isNullOrEmpty(result.getBANKL())?"":result.getBANKL());//银行代码+国家唯一
                    paramInfo.put("PLAINTEXT4", StringUtil.isNullOrEmpty(result.getZzbranch_code())?"":result.getZzbranch_code());//境外
                    paramInfo.put("PLAINTEXT3", StringUtil.isNullOrEmpty(result.getZzrouting_code())?"":result.getZzrouting_code());//境外
                    paramInfo.put("PLAINTEXT5", result.getBANKS());
                    paramInfo.put("ID", yhid);//境外
                    logService.info(name, "paramInfo: " + paramInfo);

                    String whe=" and PLAINTEXT2=#{PLAINTEXT2} and PLAINTEXT5=#{BANKS} ";
                    String where = "  AND  COALESCE(TRIM(BANKIDENTIFIER),'AAAAA')= #{LHH} and ( name_chs=#{BANKA} or name_en=#{BANKA}) ";
                    String wher = "  AND  COALESCE(TRIM(BANKIDENTIFIER),'AAAAA')= #{LHH}  ";
                    String aa=" and ID=#{ID} ";
                    if (!"CN".equals(result.getBANKS())) {
                        where = "  AND  COALESCE(TRIM(SWIFTCODE),'AAAAA') =#{SWIFT}  and ( name_chs=#{BANKA} or name_en=#{BANKA}) and PLAINTEXT3=#{PLAINTEXT3}  and PLAINTEXT4=#{PLAINTEXT4} ";
                        wher = "  AND  COALESCE(TRIM(SWIFTCODE),'AAAAA') =#{SWIFT}  and PLAINTEXT3=#{PLAINTEXT3}  and PLAINTEXT4=#{PLAINTEXT4}  ";
                    }
                    logService.info(name, "select ID,BANKTYPE,STATE_ISENABLED,PLAINTEXT2 from  BFBANK  where   1=1  " + where);
                    //第一步，银行代码+国家是否存在
                    Map<String, Object> yhmap = sqlSession.selectOne(Map.class, "select ID,BANKTYPE,STATE_ISENABLED,PLAINTEXT2 from  BFBANK  where   1=1  " + whe, paramInfo);
                    if (yhmap == null) {
                        //第二步 联行号或swift 是否存在  名称是否存在
                        yhmap = sqlSession.selectOne(Map.class, "select ID,BANKTYPE,STATE_ISENABLED,PLAINTEXT2 from  BFBANK  where   1=1  " + where, paramInfo);
                    }
                    if (yhmap == null) {
                        //第三步  联行号或swift 是否存在
                        yhmap = sqlSession.selectOne(Map.class, "select ID,BANKTYPE,STATE_ISENABLED,PLAINTEXT2 from  BFBANK  where   1=1  " + wher, paramInfo);
                    }
                    if (yhmap == null) {
                        //第三步  主键 是否存在
                        yhmap = sqlSession.selectOne(Map.class, "select ID,BANKTYPE,STATE_ISENABLED,PLAINTEXT2 from  BFBANK  where   1=1  " + aa, paramInfo);
                    }

                    Map<String, Object> maps = new HashMap<>();
                    maps.put("NAME", result.getBANKA());
                    String yhhb = sqlSession.selectOne(String.class, " select id from BFBANKTYPE where NAME_CHS=#{NAME} ", maps);
                    String ifqy="1";
                    String yhdm="";
                    if (yhmap != null) {
                        yhid = StringUtil.isNullOrEmpty(String.valueOf(yhmap.get("ID"))) ? yhid.replaceAll("\\s",  "") : String.valueOf(yhmap.get("ID"));
                        yhhb = StringUtil.isNullOrEmpty(String.valueOf(yhmap.get("BANKTYPE"))) ? yhhb : String.valueOf(yhmap.get("BANKTYPE"));
                        ifqy=StringUtil.isNullOrEmpty(String.valueOf(yhmap.get("STATE_ISENABLED"))) ? ifqy : String.valueOf(yhmap.get("STATE_ISENABLED"));
                        yhdm=StringUtil.isNullOrEmpty(String.valueOf(yhmap.get("PLAINTEXT2"))) ? yhdm : String.valueOf(yhmap.get("PLAINTEXT2"));
                        //如果ifqy 查到的银行停用了，但是loevm 不为X 则需要启用
//                        if(yhdm!=result.getBANKL()&&!"X".equals(result.getLOEVM())){
//                            result.setFlag("A");
//                        }
//                        else
                            if("0".equals(ifqy)&&!"X".equals(result.getLOEVM())){
                            result.setFlag("M1");
                        }else if("1".equals(ifqy)&&"X".equals(result.getLOEVM())){
                            result.setFlag("D");
                        }else {
                            if ("X".equals(result.getLOEVM())) {
                                result.setFlag("D");
                            } else if ("A".equals(result.getFlag())) {
                                result.setFlag("M");
                            }
                        }
                    } else if ("X".equals(result.getLOEVM())) {
                        result.setSksyncstatus("2");
                        result.setSksyncmsg("删除");
                        yhResultObjectRepository.save(result);
                        continue;
                    } else {
                        result.setFlag("A");
                    }
                    if (StringUtil.isNullOrEmpty(yhhb)) {
                        Map<String, Object> mapss = new HashMap<>();
                        mapss.put("NAME", result.getBRNCH());
                        yhhb =  sqlSession.selectOne(String.class, " select id from BFBANKTYPE where NAME_CHS=#{NAME} ", maps);
                    }
                    if (StringUtil.isNullOrEmpty(yhhb)) {
                        yhhb = sqlSession.selectOne(String.class, " select id from BFBANKTYPE where code='WFL' ");
                    }
                    hashMap.put("operation", "A".equals(result.getFlag()) ? "add" : ("M".equals(result.getFlag()) ? "modify" : ("D".equals(result.getFlag()) ? "disable" : ("M1".equals(result.getFlag()) ? "enable" : "disable"))));//操作类型 delete
                    Map<String, Object> orgData = new HashMap<>();

                    orgData.put("ID", yhid);//TODO:重点关注下这个银行代码   新增时银行id 使用这个银行代码
                    String code=result.getBNKLZ();
                    if(StringUtil.isNullOrEmpty(code)){
                        code= result.getBANKL();
                    }
                    orgData.put("CODE", code);//银行编号
                    String yhmc=result.getBANKA();//默认银行名称
                    if(!StringUtil.isNullOrEmpty(result.getBRNCH())&&!"NA".equals(result.getBRNCH())&&!"00".equals(result.getBRNCH())){
                        yhmc=result.getBRNCH();
                    }
                    orgData.put("NAME", yhmc);//名称
                    orgData.put("BANKTYPE", yhhb); // 银行行别，新增必填  银行行别目前SAP中没有，现方案是创建一个未分类的银行行别；银行行别没有的 默认未分类 增加预警，通知手工维护； TODO:
                    orgData.put("ISBANKSRELATED", ""); // 银企直联，0：否、1：是
                    orgData.put("ISINNER", ""); // 内部银行，0：否、1：是
                    orgData.put("ISBRANCH", ""); // 分支机构，0：否、1：是
                    orgData.put("BANKIDENTIFIER", result.getBNKLZ()); // 联行号
                    orgData.put("SWIFTCODE", result.getSWIFT()); // SWIFT CODE
                    orgData.put("TEL", ""); // 主业务电话
                    orgData.put("PARTNERSHIPLEVEL", ""); // 合作紧密程度ID，引用合作紧密程度
                    orgData.put("BANKLEVEL", ""); // 银行层级ID，引用银行层级
                    orgData.put("SUPERIORBANK", ""); // 上级银行ID，引用银行定义
                    Map<String, Object> mapsb = new HashMap<>();
                    mapsb.put("CODE", result.getBANKS());
                    String gj = sqlSession.selectOne(String.class, "select id from  BFNATIONALANDREGIONALDICT where  twocharcode= #{CODE}  ORDER BY code  LIMIT 1  ", mapsb);
//                    if (!StringUtils.isEmpty(gj)) {
//                    logService.info(name,"GJ"+gj);
                    orgData.put("COUNTRYORREGION", gj); // 国家地区ID，引用国家地区字典
//                    }
//                String dq=sqlSession.selectOne(String.class,"select id from  BFNATIONALANDREGIONALDICT where  twocharcode= #{CODE} ORDER BY code  LIMIT 1 ",result.getPROVZ());
//                if(!StringUtils.isEmpty(dq)) {
//                    orgData.put("OFREGION", dq); // 所属地区ID，引用行政区划
//                }
                    orgData.put("PROVINCE", ""); // 省
                    orgData.put("CITY", result.getORT01()); // 市
                    orgData.put("REMARK", ""); // 备注
                    orgData.put("PLAINTEXT2", result.getBANKL()); // SAP银行代码
                    orgData.put("PLAINTEXT3", result.getZzrouting_code());
                    orgData.put("PLAINTEXT4", result.getZzbranch_code());
                    hashMap.put("data", orgData);
                    orgparam.put("param", hashMap);//hashmap内容为所需参数
                    String su = "df";
                    String serviceID = "com.inspur.gs.bf.df.commonservice.api.IBankService.synchronous";
                    logService.info(name, "automaticGenerationBank-产品入参：" + JSONSerializer.serialize(orgparam));
                    HashMap results = rpcClient.invoke(HashMap.class, serviceID, su, orgparam, null);
                    logService.info(name, "automaticGenerationBank-产品返回：" + results);
                    if ("success".equals(String.valueOf(results.get("status")))) {
                        result.setSksyncstatus("1");
                        result.setSksyncmsg("");
                        if(!"M1".equals(result.getFlag())){
                        yhResultObjectRepository.save(result);}
                    } else {
                        result.setSksyncstatus("2");
                        result.setSksyncmsg(String.valueOf(results.get("message")));
                        yhResultObjectRepository.save(result);
                    }
                }catch (Throwable ex){
                    logService.error( name, "银行主数据同步异常：",ex);
                }
            }
        }catch (Throwable ex){
            logService.error( name, "银行主数据同步司库异常",ex);
        }finally {
            //logService.info( name, "结束：");
            logService.flush();
        }
    }
     /*
     获取往来单位 贸易伙伴 SAP数据湖 存入二开中间表 作废
      */
    public void automaticPartner() throws Exception {
        String name="automaticPartner";//获取SAP往来单位";
        logService.init(name);
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
        if (StringUtil.isNullOrEmpty(dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        RelationalDbExector exector = ExectorFactory.create(dataSourceId);
        try{
            logService.info( name, "automaticPartner");
            //#region
            List<WLDWResultObject> yhresults=new ArrayList<>();
            List<WLDWResultObjectDto> resultList=new ArrayList<>();
            logService.info( name, "automaticPartner-dataSourceId:"+dataSourceId);
            ResultSet gsOfOut = exector.query(" select PARTNER, NAME_ORG,VBUND  from  v_sap_gsc_trading_partner_i_rt ", null);
            logService.info( name, "automaticPartner-ResultSet:" );
            while (gsOfOut.next()) {
                WLDWResultObjectDto entity = new WLDWResultObjectDto();
                entity.setPARTNER(gsOfOut.getString(1) );
                entity.setNAME_ORG1(gsOfOut.getString(2) );
                entity.setVBUND(gsOfOut.getString(3) );
                resultList.add(entity);
            }
            logService.info( name, "automaticPartner-ResultSet11:" );
            for(WLDWResultObjectDto item:resultList){
                try {
                    boolean infoChange = false;
                    //存入二开中间表，根据code  实时更新中间表
                    WLDWResultObject wldwResultObject = new WLDWResultObject(item);
                    WLDWResultObject infoInDB = wldwResultObjectRepository.findByPARTNER(item.getPARTNER());//查询是否有相同编号的
                    if (infoInDB == null) {
                        infoChange = true;
                        wldwResultObject.setFlag("D".equals(wldwResultObject.getFlag()) ? "D" : "A");//A新增，M修改，D删除
                    } else {
                        if (Objects.equals(infoInDB.getPARTNER(), wldwResultObject.getPARTNER()) && Objects.equals(infoInDB.getNAME_ORG1(), wldwResultObject.getNAME_ORG1())
                                && Objects.equals(infoInDB.getVBUND(), wldwResultObject.getVBUND())) {
                            // 如果更新时间完全相同，此中情况不再进行更新操作
                        } else {
                            infoChange = true;
                            wldwResultObject.setFlag("D".equals(wldwResultObject.getFlag()) ? "D" : "M");
                        }
                    }
                    if (infoChange) {
                        wldwResultObject.setSksyncstatus("0");
                        wldwResultObject.setSksyncmsg("");
                        wldwResultObjectRepository.save(wldwResultObject);
                    }
                }catch (Throwable ex){
                    logService.error( name, "获取贸易伙伴异常：",ex);
                }
            }

        }catch (Throwable ex){
            logService.error( name, "获取贸易伙伴异常",ex);
        }finally {
            //logService.info( name, "结束：");
            logService.flush();
            //关闭连接
            if(exector!=null)exector.close();
        }
    }
     /*
     获取往来单位 贸易伙伴 SAP数据湖 二开中间表 同步司库 作废
      */
    public void automaticGenerationPartner()
    {
        String name="automaticGenerationPartner";//获取SAP往来单位同步司库";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationPartner");
            List<WLDWResultObject> resultList = wldwResultObjectRepository.findTop100BySksyncstatus("0");
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }
            logService.info( name, "automaticGenerationPartner-resultList:{}",JSONSerializer.serialize(resultList));
            String nbdw =sqlSession.selectOne(String.class,"select ID  FROM  BFPARTNERTYPE where code= '01'");//内部单位
            String wbdw =sqlSession.selectOne(String.class,"select ID  FROM  BFPARTNERTYPE where code= '02'");//外部单位
            for(WLDWResultObject result:resultList) {
                try {
                    LinkedHashMap<String, Object> orgparam = new LinkedHashMap<String, Object>();
                    Map<String, Object> hashMap = new HashMap<>();
                    Map<String, Object> mapsb = new HashMap<>();
                    mapsb.put("code", result.getPARTNER());
                    String myhb = sqlSession.selectOne(String.class, "select ID  from  BFPARTNER  where code= #{code}", mapsb);
                    //若往来单位中不存在，则为新增
                    if (StringUtil.isNullOrEmpty(myhb)) {
                        result.setFlag("A");
                    } else {
                        result.setFlag("M");
                    }
                    hashMap.put("operation", "A".equals(result.getFlag()) ? "add" : ("M".equals(result.getFlag()) ? "modify" : "disable"));//操作类型
                    Map<String, Object> orgData = new HashMap<>();
                    Map<String, Object> mapsa = new HashMap<>();
                    mapsa.put("code", result.getPARTNER());
                    String YHID = sqlSession.selectOne(String.class, "select ID from  BFPARTNER  where code= #{code}", mapsa);
                    orgData.put("ID", YHID);
                    orgData.put("CODE", result.getPARTNER());//编号
                    orgData.put("NAME", result.getNAME_ORG1());//名称
                    if (!"1".equals(result.getVBUND()) && !"2".equals(result.getVBUND()) && !"3".equals(result.getVBUND()) && !"4".equals(result.getVBUND())) {
                        orgData.put("TYPE", nbdw);// 往来单位类别ID，引用往来单位类别，新增必填
                    } else {
                        orgData.put("TYPE", wbdw); // 往来单位类别ID，引用往来单位类别，新增必填
                    }
//                orgData.put("AREA",""); // 往来单位地区，引用往来单位地区
//                orgData.put("DOMAINTYPE",""); // 使用范围，新增默认公有，0公有1组织私有2组织范围，若为1则所属行政组织必填，若为2则所属组织范围必填
//                orgData.put("OWNERORG",""); // 所属行政组织ID，引用行政组织
//                orgData.put("OWNERDOMAIN",result.getBNKLZ()); // 所属组织范围id，引用组织范围
//                orgData.put("COUNTRYORREGION",result.getSWIFT()); // 国家或地区，引用国家地区字典
//                orgData.put("DEFAULTCURRENCYID",""); // 默认交易币种，引用币种
//                orgData.put("SUPERIORPARTNER",""); // 上级往来单位，引用往来单位
//                orgData.put("ACTUALCONTROLLERPARTNER",""); // 实际控制单位，引用往来单位
//                orgData.put("INDUSTRY",""); // 行业，引用标准代码
//                orgData.put("SHORTNAME",result.getBANKS()); // 简称
//                orgData.put("UNITNATURE",result.getPROVZ()); // 单位性质，引用标准代码单位性质
//                orgData.put("PROVINCE",""); // 省
//                orgData.put("CITY",result.getORT01()); // 市
//                orgData.put("REMARK", ""); // 备注
                    hashMap.put("data", orgData);
                    orgparam.put("param", hashMap);//hashmap内容为所需参数
                    String su = "df";
                    String serviceID = "com.inspur.gs.bf.df.commonservice.api.IPartnerService.synchronous";
                    logService.info(name, "automaticGenerationPartner-产品入参：" + JSONSerializer.serialize(orgparam));
                    HashMap results = rpcClient.invoke(HashMap.class, serviceID, su, orgparam, null);
                    logService.info(name, "automaticGenerationPartner-产品返回：" + results);
                    if ("success".equals(String.valueOf(results.get("status")))) {
                        result.setSksyncstatus("1");
                        result.setSksyncmsg("");
                        wldwResultObjectRepository.save(result);
                    } else {
                        result.setSksyncstatus("2");
                        result.setSksyncmsg(String.valueOf(results.get("message")));
                        wldwResultObjectRepository.save(result);
                    }
                }catch (Throwable ex){
                    logService.error( name, "获取贸易伙伴异常：",ex);
                }

            }

        }catch (Throwable ex){
            logService.error( name, "获取贸易伙伴异常",ex);
        }finally {
           // logService.info( name, "结束：");
            logService.flush();
        }
    }
     /*
     获取往来单位银行账号 贸易伙伴支付信息 SAP数据湖 存入二开表
      */
    public void automaticPartnerzh() throws Exception {
        String name="automaticPartnerzh";//获取SAP往来单位银行账号";
        logService.init(name);
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
        if (StringUtil.isNullOrEmpty(dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        RelationalDbExector exector = ExectorFactory.create(dataSourceId);
        try{
            logService.info( name, "automaticPartnerzh");
            //#region
            List<WLDWYHZHResultObject> yhresults=new ArrayList<>();
            List<WLDWYHZHResultObjectDto> resultList=new ArrayList<>();
            logService.info( name, "automaticPartnerzh-dataSourceId:"+dataSourceId);
            ResultSet gsOfOut = exector.query(" select KOINH,BANKL,BANKS,LIFNR,NAME1,EBPP_ACCNAME from  v_sap_gsc_supplier_pays_i_rt ", null);
            logService.info( name, "automaticPartnerzh-ResultSet:" );
            while (gsOfOut.next()) {
                WLDWYHZHResultObjectDto entity = new WLDWYHZHResultObjectDto();
                entity.setKOINH(gsOfOut.getString(1) );
                entity.setBANKL(gsOfOut.getString(2) );
                entity.setBANKS(gsOfOut.getString(3) );
                entity.setLIFNR(gsOfOut.getString(4) );
                entity.setNAME1(gsOfOut.getString(5) );
                entity.setEBPP_ACCNAME(gsOfOut.getString(6) );
                resultList.add(entity);
            }
            logService.info( name, "automaticPartnerzh-ResultSet11:" );
            for(WLDWYHZHResultObjectDto item:resultList){
                try {
                    boolean infoChange = false;
                    //存入二开中间表，根据code  实时更新中间表
                    WLDWYHZHResultObject wldwyhzhResultObject = new WLDWYHZHResultObject(item);
                    WLDWYHZHResultObject infoInDB = wldwyhzhResultObjectRepository.findByKOINH(item.getKOINH());//查询是否有相同编号的
                    if (infoInDB == null) {
                        infoChange = true;
                        wldwyhzhResultObject.setFlag("D".equals(wldwyhzhResultObject.getFlag()) ? "D" : "A");//A新增，M修改，D删除
                    } else {
                        if (Objects.equals(infoInDB.getKOINH(), wldwyhzhResultObject.getKOINH()) && Objects.equals(infoInDB.getEBPP_ACCNAME(), wldwyhzhResultObject.getEBPP_ACCNAME())
                                && Objects.equals(infoInDB.getLIFNR(), wldwyhzhResultObject.getLIFNR()) && Objects.equals(infoInDB.getBANKL(), wldwyhzhResultObject.getBANKL())) {
                            // 如果更新时间完全相同，此中情况不再进行更新操作
                        } else {
                            infoChange = true;
                            wldwyhzhResultObject.setFlag("D".equals(wldwyhzhResultObject.getFlag()) ? "D" : "M");
                        }
                    }
                    if (infoChange) {
                        wldwyhzhResultObject.setSksyncstatus("0");
                        wldwyhzhResultObject.setSksyncmsg("");
                        wldwyhzhResultObjectRepository.save(wldwyhzhResultObject);
                    }
                }catch (Throwable ex){
                    logService.error( name, "获取贸易伙伴支付信息异常：",ex);
                }
            }
        }catch (Throwable ex){
            logService.error( name, "获取贸易伙伴支付信息异常",ex);
        }finally {
            //关闭连接
            if(exector!=null)exector.close();
           // logService.info( name, "结束：");
            logService.flush();
        }
    }
     /*
     获取往来单位银行账号 贸易伙伴支付信息 SAP数据湖  二开中间表同步司库
      */
    public void automaticGenerationPartnerzh()
    {
        String name="automaticGenerationPartnerzh";//获取SAP往来单位银行账号同步司库";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationPartnerzh");
            List<WLDWYHZHResultObject> resultList = wldwyhzhResultObjectRepository.findBywldwyhzhlist();
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }
            logService.info( name, "automaticGenerationPartnerzh-resultList:{}",JSONSerializer.serialize(resultList));
            for(WLDWYHZHResultObject result:resultList) {
                try {
                    LinkedHashMap<String, Object> orgparam = new LinkedHashMap<String, Object>();
                    Map<String, Object> hashMap = new HashMap<>();
                    Map<String, Object> mapsa = new HashMap<>();
                    mapsa.put("code", result.getKOINH());
                    String myhb = sqlSession.selectOne(String.class, "select ID from  BFPARTNERBANKACCOUNTS  where ACCOUNTCODE= #{code}", mapsa);
                    //若往来单位中不存在，则为新增
                    if (StringUtil.isNullOrEmpty(myhb)) {
                        result.setFlag("A");
                    } else {
                        result.setFlag("M");
                    }
                    hashMap.put("operation", "A".equals(result.getFlag()) ? "add" : ("M".equals(result.getFlag()) ? "modify" : "disable"));//操作类型
                    Map<String, Object> orgData = new HashMap<>();
                    orgData.put("ID", myhb);
                    orgData.put("ACCOUNTCODE", result.getKOINH());//银行编号
                    orgData.put("ACCOUNTNAME", result.getEBPP_ACCNAME());//账户名称  账户持有人
                    Map<String, Object> mapss = new HashMap<>();
                    mapss.put("wldwbh", result.getLIFNR());
                    mapss.put("khhbh", result.getBANKL());
                    String WLDWID = sqlSession.selectOne(String.class, "select ID from  BFPARTNER  where code= #{wldwbh}", mapss);
                    orgData.put("PARTNERID", WLDWID); // 往来单位ID，引用往来单位  TODO:
                    String khhid = sqlSession.selectOne(String.class, "select ID from  BFBANK  where PLAINTEXT1= #{khhbh}", mapss);
                    orgData.put("INBANK", khhid); // 所属银行ID，引用银行定义
                    orgData.put("ACCOUNTSTATE", "0"); // 账号状态，0,正常;1,冻结;2,其他，默认为0
//                orgData.put("PRIVATEORPUBLIC",""); // 账号性质，1,对公账号;2,对私账号，默认为1  TODO: 付款不在司库发起，无影响;
//                orgData.put("COUNTRY",result.getBNKLZ()); // 国家地区ID，引用国家地区字典
//                orgData.put("PROVINCE",result.getSWIFT()); // 省份，取行政区划省份名称
//                orgData.put("CITY",""); //  城市，取行政区划城市名称
//                orgData.put("CURRENCYLIST",""); // 币种ID，引用币种定义字典
//                orgData.put("ISMAIN",""); // 是否默认账户，0,否;1,是，默认为0
//                orgData.put("ISMAINFORINVOICING",""); // 是否默认开票账号，0,否;1,是，默认为0
//                orgData.put("ISPUBLIC",result.getBANKS()); // 是否公共，0,否;1,是，默认为1
//                orgData.put("REMARK",result.getPROVZ()); // 备注
//                orgData.put("PLAINTEXT1",""); // 扩展字段
//                orgData.put("PLAINTEXT2",result.getORT01()); // 扩展字段
//                orgData.put("PLAINTEXT3", ""); // 扩展字段
                    hashMap.put("data", orgData);
                    orgparam.put("param", hashMap);//hashmap内容为所需参数
                    String su = "df";
                    String serviceID = "com.inspur.gs.bf.df.commonservice.api.IPartnerBankAccountService.synchronous";
                    logService.info(name, "automaticGenerationPartnerzh-产品入参：" + JSONSerializer.serialize(orgparam));
                    HashMap results = rpcClient.invoke(HashMap.class, serviceID, su, orgparam, null);
                    logService.info(name, "automaticGenerationPartnerzh-产品返回：" + results);
                    if ("success".equals(String.valueOf(results.get("status")))) {
                        result.setSksyncstatus("1");
                        result.setSksyncmsg("");
                        wldwyhzhResultObjectRepository.save(result);
                    } else {
                        result.setSksyncstatus("2");
                        result.setSksyncmsg(String.valueOf(results.get("message")));
                        wldwyhzhResultObjectRepository.save(result);
                    }
                }catch (Throwable ex){
                    logService.error( name, "贸易伙伴支付信息同步异常：",ex);
                }
            }

        }catch (Throwable ex){
            logService.error( name, "贸易伙伴支付信息同步异常",ex);
        }finally {
            //logService.info( name, "结束：");
            logService.flush();
        }
    }

     /*
     获取现金流量项目 SAP数据湖 存入二开中间表
      */
    public void automaticCashFlowItems() throws Exception {
        String name="automaticCashFlowItems";//获取SAP现金流量项目";
        logService.init(name);
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
        if (StringUtil.isNullOrEmpty(dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        RelationalDbExector exector = ExectorFactory.create(dataSourceId);
        try{
            logService.info( name, "automaticCashFlowItems");
            //#region
            List<XJLLXMResultObject> yhresults=new ArrayList<>();
            List<XJLLXMResultObjectDto> resultList=new ArrayList<>();
            logService.info( name, "dataSourceId:"+dataSourceId);
            ResultSet gsOfOut = exector.query(" select distinct rstgr,txt40,substr(rstgr,1,1)dl from v_sap_gsc_cash_flow_item_i_rt", null);
            logService.info( name, "ResultSet:" );
            while (gsOfOut.next()) {
                XJLLXMResultObjectDto entity = new XJLLXMResultObjectDto();
                entity.setRSTGR(gsOfOut.getString(1) );
                entity.setTXT40(gsOfOut.getString(2) );
                entity.setBUKRS(gsOfOut.getString(3));//大类
                resultList.add(entity);
            }
            logService.info( name, "ResultSet11:" );
            for(XJLLXMResultObjectDto item:resultList){
                boolean infoChange=false;
                //存入二开中间表，根据code  实时更新中间表
                XJLLXMResultObject xjllxmResultObject=new XJLLXMResultObject(item);
                XJLLXMResultObject infoInDB = xjllxmResultObjectRepository.findByRSTGRAndBUKRS(item.getRSTGR(),item.getBUKRS());//查询是否有相同编号的
                if (infoInDB == null) {
                    infoChange = true;
                    xjllxmResultObject.setFlag("D".equals(xjllxmResultObject.getFlag())?"D":"A");//A新增，M修改，D删除
                } else {
                     if (  Objects.equals(infoInDB.getRSTGR(),xjllxmResultObject.getRSTGR())&&Objects.equals(infoInDB.getTXT40(),xjllxmResultObject.getTXT40())
                            &&Objects.equals(infoInDB.getBUKRS(),xjllxmResultObject.getBUKRS())) {
                        // 如果更新时间完全相同，此中情况不再进行更新操作
                    } else {
                        infoChange = true;
                        xjllxmResultObject.setFlag("D".equals(xjllxmResultObject.getFlag())?"D":"M");
                    }
                }
                if (infoChange) {
                    xjllxmResultObject.setSksyncstatus("0");
                    xjllxmResultObject.setSksyncmsg("");
                    xjllxmResultObjectRepository.save(xjllxmResultObject);
                }
            }
        }catch (Throwable ex){
            logService.error( name, "获取现金流量项目异常",ex);
        }finally {
            //关闭连接
            if(exector!=null)exector.close();
           // logService.info( name, "结束：");
            logService.flush();
        }
    }
     /*
     获取现金流量项目 SAP数据湖  二开中间表同步司库
      */
    public void automaticGenerationCashFlowItems()
    {
        String name="automaticGenerationCashFlowItems";//获取SAP现金流量项目同步司库";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationCashFlowItems");
            List<XJLLXMResultObject> resultList = xjllxmResultObjectRepository.findByXJLLXM();
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }
            logService.info( name, "automaticGenerationCashFlowItems-resultList:{}",JSONSerializer.serialize(resultList));
            for(XJLLXMResultObject result:resultList) {
                LinkedHashMap<String, Object> orgparam = new LinkedHashMap<String, Object>();
                Map<String, Object> hashMap = new HashMap<>();
                Map<String, Object> mapss = new HashMap<>();
                mapss.put("code",result.getRSTGR());
                String XJID=sqlSession.selectOne(String.class,"select ID from  BFCASHFLOWTYPE  where code= #{code}",mapss);
                if(!StringUtils.isEmpty(XJID)){
                    result.setFlag("M");
                }else{
                    result.setFlag("A");
                }
                hashMap.put("operation","A".equals(result.getFlag())?"add":("M".equals(result.getFlag())?"modify":"disable"));//操作类型
                Map<String,Object> orgData = new HashMap<>();

                orgData.put("ID",XJID);
                orgData.put("CODE",result.getRSTGR());//编号
                orgData.put("NAME",result.getTXT40());//名称
//                Map<String,Object> PARENTmap=sqlSession.selectOne(Map.class,"select ID from  BFPARTNER  where code= #{code}",result.getBUKRS());
//                orgData.put("PARENTID",WLDWID); // 父节点ID  TODO:
                Map<String, Object> maps = new HashMap<>();
                maps.put("code",result.getBUKRS());
                 String lbid=sqlSession.selectOne(String.class,"select ID from  BFTYPEOFCASHFLOWTYPE  where code= #{code}",maps);
                orgData.put("OFTYPE",lbid); //所属类别，引用现金流量类别，新增必填
//                orgData.put("DIRECTION","0"); // 流量方向，0,N/A;1,流入;2,流出，默认0
//                orgData.put("PRIVATEORPUBLIC",""); // 账号性质，1,对公账号;2,对私账号，默认为1
//                orgData.put("COUNTRY",result.getBNKLZ()); // 国家地区ID，引用国家地区字典
//                orgData.put("PROVINCE",result.getSWIFT()); // 省份，取行政区划省份名称
//                orgData.put("CITY",""); //  城市，取行政区划城市名称
//                orgData.put("CURRENCYLIST",""); // 币种ID，引用币种定义字典
//                orgData.put("ISMAIN",""); // 是否默认账户，0,否;1,是，默认为0
//                orgData.put("ISMAINFORINVOICING",""); // 是否默认开票账号，0,否;1,是，默认为0
//                orgData.put("ISPUBLIC",result.getBANKS()); // 是否公共，0,否;1,是，默认为1
//                orgData.put("REMARK",result.getPROVZ()); // 备注
//                orgData.put("PLAINTEXT1",""); // 扩展字段
//                orgData.put("PLAINTEXT2",result.getORT01()); // 扩展字段
//                orgData.put("PLAINTEXT3", ""); // 扩展字段
                hashMap.put("data",orgData);
                orgparam.put("param",hashMap);//hashmap内容为所需参数
                String su="df";
                String serviceID="com.inspur.gs.bf.df.commonservice.api.ICashFlowTypeService.synchronous";
                logService.info( name, "automaticGenerationCashFlowItems-产品入参："+JSONSerializer.serialize(orgparam));
                HashMap results=rpcClient.invoke(HashMap.class,serviceID,su,orgparam,null);
                logService.info( name, "automaticGenerationCashFlowItems-产品返回："+results);
                if("success".equals(String.valueOf(results.get("status")))) {
                    result.setSksyncstatus("1");
                    result.setSksyncmsg("");
                    xjllxmResultObjectRepository.save(result);
                }else {
                    result.setSksyncstatus("2");
                    result.setSksyncmsg(String.valueOf(results.get("message")));
                    xjllxmResultObjectRepository.save(result);
                }

            }
        }catch (Throwable ex){
            logService.error( name, "获取现金流量项目异常",ex);
        }finally {
           // logService.info( name, "结束：");
            logService.flush();
        }
    }
     /*
     获取是否并表 SAP数据湖  //BPC每月同步一次，司库按照月份取数，以此明确此公司是否在并表范围。如果不在并表范围内，就冻结权限；格式：2024.08；
      */
    public void automaticSFBB() throws Exception {
        String name="automaticSFBB";//获取SAP是否并表";
        logService.init(name);
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
        if (StringUtil.isNullOrEmpty(dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        Calendar calendar = Calendar.getInstance();
        // 将月份减1，获取上个月
        calendar.add(Calendar.MONTH, -1);
        // 获取上个月的日期
        Date lastMonthDate = calendar.getTime();
        // 格式化上个月的日期
        String kjqj = new SimpleDateFormat("yyyy.MM").format(lastMonthDate);
        RelationalDbExector exector = ExectorFactory.create(dataSourceId);
        try{
                logService.info( name, "automaticSFBB");
                //#region
                List<SFBBResultObject> yhresults=new ArrayList<>();
                List<SFBBResultObjectDto> resultList=new ArrayList<>();
                logService.info( name, "automaticSFBB-dataSourceId:"+dataSourceId);
                List<Object> params = new ArrayList<>();
                params.add(kjqj);
                ResultSet gsOfOut = exector.query(" select MANDT,C_SCOPE,C_ENTITY,C_TIME,C_SCOPE_T,C_ENTITY_T,C_SCOPE_PARENT,C_SCOPE_TYPE,SIGNEDDATA  from v_sap_gsc_whether_flag_i_rt  where   C_SCOPE_TYPE='A' and C_TIME =? ", params);// and
                logService.info( name, "automaticSFBB-ResultSet:" );
                while (gsOfOut.next()) {
                    SFBBResultObjectDto entity = new SFBBResultObjectDto();
                    entity.setMANDT(gsOfOut.getString(1) );//集团
                    entity.setC_SCOPE(gsOfOut.getString(2) );//合并组 
                    entity.setC_ENTITY(gsOfOut.getString(3) );//公司
                    entity.setC_TIME(gsOfOut.getString(4) );//期间
                    entity.setC_SCOPE_T(gsOfOut.getString(5) );//合并组描述 
                    entity.setC_ENTITY_T(gsOfOut.getString(6) );//子公司描述 
                    entity.setC_SCOPE_PARENT(gsOfOut.getString(7) );//父级合并组 
                    entity.setC_SCOPE_TYPE(gsOfOut.getString(8) );//合并组类型 
                    entity.setSIGNEDDATA(gsOfOut.getString(9) );//SIGNDATA
                    resultList.add(entity);
                }
                //清掉当前期间是否并表数据，重新插入
                sfbbResultObjectRepository.deleteByCtime(kjqj);
                logService.info( name, "automaticSFBB-ResultSet11:" );
                List<SFBBResultObject> sfbbResultObjectList=new ArrayList<>();
                for(SFBBResultObjectDto item:resultList){
                    try {
                        SFBBResultObject sfbbResultObject = new SFBBResultObject(item);
                        sfbbResultObjectList.add(sfbbResultObject);
                    }catch (Throwable ex){
                        logService.error( name, "获取是否并表异常：",ex);
                    }
                }
            sfbbResultObjectRepository.saveAll(sfbbResultObjectList);
            sfbbResultObjectRepository.flush();

        }catch (Throwable ex){
            logService.error( name, "获取是否并表异常",ex);
        }finally {
            //关闭连接
            if(exector!=null)exector.close();
           // logService.info( name, "结束：");
            logService.flush();
        }
    }
    /*
    获取是否并表 SAP数据湖  二开中间表更新司库  搞个组织机构版本  根据会计期间更新组织机构是否并表字段
    【每月1-5号获取上月数据；如果在1-5号没有获取到，比如2号要用数据了，可以用上上个月的】
     */
    public void automaticGenerationSFBB()
    {
        String name="automaticGenerationSFBB";//获取SAP是否并表同步司库";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationSFBB");
            //String kjqj= new SimpleDateFormat("yyyy.MM").format(new Date());
            // 创建一个Calendar实例
            Calendar calendar = Calendar.getInstance();
            // 将月份减1，获取上个月
            calendar.add(Calendar.MONTH, -1);
            // 获取上个月的日期
            Date lastMonthDate = calendar.getTime();
            // 格式化上个月的日期
            String kjqj = new SimpleDateFormat("yyyy.MM").format(lastMonthDate);
            List<SFBBResultObject> resultList = sfbbResultObjectRepository.findByCtime(kjqj);
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }
            String sql=" update BFMASTERORGANIZATION set EXTVARCHAR3='0' ";
            sqlSession.update(sql);
            String bbsql=" update BFMASTERORGANIZATION set EXTVARCHAR3='1'  where   EXISTS (select 1 from JTGKSFBBRESULT where c_time=#{kjqj} and c_entity=BFMASTERORGANIZATION.code )" +
                    " and EXTVARCHAR3='0'  ";
           int i= sqlSession.update(bbsql,kjqj);
            logService.info( name, "automaticGenerationSFBB-update:{}",i);
        }catch (Throwable ex){
            logService.error( name, "获取是否并表异常",ex);
        }finally {
          //  logService.info( name, "结束：");
            logService.flush();
        }
    }
    /*
    获取行政人员 CA 存入二开中间表  增量
     */
    public void automaticEmployees()
    {
        String name="automaticEmployees";//获取CA行政人员";
        logService.init(name);
        try{
            logService.info( name, "automaticEmployees");
            Map<String,Object> map = sqlSession.selectOne(Map.class, "SELECT KEYVALUE,URL,cxdate FROM JTGKINTERFACECONFIG WHERE CODE = 'CARY'");
            //#region
            String url=String.valueOf(map.get("URL"));//更新日期，不传此参数，则获取全量数据    scc/123456   geam/123456
//            String s = "pitims:ACvpfiw2A9GgB-HNwPfY";
            String encode = Base64.encode(String.valueOf(map.get("KEYVALUE")));
            logService.info( name, "map:"+map);
            logService.info( name, "encode:"+encode);
            List<EmployeeResultObjectDto> resultList=new ArrayList<>();
            EmployeeResultObject result = null;
            Map<String, Object> maps = new HashMap<>();
            maps.put("pageNum", "1");//页码  (必填)
            maps.put("pageSize", "1000");//数量    (必填)    一页最大1000
            maps.put("date", String.valueOf(map.get("CXDATE")));//查询开始日期  (必填)
//            maps.put("phoneNumber", "");//手机精确查找
//            maps.put("userId", "");//用户编码精确查找
//            maps.put("userName", "");//姓名模糊查询
//            maps.put("email", "");//邮箱模糊查询
//            maps.put("flag", "");//人员变动类型（N新增、M修改、D删除）
            String mapls = JSONSerializer.serialize(maps);
            HttpResponse response = HttpRequest.post(url).header("Content-Type", "application/json").header("Authorization", "Basic " + encode).body(mapls).timeout(50000)
                    .execute();
            logService.info( name, "response:"+response);
            if (response.getStatus() == 200) {
                JSONObject resultJson = JSONObject.parseObject(response.body());
                logService.info( name, "CA人员接口状态值:{}", response.getStatus());
                if (resultJson != null &&  Integer.valueOf(resultJson.getString("total"))>0 )
                {
                    resultList = JSON.parseArray(resultJson.getString("list"), EmployeeResultObjectDto.class);
                }
                Integer pages= Integer.valueOf(resultJson.getString("pages"));//总页数
                logService.info( name, "resultList:{}", JSONSerializer.serialize(resultList));
                if(!CollectionUtils.isEmpty(resultList)&&resultList.size()>0){
                    //存入二开中间表
                    for (EmployeeResultObjectDto item : resultList) {
                        try {
                            boolean infoChange = false;
                            //存入二开中间表，根据code  实时更新中间表
                            EmployeeResultObject bmResultObject = new EmployeeResultObject(item);
                            EmployeeResultObject infoInDB = employeeResultObjectRepository.findByPernr(item.getPernr());//查询是否有相同编号的
                            if (infoInDB == null) {
                                infoChange = true;
                                bmResultObject.setFlag("D".equals(bmResultObject.getFlag()) ? "D" : "A");//A新增，M修改，D删除
                            } else {
                                if (Objects.equals(infoInDB.getPernr(), bmResultObject.getPernr()) && Objects.equals(infoInDB.getEname(), bmResultObject.getEname())
                                        && Objects.equals(infoInDB.getOrgeh(), bmResultObject.getOrgeh()) && Objects.equals(infoInDB.getGesc(), bmResultObject.getGesc())
                                        && Objects.equals(infoInDB.getZhrCell(), bmResultObject.getZhrCell()) && Objects.equals(infoInDB.getZhrEmail(), bmResultObject.getZhrEmail())
                                        && Objects.equals(infoInDB.getFlag(), bmResultObject.getFlag()) && Objects.equals(infoInDB.getUpdateDate(), bmResultObject.getUpdateDate())) {
                                    // 如果更新时间完全相同，此中情况不再进行更新操作
                                } else {
                                    infoChange = true;
                                    bmResultObject.setFlag("D".equals(bmResultObject.getFlag()) ? "D" : "M");
                                }
                            }
                            if (infoChange) {
                                bmResultObject.setSksyncstatus("0");
                                bmResultObject.setSksyncmsg("");
                                bmResultObject.setYgzhstatus("0");
                                bmResultObject.setYgzhmsg("");
                                bmResultObject.setYgstatus("0");
                                bmResultObject.setYgmsg("");
                                employeeResultObjectRepository.save(bmResultObject);
                            }
                        }catch (Throwable ex){
                            logService.error( name, "获取公司异常：",ex);
                        }
                    }
                }
                for(int i=2;i<=pages;i++){
                    try {
                        maps = new HashMap<>();
                        maps.put("pageNum", i);//页码  (必填)
                        maps.put("pageSize", "1000");//数量    (必填)    一页最大1000
                        maps.put("date", String.valueOf(map.get("CXDATE")));//查询开始日期  (必填)
                        mapls = JSONSerializer.serialize(maps);
                        response = HttpRequest.post(url).header("Content-Type", "application/json").header("Authorization", "Basic " + encode).body(mapls).timeout(50000)
                                .execute();
                        if (response.getStatus() == 200) {
                            resultJson = JSONObject.parseObject(response.body());
                            logService.info(name, "CA人员接口状态值:{}", response.getStatus());
                            if (resultJson != null && Integer.valueOf(resultJson.getString("total")) > 0) {
                                resultList = JSON.parseArray(resultJson.getString("list"), EmployeeResultObjectDto.class);
                            }
                            logService.info(name, "resultList:{}", JSONSerializer.serialize(resultList));
                            if (!CollectionUtils.isEmpty(resultList) && resultList.size() > 0) {
                                //存入二开中间表
                                for (EmployeeResultObjectDto item : resultList) {
                                    boolean infoChange = false;
                                    //存入二开中间表，根据code  实时更新中间表
                                    EmployeeResultObject bmResultObject = new EmployeeResultObject(item);
                                    EmployeeResultObject infoInDB = employeeResultObjectRepository.findByPernr(item.getPernr());//查询是否有相同编号的
                                    if (infoInDB == null) {
                                        infoChange = true;
                                        bmResultObject.setFlag("D".equals(bmResultObject.getFlag()) ? "D" : "A");//A新增，M修改，D删除
                                    } else {
                                        if (Objects.equals(infoInDB.getPernr(), bmResultObject.getPernr())
                                                && Objects.equals(infoInDB.getEname(), bmResultObject.getEname())
                                                && Objects.equals(infoInDB.getOrgeh(), bmResultObject.getOrgeh()) && Objects.equals(infoInDB.getGesc(), bmResultObject.getGesc())
                                                && Objects.equals(infoInDB.getZhrCell(), bmResultObject.getZhrCell()) && Objects.equals(infoInDB.getZhrEmail(), bmResultObject.getZhrEmail())
                                                && Objects.equals(infoInDB.getFlag(), bmResultObject.getFlag())) {
                                            // 如果更新时间完全相同，此中情况不再进行更新操作
                                        } else {
                                            infoChange = true;
                                            bmResultObject.setFlag("D".equals(bmResultObject.getFlag()) ? "D" : "M");
                                        }
                                    }
                                    if (infoChange) {
                                        bmResultObject.setSksyncstatus("0");
                                        bmResultObject.setSksyncmsg("");
                                        bmResultObject.setYgzhstatus("0");
                                        bmResultObject.setYgzhmsg("");
                                        bmResultObject.setYgstatus("0");
                                        bmResultObject.setYgmsg("");
                                        employeeResultObjectRepository.save(bmResultObject);
                                    }
                                }
                            }
                        }
                    }catch (Throwable ex){
                        logService.error( name, "获取行政人员异常：",ex);
                    }
                }

            }

            //#endregion

        }catch (Throwable ex){
            logService.error( name, "获取行政人员异常",ex);
        }finally {
           // logService.info( name, "结束：");
            logService.flush();
        }
    }
    /*
    获取行政人员 CA  二开中间表 同步司库  增量
     */
    public void automaticGenerationEmployees()
    {
        String name="automaticGenerationEmployees";//获取CA行政人员同步司库";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationEmployees");
            List<EmployeeResultObject> employeeResultObjectList=employeeResultObjectRepository.findTop1000BySksyncstatus("0");
            if (CollectionUtils.isEmpty(employeeResultObjectList))
                return;
            logService.info( name, "automaticGenerationEmployees-employeeResultObjectList:{}",employeeResultObjectList.size());
            for(EmployeeResultObject result:employeeResultObjectList){
                try {
                    LinkedHashMap<String, Object> orgparam = new LinkedHashMap<String, Object>();
                    Map<String, Object> hashMap = new HashMap<>();
                    Map<String, Object> maps = new HashMap<>();
                    maps.put("code",result.getPernr());
                    String RYID = sqlSession.selectOne(String.class, "select ID from  bfemployee  where code= #{code}", maps);
                   // logService.info( name, "automaticGenerationEmployees-flag1:{}{}",result.getFlag(),RYID);
                      if("D".equals(result.getFlag())){
                        if (StringUtils.isEmpty(RYID)) {
                            result.setSksyncstatus("3");//无需处理
                            result.setSksyncmsg("");
                            employeeResultObjectRepository.save(result);
                            continue;
                        }
                    }else{
                        if (!StringUtils.isEmpty(RYID)) {
                            result.setFlag("M");
                        } else {
                            result.setFlag("A");
                        }
                    }
                   // logService.info( name, "automaticGenerationEmployees-flag:{}",result.getFlag());
                    hashMap.put("operation", "A".equals(result.getFlag()) ? "add" : ("M".equals(result.getFlag()) ? "modify" : "disable"));//操作类型
                    Map<String, Object> orgData = new HashMap<>();
                    orgData.put("ID", RYID);
                    orgData.put("SYSUSERID", result.getPernr());//系统用户ID，用于新增时指定新增用户的ID
                    orgData.put("CODE", result.getPernr());//编码
                    orgData.put("NAME", result.getEname());//名称
                    String   zzid = sqlSession.selectOne(String.class, "select BFMASTERORGANIZATION.id from  BFMASTERORGANIZATION left join JTGKSAPCAYWDY on BFMASTERORGANIZATION.code=JTGKSAPCAYWDY.SAP  where JTGKSAPCAYWDY.CA= #{code}", result.getOrgeh());
                    if(StringUtils.isEmpty(zzid)) {
                        zzid = sqlSession.selectOne(String.class, "select id from   bfmasterorganization where code= #{code} ", result.getOrgeh());
                    }

                    orgData.put("ORGANIZATION", zzid);//所在组织，新增必填，引用行政组织
                    orgData.put("GENDER", "1".equals(result.getGesc()) ? "Male" : ("2".equals(result.getGesc()) ? "Female" : "Unknown"));//性别，Male 男、Undefined、Female 女、Unknown
                    orgData.put("IDENTITYTYPE", "");//证件类型，IdentityCard(默认)、SergeantCard、StudentIDCard、OfficialCard、DriverLicense、Passport、HongKongAndMacaoPass
                    orgData.put("IDNUMBER", "");
                    orgData.put("EMPLOYEELEVEL", "");//职级
                    orgData.put("EMPLOYEECATEGORY", "");//人员类别，引用人员类别字典
                    orgData.put("SECURITYLEVEL", "");//"": "密级，PUBLIC(默认)、INTERNAL、SECRET、CONFIDENTIAL",
                    orgData.put("CONTACTINFO", result.getZhrCell());//联系方式(手机号)
                    orgData.put("COUNTRYORAERA", "");//国家或地区，引用国家地区字典
//                orgData.put("LOCATION",result.getZhrCity());//常驻地 TODO:
                    orgData.put("EMAIL", result.getZhrEmail());//邮箱
                    logService.info(name, "automaticGenerationEmployees11");
                    // 定义输入和输出的日期格式
                    DateTimeFormatter inputFormat = DateTimeFormatter.ofPattern("yyyyMMdd");
                    DateTimeFormatter outputFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    if (!"00000000".equals(result.getZhrTime1())&&!StringUtils.isEmpty(result.getZhrTime1())) {
                        try {
                            logService.error(name, "automaticGenerationEmployees-日期转换开始");
                            // 将输入字符串解析为 LocalDate
                            LocalDate date = LocalDate.parse(result.getZhrTime1(), inputFormat);

                            // 格式化为目标字符串
                            String formattedDate = date.format(outputFormat);
                            orgData.put("ENTRYDATE", formattedDate);//入职时间
                        }catch (Throwable e){
                            logService.error(name, "automaticGenerationEmployees-日期转换失败");
                        }
                    }
                    hashMap.put("data", orgData);
                    orgparam.put("param", hashMap);//hashmap内容为所需参数
                    String su = "df";
                    String serviceID = "com.inspur.gs.bf.df.commonservice.api.IEmployeeService.synchronous";
                    logService.info(name, "automaticGenerationEmployees-产品入参：" + JSONSerializer.serialize(orgparam));
                    HashMap results = rpcClient.invoke(HashMap.class, serviceID, su, orgparam, null);
                    logService.info(name, "automaticGenerationEmployees-产品返回：" + results);
                    if ("success".equals(String.valueOf(results.get("status")))) {
                        if("D".equals(result.getFlag())){
                            userDisable(result.getPernr(),name);
                        }else {
//                            // 创建用户
//                            boolean createSysUser =
//                                    createSysUser(result.getPernr(), result.getPernr(), result.getEname(), result.getZhrCell(), result.getZhrEmail(), zzid, result.getGesc(), name);
//                            if (createSysUser) {
//                                // 创建关联
//                                createEmployeeSysUserLink(result.getPernr(), RYID, name);
                                result.setSksyncstatus("1");
                                result.setSksyncmsg("");
                                employeeResultObjectRepository.save(result);
//                                //
//                            } else {
//                                // 删除创建的行政人员
//                                deleteEmployee(RYID, name);
//                            }
                        }
                    } else {
                        result.setSksyncstatus("2");
                        result.setSksyncmsg(String.valueOf(results.get("message")));
                        employeeResultObjectRepository.save(result);
                    }
                }
                catch (Throwable ex){
                    log.error(name, ex);
                    logService.error( name, "同步行政人员异常",ex);
                }

            }

        }catch (Throwable ex){
            log.error(name, ex);
            logService.error( name, "同步行政人员异常",ex);
        }finally {
            //logService.info( name, "结束：");
            logService.flush();
        }
    }


    /*
  获取行政人员 CA 存入二开中间表 全量
   */
    public void automaticEmployees_cs()
    {
        String name="automaticEmployees_cs";//获取CA行政人员";
        logService.init(name);
        try{
            logService.info( name, "automaticEmployees_cs");
            Map<String,Object> map = sqlSession.selectOne(Map.class, "SELECT KEYVALUE,URL,cxdate FROM JTGKINTERFACECONFIG WHERE CODE = 'CARYCS'");
            //#region
            String url=String.valueOf(map.get("URL"));//更新日期，不传此参数，则获取全量数据    scc/123456   geam/123456
//            String s = "pitims:ACvpfiw2A9GgB-HNwPfY";
            String encode = Base64.encode(String.valueOf(map.get("KEYVALUE")));
            logService.info( name, "map:"+map);
            logService.info( name, "encode:"+encode);
            List<EmployeeQlResultObject> resultList=new ArrayList<>();
            EmployeeQlResultObject result = null;
            for(int i=1;i<50;i++) {
                try {
                    Map<String, Object> maps = new HashMap<>();
                    maps.put("pageNum", i);//页码  (必填)
                    maps.put("pageSize", "1000");//数量    (必填)    一页最大1000
//            maps.put("date", String.valueOf(map.get("CXDATE")));//查询开始日期  (必填)
//            maps.put("phoneNumber", "");//手机精确查找
//            maps.put("userId", "");//用户编码精确查找
//            maps.put("userName", "");//姓名模糊查询
//            maps.put("email", "");//邮箱模糊查询
//            maps.put("flag", "");//人员变动类型（N新增、M修改、D删除）
                    String mapls = JSONSerializer.serialize(maps);
                    HttpResponse response = HttpRequest.post(url).header("Content-Type", "application/json").header("Authorization", "Basic " + encode).body(mapls).timeout(50000)
                            .execute();
                    logService.info(name, "response:" + response);
                    if (response.getStatus() == 200) {
                        JSONObject resultJson = JSONObject.parseObject(response.body());
                        logService.info(name, "CA人员接口状态值:{}", response.getStatus());
                        if (resultJson != null && "000".equals(resultJson.getString("code")) && !"[]".equals(resultJson.getString("employees"))) {
                            resultList = JSON.parseArray(resultJson.getString("employees"), EmployeeQlResultObject.class);
                        } else {
                            continue;
                        }
                        // Integer pages= Integer.valueOf(resultJson.getString("pages"));//总页数
                        //  logService.info( name, "resultList:{}", JSONSerializer.serialize(resultList));
                        if (!CollectionUtils.isEmpty(resultList) && resultList.size() > 0) {
                            //存入二开中间表
                            for (EmployeeQlResultObject item : resultList) {
                                try {
                                    boolean infoChange = false;
                                    //存入二开中间表，根据code  实时更新中间表
                                    EmployeeQlResultObject bmResultObject = new EmployeeQlResultObject(item);
                                    EmployeeQlResultObject infoInDB = employeeQlResultObjectRepository.findByUserId(item.getUserId());//查询是否有相同编号的
                                    if (infoInDB == null) {
                                        infoChange = true;
                                        bmResultObject.setFlag("D".equals(bmResultObject.getFlag()) ? "D" : "A");//A新增，M修改，D删除
                                    } else {
                                        if (Objects.equals(infoInDB.getUserId(), bmResultObject.getUserId()) && Objects.equals(infoInDB.getUserName(), bmResultObject.getUserName())
                                                && Objects.equals(infoInDB.getOrgeh(), bmResultObject.getOrgeh()) && Objects.equals(infoInDB.getGesc(), bmResultObject.getGesc())
                                                && Objects.equals(infoInDB.getPhoneNumber(), bmResultObject.getPhoneNumber()) && Objects.equals(infoInDB.getEmail(), bmResultObject.getEmail())
                                                && Objects.equals(infoInDB.getZhrAccount(), bmResultObject.getZhrAccount()) && Objects.equals(infoInDB.getZzKhh(), bmResultObject.getZzKhh())
                                                && Objects.equals(infoInDB.getZzLhh(), bmResultObject.getZzLhh())) {
                                            // 如果更新时间完全相同，此中情况不再进行更新操作
                                        } else {
                                            infoChange = true;
                                            bmResultObject.setFlag("D".equals(bmResultObject.getFlag()) ? "D" : "M");
                                        }
                                    }
                                    if (infoChange) {
                                        bmResultObject.setSksyncstatus("0");
                                        bmResultObject.setSksyncmsg("");
                                        employeeQlResultObjectRepository.save(bmResultObject);
                                    }
                                } catch (Throwable ex) {
                                    logService.error(name, "获取公司异常：", ex);
                                }
                            }
                        }


                    }
                }catch (Throwable ex){
                    logService.error( name, "获取公司异常：",ex);
                }
            }
            //#endregion

        }catch (Throwable ex){
            logService.error( name, "获取行政人员异常",ex);
        }finally {
            // logService.info( name, "结束：");
            logService.flush();
        }
    }
    /*
    获取行政人员 CA  二开中间表 同步司库 全量初始
     */
    public void automaticGenerationEmployees_cs()
    {
        String name="automaticGenerationEmployees_cs";//获取CA行政人员同步司库";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationEmployees_cs");
            List<EmployeeQlResultObject> employeeResultObjectList=employeeQlResultObjectRepository.findTop1000BySksyncstatus("0");
            if (CollectionUtils.isEmpty(employeeResultObjectList))
                return;
            logService.info( name, "automaticGenerationEmployees_cs-employeeResultObjectList:{}",employeeResultObjectList.size());
            for(EmployeeQlResultObject result:employeeResultObjectList){
                try {
                    LinkedHashMap<String, Object> orgparam = new LinkedHashMap<String, Object>();
                    Map<String, Object> hashMap = new HashMap<>();
                    Map<String, Object> maps = new HashMap<>();
                    maps.put("code",result.getUserId());
                    String RYID = sqlSession.selectOne(String.class, "select ID from  bfemployee  where code= #{code}", maps);
                //    logService.info( name, "automaticGenerationEmployees-flag1:{}{}",result.getFlag(),RYID);
                    if("D".equals(result.getFlag())){
                        if (StringUtils.isEmpty(RYID)) {
                            result.setSksyncstatus("3");//无需处理
                            result.setSksyncmsg("");
                            employeeQlResultObjectRepository.save(result);
                            continue;
                        }
                    }else{
                        if (!StringUtils.isEmpty(RYID)) {
                            result.setFlag("M");
                        } else {
                            result.setFlag("A");
                        }
                    }
                  //  logService.info( name, "automaticGenerationEmployees-flag:{}",result.getFlag());
                    hashMap.put("operation", "A".equals(result.getFlag()) ? "add" : ("M".equals(result.getFlag()) ? "modify" : "disable"));//操作类型
                    Map<String, Object> orgData = new HashMap<>();
                    orgData.put("ID", RYID);
                    orgData.put("SYSUSERID", result.getUserId());//系统用户ID，用于新增时指定新增用户的ID
                    orgData.put("CODE", result.getUserId());//编码
                    orgData.put("NAME", result.getUserName());//名称
                    String   zzid = sqlSession.selectOne(String.class, "select BFMASTERORGANIZATION.id from  BFMASTERORGANIZATION left join JTGKSAPCAYWDY on BFMASTERORGANIZATION.code=JTGKSAPCAYWDY.SAP  where JTGKSAPCAYWDY.CA= #{code}", result.getOrgeh());
                    if(StringUtils.isEmpty(zzid)) {
                        zzid = sqlSession.selectOne(String.class, "select id from   bfmasterorganization where code= #{code} ", result.getOrgeh());
                    }

                    orgData.put("ORGANIZATION", zzid);//所在组织，新增必填，引用行政组织
                    orgData.put("GENDER", "1".equals(result.getGesc()) ? "Male" : ("2".equals(result.getGesc()) ? "Female" : "Unknown"));//性别，Male 男、Undefined、Female 女、Unknown
                    orgData.put("IDENTITYTYPE", "");//证件类型，IdentityCard(默认)、SergeantCard、StudentIDCard、OfficialCard、DriverLicense、Passport、HongKongAndMacaoPass
                    orgData.put("IDNUMBER", "");
                    orgData.put("EMPLOYEELEVEL", "");//职级
                    orgData.put("EMPLOYEECATEGORY", "");//人员类别，引用人员类别字典
                    orgData.put("SECURITYLEVEL", "");//"": "密级，PUBLIC(默认)、INTERNAL、SECRET、CONFIDENTIAL",
                    orgData.put("CONTACTINFO", result.getPhoneNumber());//联系方式(手机号)
                    orgData.put("COUNTRYORAERA", "");//国家或地区，引用国家地区字典
//                orgData.put("LOCATION",result.getZhrCity());//常驻地 TODO:
                    orgData.put("EMAIL", result.getEmail());//邮箱
                    logService.info(name, "automaticGenerationEmployees11");
                    // 定义输入和输出的日期格式
                    DateTimeFormatter inputFormat = DateTimeFormatter.ofPattern("yyyyMMdd");
                    DateTimeFormatter outputFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    if (!"00000000".equals(result.getZhrTime1())&&!StringUtils.isEmpty(result.getZhrTime1())) {
                        try {
                            logService.error(name, "automaticGenerationEmployees-日期转换开始");
                            // 将输入字符串解析为 LocalDate
                            LocalDate date = LocalDate.parse(result.getZhrTime1(), inputFormat);

                            // 格式化为目标字符串
                            String formattedDate = date.format(outputFormat);
                            orgData.put("ENTRYDATE", formattedDate);//入职时间
                        }catch (Throwable e){
                            logService.error(name, "automaticGenerationEmployees-日期转换失败");
                        }
                    }
                    hashMap.put("data", orgData);
                    orgparam.put("param", hashMap);//hashmap内容为所需参数
                    String su = "df";
                    String serviceID = "com.inspur.gs.bf.df.commonservice.api.IEmployeeService.synchronous";
                    logService.info(name, "automaticGenerationEmployees-产品入参：" + JSONSerializer.serialize(orgparam));
                    HashMap results = rpcClient.invoke(HashMap.class, serviceID, su, orgparam, null);
                    logService.info(name, "automaticGenerationEmployees-产品返回：" + results);
                    if ("success".equals(String.valueOf(results.get("status")))) {
                        if("D".equals(result.getFlag())){
                            userDisable(result.getUserId(),name);
                        }else {
                            // 创建用户
//                            boolean createSysUser =
//                                    createSysUser(result.getUserId(), result.getUserId(), result.getUserName(), result.getPhoneNumber(), result.getEmail(), zzid, result.getGesc(), name);
//                            if (createSysUser) {
//                                // 创建关联
//                                createEmployeeSysUserLink(result.getUserId(), RYID, name);
//                                //
//                            } else {
//                                // 删除创建的行政人员
//                                deleteEmployee(RYID, name);
//                            }
                            result.setSksyncstatus("1");
                            result.setSksyncmsg("");
                            employeeQlResultObjectRepository.save(result);
                        }
                    } else {
                        result.setSksyncstatus("2");
                        result.setSksyncmsg(String.valueOf(results.get("message")));
                        employeeQlResultObjectRepository.save(result);
                    }
                }
                catch (Throwable ex){
                    log.error(name, ex);
                    logService.error( name, "同步行政人员异常",ex);
                }

            }

        }catch (Throwable ex){
            log.error(name, ex);
            logService.error( name, "同步行政人员异常",ex);
        }finally {
            //logService.info( name, "结束：");
            logService.flush();
        }
    }

    /**
     * 创建用户
     */
    public void automaticGenerationUser(){
        String name="automaticGenerationUser";
        logService.init(name);
        try{
            List<Map> userlist=sqlSession.selectList(Map.class," select BFEMPLOYEE.ID,BFEMPLOYEE.CODE,BFEMPLOYEE.NAME_CHS,BFEMPLOYEE.CONTACTINFO,BFEMPLOYEE.EMAIL,BFEMPLOYEE.ORGANIZATION ,(case when BFCODEITEMS.code='Male' then '1'  when BFCODEITEMS.code='Female' then '2' end)GENDER from BFEMPLOYEE" +
                    " left join  BFCODEITEMS on BFCODEITEMS.id=BFEMPLOYEE.GENDER  AND BFCODEITEMS.SETID='eec42b12-02c5-4206-9dec-742e41486a57' AND BFCODEITEMS.state_isenabled='1'  where BFEMPLOYEE.code not in (select code from gspuser) ORDER BY BFEMPLOYEE.CODE   LIMIT 500 ");
            for(Map user:userlist){
                // 创建用户
                boolean createSysUser =
                        createSysUser( String.valueOf(user.get("ID")),  String.valueOf(user.get("CODE")),  String.valueOf(user.get("NAME_CHS")),  String.valueOf(user.get("CONTACTINFO")),  String.valueOf(user.get("EMAIL")),  String.valueOf(user.get("ORGANIZATION")),  String.valueOf(user.get("GENDER")), name);
                if (createSysUser) {
                    // 创建关联
                    //createEmployeeSysUserLink(String.valueOf(user.get("ID")), String.valueOf(user.get("ID")), name);
                    logService.info(name,"创建用户成功");
                }
            }
            List<Map> userglgxlist=sqlSession.selectList(Map.class," select BFEMPLOYEE.id RYID,BFEMPLOYEE.code,gspuser.id USERID,bfEmployeeSysUser.id glgx from BFEMPLOYEE left join bfEmployeeSysUser on bfEmployeeSysUser.parentid=bfemployee.id\n" +
                    "left join gspuser on gspuser.code=BFEMPLOYEE.code  where bfEmployeeSysUser.id is   null and gspuser.id is not null  ");
            for(Map user:userglgxlist){
                // 创建关联
                createEmployeeSysUserLink(String.valueOf(user.get("USERID")), String.valueOf(user.get("RYID")), name);

            }

        }catch (Throwable ex){
            logService.error(name,ex);
        }finally {
            logService.flush();
        }

    }




    /*
    获取行政人员 CA  二开中间表中银行账号 同步司库行政人员银行账号  作废
     */
    public void automaticGenerationEmployeesYHZH()
    {
        String name="automaticGenerationEmployeesYHZH";//获取CA行政人员账户信息同步司库";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationEmployeesYHZH");
            List<EmployeeResultObject> employeeResultObjectList=employeeResultObjectRepository.findByEmployee();
            if (CollectionUtils.isEmpty(employeeResultObjectList))
                return;
//            logService.info( name, "automaticGenerationEmployees-employeeResultObjectList:{}",JSONSerializer.serialize(employeeResultObjectList));
            for(EmployeeResultObject result:employeeResultObjectList){
                try {
                    LinkedHashMap<String, Object> orgparam = new LinkedHashMap<String, Object>();
                    Map<String, Object> hashMap = new HashMap<>();
                    Map<String, Object> parameterMap = new HashMap<String, Object>();
                    parameterMap.put("ryzh", result.getZhrAccount());
                    parameterMap.put("rybh", result.getPernr());
                    parameterMap.put("zzid", result.getUnitId());
                    parameterMap.put("bmid", result.getOrgeh());
                    parameterMap.put("yhid", result.getZzLhh());
                    parameterMap.put("gjid", result.getLandx50());
                    String ZHID = sqlSession.selectOne(String.class, "select ID from  BFEMPLOYEEBANKACCOUNTS  where BANKACCOUNT= #{ryzh}", parameterMap);
                    String RYID = sqlSession.selectOne(String.class, "select ID from  bfemployee  where code= #{rybh}", parameterMap);
                    if (!StringUtils.isEmpty(ZHID) && !"D".equals(result.getFlag())) {
                        result.setFlag("M");
                    } else if (StringUtils.isEmpty(ZHID) && "D".equals(result.getFlag())) {
                        result.setSksyncstatus("3");//无需处理
                        result.setSksyncmsg("");
                        employeeResultObjectRepository.save(result);
                        continue;
                    } else {
                        result.setFlag("A");
                    }
                    parameterMap.put("sfid", result.getZzKhhs());
                    parameterMap.put("csid", result.getZzKhhd());
                    parameterMap.put("yhhbid", result.getZhrBank());
                    logService.info(name, "parameterMap:" + JSONSerializer.serialize(parameterMap));
                    hashMap.put("operation", "A".equals(result.getFlag()) ? "add" : ("M".equals(result.getFlag()) ? "modify" : "disable"));//操作类型
                    Map<String, Object> orgData = new HashMap<>();
                    orgData.put("ROBXZH_NM", ZHID);
                    orgData.put("ROBXZH_USERID", RYID);//所属人员ID，引用行政人员 新增必填
                    orgData.put("ROBXZH_YHZH", result.getZhrAccount());//编码
                    orgData.put("ROBXZH_ZHMC", result.getEname());//名称
                    String zzid = sqlSession.selectOne(String.class, "select id from   bfmasterorganization where code= #{zzid} ", parameterMap);
                    String bmid = sqlSession.selectOne(String.class, "select id from   bfmasterorganization where code= #{bmid} ", parameterMap);
                    orgData.put("ROBXZH_CURRENCY", "");//币种ID，引用币种定义 TODO
                    String yhid = sqlSession.selectOne(String.class, "select id from   bfbank where BANKIDENTIFIER= #{yhid} ", parameterMap);
                    orgData.put("ROBXZH_KHHID", yhid);//开户行ID，引用银行定义 新增必填
                    String gj = sqlSession.selectOne(String.class, "select id from   BFNATIONALANDREGIONALDICT where TWOCHARCODE= #{gjid}  ORDER BY code  LIMIT 1 ", parameterMap);
                    String sf = sqlSession.selectOne(String.class, "select id from   BFADMINDIVISION where name_chs like  concat('%', #{sfid}, '%') ", parameterMap);
                    String cs = sqlSession.selectOne(String.class, "select id from   BFADMINDIVISION where name_chs like  concat('%', #{csid}, '%')  ", parameterMap);
                    orgData.put("ROBXZH_SSGJID", gj);//所属省份
                    orgData.put("ROBXZH_SSSFID", sf);//所属省份
                    orgData.put("ROBXZH_SSCSID", cs);//所属城市
                    String yhhbid = sqlSession.selectOne(String.class, "select id from   BFBANKTYPE where name_chs like concat('%', #{yhhbid}, '%')  ", parameterMap);
                    orgData.put("ROBXZH_BANKTYPE", yhhbid);//银行类型ID，银行行别
                    orgData.put("ROBXZH_IFGWK", "");//是否公务卡，0：否、1：是
                    orgData.put("ROBXZH_GZK", "");//是否工资卡，0：否、1：是
                    orgData.put("ROBXZH_MR", "");//是否默认账户，0：否、1：是
                    orgData.put("ROBXZH_GK", "");//是否公开，0：否、1：是
                    orgData.put("ROBXZH_IDCARD", "");//身份证号
                    orgData.put("ROBXZH_PHONE", result.getZhrCell());//手机号
                    orgData.put("ROBXZH_PASSORT", "");//护照
                    orgData.put("ROBXZH_BM", ""); // 别名
                    orgData.put("ROBXZH_ZT", ""); // 状态
                    orgData.put("ROBXZH_TYR", ""); // 停用人
                    orgData.put("ROBXZH_TYSJ", ""); // 停用时间
                    orgData.put("ROBXZH_SHR", ""); // 审核人
                    orgData.put("ROBXZH_SHSJ", ""); // 审核时间
                    orgData.put("ROBXZH_DWID", zzid); // 单位ID
                    orgData.put("ROBXZH_BMID", bmid); // 部门ID
                    orgData.put("ROBXZH_NOTE", ""); // 备注
                    // 定义输入和输出的日期格式
                    DateTimeFormatter inputFormat = DateTimeFormatter.ofPattern("yyyyMMdd");
                    DateTimeFormatter outputFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                    if (!"00000000".equals(result.getZhrTime1()) && !StringUtils.isEmpty(result.getZhrTime1())) {
                        try {
                            // 将输入字符串解析为 LocalDate
                            LocalDate date = LocalDate.parse(result.getZhrTime1(), inputFormat);

                            // 格式化为目标字符串
                            String formattedDate = date.format(outputFormat);
                            orgData.put("ENTRYDATE", formattedDate);//入职时间
                        } catch (Throwable e) {
                        }
                    }

                    hashMap.put("data", orgData);
                    orgparam.put("param", hashMap);//hashmap内容为所需参数
                    String su = "df";
                    String serviceID = "com.inspur.gs.bf.df.commonservice.api.IROBXZHService.synchronous";
                    logService.info(name, "automaticGenerationEmployeesYHZH-产品入参：" + JSONSerializer.serialize(orgparam));
                    HashMap results = rpcClient.invoke(HashMap.class, serviceID, su, orgparam, null);
                    logService.info(name, "automaticGenerationEmployeesYHZH-产品返回：" + results);
                    if ("success".equals(String.valueOf(results.get("status")))) {
                        result.setSksyncstatus("1");
                        result.setSksyncmsg("");
                        employeeResultObjectRepository.save(result);
                    } else {
                        result.setSksyncstatus("2");
                        result.setSksyncmsg(String.valueOf(results.get("message")));
                        employeeResultObjectRepository.save(result);
                    }
                }catch (Throwable ex){
                    logService.error( name, "获取行政人员银行账号异常：",ex);
                }
            }

        }catch (Throwable ex){
            logService.error( name, "获取行政人员银行账户异常",ex);
        }finally {
            //logService.info( name, "结束：");
            logService.flush();
        }
    }
    /*
    获取SAP科目余额 SAP数据湖 同步司库  集成平台，每天一次
     */
    public void automaticGenerationKmye()
    {
        String name="automaticGenerationKmye";//获取SAP科目余额";
        try {
            logService.init(name);
            Map<String, Object> map = sqlSession.selectOne(Map.class, "SELECT KEYVALUE,URL,USERNAME,PASSWORD,MANDT FROM JTGKINTERFACECONFIG WHERE CODE = 'SAPKM'");
            String url = String.valueOf(map.get("URL"));//更新日期，不传此参数，则获取全量数据   测试环境先用这个  std/123456        scc/123456   geam/123456
//            String s = "pitims:ACvpfiw2A9GgB-HNwPfY";
            String encode = Base64.encode(String.valueOf(map.get("KEYVALUE")));

            logService.info(name, "map:" + map);
            logService.info(name, "encode:" + encode);
            // 获取当前日期
            LocalDate currentDate = LocalDate.now();
            // 获取当前年份
            int currentYear = currentDate.getYear();
            // 获取当前月份
            int currentMonth = currentDate.getMonthValue();
            // 获取下一个日期
            LocalDate nextDate = currentDate.plusDays(1);

            // 判断是否为月底
            boolean isEndOfMonth = currentDate.getMonth() != nextDate.getMonth();
            if(!isEndOfMonth){
                //不是月底的话，取上个月加这个月的
                //月份为1 的话，取去年12月的
//                if(currentMonth==1){
                    // 获取上个月的日期
                    LocalDate lastMonthDate = currentDate.minusMonths(1);
                    // 获取上个月的年份和月份
                    currentYear= lastMonthDate.getYear();
                    currentMonth = lastMonthDate.getMonthValue();
//                }
            }
            String rq="";
            logService.error(name, "currentYear:" + currentYear+",currentMonth:" + currentMonth);
            if(!StringUtils.isEmpty(map.get("USERNAME"))){
                currentYear = new Integer( String.valueOf(map.get("USERNAME")));
                currentMonth = new Integer(String.valueOf(map.get("PASSWORD")));
                rq=String.valueOf(map.get("MANDT"));
            }
//            List<String> types=new ArrayList<>();
//            types.add("00");
//            types.add("10");
//            types.add("30");
//            for (String type:types) {
                try {
                    List<ZHKMYEResultObject> resultList = new ArrayList<>();
                    Map<String, Object> req = new HashMap<>();
                    List<Map<String, Object>> mapList = new ArrayList<>();
                    //#region select BFMASTERORGANIZATION.code,bfbankaccountitems.FK01 from bfbankaccountitems left join bfbankaccounts on bfbankaccountitems.parentid=bfbankaccounts.id left join  BFMASTERORGANIZATION ON BFMASTERORGANIZATION.ID=bfbankaccounts.OPENACCOUNTUNIT  where  bfbankaccountitems.FK01 IS NOT NULL OR trim(bfbankaccountitems.FK01)!=''
                    List<Map> gslist = sqlSession.selectList(Map.class, "select * from vw_jtgkzhkmyexxb");
                    for (Map gsmap : gslist) {
                        try {
                            //#region
                            Map<String, Object> maps = new HashMap<>();
                            maps.put("BUKRS", gsmap.get("CODE"));//公司代码
                            maps.put("HKONT", gsmap.get("FK01"));//会计科目
                            maps.put("YEAR", currentYear);//年份
                            maps.put("MONTH", currentMonth);//月份
//                            maps.put("CURTYPE", type);//货币类型 调取FAGLB03,取货币类型 00凭证货币 10公司代码货币 30集团货币
                            mapList.add(maps);
                        } catch (Throwable ex) {
                            logService.error(name, "获取账户科目余额异常：", ex);
                        }
                        //#endregionc
                    }
                    //#region
                    req.put("IT_QUERY", mapList);
                    String mapls = JSONSerializer.serialize(req);
                    logService.info(name, "入参:" + mapls);
                    HttpResponse response = HttpRequest.post(url).header("Content-Type", "application/json").header("Authorization", "Basic " + encode).body(mapls).timeout(50000)
                            .execute();
                    logService.info(name, "出参:" + response);
                    if (response.getStatus() == 200) {
                        JSONObject resultJson = JSONObject.parseObject(response.body());
                        logService.info(name, "获取账户科目余额接口状态值:{}", response.getStatus());
                        if (resultJson != null && new Boolean(resultJson.getString("success"))) {
                            String data = resultJson.getString("data");
                            JSONObject dataJson = JSONObject.parseObject(data);
                            resultList = JSON.parseArray(dataJson.getString("ET_RESULT"), ZHKMYEResultObject.class);
                        }
                        logService.info(name, "resultList:{}", JSONSerializer.serialize(resultList));
                        if (!CollectionUtils.isEmpty(resultList) && resultList.size() > 0) {
                            logService.error(name, "年月：" + String.valueOf(currentYear) + String.valueOf(currentMonth));
                            List<ZHKMYEResultObject> ZHKMYEResultObjectlist = new ArrayList<>();
                            //存入二开中间表
                            for (ZHKMYEResultObject item : resultList) {
                                Map<String,Object> parameterMap = new HashMap<String,Object>();
                                parameterMap.put("FK01",item.getHKONT());
                                List<Map> accounts=sqlSession.selectList(Map.class,"select bfbankaccounts.id,bfbankaccounts.ACCOUNTNO FROM   bfBankAccountItems " +
                                        "left join bfbankaccounts ON bfBankAccountItems.parentid = bfbankaccounts.id where bfBankAccountItems.FK01=#{FK01} ",parameterMap);
                                boolean infoChange = false;
                                //存入二开中间表，根据code  实时更新中间表
                                ZHKMYEResultObject zhkmyeResultObject = new ZHKMYEResultObject(item, String.valueOf(currentYear), String.valueOf(currentMonth),rq);
                                if(accounts!=null && accounts.size()>0){
                                    zhkmyeResultObject.setAccount( !ObjectUtils.isEmpty(accounts.get(0).get("ID"))? accounts.get(0).get("ID").toString():"");
                                    zhkmyeResultObject.setAccountno(!ObjectUtils.isEmpty(accounts.get(0).get("ACCOUNTNO"))? accounts.get(0).get("ACCOUNTNO").toString():"");
                                }
                                ZHKMYEResultObjectlist.add(zhkmyeResultObject);
                            }
                            zhkmyeResultObjectRepository.saveAll(ZHKMYEResultObjectlist);
                            zhkmyeResultObjectRepository.flush();
                            logService.info(name, "插入表结束：");
                        }
                    }
                    //#endregion
                } catch (Throwable ex) {
                    logService.error(name, "获取账户科目余额异常：", ex);
                }
//            }
//            }
        }catch (Throwable ex){
            logService.error( name, "获取账户科目余额异常",ex);
        }finally {
            logService.flush();
        }
    }



    /**
     * 银行流水生单
     * @param param
     * @return
     */

    public void addOfflineTransDetailsRev(JSONObject param) {
        String name="addOfflineTransDetailsRev";//获取境外银行交易明细";
        logService.init(name);
        String result = "";
        Map<String,Object> resultMap = new HashMap<String, Object>();
        try {
            Map<String,Object> pzmap = sqlSession.selectOne(Map.class, "SELECT KEYVALUE,URL,cxdate FROM JTGKINTERFACECONFIG WHERE CODE = 'JWJYLS'");
            //#region
            String url=String.valueOf(pzmap.get("URL"));//更新日期，不传此参数，则获取全量数据    scc/123456   geam/123456
            String encode = Base64.encode(String.valueOf(pzmap.get("KEYVALUE")));
            if(StringUtil.isNullOrEmpty(url)){
                logService.error(name,"未配置接口地址");
                return;
            }
            HttpResponse response=null;
            //#region  调用SAP接口获取境外交易明细   查询入参
            List<Map> cxlist=sqlSession.selectList(Map.class," SELECT OPENACCOUNTUNIT,bfbankaccounts.accountno ACCOUNT_NO,bfcurrency.code CRNCY_CODE,TO_CHAR(NOW(), 'YYYY-MM-DD') AS DATE_START,TO_CHAR(NOW(), 'YYYY-MM-DD') AS DATE_END  from bfbankaccountitems  left join bfbankaccounts on bfbankaccounts.id=bfbankaccountitems.parentid  left join bfcurrency on bfbankaccountitems.currency=bfcurrency.id  " +
                    "left join bfnationalandregionaldict on bfbankaccounts.COUNTRY=bfnationalandregionaldict.id  " +
                    "where bfbankaccounts.ACCOUNTSTATUS='2' and bfnationalandregionaldict.TWOCHARCODE!='CN'   ");
            List<BankTransCationDetailsek> resultList=new ArrayList<>();
            for(Map<String,Object>  cxmap:cxlist){
                try {
                    String mapls = JSONSerializer.serialize(cxmap);
                    response = HttpRequest.post(url).header("Content-Type", "application/json").header("Authorization", "Basic " + encode).body(mapls).timeout(50000)
                            .execute();
                    logService.info(name, "response:" + response);
                    if (response.getStatus() == 200) {
                        JSONObject resultJson = JSONObject.parseObject(response.body());
                        logService.info(name, "接口反馈状态值:{}", response.getStatus());
                        if (resultJson != null && Integer.valueOf(resultJson.getString("RET_CODE")) > 0) {
                            resultList = JSON.parseArray(resultJson.getString("RET_BODY"), BankTransCationDetailsek.class);
                        }
                        logService.info(name, "resultList:{}", JSONSerializer.serialize(resultList));
                        if (!CollectionUtils.isEmpty(resultList) && resultList.size() > 0) {
                            List<OfflineTransDetailsEntity> InParam = new ArrayList<>();
                            //存入二开中间表
                            for (BankTransCationDetailsek item : resultList) {
                                try {
                                    OfflineTransDetailsEntity offlineTransDetailsEntity = new OfflineTransDetailsEntity();
                                    offlineTransDetailsEntity.setBankFlowNo(String.valueOf(item.getBANK_FLOW_NO()));//交易流水号
                                    String cxsql = "SELECT ID FROM BPBANKTRANSCATIONDETAILS WHERE BANKFLOWNO = '" + String.valueOf(item.getBANK_FLOW_NO()) + "'";
                                    List<Map> resultd = sqlSession.selectList(Map.class, cxsql);
                                    if (resultd.size() > 0) {
                                        resultMap.put("relFlag", "2");
                                        resultMap.put("message", "生单异常！");
                                        resultMap.put("err", "流水号：" + item.getBANK_FLOW_NO() + "重复！");
                                        logService.error(name, JSONSerializer.serialize(resultMap));
                                        continue;
                                    }
                                    Map<String, Object> khdw = sqlSession.selectOne(Map.class, "select ID,OPENACCOUNTUNIT  from bfbankaccounts where accountno=#{accountno}  ", item.getACCOUNT_NO());
                                    String bz = sqlSession.selectOne(String.class, "select id from bfcurrency where  code=#{code} ", item.getCRNCY_CODE());
                                    offlineTransDetailsEntity.setIncomeOrExpenditure(Integer.valueOf(String.valueOf(item.getRCV_PAY_FLAG())));//收支属性必填 1付款 2收款
                                    offlineTransDetailsEntity.setAccountUnit(String.valueOf(khdw.get("OPENACCOUNTUNIT")));//账户开户单位ID-必填
                                    offlineTransDetailsEntity.setBankAccountID(String.valueOf(khdw.get("ID")));//账户ID 必填
                                    offlineTransDetailsEntity.setBankAccountNo(String.valueOf(item.getACCOUNT_NO()));//银行账号-必填
                                    offlineTransDetailsEntity.setCurrency(bz);//币种ID 必填
                                    offlineTransDetailsEntity.setSettlementAmount(!ObjectUtils.isEmpty(item.getAMOUNT()) ? new BigDecimal(item.getAMOUNT()) : BigDecimal.ZERO);//交易金额 必填
                                    offlineTransDetailsEntity.setReciprocalAccountNo(String.valueOf(item.getOP_ACCOUNT_NO()));//对方账号-调拨类必填
                                    offlineTransDetailsEntity.setReciprocalAccName(String.valueOf(item.getOP_ACCOUNT_NAME()));//对方账号名称
                                    offlineTransDetailsEntity.setBankCodeOfReciprocalAccount(String.valueOf(item.getOP_BANK_CNAPS_NO()));//对方开户行行号
                                    offlineTransDetailsEntity.setProvice(String.valueOf(item.getOP_PROVINCE()));//对方省
                                    offlineTransDetailsEntity.setCity(String.valueOf(item.getOP_CITY()));//对方市
                                    offlineTransDetailsEntity.setSummary(String.valueOf(item.getABSTRACT()));//摘要 必填
                                    offlineTransDetailsEntity.setTransStatus(4);//交易状态必填 1未生成2已生成3已核销4无需生成5作废
                                    offlineTransDetailsEntity.setTransactionDate(!ObjectUtils.isEmpty(item.getTRADE_DATE()) ? new SimpleDateFormat("yyyy-MM-dd").parse(item.getTRADE_DATE()) : new Date());//交易日期 必填
                                    offlineTransDetailsEntity.setTransactionTime(!ObjectUtils.isEmpty(item.getTRADE_DATE()) ? new SimpleDateFormat("yyyy-MM-dd").parse(item.getTRADE_TIME()) : new Date());//交易时间
                                    offlineTransDetailsEntity.setImmediateAmount(!ObjectUtils.isEmpty(item.getIMMEDIATE_BALANCE()) ? new BigDecimal(item.getIMMEDIATE_BALANCE()) : null);//即时金额，这笔交易明细发生之后，账户的实时余额，一般为直联调度*使用
                                    offlineTransDetailsEntity.setDataSrc("02");//单据来源必填 01: 直联程序 02: 手工导入03: 财务公司采集 04：手工录入
                                    InParam.add(offlineTransDetailsEntity);
                                    bankTransCationDetailsekRepository.save(item);
                                }catch (Throwable ex){
                                    logService.error( name, "获取公司异常：",ex);
                                }

                            }
                            result = offlineTransDetailsRev(InParam);
                            logService.info(name, "resultData:" + JSONSerializer.serialize(result));
                            OfflineTransDetailsResult resultData = JTGKStringUtil.jsonToBean(result, OfflineTransDetailsResult.class);
                            if (resultData.isRelFlag()) {
                                resultMap.put("relFlag", "1");
                                resultMap.put("message", "生单结束！");

                                logService.info(name, JSONSerializer.serialize(resultMap));
                            } else {
                                resultMap.put("relFlag", "2");
                                resultMap.put("message", "RPC生单异常！");
                                resultMap.put("err", resultData.getRelMsg());

                                logService.error(name, JSONSerializer.serialize(resultMap));
                            }
                            bankTransCationDetailsekRepository.flush();

                        } else {
                            resultMap.put("relFlag", "2");
                            resultMap.put("message", "获取数据失败！");
                            logService.error(name, JSONSerializer.serialize(resultMap));
                        }
                    } else {
                        resultMap.put("relFlag", "2");
                        resultMap.put("message", "获取数据失败！");
                        logService.error(name, JSONSerializer.serialize(resultMap));
                    }
                }catch (Throwable ex){
                    logService.error( name, "获取公司异常：",ex);
                }



            }

            //#endregion

        } catch (Throwable  e) {
            logService.error(name,"生单异常！",e);
        }
        finally {
            logService.flush();
            return ;
        }

    }
    /**
     * 离线账户交易明细生单
     * return String errMsg:空字符串或者报错信息
     */
    public String offlineTransDetailsRev(List<OfflineTransDetailsEntity> InParam){
        List<Object> parameters=new ArrayList<Object>();
        parameters.add(InParam);
        return InternalServiceProxy.invoke("BP/Bebc/v1.0/BankTransactionDetailsInternalApi","OfflineTransDetailsRev",parameters,new TypeReference<String>(){});
    }

    private boolean createSysUser(String id, String code, String name, String phone, String email, String sysOrgId,String sex,String logname) {
        String existSQL = "select id from gspuser where id=#{id} and code=#{code}";
        String dh="";
        Map<String, Object> existSQLP = new HashMap<>();
        existSQLP.put("id", id);
        existSQLP.put("code", code);
        List<Map> list = sqlSession.selectList(Map.class, existSQL,existSQLP);
        //检查是否已创建用户，若存在用户则返回
        if (!CollectionUtils.isEmpty(list)) {
            logService.info(logname,"用户已存在");
            return true;
        }
//        if (!StringUtils.isEmpty(phone)) {
//            logService.info(logname,"手机号存在，相同的手机号导致用户创建失败");
//            Map<String, Object> mapss = new HashMap<>();
//            mapss.put("phone", phone);
//            // 检查手机号是否存在，相同的手机号会导致用户创建失败
//            String phoneExistSql = "select id from gspuserextend where mobilephone=#{phone}";
//            List<Map> phoneExistList = sqlSession.selectList(Map.class,phoneExistSql,mapss);
//            if (!CollectionUtils.isEmpty(phoneExistList)) {
//                dh=phone;
//                phone = "";
//            }
//        }
        logService.info(logname,"name:"+name);
        // 定义非法字符的正则表达式
//        String illegalCharacters = "[~`!@#\\$%\\^&*()_+\\-=\\{\\}\\[\\]:;'\",\\./<>\\?/~`|\\\\]+";

        boolean result = false;
        try {
            User user = new User();
            user.setId(id);
            user.setCode(code);
            user.setName(name);  // 使用正则表达式替换非法字符 name.replaceAll(illegalCharacters, "")
            user.setRemark("");
            //String sex = "1";
            user.setSex(sex);// 1男2女
            UserExtend userExtend = new UserExtend();
            if(!StringUtils.isEmpty(phone)){
                userExtend.setPhone(phone);
            }
//            if (!StringUtils.isEmpty(phone))
//                userExtend.setMobilePhone(phone);
            if (!StringUtils.isEmpty(email))
                userExtend.setEmail(email);
            user.setUserExtend(userExtend);
            user.setSysOrgId(sysOrgId);
            LinkedHashMap param1 = new LinkedHashMap<>();
            param1.put("user", user);
            // param1.put("pwd", defaultpwd);
            logService.info(logname,"------>>>创建系统用户入参：{}", JSONSerializer.serialize(user));
            String serviceId = "io.iec.edp.caf.sysmanager.api.manager.user.UserManager.addUser";
            User object = SpringBeanUtils.getBean(RpcClient.class).invoke(User.class, serviceId, "sys", param1, null);
            String sysUserId = object.getId();
            log.error("------>>>创建系统用户结果：{},{}", sysUserId, object);
            logService.info(logname,"------>>>创建系统用户结果：{},{}", sysUserId, object);
            if (StringUtils.isEmpty(sysUserId)) {

            } else {
                result = true;
            }
        } catch (Throwable e) {
            log.error("-----系统用户创建失败", e);
            logService.error(logname,"------>>>系统用户创建失败：{}",e);
            Throwable cause = e.getCause();
            while (cause != null && cause.getCause() != null) {
                cause = cause.getCause();
            }
        }
        return result;
    }

    private void userDisable(String userId,String name) {
        try {
            String existSQL = "select id from gspuser where id=#{id} and code=#{code}";
            Map<String, Object> existSQLP = new HashMap<>();
            existSQLP.put("id", userId);
            existSQLP.put("code", userId);
            List<Map> list = sqlSession.selectList(Map.class, existSQL,existSQLP);
            //检查是否已创建用户，若存在用户则返回
            if (CollectionUtils.isEmpty(list)) {
                logService.info(name,"用户不存在");
                return ;
            }
            LinkedHashMap param1 = new LinkedHashMap<>();
            param1.put("userIdList", Arrays.asList(userId));
            param1.put("userState", UserState.stop);
            String serviceId = "io.iec.edp.caf.sysmanager.api.manager.user.UserLimitManager.modifyUsersState";
            Object object =
                    SpringBeanUtils.getBean(RpcClient.class).invoke(Object.class, serviceId, "sys", param1, null);
        } catch (Throwable e) {
            log.error("------>>>用户禁用失败", e);
        }

    }

    private void deleteEmployee(String id,String name) {
        try {
            LinkedHashMap paramEmployee = new LinkedHashMap<>();
            Map<String, Object> data1 = new HashMap<>();
            Map<String, Object> data = new HashMap<>();
            data.put("ID", id);
            data1.put("data", data);
            data1.put("operation", "delete");
            paramEmployee.put("data", data1);
            String si = "com.inspur.gs.bf.df.commonservice.api.IEmployeeService.synchronous";
            Map<String, Object> rpcResult =
                    SpringBeanUtils.getBean(RpcClient.class).invoke(Map.class, si, "df", paramEmployee, null);
            log.error("------>>>行政人员删除结果：{}，{}", data, rpcResult);
            logService.info(name,"------>>>行政人员删除结果：{}，{}", data, rpcResult);
        } catch (Throwable e) {
            log.error("-----行政人员删除失败", e);
            logService.error(name,"------>>>行政人员删除失败：{}", e);
        }
    }

    private boolean updateEmployee(Map<String, Object> data) {
        try {
            LinkedHashMap paramEmployee = new LinkedHashMap<>();
            Map<String, Object> data1 = new HashMap<>();
            data1.put("data", data);
            data1.put("operation", "modify");
            paramEmployee.put("data", data1);
            String si = "com.inspur.gs.bf.df.commonservice.api.IEmployeeService.synchronous";
            Map<String, Object> rpcResult =
                    SpringBeanUtils.getBean(RpcClient.class).invoke(Map.class, si, "df", paramEmployee, null);
            log.error("------>>>行政人员修改结果：{}，{}", data, rpcResult);
            return true;
        } catch (Throwable e) {
            log.error("-----行政人员修改失败", e);
            return false;
        }
    }

    private void createEmployeeSysUserLink(String sysUserId, String employeeId,String name) {
        try {
            LinkedHashMap paramEmployee = new LinkedHashMap<>();
            Map<String, Object> data1 = new HashMap<>();
            Map<String, Object> data = new HashMap<>();
            data.put("SYSUSERID", sysUserId);
            data.put("EMPLOYEEID", employeeId);
            data1.put("data", data);
            data1.put("operation", "addemployeesysuser");
            paramEmployee.put("data", data1);
            String si = "com.inspur.gs.bf.df.commonservice.api.IEmployeeService.synchronous";
            Map<String, Object> rpcResult =
                    SpringBeanUtils.getBean(RpcClient.class).invoke(Map.class, si, "df", paramEmployee, null);
            logService.info(name,"------>>>行政人员关联用户创建结果：{}，{}", data, rpcResult);
            log.error("------>>>行政人员关联用户创建结果：{}，{}", data, rpcResult);
            if (rpcResult.get("status") != null && "success".equals(rpcResult.get("status").toString())) {
                String id = rpcResult.get("inspur_id").toString();
                logService.info(name,"------>>>关联成功：{}", id);
                log.error("------>>>关联成功：{}", id);
            }
        } catch (Throwable e) {
            log.error("-----行政人员创建关联关系失败", e);
            logService.info(name,"------>>>行政人员创建关联关系失败：{}", e);
        }
    }


    /*
   获取境外账户余额-花旗  同步中间库表
    */
    public void automaticHQZHYE() throws Exception {
        String name="automaticHQZHYE";//获取境外交易明细";
        logService.init(name);
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
        if (StringUtil.isNullOrEmpty(dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        RelationalDbExector exector = ExectorFactory.create(dataSourceId);
        try{
            logService.info( name, "automaticHQZHYE");
            //#region
            List<JwHqZhyeResultObject> yhresults=new ArrayList<>();
            List<JwHqZhyeResultObject> resultList=new ArrayList<>();
            logService.info( name, "automaticHQZHYE-dataSourceId:"+dataSourceId);
            ResultSet gsOfOut = exector.query("SELECT MANDT,REFNBR,ACTNBR,ST_SEQNUM,WAERS,ZOPEN_BAL,ZCLOSE_BAL,ZMT940_FNAME,ZMT940_FDAT,bal_date,cdate,ctiem,username,tcode,change_ind,uusername,udate,utime,utcode,uchange_ind from zfit097_stament  ",null);
            logService.info( name, "automaticHQZHYE-ResultSet:" );
            while (gsOfOut.next()) {
                JwHqZhyeResultObject entity = new JwHqZhyeResultObject();
                entity.setMANDT(gsOfOut.getString(1) );//集团
                entity.setREFNBR(gsOfOut.getString(2) );//付款ID
                entity.setACTNBR(gsOfOut.getString(3) );//银行账号
                entity.setST_SEQNUM(gsOfOut.getString(4) );//花旗银行账户对账单顺序号
                entity.setWAERS(gsOfOut.getString(5) );//货币码
                entity.setZOPEN_BAL(gsOfOut.getString(6) );//
                entity.setZCLOSE_BAL(gsOfOut.getString(7) );//
                entity.setZMT940_FNAME(gsOfOut.getString(8) );//MT940文件名
                entity.setZMT940_FDAT(gsOfOut.getString(9) );//对账单获取日期
                entity.setBAL_DATE(gsOfOut.getString(10) );//
                entity.setCDATE(gsOfOut.getString(11) );//
                entity.setCTIEM(gsOfOut.getString(12) );//
                entity.setUSERNAME(gsOfOut.getString(13) );//
                entity.setTCODE(gsOfOut.getString(14) );//
                entity.setCHANGE_IND(gsOfOut.getString(15) );//
                entity.setUUSERNAME(gsOfOut.getString(16) );//
                entity.setUDATE(gsOfOut.getString(17) );//
                entity.setUTIME(gsOfOut.getString(18) );//
                entity.setUTCODE(gsOfOut.getString(19) );//
                entity.setUCHANGE_IND(gsOfOut.getString(20) );//

                resultList.add(entity);
            }
            logService.info( name, "automaticHQZHYE-ResultSet11:" );
            for(JwHqZhyeResultObject item:resultList){
                boolean infoChange=false;
                //存入二开中间表，根据code  实时更新中间表
                JwHqZhyeResultObject sfbbResultObject=new JwHqZhyeResultObject(item);
                JwHqZhyeResultObject infoInDB = jwHqZhyeResultObjectRepository.findByACTNBRAndWAERSAndREFNBR(item.getACTNBR(),item.getWAERS(),item.getREFNBR());//查询会计期间
                if (infoInDB == null) {
                    infoChange = true;
                } else {
                      if(Objects.equals(infoInDB.getMANDT(),sfbbResultObject.getMANDT())&&Objects.equals(infoInDB.getREFNBR(),sfbbResultObject.getREFNBR())
                      &&Objects.equals(infoInDB.getACTNBR(),sfbbResultObject.getACTNBR())&&Objects.equals(infoInDB.getST_SEQNUM(),sfbbResultObject.getST_SEQNUM())
                      &&Objects.equals(infoInDB.getWAERS(),sfbbResultObject.getWAERS())&&Objects.equals(infoInDB.getZOPEN_BAL(),sfbbResultObject.getZOPEN_BAL())
                      &&Objects.equals(infoInDB.getZCLOSE_BAL(),sfbbResultObject.getZCLOSE_BAL())&&Objects.equals(infoInDB.getZMT940_FNAME(),sfbbResultObject.getZMT940_FNAME())
                              &&Objects.equals(infoInDB.getZMT940_FDAT(),sfbbResultObject.getZMT940_FDAT())){

                    } else {
                        infoChange = true;
                        sfbbResultObject.setFlag("D".equals(sfbbResultObject.getFlag())?"D":"M");
                    }
                }
                if (infoChange) {
                    sfbbResultObject.setSksyncstatus("0");
                    sfbbResultObject.setSksyncmsg("");
                    jwHqZhyeResultObjectRepository.save(sfbbResultObject);
                }
            }

        }catch (Throwable ex){
            logService.error( name, "获取境外账户余额-花旗异常",ex);
        }finally {
            //关闭连接
            if(exector!=null)exector.close();
            // logService.info( name, "结束：");
            logService.flush();
        }
    }

    /*
    获取境外账户余额-花旗 二开中间表 同步司库
     */
    public void automaticGenerationHQZHYE()
    {
        String name="automaticGenerationHQZHYE";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationHQZHYE");
            List<JwHqZhyeResultObject> resultList = jwHqZhyeResultObjectRepository.findByhqye();
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }

//            String nbdw =sqlSession.selectOne(String.class,"select ID  FROM  BFPARTNERTYPE where code= '01'");//内部单位
//            String wbdw =sqlSession.selectOne(String.class,"select ID  FROM  BFPARTNERTYPE where code= '02'");//外部单位
            List<Map<String, Object>> hashMap = new ArrayList<>();
            for(JwHqZhyeResultObject result:resultList) {
                try {
                    hashMap = new ArrayList<>();
                    Map<String, Object> orgData = new HashMap<>();
                    Map<String, Object> mapsb = new HashMap<>();
                    mapsb.put("code", result.getACTNBR());
                    mapsb.put("BZBH", result.getWAERS());
                    Map DWBH = sqlSession.selectOne(Map.class, "select BFBANKACCOUNTS.OPENACCOUNTUNIT ,BFBANKACCOUNTITEMS.AccountType,BFMASTERORGANIZATION.code dwbh  from  BFBANKACCOUNTS " +
                            " left join BFBANKACCOUNTITEMS on BFBANKACCOUNTS.id=BFBANKACCOUNTITEMS.PARENTID  " +
                            " left join BFMASTERORGANIZATION on BFMASTERORGANIZATION.id=BFBANKACCOUNTS.OPENACCOUNTUNIT " +
                            " left join BFCURRENCY on BFCURRENCY.id=BFBANKACCOUNTITEMS.CURRENCY  where BFBANKACCOUNTS.ACCOUNTNO= #{code}" +
                            "  AND BFCURRENCY.CODE=#{BZBH} and BFBANKACCOUNTS.ACCOUNTSTATUS ='2' and BFBANKACCOUNTS.ONLINEBANKOPENSTATUS in ('2','3') ", mapsb);
                    //grid_BFBANKACCOUNTITEMS.ACCOUNTTYPE_NAME$LANGUAGE$
                    if (DWBH == null) {
                        logService.info(name,"账户信息表中无数据");
                        result.setSksyncstatus("2");
                        result.setSksyncmsg("同步失败,账户信息表中无数据");
                        jwHqZhyeResultObjectRepository.save(result);
                        continue;
                    }
                    orgData.put("ZJYHZHYEB_DWBH", DWBH.get("DWBH"));
                    orgData.put("ZJYHZHYEB_ZHBH", result.getACTNBR());
                    orgData.put("ZJYHZHYEB_RQ", result.getBAL_DATE());
                    orgData.put("ZJYHZHYEB_BZ", result.getWAERS());
                    orgData.put("ZJYHZHYEBX_QCYE",  result.getZCLOSE_BAL());
                    orgData.put("ZJYHZHYEB_DQYE", result.getZCLOSE_BAL());
                    orgData.put("ZJYHZHYEB_GJJE",  BigDecimal.ZERO);
                    orgData.put("ZJYHZHYEB_SJ", "000000");//TODO:
//                    logService.info(name,"ZMT940_FNAME:"+result.getZMT940_FNAME());
//                    logService.info(name,"SJ:"+result.getZMT940_FNAME().split(result.getBAL_DATE())[1]);
//                    orgData.put("ZJYHZHYEB_SJ", !ObjectUtils.isEmpty(result.getBAL_DATE())?result.getBAL_DATE()+"000000":(  result.getZMT940_FNAME().split(result.getBAL_DATE())[1].length()==4?
//                            result.getZMT940_FNAME().split(result.getBAL_DATE())[1]+"00":"000000"));
                    //因为产品入参使用了public String属性，导致JSON格式化的报文内容重复
                    //小写部分属性不能省略，否则会导致产品接口获取不到数据
                orgData.put("zjyhzhyeb_DWBH",DWBH.get("DWBH"));
                orgData.put("zjyhzhyeb_ZHBH", result.getACTNBR());
                orgData.put("zjyhzhyeb_RQ", result.getBAL_DATE());
                orgData.put("zjyhzhyeb_BZ", result.getWAERS());
                orgData.put("zjyhzhyebX_QCYE",   result.getZCLOSE_BAL());
                orgData.put("zjyhzhyeb_DQYE", result.getZCLOSE_BAL());
                orgData.put("zjyhzhyeb_GJJE",  BigDecimal.ZERO);
                orgData.put("ZJYHZHYEB_SJ", "000000");//TODO:
//                orgData.put("zjyhzhyeb_SJ",!ObjectUtils.isEmpty(result.getBAL_DATE())?result.getBAL_DATE()+"000000":( result.getZMT940_FNAME().split(result.getBAL_DATE())[1].length()==4?
//                        result.getZMT940_FNAME().split(result.getBAL_DATE())[1]+"00":"000000"));
                    // ******** 账户类型
                    orgData.put("ZJYHZHYEB_ZHLX", DWBH.get("ACCOUNTTYPE"));
                    orgData.put("zjyhzhyeb_zhlx", DWBH.get("ACCOUNTTYPE"));
                    hashMap.add(orgData);
                    logService.info(name, "提交产品接口参数：" + hashMap);
                    List<Object> parameters = new ArrayList<Object>();
                    parameters.add(JSONSerializer.serialize(hashMap));
                    String jsonOut = InternalServiceProxy.invoke("BP/Bebc/v1.0/BalanceOnBank", "OfflineAccBalanceRev", parameters, new TypeReference<String>() {
                    });
                    logService.info(name, "产品接口返回：" + jsonOut);
                    JSONObject jObjectOut = JSON.parseObject(jsonOut);
                    if (jObjectOut != null && jObjectOut.getBoolean("relFlag")) {
                        result.setSksyncstatus("1");
                        result.setSksyncmsg("");
                        jwHqZhyeResultObjectRepository.save(result);
                    } else if (jObjectOut != null && !jObjectOut.getBoolean("relFlag")) {
                        result.setSksyncstatus("2");
                        result.setSksyncmsg(String.valueOf(jObjectOut.getString("relMsg")));
                        jwHqZhyeResultObjectRepository.save(result);
                    } else {
                        result.setSksyncstatus("2");
                        result.setSksyncmsg("同步失败");
                        jwHqZhyeResultObjectRepository.save(result);
                    }
                }catch (Throwable ex){
                    logService.error( name, "获取境外账户余额-花旗同步司库异常：",ex);
                }
            }

        }catch (Throwable ex){
            logService.error( name, "获取境外账户余额-花旗同步司库异常",ex);
        }finally {
            // logService.info( name, "结束：");
            logService.flush();
        }
    }

    /*
 获取境外账户余额-摩根  同步中间库表
  */
    public void automaticMGZHYE() throws Exception {
        String name="automaticMGZHYE";//获取境外交易明细";
        logService.init(name);
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
        if (StringUtil.isNullOrEmpty(dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        RelationalDbExector exector = ExectorFactory.create(dataSourceId);
        try{
            logService.info( name, "automaticMGZHYE");
            //#region
            List<JwMgZhyeResultObject> yhresults=new ArrayList<>();
            List<JwMgZhyeResultObject> resultList=new ArrayList<>();
            logService.info( name, "automaticMGZHYE-dataSourceId:"+dataSourceId);
            ResultSet gsOfOut = exector.query("select  MANDT,GUID,ZLINES,FILE_TYPE,FILENAME,MT94_20,MT94_21,MT94_25,MT94_60,MT94_61,MT94_86,MT94_62,MT94_64,MT94_65,FINAL_86,VALUE_DATE,ENTRY_DATE,CREDIT_DEBIT,FOUNDS_D,CURRENCY_CODE," +
                    "AMOUNT,TRANS_TYPE,REF_ID,BANK_REF,REFNBR,MSGID,CREDTTM,ORGNLMSGID,ORGNLNBOFTXS,ORGNLCTRLSUM,GRPSTS,ADDTLINF,ORGNLPMTINFID,ORGNLINSTRID,ORGNLENDTOENDID,TXSTS,ACCTSVCRREF,AMT,AMT_CCY,REQDEXCTNDT," +
                    "CDATE,CTIEM,USERNAME,TCODE,CHANGE_IND,UUSERNAME,UDATE,UTIME,UTCODE,UCHANGE_IND,AMT_BLANCE,AMT_CCY_BLANCE,DATE_BLANCE " +//--AMT_BLANCE,--AMT_CCY_BLANCE,--DATE_BLANCE,--INCLUDE,
                    " from  zfit003_22  where MT94_64 <> ''  ",null);
            logService.info( name, "automaticMGZHYE-ResultSet:" );
            while (gsOfOut.next()) {
                JwMgZhyeResultObject entity = new JwMgZhyeResultObject();
                entity.setMANDT(gsOfOut.getString(1) );//
                entity.setGUID(gsOfOut.getString(2) );//
                entity.setZLINES(gsOfOut.getString(3) );//
                entity.setFILE_TYPE(gsOfOut.getString(4) );//
                entity.setFILENAME(gsOfOut.getString(5) );//
                entity.setMT94_20(gsOfOut.getString(6) );//
                entity.setMT94_21(gsOfOut.getString(7) );//
                entity.setMT94_25(gsOfOut.getString(8) );//
                entity.setMT94_60(gsOfOut.getString(9) );//
                entity.setMT94_61(gsOfOut.getString(10) );//
                entity.setMT94_86(gsOfOut.getString(11) );//
                entity.setMT94_62(gsOfOut.getString(12) );//
                entity.setMT94_64(gsOfOut.getString(13) );//
                entity.setMT94_65(gsOfOut.getString(14) );//
                entity.setFINAL_86(gsOfOut.getString(15) );//
                entity.setVALUE_DATE(gsOfOut.getString(16) );//
                entity.setENTRY_DATE(gsOfOut.getString(17) );//
                entity.setCREDIT_DEBIT(gsOfOut.getString(18) );//
                entity.setFOUNDS_D(gsOfOut.getString(19) );//
                entity.setCURRENCY_CODE(gsOfOut.getString(20) );//
                entity.setAMOUNT(gsOfOut.getString(21) );//
                entity.setTRANS_TYPE(gsOfOut.getString(22) );//
                entity.setREF_ID(gsOfOut.getString(23) );//
                entity.setBANK_REF(gsOfOut.getString(24) );//
                entity.setREFNBR(gsOfOut.getString(25) );//
                entity.setMSGID(gsOfOut.getString(26) );//
                entity.setCREDTTM(gsOfOut.getString(27) );//
                entity.setORGNLMSGID(gsOfOut.getString(28) );//
                entity.setORGNLNBOFTXS(gsOfOut.getString(29) );//
                entity.setORGNLCTRLSUM(gsOfOut.getString(30) );//
                entity.setGRPSTS(gsOfOut.getString(31) );//
                entity.setADDTLINF(gsOfOut.getString(32) );//
                entity.setORGNLPMTINFID(gsOfOut.getString(33) );//
                entity.setORGNLINSTRID(gsOfOut.getString(34) );//
                entity.setORGNLENDTOENDID(gsOfOut.getString(35) );//
                entity.setTXSTS(gsOfOut.getString(36) );//
                entity.setACCTSVCRREF(gsOfOut.getString(37) );//
                entity.setAMT(gsOfOut.getString(38) );//
                entity.setAMT_CCY(gsOfOut.getString(39) );//
                entity.setREQDEXCTNDT(gsOfOut.getString(40) );//
                entity.setCDATE(gsOfOut.getString(41) );//
                entity.setCTIEM(gsOfOut.getString(42) );//
                entity.setUSERNAME(gsOfOut.getString(43) );//
                entity.setTCODE(gsOfOut.getString(44) );//
                entity.setCHANGE_IND(gsOfOut.getString(45) );//
                entity.setUUSERNAME(gsOfOut.getString(46) );//
                entity.setUDATE(gsOfOut.getString(47) );//
                entity.setUTIME(gsOfOut.getString(48) );//
                entity.setUTCODE(gsOfOut.getString(49) );//
                entity.setUCHANGE_IND(gsOfOut.getString(50) );//AMT_BLANCE,AMT_CCY_BLANCE,DATE_BLANCE
                entity.setAMT_BLANCE(gsOfOut.getString(51) );
                entity.setAMT_CCY_BLANCE(gsOfOut.getString(52) );
                entity.setDATE_BLANCE(gsOfOut.getString(53) );
                resultList.add(entity);
            }
            logService.info( name, "automaticMGZHYE-ResultSet11:" );
            for(JwMgZhyeResultObject item:resultList){
                boolean infoChange=false;
                //存入二开中间表，根据code  实时更新中间表
                JwMgZhyeResultObject sfbbResultObject=new JwMgZhyeResultObject(item);
                JwMgZhyeResultObject infoInDB = jwMgZhyeResultObjectRepository.findByGUIDAndZLINES(item.getGUID(),item.getZLINES());//查询会计期间
                if (infoInDB == null) {
                    infoChange = true;
                } else {
                    if(Objects.equals(infoInDB.getMANDT(),sfbbResultObject.getMANDT())&&
                            Objects.equals(infoInDB.getGUID(),sfbbResultObject.getGUID())&&
                            Objects.equals(infoInDB.getZLINES(),sfbbResultObject.getZLINES())&&
                            Objects.equals(infoInDB.getFILE_TYPE(),sfbbResultObject.getFILE_TYPE())&&
                            Objects.equals(infoInDB.getFILENAME(),sfbbResultObject.getFILENAME())&&
                            Objects.equals(infoInDB.getMT94_20(),sfbbResultObject.getMT94_20())&&
                            Objects.equals(infoInDB.getMT94_21(),sfbbResultObject.getMT94_21())&&
                            Objects.equals(infoInDB.getMT94_25(),sfbbResultObject.getMT94_25())&&
                            Objects.equals(infoInDB.getMT94_60(),sfbbResultObject.getMT94_60())&&
                            Objects.equals(infoInDB.getMT94_61(),sfbbResultObject.getMT94_61())&&
                            Objects.equals(infoInDB.getMT94_86(),sfbbResultObject.getMT94_86())&&
                            Objects.equals(infoInDB.getMT94_62(),sfbbResultObject.getMT94_62())&&
                            Objects.equals(infoInDB.getMT94_64(),sfbbResultObject.getMT94_64())&&
                            Objects.equals(infoInDB.getMT94_65(),sfbbResultObject.getMT94_65())&&
                            Objects.equals(infoInDB.getFINAL_86(),sfbbResultObject.getFINAL_86())&&
                            Objects.equals(infoInDB.getVALUE_DATE(),sfbbResultObject.getVALUE_DATE())&&
                            Objects.equals(infoInDB.getENTRY_DATE(),sfbbResultObject.getENTRY_DATE())&&
                            Objects.equals(infoInDB.getCREDIT_DEBIT(),sfbbResultObject.getCREDIT_DEBIT())&&
                            Objects.equals(infoInDB.getFOUNDS_D(),sfbbResultObject.getFOUNDS_D())&&
                            Objects.equals(infoInDB.getCURRENCY_CODE(),sfbbResultObject.getCURRENCY_CODE())&&
                            Objects.equals(infoInDB.getAMOUNT(),sfbbResultObject.getAMOUNT())&&
                            Objects.equals(infoInDB.getTRANS_TYPE(),sfbbResultObject.getTRANS_TYPE())&&
                            Objects.equals(infoInDB.getREF_ID(),sfbbResultObject.getREF_ID())&&
                            Objects.equals(infoInDB.getBANK_REF(),sfbbResultObject.getBANK_REF())&&
                            Objects.equals(infoInDB.getREFNBR(),sfbbResultObject.getREFNBR())&&
                            Objects.equals(infoInDB.getMSGID(),sfbbResultObject.getMSGID())&&
                            Objects.equals(infoInDB.getCREDTTM(),sfbbResultObject.getCREDTTM())&&
                            Objects.equals(infoInDB.getORGNLMSGID(),sfbbResultObject.getORGNLMSGID())&&
                            Objects.equals(infoInDB.getORGNLNBOFTXS(),sfbbResultObject.getORGNLNBOFTXS())&&
                            Objects.equals(infoInDB.getORGNLCTRLSUM(),sfbbResultObject.getORGNLCTRLSUM())&&
                            Objects.equals(infoInDB.getGRPSTS(),sfbbResultObject.getGRPSTS())&&
                            Objects.equals(infoInDB.getADDTLINF(),sfbbResultObject.getADDTLINF())&&
                            Objects.equals(infoInDB.getORGNLPMTINFID(),sfbbResultObject.getORGNLPMTINFID())&&
                            Objects.equals(infoInDB.getORGNLINSTRID(),sfbbResultObject.getORGNLINSTRID())&&
                            Objects.equals(infoInDB.getORGNLENDTOENDID(),sfbbResultObject.getORGNLENDTOENDID())&&
                            Objects.equals(infoInDB.getTXSTS(),sfbbResultObject.getTXSTS())&&
                            Objects.equals(infoInDB.getACCTSVCRREF(),sfbbResultObject.getACCTSVCRREF())&&
                            Objects.equals(infoInDB.getAMT(),sfbbResultObject.getAMT())&&
                            Objects.equals(infoInDB.getAMT_CCY(),sfbbResultObject.getAMT_CCY())&&
                            Objects.equals(infoInDB.getREQDEXCTNDT(),sfbbResultObject.getREQDEXCTNDT())&&
                            Objects.equals(infoInDB.getAMT_BLANCE(),sfbbResultObject.getAMT_BLANCE())&&
                            Objects.equals(infoInDB.getAMT_CCY_BLANCE(),sfbbResultObject.getAMT_CCY_BLANCE())&&
                            Objects.equals(infoInDB.getDATE_BLANCE(),sfbbResultObject.getDATE_BLANCE())&&
                            Objects.equals(infoInDB.getCDATE(),sfbbResultObject.getCDATE())&&
                            Objects.equals(infoInDB.getCTIEM(),sfbbResultObject.getCTIEM())&&
                            Objects.equals(infoInDB.getUSERNAME(),sfbbResultObject.getUSERNAME())&&
                            Objects.equals(infoInDB.getTCODE(),sfbbResultObject.getTCODE())&&
                            Objects.equals(infoInDB.getCHANGE_IND(),sfbbResultObject.getCHANGE_IND())&&
                            Objects.equals(infoInDB.getUUSERNAME(),sfbbResultObject.getUUSERNAME())&&
                            Objects.equals(infoInDB.getUDATE(),sfbbResultObject.getUDATE())&&
                            Objects.equals(infoInDB.getUTIME(),sfbbResultObject.getUTIME())&&
                            Objects.equals(infoInDB.getUTCODE(),sfbbResultObject.getUTCODE())&&
//                            Objects.equals(infoInDB.getAMT_BLANCE(),sfbbResultObject.getAMT_BLANCE())&&
//                            Objects.equals(infoInDB.getAMT_CCY_BLANCE(),sfbbResultObject.getAMT_CCY_BLANCE())&&
//                            Objects.equals(infoInDB.getDATE_BLANCE(),sfbbResultObject.getDATE_BLANCE())&&
                            //Objects.equals(infoInDB.getINCLUDE(),sfbbResultObject.getINCLUDE())&&
                            Objects.equals(infoInDB.getUCHANGE_IND(),sfbbResultObject.getUCHANGE_IND())
                    ){
//                            logService.info(name,"重复不更新");
                    } else {
                        infoChange = true;
                        sfbbResultObject.setFlag("D".equals(sfbbResultObject.getFlag())?"D":"M");
                    }
                }
//                logService.info(name,"infoChange:"+infoChange);
                if (infoChange) {
                    sfbbResultObject.setSksyncstatus("0");
                    sfbbResultObject.setSksyncmsg("");
                    jwMgZhyeResultObjectRepository.save(sfbbResultObject);
                }
            }

        }catch (Throwable ex){
            logService.error( name, "获取境外账户余额-摩根异常",ex);
        }finally {
            //关闭连接
            if(exector!=null)exector.close();
            // logService.info( name, "结束：");
            logService.flush();
        }
    }


    /*
    获取境外账户余额-摩根 二开中间表 同步司库
     */
    public void automaticGenerationMGZHYE()
    {
        String name="automaticGenerationMGZHYE";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationMGZHYE");
            List<JwMgZhyeResultObject> resultList = jwMgZhyeResultObjectRepository.findByMgye();
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }
//            logService.info( name, "automaticGenerationHQZHYE-resultList:{}",JSONSerializer.serialize(resultList));
//            String nbdw =sqlSession.selectOne(String.class,"select ID  FROM  BFPARTNERTYPE where code= '01'");//内部单位
//            String wbdw =sqlSession.selectOne(String.class,"select ID  FROM  BFPARTNERTYPE where code= '02'");//外部单位
            List<Map<String, Object>> hashMap = new ArrayList<>();
            for(JwMgZhyeResultObject result:resultList) {
                try {
                    logService.info(name,"result:"+JSONSerializer.serialize(result));
                    //#region MT940一天给2个，取最新的一个就是最新的余额
                    Map<String, Object> mapcf = new HashMap<>();
                    mapcf.put("MT94_25", result.getMT94_25()); //账号
                    mapcf.put("AMT_CCY_BLANCE", result.getAMT_CCY_BLANCE());//币种
                    mapcf.put("DATE_BLANCE", result.getDATE_BLANCE());//日期
                    String filename=sqlSession.selectOne(String.class,"select max(filename)filename from  JTGKJWMGZHYERESULT where MT94_25=#{MT94_25}  " +
                            " and AMT_CCY_BLANCE=#{AMT_CCY_BLANCE} and DATE_BLANCE=#{DATE_BLANCE} ",mapcf);
                    if(!filename.equals(result.getFILENAME())){
                        logService.error(name,"重复数据取最新一条数据，当前不是最新的数据，忽略");
                        result.setSksyncstatus("2");
                        result.setSksyncmsg("重复数据取最新一条数据，当前不是最新的数据，忽略");
                        jwMgZhyeResultObjectRepository.save(result);
                        continue;
                    }

                    //#endregion
                    hashMap = new ArrayList<>();
                    Map<String, Object> orgData = new HashMap<>();
//                Map<String, Object> mapsb = new HashMap<>();
//                mapsb.put("code",result.getACTNBR());
//                Map DWBH=sqlSession.selectOne(Map.class,"select BFBANKACCOUNTS.FK12,BFBANKACCOUNTITEMS.AccountType  from  BFBANKACCOUNTS " +
//                        " left join BFBANKACCOUNTITEMS on BFBANKACCOUNTS.id=BFBANKACCOUNTITEMS.PARENTID   where ACCOUNTNO= #{code}",mapsb);
//                //grid_BFBANKACCOUNTITEMS.ACCOUNTTYPE_NAME$LANGUAGE$
//                if(DWBH==null ){
//                    logService.init("账户信息表中无数据");
//                    continue;
//                }

                    Map<String, Object> mapsb = new HashMap<>();
                    mapsb.put("code", result.getMT94_25());//result.getACTNBR()
                    mapsb.put("BZBH", result.getAMT_CCY_BLANCE());
                    Map DWBH = sqlSession.selectOne(Map.class, "select BFBANKACCOUNTS.OPENACCOUNTUNIT ,BFBANKACCOUNTITEMS.AccountType,BFMASTERORGANIZATION.code dwbh  from  BFBANKACCOUNTS " +
                            " left join BFBANKACCOUNTITEMS on BFBANKACCOUNTS.id=BFBANKACCOUNTITEMS.PARENTID  " +
                            " left join BFMASTERORGANIZATION on BFMASTERORGANIZATION.id=BFBANKACCOUNTS.OPENACCOUNTUNIT " +
                            " left join BFCURRENCY on BFCURRENCY.id=BFBANKACCOUNTITEMS.CURRENCY  where ACCOUNTNO= #{code}" +
                            "  AND BFCURRENCY.CODE=#{BZBH} and BFBANKACCOUNTS.ACCOUNTSTATUS ='2'   and BFBANKACCOUNTS.ONLINEBANKOPENSTATUS in ('2','3')", mapsb);
                    //grid_BFBANKACCOUNTITEMS.ACCOUNTTYPE_NAME$LANGUAGE$
                    if (DWBH == null) {
                        logService.info(name,"账户信息表中无数据");
                        result.setSksyncstatus("2");
                        result.setSksyncmsg("同步失败,账户信息表中无数据");
                        jwMgZhyeResultObjectRepository.save(result);
                        continue;
                    }
                    orgData.put("ZJYHZHYEB_DWBH", DWBH.get("DWBH"));
                    orgData.put("ZJYHZHYEB_ZHBH", result.getMT94_25());//TODO:
                    orgData.put("ZJYHZHYEB_RQ", result.getDATE_BLANCE());
                    orgData.put("ZJYHZHYEB_BZ", result.getAMT_CCY_BLANCE());
                    orgData.put("ZJYHZHYEBX_QCYE", result.getAMT_BLANCE());
                    orgData.put("ZJYHZHYEB_DQYE", result.getAMT_BLANCE());
                    orgData.put("ZJYHZHYEB_GJJE",  BigDecimal.ZERO);
                    orgData.put("ZJYHZHYEB_SJ", "000000");//TODO:
                    orgData.put("ZJYHZHYEB_ZHLX", DWBH.get("ACCOUNTTYPE"));
                    //因为产品入参使用了public String属性，导致JSON格式化的报文内容重复
                    //小写部分属性不能省略，否则会导致产品接口获取不到数据
                    orgData.put("zjyhzhyeb_DWBH",DWBH.get("DWBH"));
                    orgData.put("zjyhzhyeb_ZHBH", result.getMT94_25());
                    orgData.put("zjyhzhyeb_RQ", result.getDATE_BLANCE());
                    orgData.put("zjyhzhyeb_BZ", result.getAMT_CCY_BLANCE());
                    orgData.put("zjyhzhyebX_QCYE",  result.getAMT_BLANCE());
                    orgData.put("zjyhzhyeb_DQYE", result.getAMT_BLANCE());
                    orgData.put("zjyhzhyeb_GJJE",  BigDecimal.ZERO);//归集余额
                    orgData.put("zjyhzhyeb_SJ", "000000");//TODO:
                    orgData.put("zjyhzhyeb_zhlx", DWBH.get("ACCOUNTTYPE"));

                    hashMap.add(orgData);
                    logService.info(name, "提交产品接口参数：" + hashMap);
                    List<Object> parameters = new ArrayList<Object>();
                    parameters.add(JSONSerializer.serialize(hashMap));
                    String jsonOut = InternalServiceProxy.invoke("BP/Bebc/v1.0/BalanceOnBank", "OfflineAccBalanceRev", parameters, new TypeReference<String>() {
                    });
                    logService.info(name, "产品接口返回：" + jsonOut);
                    JSONObject jObjectOut = JSON.parseObject(jsonOut);
                    if (jObjectOut != null && jObjectOut.getBoolean("relFlag")) {
                        result.setSksyncstatus("1");
                        result.setSksyncmsg("");
                        jwMgZhyeResultObjectRepository.save(result);
                    } else if (jObjectOut != null && !jObjectOut.getBoolean("relFlag")) {
                        result.setSksyncstatus("2");
                        result.setSksyncmsg(String.valueOf(jObjectOut.getString("relMsg")));
                        jwMgZhyeResultObjectRepository.save(result);
                    } else {
                        result.setSksyncstatus("2");
                        result.setSksyncmsg("同步失败");
                        jwMgZhyeResultObjectRepository.save(result);
                    }
                }catch (Throwable ex){
                    logService.error( name, "获取境外账户余额-摩根同步司库异常：",ex);
                }

            }

        }catch (Throwable ex){
            logService.error( name, "获取境外账户余额-摩根同步司库异常",ex);
        }finally {
            // logService.info( name, "结束：");
            logService.flush();
        }
    }

    /**
     *
     */
//    public void automaticGenerationMGzhjymx()
//    {
//        String name="automaticGenerationMGzhjymx";
//        logService.init(name);
//        try {
//            logService.info(name, "automaticGenerationMGZHYE");
//            List<JwMgZhyeResultObject> resultList = jwMgZhyeResultObjectRepository.findByMgyelist();
//            if (CollectionUtils.isEmpty(resultList)) {
//                logService.info(name,"无数据");
//                return;
//            }
//            List<Map<String, Object>> hashMap = new ArrayList<>();
//            for(JwMgZhyeResultObject result:resultList) {
//                try {
//                    logService.info(name,"result:"+JSONSerializer.serialize(result));
//                    OfflineTransDetailsEntity offlineTransDetailsEntity = new OfflineTransDetailsEntity();
////                    offlineTransDetailsEntity.setBankFlowNo(String.valueOf(item.getBANK_FLOW_NO()));//交易流水号 todo
//                    hashMap = new ArrayList<>();
//                    Map<String, Object> orgData = new HashMap<>();
//
//                    Map<String, Object> mapsb = new HashMap<>();
//                    mapsb.put("code", result.getMT94_25());//result.getACTNBR()
//                    mapsb.put("BZBH", result.getAMT_CCY_BLANCE());
//                    Map DWBH = sqlSession.selectOne(Map.class, "select BFBANKACCOUNTS.OPENACCOUNTUNIT ,BFBANKACCOUNTITEMS.AccountType,BFMASTERORGANIZATION.code dwbh  from  BFBANKACCOUNTS " +
//                            " left join BFBANKACCOUNTITEMS on BFBANKACCOUNTS.id=BFBANKACCOUNTITEMS.PARENTID  " +
//                            " left join BFMASTERORGANIZATION on BFMASTERORGANIZATION.id=BFBANKACCOUNTS.OPENACCOUNTUNIT " +
//                            " left join BFCURRENCY on BFCURRENCY.id=BFBANKACCOUNTITEMS.CURRENCY  where ACCOUNTNO= #{code}" +
//                            "  AND BFCURRENCY.CODE=#{BZBH} and BFBANKACCOUNTS.ACCOUNTSTATUS ='2' ", mapsb);
//                    //grid_BFBANKACCOUNTITEMS.ACCOUNTTYPE_NAME$LANGUAGE$
//                    if (DWBH == null) {
//                        logService.info(name,"账户信息表中无数据");
////                        result.setSksyncstatus("2");
////                        result.setSksyncmsg("同步失败,账户信息表中无数据");
////                        jwMgZhyeResultObjectRepository.save(result);
//                        continue;
//                    }
//                    //流水号重复校验
////                    String cxsql = "SELECT ID FROM BPBANKTRANSCATIONDETAILS WHERE BANKFLOWNO = '" + String.valueOf(item.getBANK_FLOW_NO()) + "'";
////                    List<Map> resultd = sqlSession.selectList(Map.class, cxsql);
////                    if (resultd.size() > 0) {
////                        resultMap.put("relFlag", "2");
////                        resultMap.put("message", "生单异常！");
////                        resultMap.put("err", "流水号：" + item.getBANK_FLOW_NO() + "重复！");
////                        logService.error(name, JSONSerializer.serialize(resultMap));
////                        continue;
////                    }
////                    CREDIT_DEBIT 付款方向：D付款，C收款
//                    offlineTransDetailsEntity.setIncomeOrExpenditure("D".equals(String.valueOf(result.getCREDIT_DEBIT()))?1:2);//收支属性必填 1付款 2收款
//                    orgData.put("ZJYHZHYEB_DWBH", DWBH.get("DWBH"));
//                    orgData.put("ZJYHZHYEB_ZHBH", result.getMT94_25());//TODO:
//                    orgData.put("ZJYHZHYEB_RQ", result.getDATE_BLANCE());
//                    orgData.put("ZJYHZHYEB_BZ", result.getAMT_CCY_BLANCE());
//                    orgData.put("ZJYHZHYEBX_QCYE", result.getAMT_BLANCE());
//                    orgData.put("ZJYHZHYEB_DQYE", result.getAMT_BLANCE());
//                    orgData.put("ZJYHZHYEB_GJJE",  BigDecimal.ZERO);
//                    orgData.put("ZJYHZHYEB_SJ", "000000");//TODO:
//                    orgData.put("ZJYHZHYEB_ZHLX", DWBH.get("ACCOUNTTYPE"));
//                    //因为产品入参使用了public String属性，导致JSON格式化的报文内容重复
//                    //小写部分属性不能省略，否则会导致产品接口获取不到数据
//                    orgData.put("zjyhzhyeb_DWBH",DWBH.get("DWBH"));
//                    orgData.put("zjyhzhyeb_ZHBH", result.getMT94_25());
//                    orgData.put("zjyhzhyeb_RQ", result.getDATE_BLANCE());
//                    orgData.put("zjyhzhyeb_BZ", result.getAMT_CCY_BLANCE());
//                    orgData.put("zjyhzhyebX_QCYE",  result.getAMT_BLANCE());
//                    orgData.put("zjyhzhyeb_DQYE", result.getAMT_BLANCE());
//                    orgData.put("zjyhzhyeb_GJJE",  BigDecimal.ZERO);//归集余额
//                    orgData.put("zjyhzhyeb_SJ", "000000");//TODO:
//
//                    hashMap.add(orgData);
//                    logService.info(name, "提交产品接口参数：" + hashMap);
//                    List<Object> parameters = new ArrayList<Object>();
//                    parameters.add(JSONSerializer.serialize(hashMap));
//                    String jsonOut = InternalServiceProxy.invoke("BP/Bebc/v1.0/BalanceOnBank", "OfflineAccBalanceRev", parameters, new TypeReference<String>() {
//                    });
//                    logService.info(name, "产品接口返回：" + jsonOut);
//                    JSONObject jObjectOut = JSON.parseObject(jsonOut);
//                    if (jObjectOut != null && jObjectOut.getBoolean("relFlag")) {
//                        result.setSksyncstatus("1");
//                        result.setSksyncmsg("");
//                        jwMgZhyeResultObjectRepository.save(result);
//                    } else if (jObjectOut != null && !jObjectOut.getBoolean("relFlag")) {
//                        result.setSksyncstatus("2");
//                        result.setSksyncmsg(String.valueOf(jObjectOut.getString("relMsg")));
//                        jwMgZhyeResultObjectRepository.save(result);
//                    } else {
//                        result.setSksyncstatus("2");
//                        result.setSksyncmsg("同步失败");
//                        jwMgZhyeResultObjectRepository.save(result);
//                    }
//                }catch (Throwable ex){
//                    logService.error( name, "获取境外账户余额-摩根同步司库异常：",ex);
//                }
//
//            }
//        }catch (Throwable ex){
//            logService.error( name, "获取境外账户余额-摩根同步司库异常",ex);
//        }finally {
//            // logService.info( name, "结束：");
//            logService.flush();
//        }
//    }

    public void zdtj(){
        String name="自动提交";
        logService.init(name);
        try{
           // List<Map> tjlist=sqlSession.selectList(Map.class,"select TMBANKACCOUNTOPENING.* from TMBANKACCOUNTOPENING   join bfbank join BFBANKTYPE on BFBANKTYPE.id=bfbank.BANKTYPE  on bank=bfbank.id  " +
                  //  " where TMBANKACCOUNTOPENING.DOCSTATUS='1'  and BFBANKTYPE.code='907' ");

//            create or replace  view VW_JTGKZHXX as
//            select * from (
//                    select  ID,APPNO,'BankAccountCancel' FLAG,'' OLDID,'' OLDAPPNO  from TMBANKACCOUNTCANCELLATION 	where DOCSTATUS='1' and APPNO='XHSQX2024120300003'
//                    UNION  all
//                    select  TMBANKACCOUNTOPENING.ID,TMBANKACCOUNTOPENING.APPNO,'AM_YHKH' FLAG,JFKJCSHBFBANKACCOUNTITEM.oldbfbankaccountid OLDID,JFKJCSHBFBANKACCOUNTITEM.oldbfbankaccountcode OLDAPPNO  from TMBANKACCOUNTOPENING
//                    join JFKJCSHBFBANKACCOUNTITEM on JFKJCSHBFBANKACCOUNTITEM.id=TMBANKACCOUNTOPENING.ID where TMBANKACCOUNTOPENING.DOCSTATUS='1'
//            )aa
            List<Map> tjlist=sqlSession.selectList(Map.class,"select * from VW_JTGKZHXX");
            for(Map maps:tjlist){
                try {
                    if("AM_YHKH".equals(String.valueOf(maps.get("FLAG")) )){
                        //更新生成的申请单
                        int i=sqlSession.update("update TMBANKACCOUNTOPENING set FK06='"+(maps.get("OLDAPPNO")) +"',FK07='"+(maps.get("OLDID")) +"',istask='1',FK05='0',FK03='0'," +
                                "FK02='"+(maps.get("FK02"))+"',FK09='"+(maps.get("FK09"))+"',TXT04='"+(maps.get("TXT04"))+"',TXT05='"+(maps.get("TXT05"))+"'," +
                                "FK11='"+(maps.get("FK11"))+"',FK12='"+(maps.get("FK12"))+"',TXT01='"+(maps.get("TXT01"))+"',TXT02='"+(maps.get("TXT02"))+"'," +
                                "TXT03='"+(maps.get("TXT03"))+"',TXT06='"+(maps.get("TXT06"))+"',TXT08='"+(maps.get("TXT08"))+"',TXT09='"+(maps.get("TXT09"))+"'," +
                                "TXT07='"+(maps.get("TXT07"))+"',TXT10='"+(maps.get("TXT10"))+"',YHHBID='"+(maps.get("YHHBID"))+"' ,TXT11='"+(maps.get("TXT11"))+"'" +
                                ",TXT12='"+(maps.get("TXT12"))+"',TXT13='"+(maps.get("TXT13"))+"',APPLICANTID='"+(maps.get("APPLICANTID"))+"',FK04='"+(maps.get("FK04"))+"'   WHERE ID='"+(maps.get("ID")) +"' ");
                        //更新原申请单
                        int j=sqlSession.update("update TMBANKACCOUNTOPENING set FK06='"+(maps.get("APPNO")) +"',FK07='"+(maps.get("ID")) +"' WHERE ID='"+(maps.get("OLDID")) +"' ");
                    }else{
//                        //更新生成的申请单
//                        int i=sqlSession.update("update TMBANKACCOUNTCANCELLATION set FK06='"+String.valueOf(maps.get("APPNO")) +"',FK07='"+String.valueOf(maps.get("ID")) +"' WHERE ID='"+String.valueOf(maps.get("LYID")) +"' ");
//                        //更新原申请单
//                        int j=sqlSession.update("update TMBANKACCOUNTCANCELLATION set FK06='"+String.valueOf(maps.get("APPNO")) +"',FK07='"+String.valueOf(maps.get("ID")) +"' WHERE ID='"+String.valueOf(maps.get("LYID")) +"' ");
                    }

//                Map<String,Object> aa=new HashMap();
//                aa.put("action","Submit");
//                aa.put("billId", maps.get("ID"));
//                aa.put("formType","AM_YHKH");
//                aa.put("docNo",maps.get("APPNO"));
//                aa.put("unitId",maps.get("APPUNIT"));

                    Map<String, Object> mapParams = new HashMap<>();
                    mapParams.put("BILLID", maps.get("ID"));
                    mapParams.put("OTHERINFO", "");
                    mapParams.put("FORMTYPE", maps.get("FLAG"));
                    //mapParams.put("CZID", "JSBL");
                    logService.error(name, maps.get("APPNO")+"自动提交入参：" + JSONSerializer.serialize(mapParams));
                    Map<String, TMProcessBillBase> bizBZDList = SpringBeanUtils.getApplicationContext().getBeansOfType(TMProcessBillBase.class);
                    logService.error(name, "11：");
                    TMProcessBillBase fsjsbzd = getBZD(String.valueOf(maps.get("FLAG")));
                    logService.error(name, "fsjsbzd11：" + JSONSerializer.serialize(fsjsbzd));
                    FsspResultRet resultRet = new FsspResultRet();
                    resultRet.setResult(true);
                    if (fsjsbzd != null) {
                        resultRet = fsjsbzd.fsspsubmit(mapParams, null);
                    }
                    logService.error(name, "自动提交结束resultRet：" + JSONSerializer.serialize(resultRet));
//                //发送企微消息
//                HttpResponse resmsg= HttpRequest.post("http://10.12.66.21:5200/api/tm/am/bankaccountopeningidp4/v1.0/bankaccountopening/fsspOperation")
//                        .header("Content-Type", "application/json").header("Authorization", "Bearer 8fac037f-9692-140f-334d-de807c8ad3e5")
//                        .header("X-ECC-Current-Tenant", "10000")
//                        .body(JSONSerializer.serialize(aa))
//                        .timeout(5000)
//                        .execute();
//                logService.error(name, "自动提交返回数据：" + resmsg);
//                if(resmsg.getStatus()==200) {
//                    logService.error(name, "自动提交返回数据：" + resmsg.body());
//                   // JSONObject resmsgO = JSONObject.parseObject(resmsg.body());
//                }
                }catch (Throwable ex){
                    logService.error( name, "自动提交异常：",ex);
                }
            }

        }catch (Throwable ex){
            logService.error( name, "自动提交异常",ex);
        }finally {
            // logService.info( name, "结束：");
            logService.flush();
        }
    }

    public void xhsdzdgx(){
        String name="销户更新自定义字段";
        logService.init(name);
        try {
            List<Map> tjlist=sqlSession.selectList(Map.class,"select * from VW_JTGKZHXX_XH");
            for(Map maps:tjlist) {
                try {
                    int j=sqlSession.update("update TMBANKACCOUNTCANCELLATION set TXT10='"+(maps.get("TXT10")) +"',TXT11='"+(maps.get("TXT11")) +"',FK11='"+(maps.get("FK11"))+"',FK12='"+(maps.get("FK12"))+"',FK09='"+(maps.get("FK09"))+"'" +
                            " ,TXT02='"+(maps.get("TXT02"))+"' ,TXT04='"+(maps.get("TXT04"))+"',TXT05='"+(maps.get("TXT05"))+"',FK13='"+(maps.get("FK13"))+"',APPLICANTID='"+(maps.get("APPLICANTID"))+"'  WHERE ID='"+(maps.get("ID")) +"' ");
                    logService.error(name,"j:"+j);
                }catch (Throwable ex){
                    logService.error(name,ex);

                }
            }
        }catch (Throwable ex){
            logService.error(name,ex);
        }
        finally {
            logService.flush();
        }
    }

    protected static TMProcessBillBase getBZD(String formtype) {
        Map<String, TMProcessBillBase> bizBZDList = SpringBeanUtils.getApplicationContext().getBeansOfType(TMProcessBillBase.class);
        log.info("开始执行获取getBZD第一轮：getFormType()");
        Iterator var2 = bizBZDList.keySet().iterator();

        String key;
        do {
            if (!var2.hasNext()) {
                log.info("开始执行获取getBZD第二轮：chkFormType()");
                var2 = bizBZDList.keySet().iterator();

                do {
                    if (!var2.hasNext()) {
                        return null;
                    }

                    key = (String)var2.next();
                    log.info("第二轮key:" + key);
                } while(!((TMProcessBillBase)bizBZDList.get(key)).chkFormType(formtype));

                log.info("第二轮：取到BZD实例：getFormType()=" + ((TMProcessBillBase)bizBZDList.get(key)).getFormType());
                return (TMProcessBillBase)bizBZDList.get(key);
            }

            key = (String)var2.next();
            log.info("第一轮key:" + key);
        } while(!((TMProcessBillBase)bizBZDList.get(key)).getFormType().equals(formtype));

        log.info("第一轮：取到BZD实例：getFormType()=" + ((TMProcessBillBase)bizBZDList.get(key)).getFormType());
        return (TMProcessBillBase)bizBZDList.get(key);
    }




    /*
    获取金风环保水厂信息底表数据字典  分公司财务总监 SAP数据湖  存入中间表
     */
    public void automaticHBSCB() throws Exception {
        String name="automaticHBSCB";//金风环保水厂信息底表数据字典";
        logService.init(name);
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
        if (StringUtil.isNullOrEmpty( dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        RelationalDbExector exector = ExectorFactory.create( dataSourceId);
        try{
            //#region
            logService.info( name, "automaticOrganization-dataSourceId:"+ dataSourceId);
            ResultSet gsOfOut = exector.query(" select formmain_0378.id, formmain_0378.state, formmain_0378.start_member_id, formmain_0378.start_date, formmain_0378.approve_member_id, formmain_0378.approve_date, formmain_0378.finishedflag, formmain_0378.ratifyflag, formmain_0378.ratify_member_id, formmain_0378.ratify_date, formmain_0378.sort, formmain_0378.modify_member_id, formmain_0378.modify_date, formmain_0378.field0001, formmain_0378.field0002, formmain_0378.field0003, formmain_0378.field0004, formmain_0378.field0005, formmain_0378.field0006, " +
                    "formmain_0378.field0007, formmain_0378.field0008, formmain_0378.field0009, formmain_0378.field0010, formmain_0378.field0011, formmain_0378.field0012, formmain_0378.field0013, formmain_0378.field0014, formmain_0378.field0015, formmain_0378.field0016, formmain_0378.field0017, formmain_0378.field0018, formmain_0378.field0019, formmain_0378.field0020, formmain_0378.field0021,org_member.code RYBH from  formmain_0378    left join org_member on formmain_0378.field0015=org_member.id where formmain_0378.finishedflag='0' ", null);
            logService.info( name, "automaticOrganization-ResultSet:" );
            List<OaSpResultObjectDto> resultList=new ArrayList<>();
            while (gsOfOut.next()) {
                OaSpResultObjectDto entity = new OaSpResultObjectDto();
                entity.setId(gsOfOut.getString(1) );//
                entity.setState(gsOfOut.getString(2) );//
                entity.setStart_member_id(gsOfOut.getString(3) );//
                entity.setStart_date(gsOfOut.getString(4) );//
                entity.setApprove_member_id(gsOfOut.getString(5) );//
                entity.setApprove_date(gsOfOut.getString(6) );//
                entity.setFinishedflag(gsOfOut.getString(7) );//
                entity.setRatifyflag(gsOfOut.getString(8) );//
                entity.setRatify_member_id(gsOfOut.getString(9) );//
                entity.setRatify_date(gsOfOut.getString(10) );//
                entity.setSort(gsOfOut.getString(11) );//
                entity.setModify_member_id(gsOfOut.getString(12) );//
                entity.setModify_date(gsOfOut.getString(13) );//
                entity.setField0001(gsOfOut.getString(14) );//
                entity.setField0002(gsOfOut.getString(15) );//
                entity.setField0003(gsOfOut.getString(16) );//
                entity.setField0004(gsOfOut.getString(17) );//
                entity.setField0005(gsOfOut.getString(18) );//
                entity.setField0006(gsOfOut.getString(19) );//
                entity.setField0007(gsOfOut.getString(20) );//
                entity.setField0008(gsOfOut.getString(21) );//
                entity.setField0009(gsOfOut.getString(22) );//
                entity.setField0010(gsOfOut.getString(23) );//
                entity.setField0011(gsOfOut.getString(24) );//
                entity.setField0012(gsOfOut.getString(25) );//
                entity.setField0013(gsOfOut.getString(26) );//
                entity.setField0014(gsOfOut.getString(27) );//
                entity.setField0015(gsOfOut.getString(28) );//
                entity.setField0016(gsOfOut.getString(29) );//
                entity.setField0017(gsOfOut.getString(30) );//
                entity.setField0018(gsOfOut.getString(31) );//
                entity.setField0019(gsOfOut.getString(32) );//
                entity.setField0020(gsOfOut.getString(33) );//
                entity.setField0021(gsOfOut.getString(34) );//
                entity.setRybh(gsOfOut.getString(35) );
                resultList.add(entity);
            }
            logService.info( name, "automaticOrganization-ResultSet11:" );
            for(OaSpResultObjectDto item:resultList){
                try {
                    boolean infoChange = false;
                    //存入二开中间表，根据code  实时更新中间表
                    OaSpResultObjectEntity gsResultObject = new OaSpResultObjectEntity(item);
                    Optional<OaSpResultObjectEntity> infoInDB = oaSpResultObjectrRepository.findById(item.getId());//查询是否有相同编号的
                    if (!infoInDB.isPresent()) {
                        infoChange = true;
                       // gsResultObject.setFlag("D".equals(gsResultObject.getFlag()) ? "D" : "A");//A新增，M修改，D删除
                    } else {
                        if (Objects.equals(infoInDB.get().getId(),gsResultObject.getId())&&
                                Objects.equals(infoInDB.get().getState(),gsResultObject.getState())&&
                                Objects.equals(infoInDB.get().getStart_member_id(),gsResultObject.getStart_member_id())&&
                                Objects.equals(infoInDB.get().getStart_date(),gsResultObject.getStart_date())&&
                                Objects.equals(infoInDB.get().getApprove_member_id(),gsResultObject.getApprove_member_id())&&
                                Objects.equals(infoInDB.get().getApprove_date(),gsResultObject.getApprove_date())&&
                                Objects.equals(infoInDB.get().getFinishedflag(),gsResultObject.getFinishedflag())&&
                                Objects.equals(infoInDB.get().getRatifyflag(),gsResultObject.getRatifyflag())&&
                                Objects.equals(infoInDB.get().getRatify_member_id(),gsResultObject.getRatify_member_id())&&
                                Objects.equals(infoInDB.get().getRatify_date(),gsResultObject.getRatify_date())&&
                                Objects.equals(infoInDB.get().getSort(),gsResultObject.getSort())&&
                                Objects.equals(infoInDB.get().getModify_member_id(),gsResultObject.getModify_member_id())&&
                                Objects.equals(infoInDB.get().getModify_date(),gsResultObject.getModify_date())&&
                                Objects.equals(infoInDB.get().getField0001(),gsResultObject.getField0001())&&
                                Objects.equals(infoInDB.get().getField0002(),gsResultObject.getField0002())&&
                                Objects.equals(infoInDB.get().getField0003(),gsResultObject.getField0003())&&
                                Objects.equals(infoInDB.get().getField0004(),gsResultObject.getField0004())&&
                                Objects.equals(infoInDB.get().getField0005(),gsResultObject.getField0005())&&
                                Objects.equals(infoInDB.get().getField0006(),gsResultObject.getField0006())&&
                                Objects.equals(infoInDB.get().getField0007(),gsResultObject.getField0007())&&
                                Objects.equals(infoInDB.get().getField0008(),gsResultObject.getField0008())&&
                                Objects.equals(infoInDB.get().getField0009(),gsResultObject.getField0009())&&
                                Objects.equals(infoInDB.get().getField0010(),gsResultObject.getField0010())&&
                                Objects.equals(infoInDB.get().getField0011(),gsResultObject.getField0011())&&
                                Objects.equals(infoInDB.get().getField0012(),gsResultObject.getField0012())&&
                                Objects.equals(infoInDB.get().getField0013(),gsResultObject.getField0013())&&
                                Objects.equals(infoInDB.get().getField0014(),gsResultObject.getField0014())&&
                                Objects.equals(infoInDB.get().getField0015(),gsResultObject.getField0015())&&
                                Objects.equals(infoInDB.get().getField0016(),gsResultObject.getField0016())&&
                                Objects.equals(infoInDB.get().getField0017(),gsResultObject.getField0017())&&
                                Objects.equals(infoInDB.get().getField0018(),gsResultObject.getField0018())&&
                                Objects.equals(infoInDB.get().getField0019(),gsResultObject.getField0019())&&
                                Objects.equals(infoInDB.get().getField0020(),gsResultObject.getField0020())&&
                                Objects.equals(infoInDB.get().getField0021(),gsResultObject.getField0021())&&
                                Objects.equals(infoInDB.get().getRybh(),gsResultObject.getRybh())
                        ) {//如果基本信息一致，不再更新

                        } else {
                            infoChange = true;
//                            gsResultObject.setFlag("D".equals(gsResultObject.getFlag()) ? "D" : "M");
                        }
                    }
                    if (infoChange) {
                        gsResultObject.setSksyncstatus("0");
                        gsResultObject.setSksyncmsg("");
                        oaSpResultObjectrRepository.save(gsResultObject);
                    }
                }catch (Throwable ex){
                    logService.error( name, "获取水厂底表异常：",ex);
                }
            }


            //#endregion

        }catch (Throwable ex){
            logService.error( name, "获取水厂底表异常：",ex);
        }
        finally{
            //关闭连接
            if(exector!=null)exector.close();
            // logService.info( name, "结束：");
            logService.flush();
        }
    }
    /*
    获取金风环保水厂信息底表数据字典  分公司财务总监 SAP数据湖  二开中间表同步司库  司库从SAP获取的分公司财务总监根据公司代码同步  财务总监【职位定义】 对应人员
    select * from BFDUTY where code='001';--职务定义
    select * from BFPOSITION---职位定义
     */
    public void automaticGenerationHBSCB()
    {
        String name="automaticGenerationHBSCB";//公司主数据同步司库";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationHBSCB");
            //#region
            List<OaSpResultObjectEntity> resultList = oaSpResultObjectrRepository.findTop1000BySksyncstatus("0");
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }
//            logService.info( name, "automaticGenerationOrganization-resultList:{}",JSONSerializer.serialize(resultList));
            for(OaSpResultObjectEntity result:resultList){
                try {
                    logService.info(name,"result入参:"+result);
                    LinkedHashMap<String, Object> orgparam = new LinkedHashMap<String, Object>();
                    Map<String, Object> hashMap = new HashMap<>();
                    Map<String, Object> orgData = new HashMap<>();

                    List<Map> zzlist=sqlSession.selectList(Map.class,"select BFPOSITION.ID,BFPOSITION.CODE,BFPOSITION.NAME_CHS,BFPOSITION.OFPOST,BFPOSITION.REMARK,BFPOSITION.OFORGANIZATION from BFPOSITION left join bfmasterorganization on BFPOSITION.OFORGANIZATION=bfmasterorganization.id WHERE OFPOST=(select id from BFDUTY where code='001') and bfmasterorganization.code='"+result.getField0001()+"' ");
                    String id="";
                    String code= "";
                    String zzmc= "";
                    String zzid= "";
                    String zwid="";String bz="";
                    if(zzlist!=null&&zzlist.size()>0){
                         hashMap.put("operation",  "modify" );//操作类型
                         id= ObjectUtils.isEmpty(zzlist.get(0).get("ID"))?"":zzlist.get(0).get("ID").toString();
                         code= ObjectUtils.isEmpty(zzlist.get(0).get("CODE"))?"":zzlist.get(0).get("CODE").toString();
                         zzmc= ObjectUtils.isEmpty(zzlist.get(0).get("NAME_CHS"))?"":zzlist.get(0).get("NAME_CHS").toString();
                         zzid= ObjectUtils.isEmpty(zzlist.get(0).get("OFORGANIZATION"))?"":zzlist.get(0).get("OFORGANIZATION").toString();
                         zwid= ObjectUtils.isEmpty(zzlist.get(0).get("OFPOST"))?"":zzlist.get(0).get("OFPOST").toString();
                         bz=ObjectUtils.isEmpty(zzlist.get(0).get("REMARK"))?"":zzlist.get(0).get("REMARK").toString();
                    }else{
                        hashMap.put("operation",  "add" );//操作类型
                    }
                    Map zzjg=sqlSession.selectOne(Map.class,"select ID,NAME_CHS from bfmasterorganization where code='"+result.getField0001()+"'");
                    logService.error(name,"zzjg:"+JSONSerializer.serialize(zzjg));
                    if(!zzjg.isEmpty()){
//                        logService.error(name,"zzmc:"+zzmc+",zzid:"+zzid);
                        if(StringUtil.isNullOrEmpty(zzmc)){
                            zzmc=ObjectUtils.isEmpty(zzjg.get("NAME_CHS"))?"":zzjg.get("NAME_CHS").toString()+"财务总监";
                        }
                        if(StringUtil.isNullOrEmpty(zzid)){
                            zzid=ObjectUtils.isEmpty(zzjg.get("ID"))?"":zzjg.get("ID").toString();
                        }
//                        logService.error(name,"zzmc:"+zzmc+",zzid:"+zzid);
                    }
                    String zw=sqlSession.selectOne(String.class,"select id from BFDUTY where code='001'");
                    if(StringUtil.isNullOrEmpty(zwid)){
                        zwid=zw;
                    }

                    orgData.put("ID", id);
                    orgData.put("CODE", code);
                    orgData.put("NAME",zzmc);//名称
                    orgData.put("OFORGANIZATION", zzid); // 所属组织 关联行政组织 新增必填
                    orgData.put("OFPOST", zwid);// 职务 关联职务 新增必填
                    orgData.put("REMARK",bz);//备注
                    List<Map<String,Object>> zjrylist= new ArrayList<>();
                    Map<String,Object> zzry= new HashMap<>();
                    //todo:
                    Map<String, Object> mapsb = new HashMap<>();
                    mapsb.put("RYBH", result.getRybh());//result.getACTNBR()
                    mapsb.put("GSDM", result.getField0001());
                    String ifcz=sqlSession.selectOne(String.class,"select count(1) from BFPOSITIONEMPLOYEE  " +
                            "left join BFPOSITION on BFPOSITIONEMPLOYEE.parentid=BFPOSITION.id " +
                            "left join bfmasterorganization on BFPOSITION.OFORGANIZATION=bfmasterorganization.id " +
                            "left join bfemployee on bfemployee.id=BFPOSITIONEMPLOYEE.EMPLOYEE " +
                            "where bfemployee.code=#{RYBH} and  bfmasterorganization.code=#{GSDM} ",mapsb);
                    if(!"0".equals(ifcz)){
                        logService.info(name,"已存在不处理mapsb:"+JSONSerializer.serialize(mapsb));
                        result.setSksyncstatus("1");
                        result.setSksyncmsg("");
                        oaSpResultObjectrRepository.save(result);
                        continue;
                    }
                    String oldry=sqlSession.selectOne(String.class,"select BFPOSITIONEMPLOYEE.ID from BFPOSITIONEMPLOYEE  " +
                            "left join BFPOSITION on BFPOSITIONEMPLOYEE.parentid=BFPOSITION.id " +
                            "left join bfmasterorganization on BFPOSITION.OFORGANIZATION=bfmasterorganization.id " +
                            "left join bfemployee on bfemployee.id=BFPOSITIONEMPLOYEE.EMPLOYEE " +
                            "where bfemployee.code!=#{RYBH} and  bfmasterorganization.code=#{GSDM} ",mapsb);
                    if(!StringUtils.isEmpty(oldry)){
                        logService.info(name,"之前的人员删除:"+JSONSerializer.serialize(mapsb));
                        Map<String,Object> oldzzry= new HashMap<>();
                        oldzzry.put("ID",oldry);
                        oldzzry.put("DELETEALL","TRUE");
                        orgData.put("DELETEPOSITIONEMPLOYEELINK",zjrylist);
                    }

                    String ryid =sqlSession.selectOne(String.class,"select id from bfemployee where code='"+result.getRybh()+"' ");
//                    zzry.put("ID","");
                    zzry.put("EMPLOYEE",ryid);// 行政人员ID
//                    zzry.put("STARTTIME","");//"yyyy-MM-dd，起始时间"
//                    zzry.put("ENDTIME","");//"yyyy-MM-dd，结束时间"
                    zjrylist.add(zzry);
                    orgData.put("ADDPOSITIONEMPLOYEELINK",zjrylist);
                    hashMap.put("data", orgData);
                    orgparam.put("param", hashMap);//hashmap内容为所需参数
                    String su = "df";
                    String serviceID = "com.inspur.gs.bf.df.commonservice.api.IPositionService.synchronous";
                    logService.info(name, "automaticGenerationZjry-产品入参：" + JSONSerializer.serialize(orgparam));
                    HashMap results = rpcClient.invoke(HashMap.class, serviceID, su, orgparam, null);

                    logService.info(name, "automaticGenerationZjry-产品返回：" + results);
                    if ("success".equals(String.valueOf(results.get("status")))) {
                        result.setSksyncstatus("1");
                        result.setSksyncmsg("");
                        oaSpResultObjectrRepository.save(result);
                    } else {
                        result.setSksyncstatus("2");
                        result.setSksyncmsg(String.valueOf(results.get("message")));
                        oaSpResultObjectrRepository.save(result);
                    }

                }catch (Throwable ex){
                    logService.error( name, "获取财务总监异常：",ex);
                }
            }
            //#endregion

        }catch (Throwable ex){
            logService.error( name, "获取财务总监异常1",ex);
        }finally {
            // logService.info( name, "结束：");
            logService.flush();
        }
    }

    /*
   获取账户科目余额 二开中间表 同步司库账户余额表  作废
    */
    public void automaticGenerationZHKMYE()
    {
        String name="automaticGenerationZHKMYE";
        logService.init(name);
        try{
            logService.info( name, "automaticGenerationZHKMYE");
            List<Map> resultList = sqlSession.selectList(Map.class,"select * from VW_JTGKZHKMYETB");
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }

            List<Map<String, Object>> hashMap = new ArrayList<>();
            for(Map result:resultList) {
                try {
                    hashMap = new ArrayList<>();
                    Map<String, Object> orgData = new HashMap<>();
                    orgData.put("ZJYHZHYEB_DWBH", result.get("DWBH"));
                    orgData.put("ZJYHZHYEB_ZHBH", result.get("ZHBH"));
                    orgData.put("ZJYHZHYEB_RQ", result.get("RQ"));
                    orgData.put("ZJYHZHYEB_BZ", result.get("BZ"));
                    orgData.put("ZJYHZHYEBX_QCYE",  result.get("QCYE"));
                    orgData.put("ZJYHZHYEB_DQYE", result.get("DQYE"));
                    orgData.put("ZJYHZHYEB_GJJE",  BigDecimal.ZERO);
                    orgData.put("ZJYHZHYEB_SJ", result.get("SJ"));
                    //因为产品入参使用了public String属性，导致JSON格式化的报文内容重复
                    //小写部分属性不能省略，否则会导致产品接口获取不到数据
                    orgData.put("zjyhzhyeb_DWBH",result.get("DWBH"));
                    orgData.put("zjyhzhyeb_ZHBH", result.get("ZHBH"));
                    orgData.put("zjyhzhyeb_RQ", result.get("RQ"));
                    orgData.put("zjyhzhyeb_BZ", result.get("BZ"));
                    orgData.put("zjyhzhyebX_QCYE",   result.get("QCYE"));
                    orgData.put("zjyhzhyeb_DQYE", result.get("DQYE"));
                    orgData.put("zjyhzhyeb_GJJE",  BigDecimal.ZERO);
                    orgData.put("zjyhzhyeb_SJ", result.get("SJ"));
                    // ******** 账户类型
                    orgData.put("ZJYHZHYEB_ZHLX", result.get("ZHLX"));
                    orgData.put("zjyhzhyeb_zhlx", result.get("ZHLX"));
                    hashMap.add(orgData);
                    logService.info(name, "提交产品接口参数：" + hashMap);
                    List<Object> parameters = new ArrayList<Object>();
                    parameters.add(JSONSerializer.serialize(hashMap));
                    String jsonOut = InternalServiceProxy.invoke("BP/Bebc/v1.0/BalanceOnBank", "OfflineAccBalanceRev", parameters, new TypeReference<String>() {
                    });
                    logService.info(name, "产品接口返回：" + jsonOut);
                    JSONObject jObjectOut = JSON.parseObject(jsonOut);
                    if (jObjectOut != null && jObjectOut.getBoolean("relFlag")) {

                    } else if (jObjectOut != null && !jObjectOut.getBoolean("relFlag")) {

                    } else {

                    }
                }catch (Throwable ex){
                    logService.error( name, "获取境外账户余额-花旗同步司库异常：",ex);
                }
            }

        }catch (Throwable ex){
            logService.error( name, "获取境外账户余额-花旗同步司库异常",ex);
        }finally {
            // logService.info( name, "结束：");
            logService.flush();
        }
    }

    /**
     * @description: 资金计划编制任务自动生成任务 select * from TBFASL  启动一次生成一个
     * @Param:
     * @return:
     * @Author: lq
     * @Date: 2023/2/1
     * CREATE VIEW JTZJ_JTGKRJHZDTJ  AS
     * select tbbbst_slnm SLNM,TBBBST_BBNM BBNM,TBBBST_ZZNM ZZNM from TBBBST
     * left join TBFASL on TBBBST_SLNM=TBFASL_SLNM
     * left join tbbzfa on  tbbzfa_fanm=TBFASL_fanm
     * where TBBZFA_GDZQ ='13'      AND TBFASL_bzks >= to_char(current_date + interval '1 day', 'YYYYMMDD')
     * AND TBFASL_bzzz <= to_char(current_date + interval '1 day', 'YYYYMMDD')  and TBBBST_STAT ='BZ'
     */
    public void automaticRjhzdtj()
    {
       String name="日计划自动提交";
        try
        {
            logService.init(name);
            List<Map> arapBillList = sqlSession.selectList(Map.class,"select * from JTZJ_JTGKRJHZDTJ");
            logService.error(name,"arapBillList.size()：" + arapBillList.size());
            if (CollectionUtils.isEmpty(arapBillList)|| arapBillList.size() < 1)
            {
                logService.error(name,"未查询到需要提交的日计划！");
                return;
            }
            logService.error(name,"需要提交的日计划列表：" + JSONSerializer.serialize(arapBillList));
            for (Map<String, Object> entity : arapBillList) {
                String serviceId = "inspur.cb.jhjh.api.PlanPushDataApi.MXJHSubmit";
                String suName = "jhpt";
                LinkedHashMap params = new LinkedHashMap();
                JSONObject dataMap = new JSONObject();
                dataMap.put("zznm", !ObjectUtils.isEmpty(entity.get("ZZNM")) ? entity.get("ZZNM").toString() : "");
                dataMap.put("slnm", !ObjectUtils.isEmpty(entity.get("SLNM")) ? entity.get("SLNM").toString() : "");
                dataMap.put("bbnm", !ObjectUtils.isEmpty(entity.get("BBNM")) ? entity.get("BBNM").toString() : "");
                params.put("parameters", dataMap.toString());
                String response = rpcClient.invoke(String.class, serviceId, suName, params, null);
                logService.error(name,"生成编制任务返回提示信息：" + response);
            }
        }
        catch (Throwable e)
        {
            logService.error(name,"日计划自动提交报错："+ ExceptionUtils.getStackTrace(e));
        }
        finally {
            logService.error(name,"日计划自动提交结束！");
            logService.flush();

        }
    }

    public void automaticRjhzdtj_wb(){
        String name="日计划自动提交_未编";
        try
        {
            logService.init(name);
            List<Map> arapBillList = sqlSession.selectList(Map.class,"select * from JTZJ_JTGKRJHZDTJ_WB");
            logService.error(name,"arapBillList.size()：" + arapBillList.size());
            if (CollectionUtils.isEmpty(arapBillList)|| arapBillList.size() < 1)
            {
                logService.error(name,"未查询到需要提交的日计划！");
                return;
            }
            logService.error(name,"需要提交的日计划列表：" + JSONSerializer.serialize(arapBillList));
            for (Map<String, Object> entity : arapBillList) {
                List<Map> glwdList = new ArrayList();
                Map map = new HashMap();
                map.put("ysnm", "1");
                map.put("short", "DX01");
                map.put("value", !ObjectUtils.isEmpty(entity.get("ZZNM")) ? entity.get("ZZNM").toString() : ""); //修改为预算组织的内码，从tbyszz中查询tbyszz_zznm
                glwdList.add(map);
                LinkedHashMap DataStruct = new LinkedHashMap();
                JSONObject structQueryjson = new JSONObject();
                structQueryjson.put("faslnm", !ObjectUtils.isEmpty(entity.get("SLNM")) ? entity.get("SLNM").toString() : ""); //修改为对应活动的内码，从tbfasl中查询tbfasl_slnm
                structQueryjson.put("bbdynm", !ObjectUtils.isEmpty(entity.get("DYNM")) ? entity.get("DYNM").toString() : "");//修改为对应预算表的内码，从tbbbdy中查询tbbbdy_dynm
                structQueryjson.put("bbdybh", !ObjectUtils.isEmpty(entity.get("DYBH")) ? entity.get("DYBH").toString() : "");//修改为对应预算表的编号，从tbbbdy中查询tbbbdy_dybh  "NDZJJH"
                structQueryjson.put("zznm",  !ObjectUtils.isEmpty(entity.get("ZZNM")) ? entity.get("ZZNM").toString() : "");//修改为预算组织的内码，从tbyszz中查询tbyszz_zznm
                structQueryjson.put("mxnm",  !ObjectUtils.isEmpty(entity.get("BS")) ? entity.get("BS").toString() : "");//修改为预算组织的唯一标识 "NDZJ"
                structQueryjson.put("glwd", JSONSerializer.serialize(glwdList));
                structQueryjson.put("frombbstat", "WB");//预算表从什么状态  未编
                structQueryjson.put("tobbstat", "SX");// 预算表更新为什么状态  生效
                structQueryjson.put("fromdatastat", "L");//预算数据从什么状态
                structQueryjson.put("todatastat", "E");//预算数据更新为什么状态
                DataStruct.put("parameters", structQueryjson.toString());
                String resulttest = rpcClient.invoke(String.class, "inspur.cb.pub.api.PubFunctionRPCApi.UpdateTableStat", "cbp", DataStruct, null);
                logService.error(name,"日计划自动提交提示信息：" + resulttest);
            }
        }
        catch (Throwable e)
        {
            e.printStackTrace();
            logService.error(name,"日计划自动提交报错："+ ExceptionUtils.getStackTrace(e));
        }
        finally {
            logService.error(name,"日计划自动提交结束！");
            logService.flush();

        }
    }


    /*
        获取ZFI641银行账户3个月未交易久悬科目列表
    */
    public void automaticJXH() throws Exception {
        String name="automaticJXH";//金风环保水厂信息底表数据字典";
        logService.init(name);
        String dataSourceId = sqlSession.selectOne(String.class, "select id from GSPDATASOURCEENTITY where code = 'SAP'");
        if (StringUtil.isNullOrEmpty( dataSourceId)) {
            logService.info( name, "外部数据源生成失败，数据源编码：HTJHDbSource, 请确认配置信息。");
            return;
        }
        RelationalDbExector exector = ExectorFactory.create( dataSourceId);
        try{
            //#region
            logService.info( name, "automaticJXH-dataSourceId:"+ dataSourceId);
            ResultSet gsOfOut = exector.query(" select BUKRS,HKONT,BANKN,BUTXT,BANKA,PERNR,CNAME,MAIL from dm_company_bank_account_manager_i_rt ", null);
            logService.info( name, "automaticJXH-ResultSet:" );
            List<JxhResultObjectDto> resultList=new ArrayList<>();
            while (gsOfOut.next()) {
                JxhResultObjectDto entity = new JxhResultObjectDto();
                entity.setBUKRS(gsOfOut.getString(1) );//
                entity.setHKONT(gsOfOut.getString(2) );//
                entity.setBANKN(gsOfOut.getString(3) );//
                entity.setBUTXT(gsOfOut.getString(4) );//
                entity.setBANKA(gsOfOut.getString(5) );//
                entity.setPERNR(gsOfOut.getString(6) );//
                entity.setCNAME(gsOfOut.getString(7) );//
                entity.setMAIL(gsOfOut.getString(8) );//
                resultList.add(entity);
            }

           int del=   sqlSession.delete("delete from JTGKJXHRESULT");
            logService.info( name, "automaticJXH-ResultSet:"+del );
            for(JxhResultObjectDto item:resultList){
                try {
                    boolean infoChange = false;
                    //存入二开中间表，根据code  实时更新中间表
                    JxhResultObjectEntity gsResultObject = new JxhResultObjectEntity(item);
                    gsResultObject.setSksyncstatus("0");
                    gsResultObject.setSksyncmsg("");
                    jxhResultObjectEntityRepository.save(gsResultObject);
                }catch (Throwable ex){
                    logService.error( name, "获取ZFI641银行账户3个月未交易久悬科目列表异常：",ex);
                }
            }


            //#endregion

        }catch (Throwable ex){
            logService.error( name, "获取ZFI641银行账户3个月未交易久悬科目列表异常：",ex);
        }
        finally{
            //关闭连接
            if(exector!=null)exector.close();
            // logService.info( name, "结束：");
            logService.flush();
        }
    }

    /*
  获取得久悬户列表，更新账户信息表
    */
    public void automaticJXHupdate()
    {
        String name="automaticJXHupdate";//
        logService.init(name);
        try{
            logService.info( name, "automaticJXHupdate");
            List<Map> resultList = sqlSession.selectList(Map.class," select * from JTGKJXHRESULT ");
            if (CollectionUtils.isEmpty(resultList)) {
                logService.info(name,"无数据");
                return;
            }
            String sql=" update bfbankaccountitems set FK06='0' where FK06='1' ";
            sqlSession.update(sql);
            String bbsql=" update bfbankaccountitems set FK06='1'  where exists (select 1 from JTGKJXHRESULT where HKONT=bfbankaccountitems.fk01) ";
            int i= sqlSession.update(bbsql);
            logService.info( name, "automaticGenerationSFBB-update:{}",i);
        }catch (Throwable ex){
            logService.error( name, "获取久悬户列表异常",ex);
        }finally {
            //  logService.info( name, "结束：");
            logService.flush();
        }
    }


}
