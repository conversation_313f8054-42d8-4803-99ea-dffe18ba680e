package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.controller.goldInterfaceController;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao.PayResultBackDto;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao.returnParamDto;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.JTGKPayResultBackEntity;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub.GUIDUtils;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.pub.WebServiceUtilek;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository.PayResultBackRepository;
import com.inspur.fastdweb.core.FastdwebSqlSession;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.common.JSONSerializer;
import io.iec.edp.caf.commons.utils.CollectionUtils;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class goldInterfaceService  implements goldInterfaceController {
//    @Autowired
//    private LogService logService;

    @Autowired
    private PayResultBackRepository payResultBackRepository;


    /**
     * 付款退回前端业务系统
     * @param param
     * @return
     */
    @Override
    public String payResultFeeback(Map<String, Object> param) {
        log.error("入参："+JSONSerializer.serialize(param));
        LogService logService=SpringBeanUtils.getBean(LogService.class);
        FastdwebSqlSession sqlSession = SpringBeanUtils.getBean(FastdwebSqlSession.class);
        String name="付款退回前端业务系统";
        logService.init(name);
        String id="";
        String mxid="";
        returnParamDto result=new returnParamDto();
        logService.info(name,"入参："+JSONSerializer.serialize(param));
        try {
            //RPC接口入参：{"parentId":"ae414297-0fdb-4804-be1b-2a485da6d910","detailId":"f722d51f-7cd5-4c3b-9d20-f3cf8d0dc8f9"}
              id = String.valueOf(param.get("parentId"));//待付池主表id
             mxid = String.valueOf(param.get("detailId"));//待付池明细表id  待付池退回时为空使用主表id获取数据退回，不为空时使用明细表id
            //#region 待付池字段解析
            //DOCSTATUS： 0,制单 X 外部系统传入司库待付池;1,待付款安排 X 需要付款安排还未安排得 ;-1,接收失败 X;
            // 2,已完成 安排完成 ;-2,已退回[这里是司库待付池手工点击退回后调用我的退回接口【SFS场景，其他异构系统得走这个调度反馈】
            //部分终止付款【付款安排+结算办理】、全部终止付款【待付池】手工退回前端业务系统【这里终止付款后不退回还是可以再次安排，退回后不能安排了】  ;
            // -3,已作废 X 共享 组合支付 拆分之后 再次回传司库场景【共享拆分后再次
            // 推送司库时会返回原单将原单作废掉，这个退回接口不处理】 共享链接还没给 TODO：
            //RESULTBACKSTATUS 付款结果回传状态(null未回传、1付款完成待回传、2回传对方接收成功、3回传对方接收失败)   接口回传后回写
            //REDDOCRESPONSE 退汇回传结果说明
            //REQUESTDOCNO 业务支付申请单号
            //REQUESTDETAILID 业务支付申请明细ID
            //REFDOCID 付款安排单ID
            //DOCSTATUS 付款结果(1,付款成功;2,付款终止;3,退回;4,银行交易失败退汇;5,票据签收)
            //REDDOCSTATUS 退汇回传状态(null未退汇、1已退汇待回传、2回传对方接收成功、3回传对方接收失败)
            //REFDETAILID 付款安排单明细ID
            //REDDOCNO 退汇单据编号
            //RESULTBACKRESPSONSE 付款结果回传说明
            //PAYDOCID 付款结算单ID
            //PAYDOCTYPE 付款结算单类型
            //REDDOCPROCESSON 退汇回传时间
            //REQUESTDOCID 业务支付申请ID
            //#endregion
            //#region
            String sql=" select * from VW_JTGKPAYRESULTBACK WHERE ID='"+mxid+"' ";
            if(StringUtils.isEmpty(mxid)){//之前得需求，由于退回得没有执行记录所以只需要查询主表，变更需求，退回没有安排得也需要有执行记录
                sql=" select * from VW_JTGKPAYRESULTBACK_DFC WHERE ID='"+id+"' ";
            }
            List<PayResultBackDto>  payLists=sqlSession.selectList(PayResultBackDto.class,sql);
            if(payLists!=null&&payLists.size()>0){
                result =paymentResultPush(name,payLists,id,logService);
            }
            //#endregion
        } catch (Throwable  e) {
            e.printStackTrace();
            result.setResult(false);
            result.setMessage("推送异常");
            result.setSrcDocId(id);
            logService.error(name,"推送异常！",e);
            return "";
        }
        finally {
            logService.flush();
            return JSONSerializer.serialize(result);
        }

    }
    public returnParamDto paymentResultPush(String name,List<PayResultBackDto> payLists,String id,LogService logService){
        returnParamDto result=new returnParamDto();
        FastdwebSqlSession sqlSession = SpringBeanUtils.getBean(FastdwebSqlSession.class);
        try {
            logService.error(name,"入参："+JSONSerializer.serialize(payLists));
            if(payLists!=null&&payLists.size()>0){
                //            通用数据字典定义
//            select * from IDD_DATADICCATE  where code='PayResultFeedback_url'
//                    --通用数据字典配置  categoryid=IDD_DATADICCATE.id
//            select * from IDD_DATADICTIONARY  where categoryid='65c9af72-49e3-5367-bb4e-add902300ba6'
                Map<String,Object> maps = new HashMap<String,Object>();
                maps.put("code",payLists.get(0).getSrcBizSys());
                logService.info(name,"payLists.get(0).getSrcBizSys():"+payLists.get(0).getSrcBizSys());
                logService.info(name,"sqlSession:"+sqlSession);
                //获取接口地址配置
                List<Map> pzlist=sqlSession.selectList(Map.class," select IDD_DATADICTIONARY.* from IDD_DATADICTIONARY  join  IDD_DATADICCATE on categoryid=IDD_DATADICCATE.id " +
                        " where IDD_DATADICCATE.code='PayResultFeedback_url'  and IDD_DATADICTIONARY.code= '"+payLists.get(0).getSrcBizSys()+"' ");
                if(pzlist==null||pzlist.size()==0){
                    result.setResult(false);
                    result.setMessage("未配置接口路径");
                    result.setSrcDocId(id);
                    logService.error(name,"未配置接口路径！");
                }
                else {
                    String url = String.valueOf(pzlist.get(0).get("BIGTXT01"));//更新日期，不传此参数，则获取全量数据    scc/123456   geam/123456
//            String s = "pitims:ACvpfiw2A9GgB-HNwPfY";
                    String username = String.valueOf(pzlist.get(0).get("TXT01"));
                    String pwd = String.valueOf(pzlist.get(0).get("TXT02"));
                    String mandt=String.valueOf(pzlist.get(0).get("CODE"));
                    String encode = Base64.encode(pzlist.get(0).get("TXT01") + ":" + pzlist.get(0).get("TXT02"));
                    logService.info(name, "url:" + url);
                    logService.info(name, "encode:" + encode);
                    String lsh = UUID.randomUUID().toString();
                    logService.error(name, "流水号：" + lsh);
                    List<returnParamDto> resultList = new ArrayList<>();
                    //DOCSTATUS=PayStatus 付款结果(1,付款成功;2,付款终止;3,退回;4,银行交易失败退汇;5,票据签收)
                    if("SFS-PAY".equals(payLists.get(0).getSrcBizSys())&&"1".equals(payLists.get(0).getPayStatus())){
                        //#region SFS接口 付款成功 走SFS反馈接口 webservice  结果反馈后 sap生成凭证
                        result=paymentResultPush_SFS(name,payLists,id,logService,url,username,pwd,mandt);
                        //#endregion
                    }else{
                        //#region 非SFS接口走通用反馈接口
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("requestId", lsh);//请求流水号
                        jsonObject.put("data", payLists);
                        logService.error(name, "接口入参：" + jsonObject.toJSONString());
                        HttpResponse resmsg = HttpRequest.post(url)
                                .header("Content-Type", "application/json")
                                .header("Authorization", "Basic " + encode)
                                .body(jsonObject.toJSONString())
                                .timeout(5000)
                                .execute();
                        logService.info(name, "付款结果反馈出参:{}", resmsg);

                        if (resmsg.getStatus() == 200) {
                            JSONObject resultJson = JSONObject.parseObject(resmsg.body());
                            if (resultJson != null ) {//&& "000".equals(resultJson.getString("code"))
                                resultList = JSON.parseArray(resultJson.getString("data"), returnParamDto.class);
                            }
                            logService.info( name, "resultList:{}", JSONSerializer.serialize(resultList));
                            if (!CollectionUtils.isEmpty(resultList) && resultList.size() > 0) {
                                if (!StringUtils.isEmpty(id)) {//待付池退回按钮 点击退回时传入得id【一个一个调，所以直接反馈】，调度入口没有传递id
                                    return resultList.get(0);
                                }
                                //调度入口
                                for (returnParamDto item : resultList) {
                                    if (item.getResult()) {
                                        sqlSession.update("update JTGKPAYMENTDETAIL set RESULTBACKSTATUS='2' where EXISTS(SELECT 1 FROM JTGKPAYMENTINFO WHERE srcDocId='" + item.getSrcDocId() + "')");
                                    } else {
                                        sqlSession.update("update JTGKPAYMENTDETAIL set RESULTBACKSTATUS='3' where EXISTS(SELECT 1 FROM JTGKPAYMENTINFO WHERE srcDocId='" + item.getSrcDocId() + "')");
                                    }
                                }
                            }
                        }
                        //#endregion
                    }

                }
            }
        } catch (Throwable  e) {
            e.printStackTrace();
            result.setResult(false);
            result.setMessage("推送异常");
            result.setSrcDocId(id);
            logService.error(name,"推送异常！",e);
        }
        finally {
//            logService.flush();
            return result;
        }
    }

    private returnParamDto paymentResultPush_SFS(String name, List<PayResultBackDto> payLists, String id, LogService logService, String url, String username, String pwd, String mandt)
    {
        FastdwebSqlSession sqlSession = SpringBeanUtils.getBean(FastdwebSqlSession.class);
        returnParamDto result=new returnParamDto();
        try
        {
            String djbh=name;
            for(PayResultBackDto item:payLists){
                try {
                    Map<String,Object> map = new HashMap<>();
                    map.put("id",item.getId());
                    List<JTGKPayResultBackEntity> JTGKPayResultBackEntityList =sqlSession.selectList(JTGKPayResultBackEntity.class, "select * from VW_JTGKPAYRESULTBACK_SFS where id=#{id}", map) ;
                    if(JTGKPayResultBackEntityList !=null&& JTGKPayResultBackEntityList.size()>0) {
                        JTGKPayResultBackEntity entity = JTGKPayResultBackEntityList.get(0);
                        String GUID = (entity.getPAY_ARRANGE() + GUIDUtils.appendCurrentDateTime()).toUpperCase();
                        String proxy_id = (entity.getPAY_ARRANGE() + GUIDUtils.appendCurrentDateTime()).toUpperCase();
                        djbh = entity.getPAY_ARRANGE();
                        entity.setId(GUID);
                        entity.setProxy_id(proxy_id);
                        String data = "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:urn=\"urn:goldwind.com:i_oa:oa_payment_arrange_result\">\n" +
                                "   <soapenv:Header/>\n" +
                                "   <soapenv:Body>\n" +
                                "      <urn:mt_oa_payment_arrange_result_req>\n" +
                                "         <iv_msg_flag>?</iv_msg_flag>\n" +
                                "         <is_msg_head>\n" +
                                "            <!--Optional:-->\n" +
                                "            <MANDT>" + mandt + "</MANDT>\n" +
                                "            <!--Optional:-->\n" +
                                "            <GUID>" + GUID + "</GUID>\n" +
                                "            <!--Optional:-->\n" +
                                "            <PROXY_ID>" + proxy_id + "</PROXY_ID>\n" +
                                "            <!--Optional:-->\n" +
                                "            <SYSTEM_ID>GSC司库</SYSTEM_ID>\n" +
                                "            <!--Optional:-->\n" +
                                "            <OPERATOR>?</OPERATOR>\n" +
                                "            <!--Optional:-->\n" +
                                "            <SPRAS>?</SPRAS>\n" +
                                "            <!--Optional:-->\n" +
                                "            <INTERFACE_ID>?</INTERFACE_ID>\n" +
                                "            <!--Optional:-->\n" +
                                "            <SENDER>?</SENDER>\n" +
                                "            <!--Optional:-->\n" +
                                "            <RECIVER>?</RECIVER>\n" +
                                "            <!--Optional:-->\n" +
                                "            <SENDTIME>?</SENDTIME>\n" +
                                "         </is_msg_head>\n" +
                                "         <!--Optional:-->\n" +
                                "         <is_header>\n" +
                                "            <!--Optional:-->\n" +
                                "            <pay_arrange>" + Optional.ofNullable(entity.getPAY_ARRANGE()).orElse("") + "</pay_arrange>\n" +
                                "            <!--Optional:-->\n" +
                                "            <approval_result>" + Optional.ofNullable(entity.getAPPROVAL_RESULT()).orElse("") + "</approval_result>\n" +
                                "            <!--Optional:-->\n" +
                                "            <reserve_f1>?</reserve_f1>\n" +
                                "            <!--Optional:-->\n" +
                                "            <reserve_f2>?</reserve_f2>\n" +
                                "            <!--Optional:-->\n" +
                                "            <reserve_f3>?</reserve_f3>\n" +
                                "         </is_header>\n" +
                                "         <!--Zero or more repetitions:-->\n" +
                                "         <it_item>\n" +
                                "            <!--Optional:-->\n" +
                                "            <pay_req>" + Optional.ofNullable(entity.getPAY_REQ()).orElse("") + "</pay_req>\n" +
                                "            <!--Optional:-->\n" +
                                "            <pay_amount>" + Optional.ofNullable(entity.getPAY_AMOUNT()).orElse("") + "</pay_amount>\n" +
                                "            <!--Optional:-->\n" +
                                "            <pay_method>" + Optional.ofNullable(entity.getPAY_METHOD()).orElse("") + "</pay_method>\n" +
                                "            <!--Optional:-->\n" +
                                "            <bankk>" + Optional.ofNullable(entity.getBANKK()).orElse("") + "</bankk>\n" +
                                "            <!--Optional:-->\n" +
                                "            <bankn>" + Optional.ofNullable(entity.getBANKN()).orElse("") + "</bankn>\n" +
                                "            <!--Optional:-->\n" +
                                "            <boe_type>" + Optional.ofNullable(entity.getBOE_TYPE()).orElse("") + "</boe_type>\n" +
                                "            <!--Optional:-->\n" +
                                "            <boe_hkont>" + Optional.ofNullable(entity.getBOE_HKONT()).orElse("") + "</boe_hkont>\n" +
                                "            <!--Optional:-->\n" +
                                "            <zfbdt>" + Optional.ofNullable(entity.getZFBDT()).orElse("") + "</zfbdt>\n" +
                                "            <!--Optional:-->\n" +
                                "            <bill_number>" + Optional.ofNullable(entity.getBILL_NUMBER()).orElse("") + "</bill_number>\n" +
                                "            <!--Optional:-->\n" +
                                "            <lifnr_text>" + Optional.ofNullable(entity.getLIFNR_TEXT()).orElse("") + "</lifnr_text>\n" +
                                "            <!--Optional:-->\n" +
                                "            <lifnr>" + Optional.ofNullable(entity.getLIFNR()).orElse("") + "</lifnr>\n" +
                                "            <!--Optional:-->\n" +
                                "            <blart>" + Optional.ofNullable(entity.getBLART()).orElse("") + "</blart>\n" +
                                "            <!--Optional:-->\n" +
                                "            <pay_date>" + Optional.ofNullable(entity.getPAY_DATE()).orElse("") + "</pay_date>\n" +
                                "            <!--Optional:-->\n" +
                                "            <waers>" + Optional.ofNullable(entity.getWAERS()).orElse("") + "</waers>\n" +
                                "            <!--Optional:-->\n" +
                                "            <invoice_number>" + Optional.ofNullable(entity.getINVOICE_NUMBER()).orElse("") + "</invoice_number>\n" +
                                "            <!--Optional:-->\n" +
                                "            <discount_amount>" + Optional.ofNullable(entity.getDISCOUNT_AMOUNT()).orElse("") + "</discount_amount>\n" +
                                "            <!--Optional:-->\n" +
                                "            <bankk_receive>" + Optional.ofNullable(entity.getBANKK_RECEIVE()).orElse("") + "</bankk_receive>\n" +
                                "            <!--Optional:-->\n" +
                                "            <kursf>" + Optional.ofNullable(entity.getKURSF()).orElse("") + "</kursf>\n" +
                                "            <!--Optional:-->\n" +
                                "            <kursf_date>" + Optional.ofNullable(entity.getKURSF_DATE()).orElse("") + "</kursf_date>\n" +
                                "            <!--Optional:-->\n" +
                                "            <remark>" + Optional.ofNullable(entity.getREMARK()).orElse("") + "</remark>\n" +
                                "            <!--Optional:-->\n" +
                                "            <return_flag>?</return_flag>\n" +
                                "            <!--Optional:-->\n" +
                                "            <reserve_f1>?</reserve_f1>\n" +
                                "            <!--Optional:-->\n" +
                                "            <reserve_f2>?</reserve_f2>\n" +
                                "            <!--Optional:-->\n" +
                                "            <reserve_f3>?</reserve_f3>\n" +
                                "         </it_item>\n" +
                                "      </urn:mt_oa_payment_arrange_result_req>\n" +
                                "   </soapenv:Body>\n" +
                                "</soapenv:Envelope>";

                        String soapAction = "";
                        logService.info(djbh, "入参：" + data);
                        String xmlString = WebServiceUtilek.webServiceRequest(logService, djbh, url, soapAction, data, username, pwd, true);
                        logService.info(djbh, "出参：" + xmlString);
                        entity.setStatus("1");//已推送
                        payResultBackRepository.save(entity);
                    }
                }
                catch (Throwable  e) {
                    e.printStackTrace();
                    result.setResult(false);
                    result.setMessage("推送异常");
                    result.setSrcDocId(id);
                    logService.error(name,"推送异常！",e);
                }
//                return true;
            }
            result.setResult(true);
            result.setMessage("");
            result.setSrcDocId(id);
        }
        catch (Throwable  e) {
            e.printStackTrace();
            result.setResult(false);
            result.setMessage("推送异常");
            result.setSrcDocId(id);
            logService.error(name,"推送异常！",e);
        }
        finally {
            return result;
        }
    }

    /**
     * 司库给银企推送报文中，追加 这两个参数CreateUserName（录入人名称），ComfirmUserName（复核人名称）
     * @param paymentInstruction  组织的BE
     * @return
     * @throws JsonProcessingException
     * ---企业付款发送银行指令扩展
     * insert into bpbizeventlistenings (id, listentargetsucode, listentargetdoctypeid, listentime, listendirection, listenstatus, listensrcsucode, listensvc, svctype)
     * values (uuid_generate_v4(), 'bebc', 'PaymentInstructions', 'generate', 'Pre', 2,'goldwind', 'com.inspur.cloud.jtgk.goldwind.jk.jcsj.controller.goldInterfaceController.addPostInfo', '2');
     */
    @Override
    public String addPostInfo(Map<String, Object> paymentInstruction) throws JsonProcessingException {
        FastdwebSqlSession sqlSession = SpringBeanUtils.getBean(FastdwebSqlSession.class);
        log.error("结算单号[{}]", paymentInstruction.get("srcdocno"));
        log.error("结算单主键[{}]", paymentInstruction.get("srcdocid"));
        String srcdocid= !ObjectUtils.isEmpty(paymentInstruction.get("srcdocid"))?paymentInstruction.get("srcdocid").toString():null;
        Map<String,Object> map=new HashMap<>();
        Map<String, Object> maps = new HashMap<>();
        maps.put("srcdocid",srcdocid);
        if(!StringUtils.isEmpty(srcdocid)) {
            map = sqlSession.selectOne(Map.class,"select APPLICANTNAME,REVIEWERNAME from  TMPAYMENTSETTLEMENT   join TMJSXX ON TMJSXX.JSDNM=TMPAYMENTSETTLEMENT.ID WHERE TMJSXX.JSDNM=#{srcdocid}  ",maps);
        }
        String sqr="";
        String fhr="";
        List<Map<String, String>> list = new LinkedList<>();
        if(!ObjectUtils.isEmpty(map)) {
            if (!ObjectUtils.isEmpty(map.get("APPLICANTNAME"))) {
                sqr=map.get("APPLICANTNAME").toString();
            }
            if(!ObjectUtils.isEmpty(map.get("REVIEWERNAME"))){
                fhr=map.get("REVIEWERNAME").toString();
            }
            Map<String, String> item = new HashMap<>();
            item.put("CreateUserName", sqr);
            item.put("ComfirmUserName", fhr);

                Map<String, String> item1 = new HashMap<>();
                item1.put("column", "TXT09"); // 要保存到指令表的哪一列
                item1.put("key", "ExtendInfo"); // 发送银企直联的报文里的PostInfo里的字段名,与银企直联约定
                item1.put("value", JSONSerializer.serialize(item)); // 发送银企直联的报文里的PostInfo里的字段值
                list.add(item1);
        }else{
            return "";
        }
        return    new ObjectMapper().writeValueAsString(list);//JSONSerializer.serialize(list);

    }
}
