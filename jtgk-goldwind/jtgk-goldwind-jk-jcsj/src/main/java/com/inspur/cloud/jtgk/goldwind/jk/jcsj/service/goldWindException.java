package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service;

import io.iec.edp.caf.commons.exception.ExceptionLevel;
import io.iec.edp.caf.commons.exception.CAFRuntimeException;

public class goldWindException extends CAFRuntimeException {
    public goldWindException(String exceptionCode, String[] messageParams, Exception innerException) {
        super("AM", "", exceptionCode, messageParams, innerException, ExceptionLevel.Warning, true);
    }

    public goldWindException(String exceptionCode, String[] messageParams) {
        super("AM", "", exceptionCode, messageParams, null, ExceptionLevel.Warning, true);
    }

    public goldWindException(String exceptionCode) {
        super("AM", "", exceptionCode, new String[0], null, ExceptionLevel.Warning, true);
    }

}
