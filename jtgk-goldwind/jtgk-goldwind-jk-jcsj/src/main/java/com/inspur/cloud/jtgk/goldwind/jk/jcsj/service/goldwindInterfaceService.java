package com.inspur.cloud.jtgk.goldwind.jk.jcsj.service;

import com.alibaba.fastjson.JSONObject;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.controller.goldwindInterfaceController;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.entity.JTGKDepositPZEntity;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.repository.JTGKDepositPZRepository;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.service.CKGL.automaticDepositService;
import com.inspur.fastdweb.core.FastdwebSqlSession;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.common.JSONSerializer;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Service
public class goldwindInterfaceService  implements goldwindInterfaceController {

    @Autowired
    private LogService logService;
    @Autowired
    private FastdwebSqlSession sqlSession;
    @Autowired
    private JTGKDepositPZRepository JTGKDepositPZRepository;

    /**
     * 存款台账重新推送sap 生成凭证
     * @param param
     * @return
     */
    @Override
    public String cktzpush(JSONObject param) {
        //#region 初始化参数
        String name="存款台账重新推送sap";
        logService.init(name);
        Map<String,Object> result=new HashMap<>();
        String msg="";
        logService.info(name,"入参："+ JSONSerializer.serialize(param));
        String	ID= (!ObjectUtils.isEmpty(param.get("ID"))?param.get("ID").toString():"")	;//	来源系统标识
        String	tsr= (!ObjectUtils.isEmpty(param.get("sqr"))?param.get("sqr").toString():"")	;//	推送人
        //#endregion
        try {
            List<Map> pzlist=sqlSession.selectList(Map.class," select IDD_DATADICTIONARY.bigtxt01,IDD_DATADICTIONARY.code,IDD_DATADICTIONARY.txt01,IDD_DATADICTIONARY.txt02 from IDD_DATADICTIONARY  join  IDD_DATADICCATE on categoryid=IDD_DATADICCATE.id  " +
                    "where IDD_DATADICCATE.code='sap-ckpz'   ");
            if(pzlist==null||pzlist.size()<=0){
                logService.error(name,"未配置接口路径");
                result.put("result", false);
                result.put("message", "未配置接口路径");
                return JSONSerializer.serialize(result);
            }
            Map<String,Object>  map=pzlist.get(0);
            String url=String.valueOf(map.get("BIGTXT01"));//更新日期，不传此参数，则获取全量数据    scc/123456   geam/123456
            String username=String.valueOf(map.get("TXT01"));
            String pwd=String.valueOf(map.get("TXT02"));
            String mandt=String.valueOf(map.get("CODE"));
            //#region 单据是否存在或单据是否可以撤回检查
            Optional<JTGKDepositPZEntity> jtgkDepositPZEntity= JTGKDepositPZRepository.findById(ID);
            if(jtgkDepositPZEntity.isPresent()){
                automaticDepositService aa = SpringBeanUtils.getBean(automaticDepositService.class);
                if("2".equals(jtgkDepositPZEntity.get().getDEPOSITTYPE())){
                    jtgkDepositPZEntity.get().setCdlx_code("4");
                    jtgkDepositPZEntity.get().setCdlx_name("4-通知存款");
                }
                else if("1".equals(jtgkDepositPZEntity.get().getCdlx())){
                    jtgkDepositPZEntity.get().setCdlx_code(jtgkDepositPZEntity.get().getCdlx());
                    jtgkDepositPZEntity.get().setCdlx_name("1-定期存款");
                }
                else if("2".equals(jtgkDepositPZEntity.get().getCdlx())){
                    jtgkDepositPZEntity.get().setCdlx_code(jtgkDepositPZEntity.get().getCdlx());
                    jtgkDepositPZEntity.get().setCdlx_name("2-结构性存款");
                }
                else if("3".equals(jtgkDepositPZEntity.get().getCdlx())){
                    jtgkDepositPZEntity.get().setCdlx_code(jtgkDepositPZEntity.get().getCdlx());
                    jtgkDepositPZEntity.get().setCdlx_name("3-大额存单");
                }
                jtgkDepositPZEntity.get().setPushdate(new SimpleDateFormat("yyyy-MM-dd").format(new Date()));
                jtgkDepositPZEntity.get().setPushr(tsr);
                if(aa.cktzpushsap(jtgkDepositPZEntity.get().getDJBH(),logService,jtgkDepositPZEntity.get(),url,username,pwd,mandt)) {
                    result.put("result", true);
                    result.put("message", "推送成功！");
                    logService.info(name, "推送成功！");
                }else{
                    result.put("result", false);
                    result.put("message", "推送失败");
                }
            }
            //#endregion
        } catch (Throwable  e) {
            e.printStackTrace();
            result.put("result", false);
            result.put("message", msg);
            logService.error(name,"推送异常："+ JSONSerializer.serialize(result));
            logService.error(name,"推送异常！",e);
        }
        finally {
            logService.flush();
            return JSONSerializer.serialize(result);
        }
    }
}
