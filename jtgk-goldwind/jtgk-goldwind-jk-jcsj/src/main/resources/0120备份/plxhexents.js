//  /apps/jtgk/jfkj/extend/xhsqexents.js
idp.event.bind("loadData", function(e, context) {
    let sqrid=idp.uiview.modelController.getMainRowObj().APPLICANTID;
    if(sqrid==null||sqrid==''||sqrid==undefined){
        sqrid=  idp.context.get("UserId");
    }
    let sqlId1 = 'b59820f9-554f-2be1-8585-356b14031c5c';
    let filters1 = [];
    filters1.push({
        Left: "",
        Field: 'gspuser.id',
        Operate: " = ",
        IsExpress: false,
        Value: sqrid,
        Right: "",
        Logic: "AND"
    });
    let result1 = [];
    getQueryData(sqlId1, filters1, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result1 = data.Data.Rows;
        }).fail(function(data) {});
    if(result1.length > 0){debugger
        let zrr =result1[0].SJINCHARGE ;
        let fgld =result1[0].INCHARGE ;
        let xzzz =result1[0].XZZZ ;
        let bjzz =result1[0].BJZZ ;
        let sjfzr =result1[0].SJHEAD ;
        idp.control.get("TXT06").setValue(xzzz);//发起人的行政单位
        idp.control.get("TXT08").setValue(zrr);//上级分管领导
        idp.control.get("TXT09").setValue(fgld);//负责人
        idp.control.get("TXT01").setValue(bjzz);//本级分管领导
        idp.control.get("TXT03").setValue(sjfzr);//上级负责人
        idp.loaded();
    }
    let sqlId = '34777655-e68e-4647-5aa2-1a9988f4eb37';
    let filters = [];
    filters.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "10082361",
        Right: "",
        Logic: "AND"
    });
    let result = [];
    getQueryData(sqlId, filters, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT02").setValue(id);//发起人的行政单位
        idp.loaded();
    }
    let filters2 = [];
    filters2.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "10082362",
        Right: "",
        Logic: "AND"
    });
    getQueryData(sqlId, filters2, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT04").setValue(id);//发起人的行政单位
        idp.loaded();
    }
    let filters3 = [];
    filters3.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "40003643",
        Right: "",
        Logic: "AND"
    });
    getQueryData(sqlId, filters3, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT05").setValue(id);
        idp.loaded();
    }


})

idp.event.bind("viewReady", function(e, context) {//APPLICANTID
    const data = idp.uiview.modelController.getMainRowObj();
    let sqlId1 = 'b59820f9-554f-2be1-8585-356b14031c5c';
    let filters1 = [];
    filters1.push({
        Left: "",
        Field: 'gspuser.id',
        Operate: " = ",
        IsExpress: false,
        Value: idp.context.get("UserId"),
        Right: "",
        Logic: "AND"
    });
    let result1 = [];
    getQueryData(sqlId1, filters1, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result1 = data.Data.Rows;
        }).fail(function(data) {});
    if(result1.length > 0){debugger
        let zrr =result1[0].SJINCHARGE ;
        let fgld =result1[0].INCHARGE ;
        let xzzz =result1[0].XZZZ ;
        let bjzz =result1[0].BJZZ ;
        let sjfzr =result1[0].SJHEAD ;
        idp.control.get("TXT06").setValue(xzzz);
        idp.control.get("TXT08").setValue(zrr);
        idp.control.get("TXT09").setValue(fgld);
        idp.control.get("TXT01").setValue(bjzz);
        idp.control.get("TXT03").setValue(sjfzr);
        idp.loaded();
    }
    let sqlId = '34777655-e68e-4647-5aa2-1a9988f4eb37';
    let filters = [];
    filters.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "10082361",
        Right: "",
        Logic: "AND"
    });
    let result = [];
    getQueryData(sqlId, filters, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT02").setValue(id);//发起人的行政单位
        idp.loaded();
    }
    let filters2 = [];
    filters2.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "10082362",
        Right: "",
        Logic: "AND"
    });
    getQueryData(sqlId, filters2, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT04").setValue(id);//发起人的行政单位
        idp.loaded();
    }
    let filters3 = [];
    filters3.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "40003643",
        Right: "",
        Logic: "AND"
    });
    getQueryData(sqlId, filters3, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT05").setValue(id);
        idp.loaded();
    }


})

//取数
function getQueryData(sqlId, filter, orders, bizinfo, callback, pageSize) {
    var params = {
        sqlId: sqlId,
        orders: orders,
        fields: filter,
        page: 0,
        pageSize: pageSize || 0,
        bizId: bizinfo.bizId,
        bizOpId: bizinfo.bizOpId
    };
    if (!callback)
        return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false);
    return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false).done(function(data) {
        callback(data);
    }).fail(function(data) {
    });
}


idp.event.bind("domReady", function () {
    idp.event.register("TXT13", "beforeHelpFilter", function(e, ctrl, col, row){

        debugger
        var currentData = idp.uiview.modelController.getMainRowObj();
        var treePath = currentData.TXT15;
        if(treePath!=null) {
            if (treePath.length > 9) {
                treePath = treePath.substring(0, 9);
            }
            var treePathList = [];
            var item = "";
            filters = [];
            for (var i = 0; i < treePath.length / 4 - 1; i++) {
                item += treePath.substring(i * 4, (i + 1) * 4);
                treePathList.push(item);
            }

            if (treePath == '0001') {

            } else {

                for (let i = 0; i <= treePathList.length; i++) {
                    if (i == treePathList.length) {
                        filters.push({
                            "Left": "",
                            "Field": "TREEINFO_PATH",
                            "Operate": "leftlike",
                            "IsExpress": false,
                            "Value": treePath,
                            "Right": "",
                            "Logic": ""
                        });
                        continue;
                    }

                }
            }
            return filters;
        }


    });
})