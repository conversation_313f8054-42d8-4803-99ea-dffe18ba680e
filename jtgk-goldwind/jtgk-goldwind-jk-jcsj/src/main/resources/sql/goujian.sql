delete from fsspcomponentinvoke where FSSPCOMPONENTINVOKE_NM='UpdateStatezhkm_before'
;
INSERT INTO fsspcomponentinvoke (
    FSSPCOMPONENTINVOKE_NM,
    FSSPCOMPONENTINVOKE_CZFID,
    FSSPCOMPONENTINVOKE_CZID,
    FSSPCOMPONENTINVOKE_CZMC,
    FSSPCOMPONENTINVOKE_GJID,
    FSSPCOMPONENTINVOKE_GJMC,
    FSSPCOMPONENTINVOKE_INVOKETYPE,
    FSSPCOMPONENTINVOKE_MPATH,
    FSSPCOMPONENTINVOKE_SNAME,
    FSSPCOMPONENTINVOKE_SU,
    FSSPCOMPONENTINVOKE_YWLYID,
    FSSPCOMPONENTINVOKE_YWLYMC,
    POSTDATATYPE
)
VALUES
    (
        'UpdateStatezhkm_before',
        'FSPF',
        'UpdateStatezhkm_before',
        '账户科目维护前构件',
        'UpdateStatezhkm_before',
        '账户科目维护前构件',
        'platformrpc',
        'com.inspur.cloud.jtgk.jfkj.zhgl.controller.ProcessComponentsController.UpdateZhkmState_before',
        'ProcessComponentsController',
        'AM',
        'goldwind',
        '账户科目维护前构件',
        'map'
    )
;
delete from pfgecomponentmethod where id = 'UpdateStatezhkm_before'
;
INSERT INTO pfgecomponentmethod (
    ID,
    ASSEMBLY,
    CALLMODE,
    CLASSNAME,
    FORMTYPECODE,
    METHODCODE,
    METHODNAME,
    NOTE,
    SERVICEID,
    SERVICENAME
)
VALUES
    (
        'UpdateStatezhkm_before',
        '',
        '1',
        '',
        '',
        'UpdateStatezhkm_before',
        '账户科目维护前构件',
        '',
        'UpdateStatezhkm_before',
        ''
    )
;
delete from pfgeformaction where id='UpdateStatezhkm_before'
;
INSERT INTO pfgeformaction (
    ID,
    ACTIONCODE,
    ACTIONNAME,
    BIZAREA,
    BIZCATCODE,
    FORMTYPE,
    METHODID,
    NOTE,
    OPERATIONTYPE,
    ORD,
    ROLLBAKMETHODID,
    ifpublic
)
VALUES
    (
        'UpdateStatezhkm_before',
        'UpdateStatezhkm_before',
        '账户科目维护前构件',
        'Brpc',
        '',
        '',
        'UpdateStatezhkm_before',
        '',
        '',
        NULL,
        '',
        '1'
    )
;
-----------------------账户科目维护后------------------------------------
delete from fsspcomponentinvoke where FSSPCOMPONENTINVOKE_NM='UpdateZhkmState_after'
;
INSERT INTO fsspcomponentinvoke (
    FSSPCOMPONENTINVOKE_NM,
    FSSPCOMPONENTINVOKE_CZFID,
    FSSPCOMPONENTINVOKE_CZID,
    FSSPCOMPONENTINVOKE_CZMC,
    FSSPCOMPONENTINVOKE_GJID,
    FSSPCOMPONENTINVOKE_GJMC,
    FSSPCOMPONENTINVOKE_INVOKETYPE,
    FSSPCOMPONENTINVOKE_MPATH,
    FSSPCOMPONENTINVOKE_SNAME,
    FSSPCOMPONENTINVOKE_SU,
    FSSPCOMPONENTINVOKE_YWLYID,
    FSSPCOMPONENTINVOKE_YWLYMC,
    POSTDATATYPE
)
VALUES
    (
        'UpdateZhkmState_after',
        'FSPF',
        'UpdateZhkmState_after',
        '账户科目维护后构件',
        'UpdateZhkmState_after',
        '账户科目维护后构件',
        'platformrpc',
        'com.inspur.cloud.jtgk.jfkj.zhgl.controller.ProcessComponentsController.UpdateZhkmState_after',
        'ProcessComponentsController',
        'AM',
        'goldwind',
        '账户科目维护后构件',
        'map'
    )
;
delete from pfgecomponentmethod where id = 'UpdateZhkmState_after'
;
INSERT INTO pfgecomponentmethod (
    ID,
    ASSEMBLY,
    CALLMODE,
    CLASSNAME,
    FORMTYPECODE,
    METHODCODE,
    METHODNAME,
    NOTE,
    SERVICEID,
    SERVICENAME
)
VALUES
    (
        'UpdateZhkmState_after',
        '',
        '1',
        '',
        '',
        'UpdateZhkmState_after',
        '账户科目维护后构件',
        '',
        'UpdateZhkmState_after',
        ''
    )
;
delete from pfgeformaction where id='UpdateZhkmState_after'
;
INSERT INTO pfgeformaction (
    ID,
    ACTIONCODE,
    ACTIONNAME,
    BIZAREA,
    BIZCATCODE,
    FORMTYPE,
    METHODID,
    NOTE,
    OPERATIONTYPE,
    ORD,
    ROLLBAKMETHODID,
    ifpublic
)
VALUES
    (
        'UpdateZhkmState_after',
        'UpdateZhkmState_after',
        '账户科目维护后构件',
        'Brpc',
        '',
        '',
        'UpdateZhkmState_after',
        '',
        '',
        NULL,
        '',
        '1'
    )
;






-----------------------通用反馈接口------------------------------------
delete from fsspcomponentinvoke where FSSPCOMPONENTINVOKE_NM='Feedback'
;
INSERT INTO fsspcomponentinvoke (
    FSSPCOMPONENTINVOKE_NM,
    FSSPCOMPONENTINVOKE_CZFID,
    FSSPCOMPONENTINVOKE_CZID,
    FSSPCOMPONENTINVOKE_CZMC,
    FSSPCOMPONENTINVOKE_GJID,
    FSSPCOMPONENTINVOKE_GJMC,
    FSSPCOMPONENTINVOKE_INVOKETYPE,
    FSSPCOMPONENTINVOKE_MPATH,
    FSSPCOMPONENTINVOKE_SNAME,
    FSSPCOMPONENTINVOKE_SU,
    FSSPCOMPONENTINVOKE_YWLYID,
    FSSPCOMPONENTINVOKE_YWLYMC,
    POSTDATATYPE
)
VALUES
    (
        'Feedback',
        'FSPF',
        'Feedback',
        '通用反馈接口',
        'Feedback',
        '通用反馈接口',
        'platformrpc',
        'com.inspur.cloud.jtgk.jfkj.controller.GenFeedbackInterfaceController.Feedback',
        'GenFeedbackInterfaceController',
        'AM',
        'goldwind',
        '账户科目维护后构件',
        'map'
    )
;
delete from pfgecomponentmethod where id = 'Feedback'
;
INSERT INTO pfgecomponentmethod (
    ID,
    ASSEMBLY,
    CALLMODE,
    CLASSNAME,
    FORMTYPECODE,
    METHODCODE,
    METHODNAME,
    NOTE,
    SERVICEID,
    SERVICENAME
)
VALUES
    (
        'Feedback',
        '',
        '1',
        '',
        '',
        'Feedback',
        '通用反馈接口',
        '',
        'Feedback',
        ''
    )
;
delete from pfgeformaction where id='Feedback'
;
INSERT INTO pfgeformaction (
    ID,
    ACTIONCODE,
    ACTIONNAME,
    BIZAREA,
    BIZCATCODE,
    FORMTYPE,
    METHODID,
    NOTE,
    OPERATIONTYPE,
    ORD,
    ROLLBAKMETHODID,
    ifpublic
)
VALUES
    (
        'Feedback',
        'Feedback',
        '通用反馈接口',
        'Brpc',
        '',
        '',
        'Feedback',
        '',
        '',
        NULL,
        '',
        '1'
    )
;

---预警通知
insert into gspwarningextend (ID, CODE, DESCRIBE, NAME, SERVICEUNIT, TYPE)
values ('ZHYQYJ', 'ZHYQYJ', '账户久悬户提醒', '账户久悬户提醒', 'bebc', 0);

update   GSPSCHEDULERJOB  set bsession='{"UserId":"21bfb2a5-740b-4f6c-bfb7-d92a65590107","UserCode":"XTZD","UserName":"系统自动"}'   WHERE code='zdtj';
select * from gspuser where code='XTZD'