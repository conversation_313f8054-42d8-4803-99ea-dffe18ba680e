 

--创建接口配置表
create table JTGKINTERFACECONFIG(
        ID varchar(36),
        code varchar(36),
        name varchar(100),
        isEnable varchar(36),
        keyValue varchar(36),
        cxdate  varchar(10),
        url  varchar(300),
		username varchar(50),
		password varchar(50),
		MANDT varchar(50)
)
delete from  JTGKINTERFACECONFIG where id in ('CABM','CARY');
insert into JTGKINTERFACECONFIG(id,code,name,isEnable,keyValue,url,cxdate)values('CABM','CABM','CA部门','1','scc:123456','http://openapi-test.goldwind.com/gateway/api-ca/rest/organizationApi/getIncrementOrgInfos','2024-08-01');
insert into JTGKINTERFACECONFIG(id,code,name,isEnable,keyValue,url,cxdate)values('CARY','CARY','CA人员','1','scc:123456','http://openapi-test.goldwind.com/gateway/api-ca/rest/employeeApi/getEmployeesIncrementInPage','2024-08-01');
insert into JTGKINTERFACECONFIG(id,code,name,isEnable,keyValue,url,cxdate)values('CARYCS','CARYCS','CA全量人员','1','scc:123456','http://openapi-test.goldwind.com/gateway/api-ca/rest/organizationApi/getIncrementOrgInfos','2024-08-01');
delete from JTGKINTERFACECONFIG WHERE CODE = 'SAPKM';
insert into JTGKINTERFACECONFIG(id,code,name,isEnable,keyValue,url,cxdate)values('SAPKM','SAPKM','科目余额','1','gsc:123456','http://openapi-test.goldwind.com/gateway/sap/rfc/ZFM_FI113_GET_FAGLB03','2024-08-01');


update JTGKINTERFACECONFIG  set  KEYVALUE='scc:123456'   WHERE CODE = 'CABM';--scc/123456   geam/123456
--公司
create table JTGKGSRESULT(
         id  varchar(50),
         BUKRS	varchar(100),
         FULL_NAME	varchar(300),
         SPRAS	varchar(100),
         LAND1	varchar(100),
         WAERS	varchar(100),
         STCD5	varchar(100),
         PSTLZ	varchar(100),
         ORT01	varchar(100),
         JXGKBUSI	varchar(100),
         BUSI	varchar(100),
         ZZGXHS	varchar(100),
         ZZJXGKLW	varchar(100),
         ZZ_INVALID	varchar(100),
         IS_DISCOUNT	varchar(100),
         flag  varchar(10),   ---数据变更状态（A新增，M修改，D删除）
         updateDate  varchar(50),   ---数据更新日期
         sksyncstatus  varchar(10),--- 司库同步状态  0未同步、1已同步 2 异常
         sksyncmsg  varchar(500) --- 司库同步备注
)

create table JTGKEMPLOYEEQLRESULT(
           id	varchar(50),
           userId	varchar(100),
           userName	varchar(100),
           unitId	varchar(100),
           unitTxt	varchar(100),
           systemId	varchar(100),
           systemTxt	varchar(100),
           stell	varchar(100),
           stext	varchar(100),
           deptId	varchar(100),
           deptName	varchar(100),
           phoneNumber	varchar(100),
           email	varchar(100),
           centerId	varchar(100),
           centerTxt	varchar(100),
           zhrTime1	varchar(100),
           zhrRzrq	varchar(100),
           orgeh	varchar(100),
           jglb	varchar(100),
           officeId	varchar(100),
           officeTxt	varchar(100),
           directorCode	varchar(100),
           branchCode	varchar(100),
           zhrOtext	varchar(100),
           zhrCost	varchar(100),
           zhrCostTxt	varchar(100),
           werks	varchar(100),
           werksTxt	varchar(100),
           inst	varchar(100),
           gesc	varchar(100),
           persg	varchar(100),
           zhrLoca	varchar(100),
           levelpk	varchar(100),
           zhrPtext	varchar(100),
           company	varchar(100),
           companyName	varchar(100),
           zhrBank	varchar(100),
           zhrAccount	varchar(100),
           zzKhhs	varchar(100),
           zzKhhd	varchar(100),
           zzKhh	varchar(100),
           zzYhh	varchar(100),
           zzLhh	varchar(100),
           status	varchar(100),
           plans	varchar(100),
           flag  varchar(10),   ---数据变更状态（A新增，M修改，D删除）
           updateDate  varchar(50),   ---数据更新日期
           sksyncstatus  varchar(10),--- 司库同步状态  0未同步、1已同步 2 异常
           sksyncmsg  varchar(500) --- 司库同步备注

)


----创建部门中间表
create table JTGKBMRESULT(
         id  varchar(50),
         orgCode  varchar(50),   ---组织编码
         orgDirectorLeader  varchar(100),   ---直属领导
         orgBranchLeader  varchar(100),   ---分管领导
         orgName  varchar(200),---   金风集团 组织中文名称
         orgEName  varchar(500),---金风集团,   ---组织英文名称
         orgPostCode   varchar(100),---
         parentOrgCode  varchar(100),   ---父级组织编码
         parentOrgName  varchar(200),   ---父级组织中文名称
         parentOrgEName  varchar(500),   ---父级组织英文名称
         jglb  varchar(50),   ---机构类别
         jgbm  varchar(50),   ---机构编码
         type  varchar(10),   ---数据变更状态（A新增，M修改，D删除）
         updateDate  varchar(50),   ---数据更新日期
         sksyncstatus  varchar(10),--- 司库同步状态  0未同步、1已同步 2 异常
         sksyncmsg  varchar(500) --- 司库同步备注
)

----创建人员中间表
create table JTGKEMPLOYEERESULT(
           id  varchar(50),
           pernr	varchar(100),
           ename	varchar(100),
           inits	varchar(100),
           gesc	varchar(100),
           orgeh	varchar(100),
           plans	varchar(100),
           zhrOtext	varchar(100),
           zhrPtext	varchar(100),
           unitId	varchar(100),
           unitTxt	varchar(100),
           systemId	varchar(100),
           systemTxt	varchar(100),
           centerId	varchar(100),
           centerTxt	varchar(100),
           deptId	varchar(100),
           deptName	varchar(100),
           officeId	varchar(100),
           officeName	varchar(100),
           stell	varchar(100),
           zhrStext	varchar(100),
           usrid	varchar(100),
           landx50	varchar(100),
           ltext	varchar(100),
           ptext	varchar(100),
           zhrTime1	varchar(100),
           zhrPtype	varchar(100),
           ftext	varchar(100),
           zhrEmail	varchar(100),
           zhrCell	varchar(100),
           zhrTell	varchar(100),
           zhrLoca	varchar(100),
           zhrXqtc	varchar(100),
           zhrProv	varchar(100),
           zhrCity	varchar(100),
           zhrBank	varchar(100),
           zhrAccount	varchar(100),
           gbdat	varchar(100),
           gbort	varchar(100),
           zhrBtype	varchar(100),
           zhrZsfbs	varchar(100),
           zhrCjgz	varchar(100),
           locat	varchar(100),
           zhrRzrq	varchar(100),
           zhrLzrq	varchar(100),
           zzKhhs	varchar(100),
           zzKhhd	varchar(100),
           zzKhh	varchar(100),
           zzYhh	varchar(100),
           zzLhh	varchar(100),
           trfgr	varchar(100),
           werks	varchar(100),
           werksT	varchar(100),
           btrtl	varchar(100),
           btrtlT	varchar(100),
           qdzt	varchar(100),
           qdztT	varchar(100),
           persg	varchar(100),
           persk	varchar(100),
           levelpk	varchar(100),
           zhrCost	varchar(100),
           zhrCosttxt	varchar(100),
           zhrYglx	varchar(100),
           zhrHypy	varchar(100),
           zhrFlag	varchar(100),
           zhrStatus	varchar(100),
           backTime	varchar(100),
           zgslx	varchar(100),
           company	varchar(100),
           companyName	varchar(100),
           parentOrgCode	varchar(100),
           directorCode	varchar(100),
           branchCode	varchar(100),
           flag	varchar(10),
           updateDate	varchar(10),
           sksyncstatus	varchar(10),
           sksyncmsg	varchar(100)
)

--银行
create table JTGKYHRESULT (
    id   varchar(50),
    MANDT	varchar(100),
    BANKS	varchar(100),
    BANKL	varchar(100),
    BANKA	varchar(100),
    PROVZ	varchar(100),
    STRAS	varchar(100),
    ORT01	varchar(100),
    SWIFT	varchar(100),
    LOEVM	varchar(100),
    BNKLZ	varchar(100),
    BRNCH	varchar(100),
    zzrouting_code varchar(100),
    zzbranch_code  varchar(100),
    flag  varchar(10),   ---数据变更状态（A新增，M修改，D删除）
    updateDate  varchar(50),   ---数据更新日期
    sksyncstatus  varchar(10),--- 司库同步状态  0未同步、1已同步 2 异常
    sksyncmsg  varchar(500) --- 司库同步备注
)
alter table JTGKYHRESULT add zzbranch_code varchar(100);
alter table JTGKYHRESULT add zzrouting_code varchar(100);
---贸易伙伴
CREATE TABLE JTGKWLDWRESULT(
           ID VARCHAR(50),
           PARTNER  varchar(100),
           NAME_ORG1   varchar(500),
           VBUND   varchar(500),
           flag  varchar(10),   ---数据变更状态（A新增，M修改，D删除）
           updateDate  varchar(50),   ---数据更新日期
           sksyncstatus  varchar(10),--- 司库同步状态  0未同步、1已同步 2 异常
           sksyncmsg  varchar(500) --- 司库同步备注

)
---  INSERT INTO JTGKWLDWRESULT(PARTNER, NAME_ORG1, VBUND,ID,flag,sksyncstatus) VALUES ('**********', '泰安众恒复合材料有限公司', '000004','000004','A','0');
create table JTGKSFBBRESULT(
           ID VARCHAR(50),
           MANDT	varchar(100),	--	集团
           C_SCOPE	varchar(100),	--	合并组
           C_ENTITY	varchar(100),	--	公司
           C_TIME	varchar(100),	--	期间
           C_SCOPE_T	varchar(100),	--	合并组描述
           C_ENTITY_T	varchar(100),	--	子公司描述
           C_SCOPE_PARENT	varchar(100),	--	父级合并组
           C_SCOPE_TYPE	varchar(100),	--	合并组类型
           SIGNEDDATA	varchar(100),	--	SIGNDATA
           flag  varchar(10),   ---数据变更状态（A新增，M修改，D删除）
           updateDate  varchar(50),   ---数据更新日期
           sksyncstatus  varchar(10),--- 司库同步状态  0未同步、1已同步 2 异常
           sksyncmsg  varchar(500) --- 司库同步备注
)
---往来单位银行账号
create table JTGKWLDWYHZHRESULT(
       ID VARCHAR(50),
       KOINH	varchar(100),	--	银行账号
       BANKL	varchar(100),	--	银行代码
       BANKS	varchar(100),	--	银行国家代码
       LIFNR	varchar(100),	--	业务伙伴编号
       NAME1	varchar(100),	--	贸易伙伴名称
       EBPP_ACCNAME	varchar(100),	--	账户持有人
       flag  varchar(10),   ---数据变更状态（A新增，M修改，D删除）
       updateDate  varchar(50),   ---数据更新日期
       sksyncstatus  varchar(10),--- 司库同步状态  0未同步、1已同步 2 异常
       sksyncmsg  varchar(500) --- 司库同步备注
)
---现金流量项目
create table JTGKXJLLXMRESULT(
             ID VARCHAR(50),
             BUKRS	varchar(100),	--	父级
             RSTGR	varchar(100),	--	原因代码
             TXT40	varchar(100),	--	长文本
             flag  varchar(10),   ---数据变更状态（A新增，M修改，D删除）
             updateDate  varchar(50),   ---数据更新日期
             sksyncstatus  varchar(10),--- 司库同步状态  0未同步、1已同步 2 异常
             sksyncmsg  varchar(500) --- 司库同步备注
)

----账户科目维护

select * from pfgetasktype where typename_chs like '%账户%'
select *from pfgefuncdefine where funcname like '%账户%'
INSERT INTO pfgefuncdefine(funcid, authmethodid, bizarea, funccode, funcname, ifsupportfssc, linkmethodid, restrictions, returnforbidden, tasktypecode) VALUES ('ZHKMWH', 'AM_YHKH_GetUser', 'AM', 'AM_BankAccountKmwh', '银行账户科目维护', NULL, 'AM_YHKH_ViewBillByFSPF', NULL, NULL, 'AM_ZHKM_WH');
delete from pfgetasktype where id='AM_ZHKM_WH';
INSERT INTO PFGETASKTYPE(id, bizarea, bizareaid, ifcustom, note, typecode, typename_chs, typename_cht, typename_en, typename_es, typename_pt, iffssp)
VALUES ('AM_ZHKM_WH', 'BM', 'BM', '1','','AM_ZHKM_WH','银行账户科目维护','銀行賬戶科目維護', 'BankAccount Subject Update', '', '', NULL);

create table JTGKZHKMYERESULT(
         ID VARCHAR(50),
         BUKRS  varchar(100),
         HKONT  varchar(100),--会计科目
         BALANCE_CUM  varchar(100),--科目余额
         WAERS  varchar(100),--WAERS
         ZRESERVE_F1  varchar(100),--预留字段1
         ZRESERVE_F2  varchar(100),--预留字段2
         ZRESERVE_F3  varchar(100),--预留字段3
         year  varchar(100),--年份
         MOUTH  varchar(100),--月份
         flag  varchar(10),   ---数据变更状态（A新增，M修改，D删除）
         updateDate  varchar(50),   ---数据更新日期
         sksyncstatus  varchar(10),--- 司库同步状态  0未同步、1已同步 2 异常
         sksyncmsg  varchar(500) --- 司库同步备注

)

create table  JTGKJWHQZHYERESULT(
        ID VARCHAR(100),
        MANDT	varchar(100),
        REFNBR	varchar(100),
        ACTNBR	varchar(100),
        ST_SEQNUM	varchar(100),
        WAERS	varchar(100),
        ZOPEN_BAL	varchar(100),
        ZCLOSE_BAL	varchar(100),
        ZMT940_FNAME	varchar(100),
        ZMT940_FDAT	varchar(100),
        BAL_DATE	varchar(100),
        CDATE	varchar(100),
        CTIEM	varchar(100),
        USERNAME	varchar(100),
        TCODE	varchar(100),
        CHANGE_IND	varchar(100),
        UUSERNAME	varchar(100),
        UDATE	varchar(100),
        UTIME	varchar(100),
        UTCODE	varchar(100),
        UCHANGE_IND	varchar(100),
        flag  varchar(10),   ---数据变更状态（A新增，M修改，D删除）
        updateDate  varchar(50),   ---数据更新日期
        sksyncstatus  varchar(10),--- 司库同步状态  0未同步、1已同步 2 异常
        sksyncmsg  varchar(500) --- 司库同步备注
)

create table jtgk_pf_task_activity_log(
          id varchar(36),
          CREATE_TIME varchar(36),
          EVENT_TYPE varchar(36),
          PF_MAIN_TASK_ID varchar(36)
)


alter table  jtgk_pf_task_activity_log  add assignee_id varchar(100);
alter table  jtgk_pf_task_activity_log  add pf_proc_inst_id varchar(100);
alter table  jtgk_pf_task_activity_log  add activity_id varchar(100);

-- create table JTGKJWHQZHYERESULT
-- (
--     ID VARCHAR(100),
--     MANDT varchar(10),
--     REFNBR varchar(16),
--     ACTNBR varchar(70),
--     ST_SEQNUM varchar(10),
--     WAERS varchar(10),
--     ZOPEN_BAL varchar(20),
--     ZCLOSE_BAL varchar(20),
--     ZMT940_FNAME varchar(50),
--     ZMT940_FDAT varchar(10),
--     flag  varchar(10),   ---数据变更状态（A新增，M修改，D删除）
--     updateDate  varchar(50),   ---数据更新日期
--     sksyncstatus  varchar(10),--- 司库同步状态  0未同步、1已同步 2 异常
--     sksyncmsg  varchar(500) --- 司库同步备注
-- )

create table JTGKJWMGZHYERESULT(
       ID VARCHAR(100),
       MANDT	varchar(3),
       GUID	varchar(32),
       ZLINES	varchar(19),
       FILE_TYPE	varchar(30),
       FILENAME	varchar(60),
       MT94_20	varchar(60),
       MT94_21	varchar(16),
       MT94_25	varchar(40),
       MT94_60	varchar(40),
       MT94_61	varchar(100),
       MT94_86	varchar(1024),
       MT94_62	varchar(30),
       MT94_64	varchar(30),
       MT94_65	varchar(30),
       FINAL_86	varchar(1),
       VALUE_DATE	varchar(8),
       ENTRY_DATE	varchar(8),
       CREDIT_DEBIT	varchar(1),
       FOUNDS_D	varchar(1),
       CURRENCY_CODE	varchar(5),
       AMOUNT	varchar(30),
       TRANS_TYPE	varchar(10),
       REF_ID	varchar(60),
       BANK_REF	varchar(60),
       REFNBR	varchar(10),
       MSGID	varchar(60),
       CREDTTM	varchar(60),
       ORGNLMSGID	varchar(35),
       ORGNLNBOFTXS	varchar(15),
       ORGNLCTRLSUM	varchar(35),
       GRPSTS	varchar(10),
       ADDTLINF	varchar(1024),
       ORGNLPMTINFID	varchar(35),
       ORGNLINSTRID	varchar(35),
       ORGNLENDTOENDID	varchar(35),
       TXSTS	varchar(10),
       ACCTSVCRREF	varchar(60),
       AMT	varchar(35),
       AMT_CCY	varchar(5),
       REQDEXCTNDT	varchar(10),
       AMT_BLANCE	varchar(35),
       AMT_CCY_BLANCE	varchar(5),
       DATE_BLANCE	varchar(10),
       INCLUDE	varchar(50),
       CDATE	varchar(8),
       CTIEM	varchar(6),
       USERNAME	varchar(12),
       TCODE	varchar(20),
       CHANGE_IND	varchar(1),
       UUSERNAME	varchar(12),
       UDATE	varchar(8),
       UTIME	varchar(6),
       UTCODE	varchar(20),
       UCHANGE_IND	varchar(1),
       flag  varchar(10),   ---数据变更状态（A新增，M修改，D删除）
       updateDate  varchar(50),   ---数据更新日期
       sksyncstatus  varchar(10),--- 司库同步状态  0未同步、1已同步 2 异常
       sksyncmsg  varchar(500) --- 司库同步备注
)


create table  JTGKOASPRESULT(
                  id 	VARCHAR(36),
                  state  	VARCHAR(50),
                  start_member_id    	VARCHAR(50),
                  start_date 	VARCHAR(50),
                  approve_member_id  	VARCHAR(50),
                  approve_date   	VARCHAR(50),
                  finishedflag   	VARCHAR(50),
                  ratifyflag 	VARCHAR(50),
                  ratify_member_id   	VARCHAR(50),
                  ratify_date    	VARCHAR(50),
                  sort   	VARCHAR(50),
                  modify_member_id   	VARCHAR(50),
                  modify_date    	VARCHAR(50),
                  field0001  	VARCHAR(100),
                  field0002  	VARCHAR(20),
                  field0003  	VARCHAR(20),
                  field0004  	VARCHAR(20),
                  field0005  	VARCHAR(20),
                  field0006  	VARCHAR(20),
                  field0007  	VARCHAR(20),
                  field0008  	VARCHAR(20),
                  field0009  	VARCHAR(20),
                  field0010  	VARCHAR(20),
                  field0011  	VARCHAR(20),
                  field0012  	VARCHAR(20),
                  field0013  	VARCHAR(20),
                  field0014  	VARCHAR(20),
                  field0015  	VARCHAR(20),
                  field0016  	VARCHAR(20),
                  field0017  	VARCHAR(20),
                  field0018  	VARCHAR(20),
                  field0019  	VARCHAR(20),
                  field0020  	VARCHAR(20),
                  field0021  	VARCHAR(20),
                  rybh   	VARCHAR(50),
                  flag   	VARCHAR(10),
                  updateDate 	VARCHAR(20),
                  sksyncstatus	VARCHAR(10),
                  sksyncmsg	VARCHAR(200)
)

create or replace  view jtzj_jtgkrjhzdtj as
select TBBBST_ZZNM ZZNM,tbbbst_slnm SLNM,TBBBST_BBNM BBNM,TBBBDY.TBBBDY_dybh DYBH,TBBBDY.TBBBDY_dynm DYNM,'NDZJ' BS from TBBBST
 left join TBFASL on TBBBST_SLNM=TBFASL_SLNM
 left join tbbzfa on tbbzfa_fanm=TBFASL_fanm
 left JOIN TBBBDY ON TBBBDY_DYBH = TBBBST_DYBH
where TBBZFA_GDZQ ='13'  AND TBFASL_bzks >= to_char(current_date + interval '1 day', 'YYYYMMDD')
  AND TBFASL_bzzz <= to_char(current_date + interval '1 day', 'YYYYMMDD')  and TBBBST_STAT  ='BZ';


create or replace  view JTZJ_JTGKRJHZDTJ_WB as
select TBBBST_ZZNM ZZNM,tbbbst_slnm SLNM,TBBBST_BBNM BBNM,TBBBDY.TBBBDY_dybh DYBH,TBBBDY.TBBBDY_dynm DYNM,'NDZJ' BS from TBBBST
 left join TBFASL on TBBBST_SLNM=TBFASL_SLNM
 left join tbbzfa on tbbzfa_fanm=TBFASL_fanm
 left JOIN TBBBDY ON TBBBDY_DYBH = TBBBST_DYBH
where TBBZFA_GDZQ ='13'  AND TBFASL_bzks >= to_char(current_date + interval '1 day', 'YYYYMMDD')
  AND TBFASL_bzzz <= to_char(current_date + interval '1 day', 'YYYYMMDD')  and TBBBST_STAT ='WB';

alter table JTGKEMPLOYEERESULT add ygstatus varchar(10);
alter table JTGKEMPLOYEERESULT add ygmsg varchar(500);
alter table JTGKEMPLOYEERESULT add ygzhstatus varchar(10);
alter table JTGKEMPLOYEERESULT add ygzhmsg varchar(500);
update JTGKEMPLOYEERESULT set ygzhstatus='0',ygstatus='0';


create table JTGKCBZXRESULT
(
    id 	VARCHAR(300),
    group_id	varchar(9)	,
    cost_center_code	varchar(30)	,
    end_date	varchar(24)	,
    control_area	varchar(12)	,
    language_code	varchar(3)	,
    general_text	varchar(60)	,
    long_text	varchar(120)	,
    match_text	varchar(60)	,
    cost_center_type	varchar(3)	,
    create_date	varchar(24)	,
    start_date	varchar(24)	,
    change_date	varchar(24)	,
    function_area	varchar(48)	,
    user_name	varchar(36)	,
    cost_center_person	varchar(60)	,
    cost_center_user	varchar(36)	,
    company_code	varchar(12)	,
    profit_center_code	varchar(30)	,
    region_code	varchar(9)	,
    organization_code	varchar(18)	,
    organization_type	varchar(6)	,
    dept_code	varchar(36)	,
    dept_name	varchar(480)	,
    dept2_name	varchar(120)	,
    object_identifier	varchar(24)	,
    object_number	varchar(66)	,
    currency	varchar(15)	,
    tax_jurisdiction	varchar(45)	,
    record_complete_flag	varchar(3)	,
    complete_description	varchar(480)	,
    statistical_object_flag	varchar(3)	,
    budgeted_cost_center	varchar(30)	,
    budget_availability_profile	varchar(18)	,
    budget_availab_active_flag	varchar(3)	,
    actual_primary_freeze_flag	varchar(3)	,
    actual_secondary_freeze_flag	varchar(3)	,
    actual_income_freeze_flag	varchar(3)	,
    plann_primary_freeze_flag	varchar(3)	,
    plann_secondary_freeze_flag	varchar(3)	,
    plann_income_freeze_flag	varchar(3)	,
    open_item_freeze_flag	varchar(3)	,
    standard_hierarchy_area	varchar(36)	,
    cost_aggregator_code	varchar(69)	,
    flag   	VARCHAR(10),
    updateDate 	VARCHAR(20),
    sksyncstatus	VARCHAR(10),
    sksyncmsg	VARCHAR(200)

)

create table JTGKCBZXGSGXRESULT
(
    id 	VARCHAR(300),
    group_id	varchar(9),
    control_area	varchar(12),
    profit_center_code	varchar(30),
    company_code	varchar(12)

)

create table JTGKLRZXRESULT
(
    id 	VARCHAR(300),
    group_id	varchar(9),
    profit_center_code	varchar(30),
    control_area	varchar(12),
    end_date	varchar(24),
    language_code	varchar(3),
    general_text	varchar(60),
    long_text	varchar(120),
    match_text	varchar(60),
    create_date	varchar(24),
    start_date	varchar(24),
    user_name	varchar(36),
    profit_center_person	varchar(60),
    profit_center_group_code	varchar(36),
    unit_code	varchar(12),
    flag   	VARCHAR(10),
    updateDate 	VARCHAR(20),
    sksyncstatus	VARCHAR(10),
    sksyncmsg	VARCHAR(200)

)

create table JTGKEMPLOYEEBCRESULT(
     id	varchar(36),
     userId	varchar(36),
     userName	varchar(100),
     unitId	varchar(36),
     unitTxt	varchar(100),
     systemId	varchar(60),
     systemTxt	varchar(100),
     stell	varchar(40),
     stext	varchar(100),
     deptId	varchar(100),
     deptName	varchar(100),
     zhrOtext	varchar(100),
     phoneNumber	varchar(30),
     email	varchar(30),
     centerId	varchar(36),
     centerTxt	varchar(100),
     zhrTime1	varchar(10),
     zhrRzrq	varchar(10),
     orgeh	varchar(10),
     officeId	varchar(36),
     officeTxt	varchar(100),
     directorCode	varchar(30),
     branchCode	varchar(30),
     zhrCost	varchar(60),
     zhrCosttxt	varchar(100),
     werks	varchar(60),
     werksTxt	varchar(100),
     inst	varchar(50),
     gesc	varchar(10),
     persg	varchar(10),
     zhrLoca	varchar(100),
     levelpk	varchar(10),
     zhrPtext	varchar(100),
     status	varchar(10),
     company	varchar(30),
     companyName	varchar(100),
     zhrBank	varchar(100),
     zhrAccount	varchar(36),
     zzKhhs	varchar(30),
     zzKhhd	varchar(30),
     zzKhh	varchar(200),
     zzYhh	varchar(100),
     zzLhh	varchar(30),
     plans	varchar(50),
     flag	varchar(8),
     ygzhstatus	varchar(6),
     ygzhmsg	varchar(500),
     ygstatus	varchar(20),
     ygmsg	varchar(500)
)


create table JTGKJXHRESULT (
     id	varchar(36),
     BUKRS varchar(100),
     HKONT varchar(100),
     BANKN varchar(100),
     BUTXT varchar(100),
     BANKA varchar(100),
     PERNR varchar(100),
     CNAME varchar(100),
     MAIL varchar(100),
    flag	varchar(8),
    updateDate 	VARCHAR(20),
    sksyncstatus	VARCHAR(10),
    sksyncmsg	VARCHAR(200)
)

create table JTGKKSZSJRESULT (
      id			varchar(36)	,
      category_code			varchar(120)	,
      category_name			varchar(1200)	,
      code			varchar(120)	,
      assistant_code1			varchar(120)	,
      assistant_code2			varchar(120)	,
      assistant_code3			varchar(120)	,
      assistant_code4			varchar(120)	,
      mnemonic_code			varchar(1200)	,
      parent_id			varchar(36)	,
      recorder_code			varchar(90)	,
      recorder_name			varchar(180)	,
      recorder_time			varchar(36)	,
      pretrial_code			varchar(90)	,
      pretrial_name			varchar(180)	,
      pretrial_time			varchar(36)	,
      auditor_code			varchar(90)	,
      auditor_name			varchar(180)	,
      audit_time			varchar(36)	,
      audit_level			varchar(9)	,
      audit_flag			varchar(9)	,
      retrieve_flag			varchar(9)	,
      retriever_code			varchar(90)	,
      retriever_name			varchar(180)	,
      retrieve_time			varchar(36)	,
      freeze_flag			varchar(9)	,
      freezer_code			varchar(90)	,
      freezer_name			varchar(180)	,
      freeze_time			varchar(36)	,
      workflow_id			varchar(36)	,
      recorder_corp			varchar(90)	,
      mdm_code			varchar(180)	,
      audit_node_name			varchar(768)	,
      master_data_version			varchar(36)	,
      physical_model_version			varchar(36)	,
      filing_flag			varchar(180)	,
      filing_user_code			varchar(90)	,
      filing_user_name			varchar(180)	,
      filing_date			varchar(36)	,
      lucence_flag			varchar(9)	,
      lucence_time			varchar(36)	,
      submit_corp			varchar(90)	,
      desc_long			varchar(1500)	,
      desc_short			varchar(1500)	,
      partner_type			varchar(900)	,
      merchant_code			varchar(900)	,
      merchant_name			varchar(900)	,
      search_term			varchar(900)	,
      language			varchar(900)	,
      trade_partner			varchar(900)	,
      country_region			varchar(900)	,
      province_code			varchar(900)	,
      city			varchar(900)	,
      postal_code			varchar(900)	,
      remark			varchar(1500)	,
      validate_msg			varchar(1500)	,
      modify_group_label_code			varchar(1500)	,
      category_version			varchar(36)	,
      mdm_code_createable			varchar(9)	,
      last_modify_recorder_code			varchar(90)	,
      last_modify_recorder_name			varchar(180)	,
      last_modify_record_time			varchar(36)	,
      submit_time			varchar(36)	,
      flow_para			varchar(120)	,
      file_count			varchar(36)	,
      temp_save_flag			varchar(9)	,
      task_flag			varchar(9)	,
      uuid			varchar(96)	,
      error_msg			varchar(1500)	,
      auditing_flag			varchar(15)	,
      release_flag			varchar(15)	,
      data_source_flag			varchar(30)	,
      last_modify_submit_corp			varchar(90)	,
      workflow			varchar(9)	,
      stand_info			varchar(2400)	,
      security_level_code			varchar(90)	,
      submitter_name			varchar(180)	,
      address			varchar(180)	,
      tax_category			varchar(180)	,
      partner_nature			varchar(180)	,
      tax_number			varchar(180)	,
      Billing_phone			varchar(180)	,
      mobile_phone			varchar(180)	,
      email			varchar(180)	,
      file			varchar(180)	,
      freeze_customer			varchar(180)	,
      delete_customer			varchar(180)	,
      freeze_client_companies			varchar(180)	,
      freeze_sales_organizations			varchar(180)	,
      freeze_supplier			varchar(180)	,
      delete_supplier			varchar(180)	,
      freeze_supplier_companies			varchar(180)	,
      freeze_purchasing_organizations			varchar(180)	,
      supply_range			varchar(180)	,
      legal_nature			varchar(180)	,
      crt_number			varchar(180)	,
      icms_taxpayer			varchar(180)	,
      industry_main_type			varchar(180)	,
      tax_declaration_type			varchar(180)	,
      customer_group_abbreviation			varchar(180)	,
      second_level_company			varchar(180)	,
      affiliated_group			varchar(180)	,
      employee_code			varchar(180)	,
      is_finance			varchar(180)	,
      data_source			varchar(180)	,
      merchant_type			varchar(180)	,
      supplier_type			varchar(180)	,
      customer_classification			varchar(180)	,
      transportation_supplier			varchar(180)	,
      is_portal_supplier			varchar(180)	,
      reason			varchar(180)	,
      tax_jurisdiction			varchar(180)	,
      house_number			varchar(180)	,
      province_name			varchar(180)	,
      gw_resource_dev_Engineer			varchar(180)	,
      wind_turbine_supply_chain			varchar(180)	,
      contact_person			varchar(180)	,
      flag	varchar(8),
      updateDate 	VARCHAR(20),
      sksyncstatus	VARCHAR(10),
      sksyncmsg	VARCHAR(200)

)


create table JTGKDEPOSITPZ(
          ID	VARCHAR(36),
          DJID	VARCHAR(36),
          DJBH	VARCHAR(100),
          SKCJBH	VARCHAR(3),
          SKCJMC	VARCHAR(100),
          CDLX_CODE	VARCHAR(1),
          CDLX_NAME	VARCHAR(100),
          bukrs	VARCHAR(10),
          butxt	VARCHAR(1000),
          actnbr	VARCHAR(36),
          zszfs	VARCHAR(1),
          waers	VARCHAR(100),
          waers_nm	VARCHAR(1000),
          amount	VARCHAR(100),
          interest	VARCHAR(100),
          bnklz	VARCHAR(12),
          banka	VARCHAR(100),
          kunnr	VARCHAR(10),
          prctr	VARCHAR(10),
          ltext	VARCHAR(60),
          kostl	VARCHAR(10),
          ltext_cb	VARCHAR(40),
          zsgtx	VARCHAR(1024),
          zsqr	VARCHAR(128),
          zblr	VARCHAR(128),
          zdate	VARCHAR(10),
          proxy_id	VARCHAR(36),
          flag	VARCHAR(10),
          status	VARCHAR(10),
          msg	VARCHAR(1000),
         cdlx varchar(10),
          DEPOSITTYPE varchar(10)

)



CREATE TABLE "jtgkjrcktzresult" (
     "id" varchar(36) COLLATE "pg_catalog"."default" NOT NULL,
     "serial" varchar(32) COLLATE "pg_catalog"."default",
     "bank_name" varchar(254) COLLATE "pg_catalog"."default",
     "product" varchar(254) COLLATE "pg_catalog"."default",
     "product_name" varchar(254) COLLATE "pg_catalog"."default",
     "low_amount" varchar(254) COLLATE "pg_catalog"."default",
     "hign_amount" varchar(254) COLLATE "pg_catalog"."default",
     "configprice" varchar(53) COLLATE "pg_catalog"."default",
     "currentrate" varchar(254) COLLATE "pg_catalog"."default",
     "configdate" varchar(10) COLLATE "pg_catalog"."default",
     "enddate" varchar(10) COLLATE "pg_catalog"."default",
     "deposit_day" varchar(254) COLLATE "pg_catalog"."default",
     "expected_amonut" varchar(53) COLLATE "pg_catalog"."default",
     "deposit_state" varchar(254) COLLATE "pg_catalog"."default",
     "amount" varchar(30),
     "clint_name" varchar(100) COLLATE "pg_catalog"."default",
     "payment_method" varchar(100) COLLATE "pg_catalog"."default",
     CONSTRAINT "jtgkjrcktzresult_pkey" PRIMARY KEY ("id")
)
;

-- ALTER TABLE "jtgkjrcktzresult"
--     OWNER TO gscloud_qas_test;

COMMENT ON COLUMN "jtgkjrcktzresult"."serial" IS '序号';

COMMENT ON COLUMN "jtgkjrcktzresult"."bank_name" IS '银行名称';

COMMENT ON COLUMN "jtgkjrcktzresult"."product" IS '产品';

COMMENT ON COLUMN "jtgkjrcktzresult"."product_name" IS '产品名称';

COMMENT ON COLUMN "jtgkjrcktzresult"."low_amount" IS '最低金额';

COMMENT ON COLUMN "jtgkjrcktzresult"."hign_amount" IS '最高金额';

COMMENT ON COLUMN "jtgkjrcktzresult"."configprice" IS '年化收益率';

COMMENT ON COLUMN "jtgkjrcktzresult"."currentrate" IS '活期利率';

COMMENT ON COLUMN "jtgkjrcktzresult"."configdate" IS '到期日期';

COMMENT ON COLUMN "jtgkjrcktzresult"."enddate" IS '交易日期';

COMMENT ON COLUMN "jtgkjrcktzresult"."deposit_day" IS '天数';

COMMENT ON COLUMN "jtgkjrcktzresult"."expected_amount" IS '预期收益';

COMMENT ON COLUMN "jtgkjrcktzresult"."deposit_state" IS '状态';
COMMENT ON COLUMN "jtgkjrcktzresult"."amount" IS '交易金额';
COMMENT ON COLUMN "jtgkjrcktzresult"."clint_name" IS '开户单位';
COMMENT ON COLUMN "jtgkjrcktzresult"."payment_method" IS '付息方式';

COMMENT ON TABLE "jtgkjrcktzresult" IS '同业存款明细表';


alter table  jtgkjrcktzresult add amount   varchar(30);
alter table  jtgkjrcktzresult add clint_name   varchar(30);
alter table  jtgkjrcktzresult add payment_method   varchar(30);

create table JTGKNBDDRESULT(
       id	varchar(200),
       mandt	varchar(9)	,
       order_no	varchar(36)	,
       order_type	varchar(12)	,
       order_category	varchar(6)	,
       order_currency	varchar(15)	,
       order_status	varchar(6)	,
       modify_date	varchar(24)	,
       order_flag	varchar(3)	,
       reference_order_no	varchar(36)	,
       enterer	varchar(36)	,
       applicant	varchar(60)	,
       applicant_phone	varchar(60)	,
       relevant_person_phone	varchar(60)	,
       responsible_person	varchar(60)	,
       last_modifier	varchar(36)	,
       order_estimated_cost	varchar(45)		,
       depart	varchar(45)	,
       object_no	varchar(66)	,
       profit_center_code	varchar(30)	,
       release_date	varchar(24)	,
       technical_complete_date	varchar(24)	,
       settlement_date	varchar(24)	,
       modify_time	varchar(18)	,
       create_time	varchar(18)	,
       timestamp	varchar(30)	,
       descc	varchar(120)	,
       long_text	varchar(3)	,
       factory1_code	varchar(12)	,
       factory2_code	varchar(12)	,
       business_range	varchar(12)	,
       control_range	varchar(12)	,
       collector_code	varchar(69)	,
       position	varchar(30)	,
       condition_table_use	varchar(3)	,
       application	varchar(6)	,
       cost_calculation_table	varchar(18)	,
       distribute_group	varchar(6)	,
       settlement_cost_element	varchar(30)	,
       company_code	varchar(12)	,
       request_company_code	varchar(12)	,
       request_cost_center_code	varchar(30)	,
       cost_center_code	varchar(30)	,
       basic_list_cost_center	varchar(30)	,
       posted_cost_center	varchar(30)	,
       maintain_task_work_center	varchar(24)	,
       wbs	varchar(24)	,
       difference_code	varchar(18)	,
       result_analysis_code	varchar(18)	,
       functional_scope	varchar(48)	,
       object_class	varchar(6)	,
       external_order_number	varchar(60)	,
       product_process	varchar(36)	,
       process_category	varchar(12)	,
       responsibilities_relate_factory	varchar(12)	,
       regulatory	varchar(12)	,
       maintenance_reason	varchar(60)	,
       production_line	varchar(60)	,
       supplier_account_number	varchar(30)	,
       storage_location	varchar(12)	,
       quality_nation_alloca_proj	varchar(60)	,
       zhtbh	varchar(90)

);

    insert into bpbizeventlistenings (id, listentargetsucode, listentargetdoctypeid, listentime, listendirection, listenstatus, listensrcsucode, listensvc, svctype) values (gen_random_uuid(), 'bebc', 'PaymentInstructions', 'generate', 'Pre', 2,'jfkj', 'com.inspur.cloud.jtgk.goldwind.jk.jcsj.controller.goldInterfaceController.addPostInfo', '2');

create table  JTGKWBSXMRESULT(
                 ID	varchar(100)	,
                 mandt	varchar(9)	,
                 wbs_factor	varchar(24)	,
                 wbs_element	varchar(72)	,
                 wbs_short_desc	varchar(120)	,
                 wbs_short_upper_desc	varchar(120)	,
                 wbs_identifier	varchar(48)	,
                 wbs_company_code	varchar(12)	,
                 wbs_control_range	varchar(12)	,
                 wbs_currency	varchar(15)	,
                 t_wbs_element	varchar(3)	,
                 standard_wbs	varchar(24)	,
                 project_in_code	varchar(24)	,
                 project_type	varchar(6)	,
                 project_hierarchy_level	varchar(30)	,
                 profit_center_code	varchar(30)	,
                 actual_posting_cost_center_code	varchar(30)	,
                 function_range	varchar(48)	,
                 request_company_code	varchar(12)	,
                 factory_code	varchar(12)	,
                 object_no	varchar(66)	,
                 object_class	varchar(6)	,
                 applicant_code	varchar(24)	,
                 applicant_name	varchar(75)	,
                 responsible_person_code	varchar(24)	,
                 responsible_person_name	varchar(75)	,
                 creator_name	varchar(36)	,
                 create_date	varchar(24)	,
                 modifier_name	varchar(36)	,
                 modify_date	varchar(24)	,
                 plan_element_flag	varchar(3)	,
                 account_allocation_flag	varchar(3)	,
                 wbs_project_summary_flag	varchar(3)	,
                 keyword_id	varchar(21)	,
                 ps_progress	varchar(21)

);

create table  JTGKPayresultRESULT(
                ID  VARCHAR(50),
                  PAY_ARRANGE	varchar(100)	,
                  APPROVAL_RESULT	varchar(10)	,
                  PAY_REQ	varchar(100)	,
                  PAY_AMOUNT	varchar(50)	,
                  PAY_METHOD	varchar(10)	,
                  BANKK	varchar(500)	,
                  BANKN	varchar(100)	,
                  BOE_TYPE	varchar(10)	,
                  BOE_HKONT	varchar(50)	,
                  ZFBDT	varchar(10)	,
                  BILL_NUMBER	varchar(50)	,
                  BOE_PROVIDER	varchar(100)	,
                  BOE_RECEIVER	varchar(100)	,
                  indorser	varchar(100)	,
                  LIFNR_TEXT	varchar(500)	,
                  LIFNR	varchar(50)	,
                  BLART	varchar(10)	,
                  PAY_DATE	varchar(10)	,
                  WAERS	varchar(10)	,
                  INVOICE_NUMBER	varchar(50)	,
                  DISCOUNT_AMOUNT	varchar(50)	,
                  BANKK_RECEIVE	varchar(50)	,
                  KURSF	varchar(50)	,
                  KURSF_DATE	varchar(10)	,
                  REMARK	varchar(500)	,
                  RETURN_FLAG	varchar(10)	,
                  ZRESERVE_I_F1	varchar(500)	,
                  ZRESERVE_I_F2	varchar(500)	,
                  ZRESERVE_I_F3	varchar(500)	,
                  ZRESERVE_I_F4	varchar(500)	,
                  ZRESERVE_I_F5	varchar(500)	,
                  pushdate	varchar(10)	,
                  pushr	varchar(50)	,
                  status 	varchar(10)	,
                  msg    	varchar(500)	,
                  proxy_id   	varchar(50)

);

CREATE TABLE jtgkbzhlresult (
                 mandt varchar(50) NULL,
                 gw_category varchar(50) NULL,
                 gw_inputcurrency varchar(50) NULL,
                 gw_r_account varchar(50) NULL,
                 gw_r_entity varchar(50) NULL,
                 gw_time varchar(50) NULL,
                 bpc_signdata varchar(50) NULL,
                 id varchar(100) NULL
);