//移动审批扩展
///web/platform/runtime/wf/webapp/mobiletaskcenter
(function (ua, win) {
    let protocol = window.location.protocol
    let host = window.location.host
    let authority = `${protocol}//${host}`

    let extend = win.extend || {}

/**
   * 移动审批二开兼容性扩展，按钮返回键扩展方法
   */
   extend.IsShowTitle=function(){
	 return false;
   }

    //移动审批二开兼容性扩展,按钮返回键扩展方法
    extend.closeTaskCenter = () => {
        //window.location.href = `${authority}/platform/runtime/wf/webapp/proc-center-mobile/index.html#/`
        //window.close();
    }

    // 从EIS列表打开待办任务详情页导航栏返回按钮以及手势返回触发方法
    extend.detailBack4OA = (e) => {
        //window.location.href = `${authority}/platform/runtime/wf/webapp/proc-center-mobile/index.html#/`
        //window.close();
        try {
            if (window.android) {
                window.android.goList();
            } else {
                window.webkit.messageHandlers.goList.postMessage(null);
            }
        } catch (error) {
            if (callback) {
                callback();
            } else {
                alert('关闭失败！');
            }
        }
    }

    // 任务办理界面点击提交方法请求成功后，触发方法
    extend.actionBack4OA = (e) => {
        // http://ip:host/apps/fastdweb/views/mobile/index.html#/65252a6e-2b51-f4e1-6f99-fd8cbe1d2441/list
        // window.location.href = `${authority}/platform/runtime/wf/webapp/proc-center-mobile/index.html#/`
        try {
            if (window.android) {
                window.android.goList();
            } else {
                window.webkit.messageHandlers.goList.postMessage(null);
            }
        } catch (error) {
            if (callback) {
                callback();
            } else {
                alert('关闭失败！');
            }
        }

    }

    win.extend = extend
})(navigator.userAgent, window)

if (typeof module === "object" && module && typeof module.exports === "object") {
    module.exports = window.extend
} else {
    if (typeof define === "function" && define.amd) {
        define("extend", [], function () {
            return window.extend
        })
    }
}
// 部署路径：6.	GSC安装目录/web/platform/runtime/wf/webapp/mobiletaskcenter/extend.js
//司库用的jQuery库，需要客户提供一下APP底座的使用方法