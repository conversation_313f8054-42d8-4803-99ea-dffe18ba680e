// \apps\bp\pf\xevents\viewprocess_event.js
// \apps\bp\pf\xevents\viewprocess_event.js
function customizeLogs(vos) {debugger
    var state="0";//是否批量生成
    for(var i=0;i<vos.length;i++){
        var userTasks=vos[i].userTasks;
        var n=0; var list=[];
        for(var j=0;j<userTasks.length;j++){
            var stateName =userTasks[j].name;//0 提交 4 撤回
            if(stateName=="批量销户审批开始"){
                state="1";
            }
            if(userTasks[j].code=="START-1"&&state=="1"){

            }else{
                list[n]=userTasks[j];
                n++;
            }

        }
        vos[i].userTasks=list;
    }

}