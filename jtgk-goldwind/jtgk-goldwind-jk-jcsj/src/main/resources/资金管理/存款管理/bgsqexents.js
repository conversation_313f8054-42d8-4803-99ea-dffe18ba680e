//  /apps/jtgk/jfkj/extend/bgsqexents.js
idp.event.bind("domReady", function(e, context) {
    idp.event.register("OPENUNIT_NAME", "selected", function(e, value, name, obj) {
        var unit = value.curData[0].ID;
        let sqlId1 = 'ffed18ac-3a4e-33a8-4b7c-fbe7f62e35b2';
        let filters3 = [];
        filters3.push({
            Left: "",
            Field: 'id',
            Operate: " = ",
            IsExpress: false,
            Value: unit,
            Right: "",
            Logic: "AND"
        });
        let result3 = [];
        getQueryData(sqlId1, filters3, [], {})
            .done(function(data) {
                if (data.Code !== 'ok') {
                    return;
                }
                result3 = data.Data.Rows;
            }).fail(function(data) {});
        if(result3.length > 0){debugger
            let YWDYBH =result3[0].YWDYBH ;
            let YWDYMC =result3[0].YWDYMC ;
            idp.control.get("TXT02").setValue(YWDYBH);//业务单元编号
            idp.control.get("TXT04").setValue(YWDYMC);//业务单元名称
            idp.loaded();
        }
    })
})
idp.event.bind("loadData", function(e, context) {
    let sqrid=idp.uiview.modelController.getMainRowObj().APPLICANTID;
    if(sqrid==null||sqrid==''||sqrid==undefined){
        sqrid=  idp.context.get("UserId");
    }
    let sqlId1 = 'b59820f9-554f-2be1-8585-356b14031c5c';
    let filters1 = [];
    filters1.push({
        Left: "",
        Field: 'gspuser.id',
        Operate: " = ",
        IsExpress: false,
        Value: sqrid,
        Right: "",
        Logic: "AND"
    });
    let result1 = [];
    getQueryData(sqlId1, filters1, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result1 = data.Data.Rows;
        }).fail(function(data) {});
    if(result1.length > 0){debugger
        let zrr =result1[0].SJINCHARGE ;
        let fgld =result1[0].INCHARGE ;
        let xzzz =result1[0].XZZZ ;
        let bjzz =result1[0].BJZZ ;
        let sjfzr =result1[0].SJHEAD ;
        idp.control.get("TXT06").setValue(xzzz);
        // idp.control.get("TXT08").setValue(zrr);
        idp.control.get("TXT09").setValue(fgld);
        idp.control.get("TXT05").setValue(bjzz);
        idp.control.get("TXT03").setValue(sjfzr);//上级负责人
        idp.loaded();
    }


})

idp.event.bind("viewReady", function(e, context) {//APPLICANTID
    const data = idp.uiview.modelController.getMainRowObj();
    let sqlId1 = 'b59820f9-554f-2be1-8585-356b14031c5c';
    let filters1 = [];
    filters1.push({
        Left: "",
        Field: 'gspuser.id',
        Operate: " = ",
        IsExpress: false,
        Value: idp.context.get("UserId"),
        Right: "",
        Logic: "AND"
    });
    let result1 = [];
    getQueryData(sqlId1, filters1, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result1 = data.Data.Rows;
        }).fail(function(data) {});
    if(result1.length > 0){debugger
        let zrr =result1[0].SJINCHARGE ;
        let fgld =result1[0].INCHARGE ;
        let xzzz =result1[0].XZZZ ;
        let bjzz =result1[0].BJZZ ;
        let sjfzr =result1[0].SJHEAD ;
        idp.control.get("TXT06").setValue(xzzz);
        // idp.control.get("TXT08").setValue(zrr);
        idp.control.get("TXT09").setValue(fgld);
        idp.control.get("TXT05").setValue(bjzz);//由于TXT01用了，换成05
        idp.control.get("TXT03").setValue(sjfzr);//上级负责人
        idp.loaded();
    }


})

//取数
function getQueryData(sqlId, filter, orders, bizinfo, callback, pageSize) {
    var params = {
        sqlId: sqlId,
        orders: orders,
        fields: filter,
        page: 0,
        pageSize: pageSize || 0,
        bizId: bizinfo.bizId,
        bizOpId: bizinfo.bizOpId
    };
    if (!callback)
        return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false);
    return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false).done(function(data) {
        callback(data);
    }).fail(function(data) {
    });
}


//二开代理
idp.custom.delegate({
    "controls":{
        // 工具栏Id
        "toolbar1":{
            //工具栏点击事件
            "toolbarClick":{
                "before":function(e,btn,toolbar){
                    if(e.id == "baritem_save"){
                        //var data=idp.uiview.gridController.mainGrid.data.Rows;
                        var data=idp.uiview.modelController.deafaultData[1].data;//明细表
                        var msg="";
                        for (var i = 0; i < data.length; i++){
                            for (var j = 1; j < data.length; j++) {
                                if(data[i].SETTINGITEM_ITEMNAME$LANGUAGE$==data[j].SETTINGITEM_ITEMNAME$LANGUAGE$ &&i!=j) {
                                    msg+= "第"+(i+1)+"行与第"+(j+1)+"行变更项重复！<br>";
                                    break;
                                }
                            }

                        }
                        if(msg!=""){
                            idp.warn(msg);
                            throw(msg);
                        }
                    }else if(e.id == "baritem_submit"){
                        //var data=idp.uiview.gridController.mainGrid.data.Rows;
                        var data=idp.uiview.modelController.deafaultData[1].data;//明细表
                        var msg="";
                        for (var i = 0; i < data.length; i++){
                            for (var j = 1; j < data.length; j++) {
                                if(data[i].SETTINGITEM_ITEMNAME$LANGUAGE$==data[j].SETTINGITEM_ITEMNAME$LANGUAGE$ &&i!=j) {
                                    msg+= "第"+(i+1)+"行与第"+(j+1)+"行变更项重复！<br>";
                                    break;
                                }
                            }

                        }
                        if(msg!=""){
                            idp.warn(msg);
                            throw(msg);
                        }
                    }
                }
            },
        },
    }
})