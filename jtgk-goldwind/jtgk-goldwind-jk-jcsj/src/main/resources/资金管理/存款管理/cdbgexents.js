//  /apps/jtgk/jfkj/extend/cdbgexents.js
idp.event.bind("domReady", function(e, context) {//APPLICANTID
    idp.event.register("grid_tmbankdeposit", "afterShowData", function (e, data) {
        // if (idp.control.get('grid_tmbankdeposit').rows.length === 1) {
        //     var billListRow = idp.control.get('grid_tmbankdeposit').rows[0];
        //     var ID = billListRow.ID;
        var myURL = new URL(window.location.href);
        var searchParams = new URLSearchParams(myURL.search);
        var depositID = searchParams.get("depositid");
        if (depositID) {
            var ID = depositID;
            //alert(unit);
            let sqlId = 'dbb6133f-2837-2ed6-4372-725beedd030e';
            let filters = [];
            filters.push({
                Left: "",
                Field: 'TMBANKDEPOSIT.ID',
                Operate: " = ",
                IsExpress: false,
                Value: ID,
                Right: "",
                Logic: "AND"
            });
            let result = [];
            getQueryData(sqlId, filters, [], {})
                .done(function (data) {
                    if (data.Code !== 'ok') {
                        return;
                    }
                    result = data.Data.Rows;
                }).fail(function (data) {
            });
            if (result.length > 0) {
                let TXT01 = result[0].TXT01;
                idp.control.get("DEPOSITID_TXT01").setValue(TXT01);//财务公司营业部负责人
                idp.control.get("DEPOSITID_TXT02").setValue(result[0].TXT02);
                idp.control.get("DEPOSITID_TXT19").setValue(result[0].TXT19);
                idp.control.get("DEPOSITID_TXT20").setValue(result[0].TXT20);
                idp.control.get("DEPOSITID_TXT11").setValue(result[0].TXT11);
                idp.control.get("DEPOSITID_TXT12").setValue(result[0].TXT12);
                idp.control.get("DEPOSITID_TXT13").setValue(result[0].TXT13);
                idp.control.get("DEPOSITID_AMT09").setValue(result[0].AMT09);
                idp.control.get("DEPOSITID_AMT10").setValue(result[0].AMT10);
                idp.loaded();
            }
        }
    })
})
idp.event.bind("loadData", function(e, context) {//APPLICANTID
    // if (idp.control.get('grid_tmbankdeposit').rows.length === 1) {
    //     var billListRow = idp.control.get('grid_tmbankdeposit').rows[0];
    //     var ID = billListRow.ID;
    var myURL = new URL(window.location.href);
    var searchParams = new URLSearchParams(myURL.search);
    var depositID = searchParams.get("depositid");
    if (depositID) {
        var ID = depositID;
        //alert(unit);
        let sqlId = 'dbb6133f-2837-2ed6-4372-725beedd030e';
        let filters = [];
        filters.push({
            Left: "",
            Field: 'TMBANKDEPOSIT.ID',
            Operate: " = ",
            IsExpress: false,
            Value: ID,
            Right: "",
            Logic: "AND"
        });
        let result = [];
        getQueryData(sqlId, filters, [], {})
            .done(function (data) {
                if (data.Code !== 'ok') {
                    return;
                }
                result = data.Data.Rows;
            }).fail(function (data) {
        });
        if (result.length > 0) {
            let TXT01 = result[0].TXT01;
            idp.control.get("DEPOSITID_TXT01").setValue(TXT01);//财务公司营业部负责人
            idp.control.get("DEPOSITID_TXT02").setValue(result[0].TXT02);
            idp.control.get("DEPOSITID_TXT19").setValue(result[0].TXT19);
            idp.control.get("DEPOSITID_TXT20").setValue(result[0].TXT20);
            idp.control.get("DEPOSITID_TXT11").setValue(result[0].TXT11);
            idp.control.get("DEPOSITID_TXT12").setValue(result[0].TXT12);
            idp.control.get("DEPOSITID_TXT13").setValue(result[0].TXT13);
            idp.control.get("DEPOSITID_AMT09").setValue(result[0].AMT09);
            idp.control.get("DEPOSITID_AMT10").setValue(result[0].AMT10);
            idp.loaded();
        }
    }

})

idp.event.bind("viewReady", function(e, context) {//APPLICANTID
    // if (idp.control.get('grid_tmbankdeposit').rows.length === 1) {
    //     var billListRow = idp.control.get('grid_tmbankdeposit').rows[0];
    //     var ID = billListRow.ID;
    var myURL = new URL(window.location.href);
    var searchParams = new URLSearchParams(myURL.search);
    var depositID = searchParams.get("depositid");
    if (depositID) {
        var ID = depositID;
        //alert(unit);
        let sqlId = 'dbb6133f-2837-2ed6-4372-725beedd030e';
        let filters = [];
        filters.push({
            Left: "",
            Field: 'TMBANKDEPOSIT.ID',
            Operate: " = ",
            IsExpress: false,
            Value: ID,
            Right: "",
            Logic: "AND"
        });
        let result = [];
        getQueryData(sqlId, filters, [], {})
            .done(function (data) {
                if (data.Code !== 'ok') {
                    return;
                }
                result = data.Data.Rows;
            }).fail(function (data) {
        });
        if (result.length > 0) {
            let TXT01 = result[0].TXT01;
            idp.control.get("DEPOSITID_TXT01").setValue(TXT01);//财务公司营业部负责人
            idp.control.get("DEPOSITID_TXT02").setValue(result[0].TXT02);
            idp.control.get("DEPOSITID_TXT19").setValue(result[0].TXT19);
            idp.control.get("DEPOSITID_TXT20").setValue(result[0].TXT20);
            idp.control.get("DEPOSITID_TXT11").setValue(result[0].TXT11);
            idp.control.get("DEPOSITID_TXT12").setValue(result[0].TXT12);
            idp.control.get("DEPOSITID_TXT13").setValue(result[0].TXT13);
            idp.control.get("DEPOSITID_AMT09").setValue(result[0].AMT09);
            idp.control.get("DEPOSITID_AMT10").setValue(result[0].AMT10);
            idp.loaded();
        }
    }

})



//取数
function getQueryData(sqlId, filter, orders, bizinfo, callback, pageSize) {
    var params = {
        sqlId: sqlId,
        orders: orders,
        fields: filter,
        page: 0,
        pageSize: pageSize || 0,
        bizId: bizinfo.bizId,
        bizOpId: bizinfo.bizOpId
    };
    if (!callback)
        return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false);
    return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false).done(function(data) {
        callback(data);
    }).fail(function(data) {
    });
}
