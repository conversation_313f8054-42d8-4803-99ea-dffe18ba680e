//  /apps/jtgk/jfkj/extend/ckdqblexents.js
idp.event.bind("loadData", function(e, context) {
    const data=idp.uiview.modelController.getMainRowObj();
    var myURL = new URL(window.location.href);
    var searchParams = new URLSearchParams(myURL.search);
    var depositID = searchParams.get("depositid");
    var unit =idp.control.get("DEPOSITID_UNIT").getValue();
    let sqlId = '34777655-e68e-4647-5aa2-1a9988f4eb37';
    let filters = [];
    filters.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "10082359",
        Right: "",
        Logic: "AND"
    });
    let result = [];
    getQueryData(sqlId, filters, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT01").setValue(id);//财务公司营业部负责人
        idp.loaded();
    }
    let filters2 = [];
    filters2.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "40004563",
        Right: "",
        Logic: "AND"
    });
    getQueryData(sqlId, filters2, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT02").setValue(id);//资金管理部分管领导
        idp.loaded();
    }
    let filters4 = [];
    filters4.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "20000394",
        Right: "",
        Logic: "AND"
    },{
        Left: "",
        Field: 'lx',
        Operate: " = ",
        IsExpress: false,
        Value: "fgld",
        Right: "",
        Logic: "AND"
    });

    getQueryData(sqlId, filters4, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT09").setValue(id);//资金管理部分管领导
        idp.loaded();
    }
    let filters5 = [];
    filters5.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "20000394",
        Right: "",
        Logic: "AND"
    },{
        Left: "",
        Field: 'lx',
        Operate: " = ",
        IsExpress: false,
        Value: "fzr",
        Right: "",
        Logic: "AND"
    });

    getQueryData(sqlId, filters5, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT08").setValue(id);//资金管理部分管领导
        idp.loaded();
    }
    let sqlId1 = 'ffed18ac-3a4e-33a8-4b7c-fbe7f62e35b2';
    let filters3 = [];
    filters3.push({
        Left: "",
        Field: 'id',
        Operate: " = ",
        IsExpress: false,
        Value: unit,
        Right: "",
        Logic: "AND"
    });
    let result3 = [];
    getQueryData(sqlId1, filters3, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result3 = data.Data.Rows;
        }).fail(function(data) {});
    if(result3.length > 0){debugger
        let YWDYBH =result3[0].YWDYBH ;
        let YWDYMC =result3[0].YWDYMC ;
        idp.control.get("TXT20").setValue(YWDYBH);//业务单元编号
        idp.control.get("TXT19").setValue(YWDYMC);//业务单元名称
        idp.loaded();
    }
    if (depositID) {
        var ID = depositID;
        //alert(unit);
        let sqlIda = 'dbb6133f-2837-2ed6-4372-725beedd030e';
        let filtersa = [];
        filtersa.push({
            Left: "",
            Field: 'TMBANKDEPOSIT.ID',
            Operate: " = ",
            IsExpress: false,
            Value: ID,
            Right: "",
            Logic: "AND"
        });
        let result = [];
        getQueryData(sqlIda, filtersa, [], {})
            .done(function (data) {
                if (data.Code !== 'ok') {
                    return;
                }
                result = data.Data.Rows;
            }).fail(function (data) {
        });
        if (result.length > 0) {
            idp.control.get("DEPOSITID_TXT11").setValue(result[0].TXT11);
            idp.control.get("DEPOSITID_TXT12").setValue(result[0].TXT12);
            idp.control.get("DEPOSITID_TXT13").setValue(result[0].TXT13);
            // idp.control.get("DEPOSITID_AMT09").setValue(result[0].AMT09);
            // idp.control.get("DEPOSITID_AMT10").setValue(result[0].AMT10);
            idp.control.get("AMT09").setValue(result[0].AMT09);
            idp.control.get("AMT10").setValue(result[0].AMT10);
            idp.loaded();
        }
    }



})

idp.event.bind("viewReady", function(e, context) {//APPLICANTID
    const data = idp.uiview.modelController.getMainRowObj();
    var myURL = new URL(window.location.href);
    var searchParams = new URLSearchParams(myURL.search);
    var depositID = searchParams.get("depositid");
    var unit =idp.control.get("DEPOSITID_UNIT").getValue();
    let sqlId = '34777655-e68e-4647-5aa2-1a9988f4eb37';
    let filters = [];
    filters.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "10082359",
        Right: "",
        Logic: "AND"
    });
    let result = [];
    getQueryData(sqlId, filters, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT01").setValue(id);//财务公司营业部负责人
        idp.loaded();
    }
    let filters2 = [];
    filters2.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "40004563",
        Right: "",
        Logic: "AND"
    });
    getQueryData(sqlId, filters2, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT02").setValue(id);//资金管理部分管领导
        idp.loaded();
    }
    let filters4 = [];
    filters4.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "20000394",
        Right: "",
        Logic: "AND"
    },{
        Left: "",
        Field: 'lx',
        Operate: " = ",
        IsExpress: false,
        Value: "fgld",
        Right: "",
        Logic: "AND"
    });

    getQueryData(sqlId, filters4, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT09").setValue(id);//资金管理部分管领导
        idp.loaded();
    }
    let filters5 = [];
    filters5.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "20000394",
        Right: "",
        Logic: "AND"
    },{
        Left: "",
        Field: 'lx',
        Operate: " = ",
        IsExpress: false,
        Value: "fzr",
        Right: "",
        Logic: "AND"
    });

    getQueryData(sqlId, filters5, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT08").setValue(id);//资金管理部分管领导
        idp.loaded();
    }
    let filters6 = [];
    filters6.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "4000",
        Right: "",
        Logic: "AND"
    },{
        Left: "",
        Field: 'lx',
        Operate: " = ",
        IsExpress: false,
        Value: "fgld",
        Right: "",
        Logic: "AND"
    });

    getQueryData(sqlId, filters6, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT10").setValue(id);//资金管理部分管领导
        idp.loaded();
    }
    let sqlId1 = 'ffed18ac-3a4e-33a8-4b7c-fbe7f62e35b2';
    let filters3 = [];
    filters3.push({
        Left: "",
        Field: 'id',
        Operate: " = ",
        IsExpress: false,
        Value: unit,
        Right: "",
        Logic: "AND"
    });
    let result3 = [];
    getQueryData(sqlId1, filters3, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result3 = data.Data.Rows;
        }).fail(function(data) {});
    if(result3.length > 0){debugger
        let YWDYBH =result3[0].YWDYBH ;
        let YWDYMC =result3[0].YWDYMC ;
        idp.control.get("TXT20").setValue(YWDYBH);//业务单元编号
        idp.control.get("TXT19").setValue(YWDYMC);//业务单元名称
        idp.loaded();
    }
    if (depositID) {
        var ID = depositID;
        //alert(unit);
        let sqlIda = 'dbb6133f-2837-2ed6-4372-725beedd030e';
        let filtersa = [];
        filtersa.push({
            Left: "",
            Field: 'TMBANKDEPOSIT.ID',
            Operate: " = ",
            IsExpress: false,
            Value: ID,
            Right: "",
            Logic: "AND"
        });
        let result = [];
        getQueryData(sqlIda, filtersa, [], {})
            .done(function (data) {
                if (data.Code !== 'ok') {
                    return;
                }
                result = data.Data.Rows;
            }).fail(function (data) {
        });
        if (result.length > 0) {
            idp.control.get("DEPOSITID_TXT11").setValue(result[0].TXT11);
            idp.control.get("DEPOSITID_TXT12").setValue(result[0].TXT12);
            idp.control.get("DEPOSITID_TXT13").setValue(result[0].TXT13);
            // idp.control.get("DEPOSITID_AMT09").setValue(result[0].AMT09);
            // idp.control.get("DEPOSITID_AMT10").setValue(result[0].AMT10);
            idp.control.get("AMT09").setValue(result[0].AMT09);
            idp.control.get("AMT10").setValue(result[0].AMT10);
            idp.loaded();
        }
    }

})


//取数
function getQueryData(sqlId, filter, orders, bizinfo, callback, pageSize) {
    var params = {
        sqlId: sqlId,
        orders: orders,
        fields: filter,
        page: 0,
        pageSize: pageSize || 0,
        bizId: bizinfo.bizId,
        bizOpId: bizinfo.bizOpId
    };
    if (!callback)
        return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false);
    return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false).done(function(data) {
        callback(data);
    }).fail(function(data) {
    });
}
