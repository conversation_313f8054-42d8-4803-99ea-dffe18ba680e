//  /apps/jtgk/jfkj/extend/tzcksqcardexents.js 
idp.event.bind("domReady", function(e, context) {
    idp.event.register("UNIT_NAME", "selected", function(e, value, name, obj) {
        var unit = value.curData[0].ID;
        let sqlId1 = 'ffed18ac-3a4e-33a8-4b7c-fbe7f62e35b2';
        let filters3 = [];
        filters3.push({
            Left: "",
            Field: 'id',
            Operate: " = ",
            IsExpress: false,
            Value: unit,
            Right: "",
            Logic: "AND"
        });
        let result3 = [];
        getQueryData(sqlId1, filters3, [], {})
            .done(function(data) {
                if (data.Code !== 'ok') {
                    return;
                }
                result3 = data.Data.Rows;
            }).fail(function(data) {});
        if(result3.length > 0){debugger
            let YWDYBH =result3[0].YWDYBH ;
            let YWDYMC =result3[0].YWDYMC ;
            let DWBH =result3[0].CODE ;
            idp.control.get("TXT20").setValue(YWDYBH);//业务单元编号
            idp.control.get("TXT19").setValue(YWDYMC);//业务单元名称
            idp.control.get("TXT07").setValue(DWBH);//单位编号
            idp.control.get("TXT05").setValue(idp.control.get("TXT07").getValue()+idp.control.get("TXT06").getValue());//单位编号
            idp.loaded();
        }
    })
    idp.event.register("TXT04", "selected", function(e, value, name, obj){
        var CODE = value.curData[0].CODE;
        let sqlidss='581894e8-b1fd-53b3-1cfb-a85db7a6f1a0';
        let filterss = [];
        filterss.push({
            Left: "",
            Field: 'code',
            Operate: " = ",
            IsExpress: false,
            Value: CODE,
            Right: "",
            Logic: "AND"
        });
        let results = [];
        getQueryData(sqlidss, filterss, [], {})
            .done(function(data) {
                if (data.Code !== 'ok') {
                    return;
                }
                results = data.Data.Rows;
            }).fail(function(data) {});
        if(results.length > 0){debugger
            let id =results[0].ID ;
            idp.control.get("TXT06").setValue(results[0].EXTVARCHAR4);//机构编码
            idp.loaded();
        }
        idp.control.get("TXT05").setValue(idp.control.get("TXT07").getValue()+idp.control.get("TXT06").getValue());//单位编号
    })
})
idp.event.bind("loadData", function(e, context) {
    const data=idp.uiview.modelController.getMainRowObj();
    let userid= idp.context.get("UserId");
    if(userid!=null&&userid!=''&&userid!=undefined){
        let sqlidss='581894e8-b1fd-53b3-1cfb-a85db7a6f1a0';
        let filterss = [];
        filterss.push({
            Left: "",
            Field: 'userid',
            Operate: " = ",
            IsExpress: false,
            Value: userid,
            Right: "",
            Logic: "AND"
        });
        let results = [];
        getQueryData(sqlidss, filterss, [], {})
            .done(function(data) {
                if (data.Code !== 'ok') {
                    return;
                }
                results = data.Data.Rows;
            }).fail(function(data) {});
        if(results.length > 0){
            let id =results[0].ID ;
            idp.control.get("TXT04").setValue(results[0].CODE);//部门
            idp.control.get("TXT04").setText(results[0].NAME$LANGUAGE$);//部门
            idp.control.get("TXT06").setValue(results[0].EXTVARCHAR4);//机构编码
            idp.loaded();
        }
    }
    let sqlId = '34777655-e68e-4647-5aa2-1a9988f4eb37';
    let filters = [];
    filters.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "10082359",
        Right: "",
        Logic: "AND"
    });
    let result = [];
    getQueryData(sqlId, filters, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){
        let id =result[0].ID ;
        idp.control.get("TXT01").setValue(id);//财务公司营业部负责人
        idp.loaded();
    }
    let filters2 = [];
    filters2.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "40004563",
        Right: "",
        Logic: "AND"
    });
    getQueryData(sqlId, filters2, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){
        let id =result[0].ID ;
        idp.control.get("TXT02").setValue(id);//资金管理部分管领导
        idp.loaded();
    }
    let filters4 = [];
    filters4.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "20000394",
        Right: "",
        Logic: "AND"
    },{
        Left: "",
        Field: 'lx',
        Operate: " = ",
        IsExpress: false,
        Value: "fgld",
        Right: "",
        Logic: "AND"
    });

    getQueryData(sqlId, filters4, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT09").setValue(id);//资金管理部分管领导
        idp.loaded();
    }
    let filters5 = [];
    filters5.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "20000394",
        Right: "",
        Logic: "AND"
    },{
        Left: "",
        Field: 'lx',
        Operate: " = ",
        IsExpress: false,
        Value: "fzr",
        Right: "",
        Logic: "AND"
    });

    getQueryData(sqlId, filters5, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT08").setValue(id);//资金管理部分管领导
        idp.loaded();
    }
    let filters6 = [];
    filters6.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "4000",
        Right: "",
        Logic: "AND"
    },{
        Left: "",
        Field: 'lx',
        Operate: " = ",
        IsExpress: false,
        Value: "fgld",
        Right: "",
        Logic: "AND"
    });

    getQueryData(sqlId, filters6, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT10").setValue(id);//资金管理部分管领导
        idp.loaded();
    }


})

idp.event.bind("viewReady", function(e, context) {//APPLICANTID
    const data = idp.uiview.modelController.getMainRowObj();
    let userid= idp.context.get("UserId");
    if(userid!=null&&userid!=''&&userid!=undefined){
        let sqlidss='581894e8-b1fd-53b3-1cfb-a85db7a6f1a0';
        let filterss = [];
        filterss.push({
            Left: "",
            Field: 'userid',
            Operate: " = ",
            IsExpress: false,
            Value: userid,
            Right: "",
            Logic: "AND"
        });
        let results = [];
        getQueryData(sqlidss, filterss, [], {})
            .done(function(data) {
                if (data.Code !== 'ok') {
                    return;
                }
                results = data.Data.Rows;
            }).fail(function(data) {});
        if(results.length > 0){
            let id =results[0].ID ;
            idp.control.get("TXT04").setValue(results[0].CODE);//部门
            idp.control.get("TXT04").setText(results[0].NAME$LANGUAGE$);//部门
            idp.control.get("TXT06").setValue(results[0].EXTVARCHAR4);//机构编码
            idp.loaded();
        }
    }
    let sqlId = '34777655-e68e-4647-5aa2-1a9988f4eb37';
    let filters = [];
    filters.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "10082359",
        Right: "",
        Logic: "AND"
    });
    let result = [];
    getQueryData(sqlId, filters, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){
        let id =result[0].ID ;
        idp.control.get("TXT01").setValue(id);//财务公司营业部负责人
        idp.loaded();
    }
    let filters2 = [];
    filters2.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "40004563",
        Right: "",
        Logic: "AND"
    });
    getQueryData(sqlId, filters2, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){
        let id =result[0].ID ;
        idp.control.get("TXT02").setValue(id);//资金管理部分管领导
        idp.loaded();
    }
    let filters4 = [];
    filters4.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "20000394",
        Right: "",
        Logic: "AND"
    },{
        Left: "",
        Field: 'lx',
        Operate: " = ",
        IsExpress: false,
        Value: "fgld",
        Right: "",
        Logic: "AND"
    });

    getQueryData(sqlId, filters4, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT09").setValue(id);//资金管理部分管领导
        idp.loaded();
    }
    let filters5 = [];
    filters5.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "20000394",
        Right: "",
        Logic: "AND"
    },{
        Left: "",
        Field: 'lx',
        Operate: " = ",
        IsExpress: false,
        Value: "fzr",
        Right: "",
        Logic: "AND"
    });

    getQueryData(sqlId, filters5, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT08").setValue(id);//资金管理部分管领导
        idp.loaded();
    }
    let filters6 = [];
    filters6.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "4000",
        Right: "",
        Logic: "AND"
    },{
        Left: "",
        Field: 'lx',
        Operate: " = ",
        IsExpress: false,
        Value: "fgld",
        Right: "",
        Logic: "AND"
    });

    getQueryData(sqlId, filters6, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT10").setValue(id);//资金管理部分管领导
        idp.loaded();
    }
})


//取数
function getQueryData(sqlId, filter, orders, bizinfo, callback, pageSize) {
    var params = {
        sqlId: sqlId,
        orders: orders,
        fields: filter,
        page: 0,
        pageSize: pageSize || 0,
        bizId: bizinfo.bizId,
        bizOpId: bizinfo.bizOpId
    };
    if (!callback)
        return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false);
    return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false).done(function(data) {
        callback(data);
    }).fail(function(data) {
    });
}
