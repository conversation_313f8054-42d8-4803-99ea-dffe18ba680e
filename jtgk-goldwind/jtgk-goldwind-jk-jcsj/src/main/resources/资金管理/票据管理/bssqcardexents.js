//  /apps/jtgk/jfkj/extend/bssqcardexents.js
debugger;
let idy =sessionStorage.getItem("BILLID_RECLIST");
idp.event.bind("domReady", function(e, context) {debugger
    idp.event.register("PAYUNITNAME", "selected", function(e, value, name, obj) {
        var unit = value.curData[0].ID;
        let sqlId1 = 'ffed18ac-3a4e-33a8-4b7c-fbe7f62e35b2';
        let filters3 = [];
        filters3.push({
            Left: "",
            Field: 'id',
            Operate: " = ",
            IsExpress: false,
            Value: unit,
            Right: "",
            Logic: "AND"
        });
        let result3 = [];
        getQueryData(sqlId1, filters3, [], {})
            .done(function(data) {
                if (data.Code !== 'ok') {
                    return;
                }
                result3 = data.Data.Rows;
            }).fail(function(data) {});
        if(result3.length > 0){
            let YWDYBH =result3[0].YWDYBH ;
            let YWDYMC =result3[0].YWDYMC ;
            let DWBH =result3[0].CODE ;
            idp.control.get("TXT01").setValue(YWDYMC);//业务单元名称
            idp.control.get("TXT02").setValue(YWDYBH);//业务单元编号
            idp.loaded();
        }
    })
})
idp.event.bind("loadData", function(e, context) {debugger
    var maindata = idp.uiview.modelController.getMainRowObj();
    var djrks = idp.utils.getQuery("djrk");
    if (djrks != 'tgpj' && idp.utils.getQuery("status") == "add" && idy != null && idy != "") {
        idp.service.fetch(`/api/tm/bm/v1.0/bmbillrecite/rpc/getBillReciteReqInfo/${maindata.ID}`, {}, true, "GET").then(function(res) {
            if(res && res.result && res.code == 0) {
                getYSKCek(idy);
                let unit= idp.control.get("PAYUNIT").getValue();
                let sqlId1 = 'ffed18ac-3a4e-33a8-4b7c-fbe7f62e35b2';
                let filters3 = [];
                filters3.push({
                    Left: "",
                    Field: 'id',
                    Operate: " = ",
                    IsExpress: false,
                    Value: unit,
                    Right: "",
                    Logic: "AND"
                });
                let result3 = [];
                getQueryData(sqlId1, filters3, [], {})
                    .done(function(data) {
                        if (data.Code !== 'ok') {
                            return;
                        }
                        result3 = data.Data.Rows;
                    }).fail(function(data) {});
                if(result3.length > 0){
                    let YWDYBH =result3[0].YWDYBH ;
                    let YWDYMC =result3[0].YWDYMC ;
                    let DWBH =result3[0].CODE ;
                    idp.control.get("TXT01").setValue(YWDYMC);//业务单元名称
                    idp.control.get("TXT02").setValue(YWDYBH);//业务单元编号
                    idp.loaded();
                }
            }
        });
    }
})


//取数
function getQueryData(sqlId, filter, orders, bizinfo, callback, pageSize) {
    var params = {
        sqlId: sqlId,
        orders: orders,
        fields: filter,
        page: 0,
        pageSize: pageSize || 0,
        bizId: bizinfo.bizId,
        bizOpId: bizinfo.bizOpId
    };
    if (!callback)
        return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false);
    return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false).done(function(data) {
        callback(data);
    }).fail(function(data) {
    });
}

/**
 * 获取应收库存数据并子表赋值
 * @param ids 票据id，数组
 * @param billOtherMap 票据其他信息，可不传。map，键（票据id）值（obj）
 */
function getYSKCek(ids, billOtherMap) {
    var gridMX = idp.control.get("grid_427369");
    var rowsMX = gridMX.getData();
    if (rowsMX.length > 0) {
        return;
    }
    var idsMX = [];
    for (var rowitem in rowsMX) {
        idsMX.push(rowsMX[rowitem]["BILLID"]);
    }
    var idArr = new Array();
    idArr = ids.split(',');
    var gxdjid = idp.store.get(`ZFSQDID`) || [];
    const params = {
        BILLID: idArr,
        GXDJID: gxdjid
    };
    idp.service.fetch('/api/tm/bm/v1.0/bmbillreceipt/rpc/getyskcbyid/', params, false, "POST")
        .then(function (retObj) {
            console.info(retObj);
            if (retObj.result) {
                var selectRows = retObj.value;
                var unit = '';
                var unitName = '';
                for (var item in selectRows) {
                    if ($.inArray(selectRows[item].id, idsMX) >= 0) {
                        continue;
                    }
                    if (idp.utils.getQuery('djrk') == 'zgpj') {
                        if (selectRows[item].receiverunit && (unit == '' || unit == selectRows[item].receiverunit)) {
                            unit = selectRows[item].receiverunit;
                            unitName = selectRows[item].receiverunitname;
                        } else {
                            //所选票据不是同一持票单位
                            idp.alert(idp.lang.get("PJBSREQMSG_16")); //所选票据不是同一持票单位
                            return false;
                        }
                    }
                }
                if (unit != '') {
                    idp.control.get("PAYUNIT").setValue(unit);
                    // idp.uiview.setMainFieldValueWithTrigger('PAYUNIT', unit, 0);
                    // idp.uiview.setMainFieldValueWithTrigger('PAYUNITNAME', unitName, 0);
                    // idp.control.get("PAYUNITNAME").setText(unitName);
                    // if (idp.utils.getQuery('djrk') == 'zgpj') {
                    //     idp.uiview.setMainFieldValueWithTrigger('USINGUNIT', unit, 0);
                    //     idp.uiview.setMainFieldValueWithTrigger('USINGUNIT_NAME', unitName, 0);
                    // }
                }
            } else {
                idp.warn(retObj.message);
                return false;
            }
        })
        .fail(function (data) {
            return false;
        });
}
