//  /apps/jtgk/jfkj/extend/djsqexents.js
idp.event.bind("loadData", function(e, context) {

    let sqrid=idp.uiview.modelController.getMainRowObj().APPLICANTID;
    let sqdw=idp.uiview.modelController.getMainRowObj().APPUNIT;
    if(sqrid==null||sqrid==''||sqrid==undefined){
        sqrid=  idp.context.get("UserId");
    }
    let sqlId1 = 'b59820f9-554f-2be1-8585-356b14031c5c';
    let filters1 = [];
    filters1.push({
        Left: "",
        Field: 'gspuser.id',
        Operate: " = ",
        IsExpress: false,
        Value: sqrid,
        Right: "",
        Logic: "AND"
    });
    let result1 = [];
    getQueryData(sqlId1, filters1, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result1 = data.Data.Rows;
        }).fail(function(data) {});
    if(result1.length > 0){debugger
        let zrr =result1[0].SJINCHARGE ;
        let fgld =result1[0].INCHARGE ;
        let xzzz =result1[0].XZZZ ;
        let bjzz =result1[0].BJZZ ;
        let sjfzr =result1[0].SJHEAD ;
        idp.control.get("TXT06").setValue(xzzz);
        idp.control.get("TXT08").setValue(zrr);
        idp.control.get("TXT09").setValue(fgld);
        idp.control.get("TXT01").setValue(bjzz);
        idp.control.get("TXT03").setValue(sjfzr);
        idp.loaded();
    }


})

idp.event.bind("viewReady", function(e, context) {//APPLICANTID
    const data = idp.uiview.modelController.getMainRowObj();

    let sqlId1 = 'b59820f9-554f-2be1-8585-356b14031c5c';
    let filters1 = [];
    filters1.push({
        Left: "",
        Field: 'gspuser.id',
        Operate: " = ",
        IsExpress: false,
        Value: idp.context.get("UserId"),
        Right: "",
        Logic: "AND"
    });
    let result1 = [];
    getQueryData(sqlId1, filters1, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result1 = data.Data.Rows;
        }).fail(function(data) {});
    if(result1.length > 0){debugger
        let zrr =result1[0].SJINCHARGE ;
        let fgld =result1[0].INCHARGE ;
        let xzzz =result1[0].XZZZ ;
        let bjzz =result1[0].BJZZ ;
        let sjfzr =result1[0].SJHEAD ;
        idp.control.get("TXT06").setValue(xzzz);
        idp.control.get("TXT08").setValue(zrr);
        idp.control.get("TXT09").setValue(fgld);
        idp.control.get("TXT01").setValue(bjzz);
        idp.control.get("TXT03").setValue(sjfzr);
        idp.loaded();
    }


})

//取数
function getQueryData(sqlId, filter, orders, bizinfo, callback, pageSize) {
    var params = {
        sqlId: sqlId,
        orders: orders,
        fields: filter,
        page: 0,
        pageSize: pageSize || 0,
        bizId: bizinfo.bizId,
        bizOpId: bizinfo.bizOpId
    };
    if (!callback)
        return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false);
    return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false).done(function(data) {
        callback(data);
    }).fail(function(data) {
    });
}
