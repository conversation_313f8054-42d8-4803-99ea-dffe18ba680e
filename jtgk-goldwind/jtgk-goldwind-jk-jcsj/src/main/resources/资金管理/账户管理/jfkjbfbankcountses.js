idp.event.bind("domReady", function(e, context) {
    idp.event.register("grid_BFYHZHZSMX", "overwriteHelpFilter", function (g, p, field, row, rowindex, filters) {
        if (field == 'ZSID_CYRID_NAME$LANGUAGE$') {

            debugger
            var currentData = idp.uiview.modelController.getMainRowObj();
            var treePath = currentData.OPENACCOUNTUNIT_TREEINFO_PATH;
            var treePathList = [];
            var item = "";
            filters=[];
            // if (filters.length > 0) {
            //     filters[filters.length - 1].Logic = "and";
            // }
            for (var i = 0; i < treePath.length / 4 - 1; i++) {
                item += treePath.substring(i * 4, (i + 1) * 4);
                treePathList.push(item);
            }
            filters.push({
                "Left": "",
                "Field": "GSPUSERID",
                "Operate": "notnull",
                "IsExpress": false,
                "Value": "",
                "Right": "",
                "Logic": "and"
            });
            for (let i = 0; i <= treePathList.length; i++) {
                if (i == treePathList.length) {//最后一位
                    filters.push({
                        "Left": i == 0 ? "(" : "",
                        "Field": "TREEINFO_PATH",
                        "Operate": "leftlike",
                        "IsExpress": false,
                        "Value": treePath,
                        "Right": ")",
                        "Logic": ""
                    });
                    continue;
                }
                if(treePathList[i].length==4){
                    filters.push({
                        "Left": i == 0 ? "(" : "",
                        "Field": "TREEINFO_PATH",
                        "Operate": "=",
                        "IsExpress": false,
                        "Value": treePathList[i],
                        "Right": "",
                        "Logic": "or"
                    });
                    continue;
                }

                filters.push({
                    "Left": i == 0 ? "(" : "",
                    "Field": "TREEINFO_PATH",
                    "Operate": "leftlike",
                    "IsExpress": false,
                    "Value": treePathList[i],
                    "Right": "",
                    "Logic": "or"
                });
                filters.push({
                    "Left":  "",
                    "Field": "BFADMINORGANIZATIONCODE",
                    "Operate": "=",
                    "IsExpress": false,
                    "Value": "40007551",//19=0001000100180001000100010001
                    //16=0001001300170001000100050001
                    "Right": "",
                    "Logic": "or"
                });
            }
            return filters;
        }
    });
})