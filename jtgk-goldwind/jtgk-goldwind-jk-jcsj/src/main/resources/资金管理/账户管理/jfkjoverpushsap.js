let jfkjoverpushsap={
    userInfoopenkpushsapover: function () {

        var row = idp.func.getGridSelected("grid_main");
        if ('3' !== row.STATUS){
            idp.warn("非推送失败状态无法推送");
            return false;
        }
        if ('1' !== row.TYPE){
            idp.warn("非开户类型无法推送");
            return false;
        }
        if ('1' === row.ISORVERSEND){
            idp.warn("已重新发送的无法再次发送");
            return false;
        }


        var url = "/api/jtgk/jfkjsapsendcontroller/userInfoopenkpushsapover";

        idp.service.fetch(url,{
            itemid:row.ACCOUNTIMEID,
            changeid:row.CHANGEID,
            guid: row.ID
        }, false, "POST",false).done(function (data) {
            if (data.code == '200') {
                idp.tips("发送成功！");
                idp.uiview.reloadData();
                return true;
            }else{
                err=data.message;
                idp.warn(err);
                return false;
            }
        })
    },


    userInfochangekpushsapover: function () {

        var row = idp.func.getGridSelected("grid_main");
        if ('3' !== row.STATUS){
            idp.warn("非推送失败状态无法推送");
            return false;
        }
        if ('3' !== row.TYPE){
            idp.warn("非变更类型无法推送");
            return false;
        }
        if ('1' === row.ISORVERSEND){
            idp.warn("已重新发送的无法再次发送");
            return false;
        }
        var url = "/api/jtgk/jfkjsapsendcontroller/userInfochangekpushsapover";

        idp.service.fetch(url,{
            itemid:row.ACCOUNTIMEID,
            changeid:row.CHANGEID,
            guid: row.ID
        }, false, "POST",false).done(function (data) {
            if (data.code == '200') {
                idp.tips("发送成功！");
                idp.uiview.reloadData();
                return true;
            }else{
                err=data.message;
                idp.warn(err);
                return false;
            }
        })
    },

    onlinebankopenstatuspushsapover: function () {

        debugger
        var row = idp.func.getGridSelected("grid_main");
        if ('3' !== row.STATUS){
            idp.warn("非推送失败状态无法推送");
            return false;
        }
        if ('4' !== row.TYPE){
            idp.warn("非直联状态变更类型无法推送");
            return false;
        }
        if ('1' === row.ISORVERSEND){
            idp.warn("已重新发送的无法再次发送");
            return false;
        }
        var url = "/api/jtgk/jfkjsapsendcontroller/onlinebankopenstatuspushsapover";

        idp.service.fetch(url,{
            bfbankaccountid:row.ACCOUNTID,
            guid: row.ID
        }, false, "POST",false).done(function (data) {
            if (data.code == '200') {
                idp.tips("发送成功！");
                idp.uiview.reloadData();
                return true;
            }else{
                err=data.message;
                idp.warn(err);
                return false;
            }
        })
    },
    ledgeraccountpushsapover: function () {

        var row = idp.func.getGridSelected("grid_main");
        if ('3' !== row.STATUS){
            idp.warn("非推送失败状态无法推送");
            return false;
        }
        if ('5' !== row.TYPE){
            idp.warn("非会计科目变更类型无法推送");
            return false;
        }
        if ('1' === row.ISORVERSEND){
            idp.warn("已重新发送的无法再次发送");
            return false;
        }
        var url = "/api/jtgk/jfkjsapsendcontroller/ledgeraccountpushsapover";

        idp.service.fetch(url,{
            itemid:row.ACCOUNTIMEID,
            changeid:row.CHANGEID,
            guid: row.ID
        }, false, "POST",false).done(function (data) {
            if (data.code == '200') {
                idp.tips("发送成功！");
                idp.uiview.reloadData();
                return true;
            }else{
                err=data.message;
                idp.warn(err);
                return false;
            }
        })
    }
}