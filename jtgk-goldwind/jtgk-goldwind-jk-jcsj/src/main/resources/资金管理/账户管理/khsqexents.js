//  /apps/jtgk/jfkj/extend/khsqexents.js
//账户开户申请界面，系统自动判断账户类型为基本户时，在开立商业银行账户的同时，系统默认自动勾选“开立财务公司账户FK03”（支持人工去掉勾选）
idp.event.bind("domReady", function(e, context) {
        idp.event.register("BANK", "selected", function (e, value, name, obj) {
            var bank = value.getCurData();
            if(value.curData[0].ID == null||value.curData[0].ID==""||value.curData[0].ID==undefined) {
                idp.control.get("ONLINEBANKOPENSTATUS").setValue(1);
            }else{
                getBankLinkInfos(bank);
            }
            if (idp.control.get("BANK_BANKTYPE_NAME").getValue() !== "财务公司" && idp.control.get("ACCOUNTPROPERTY").getValue() == "基本存款账户") {
                idp.control.get("FK03").setValue(1);
                idp.loaded();
            } else {
                idp.control.get("FK03").setValue(0);
                idp.loaded();
            }
    })
    idp.event.register("ACCOUNTPROPERTY", "selected", function() {
        if (idp.control.get("BANK_BANKTYPE_NAME").getValue() !== "财务公司" && idp.control.get("ACCOUNTPROPERTY").getValue() == "基本存款账户") {
            idp.control.get("FK03").setValue(1);
            idp.loaded();
        } else {
            idp.control.get("FK03").setValue(0);
            idp.loaded();
        }
    })
    //离岸账户	开户地国家只能为中国
    idp.event.register("COUNTRY_NAME", "beforeHelpFilter", function(e, ctrl, col, row){debugger
        var filters = [];
        if( idp.control.get("BOUNDARYTYPE").getValue()=="1" ) {
            filters.push({
                "Left": "",
                "Field": "twocharcode",
                "Operate": " =",
                "IsExpress": false,
                "Value": "CN",
                "Right": "",
                "Logic": " and "
            });

        }else{
            filters.push({
                "Left": "",
                "Field": "twocharcode",
                "Operate": " !=",
                "IsExpress": false,
                "Value": "CN",
                "Right": "",
                "Logic": " and "
            });

        }
        if(idp.control.get("ACCOUNTPROPERTY").getValue() == "境内离岸账户"  ) {
            filters.push({
                "Left": "",
                "Field": "twocharcode",
                "Operate": " =",
                "IsExpress": false,
                "Value": "CN",
                "Right": "",
                "Logic": " and "
            });

        }
        if (e.result == undefined || e.result == "" || e.result == null) {
            return filters;
        }
        if (e.result.length > 0) {
            for (i = 0; i < e.result.length; i++) {
                filters.push(e.result[i]);
            }
        }
        return filters;

    })
    // 01	基本存款账户	币种只能为CNY
    // 02	一般存款账户	币种只能为CNY
    // 03	专用存款账户	币种只能为CNY
    // 04	临时存款账户	币种只能为CNY
    // 05	人民币非结算账户	币种只能为CNY
    idp.event.register("DEFAULTCURRENCY", "beforeHelpFilter", function(e, ctrl, col, row){debugger
        if(idp.control.get("ACCOUNTPROPERTY").getValue() == "基本存款账户"||idp.control.get("ACCOUNTPROPERTY").getValue() == "一般存款账户"
            ||idp.control.get("ACCOUNTPROPERTY").getValue() == "专用存款账户"||idp.control.get("ACCOUNTPROPERTY").getValue() == "临时存款账户"
            ||idp.control.get("ACCOUNTPROPERTY").getValue() == "人民币非结算账户") {
            var filters = [];
            filters.push({
                "Left": "",
                "Field": "CODE",
                "Operate": " =",
                "IsExpress": false,
                "Value": "CNY",
                "Right": "",
                "Logic": " and "
            });
            if (e.result == undefined || e.result == "" || e.result == null) {
                return filters;
            }
            if (e.result.length > 0) {
                for (i = 0; i < e.result.length; i++) {
                    filters.push(e.result[i]);
                }
            }
            return filters;
        }
    })
    idp.event.register("grid_TMBANKACCOUNTOPENITEMS", "beforeHelpFilter", function(e, ctrl, col, row) {debugger
        if (col == "CURRENCY_NAME$LANGUAGE$") {
            if(idp.control.get("ACCOUNTPROPERTY").getValue() == "基本存款账户"||idp.control.get("ACCOUNTPROPERTY").getValue() == "一般存款账户"
            ||idp.control.get("ACCOUNTPROPERTY").getValue() == "专用存款账户"||idp.control.get("ACCOUNTPROPERTY").getValue() == "临时存款账户"
            ||idp.control.get("ACCOUNTPROPERTY").getValue() == "人民币非结算账户") {
                var filters = [];
                filters.push({
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "CNY",
                    "Right": "",
                    "Logic": " and "
                });
                if (e.result == undefined || e.result == "" || e.result == null) {
                    return filters;
                }
                if (e.result.length > 0) {
                    for (i = 0; i < e.result.length; i++) {
                        filters.push(e.result[i]);
                    }
                }
                return filters;
            }
        }
        else if(col == "ACCOUNTTYPE_NAME$LANGUAGE$"){
            // 01	日常结算账户	账户类型=1 活期
            //基本存款账户、一般存款账户、专用存款账户、外汇账户 账户类型为活期
            if(idp.control.get("ACCOUNTUSE").getValue() == "日常结算账户"||idp.control.get("ACCOUNTPROPERTY").getValue() == "基本存款账户"
                ||idp.control.get("ACCOUNTPROPERTY").getValue() == "一般存款账户"
                ||idp.control.get("ACCOUNTPROPERTY").getValue() == "专用存款账户"
                ||idp.control.get("ACCOUNTPROPERTY").getValue() == "外汇账户") {
                var filters = [];
                filters.push({
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "1",
                    "Right": "",
                    "Logic": " and "
                });
                if (e.result == undefined || e.result == "" || e.result == null) {
                    return filters;
                }
                if (e.result.length > 0) {
                    for (i = 0; i < e.result.length; i++) {
                        filters.push(e.result[i]);
                    }
                }
                return filters;
            }
            //人民币非结算账户 账户类型为非活期
            if(idp.control.get("ACCOUNTPROPERTY").getValue() == "人民币非结算账户")
            {
                var filters = [];
                filters.push({
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " <> ",
                    "IsExpress": false,
                    "Value": "1",
                    "Right": "",
                    "Logic": " and "
                });
                if (e.result == undefined || e.result == "" || e.result == null) {
                    return filters;
                }
                if (e.result.length > 0) {
                    for (i = 0; i < e.result.length; i++) {
                        filters.push(e.result[i]);
                    }
                }
                return filters;
            }
        }
    })


    // 05	基本建设资金专用账户	账户性质=专用存款账户
    // 06	更新改造资金专用账户	账户性质=专用存款账户
    // 07	复垦押金专用账户	账户性质=专用存款账户
    // 08	农民工工资专用账户	账户性质=专用存款账户
    // 09	存放同业专用账户	账户性质=专用存款账户
    // 10	其它专用账户	账户性质=专用存款账户
    // 保证金	账户性质=专用存款账户
    idp.event.register("ACCOUNTPROPERTY", "beforeHelpFilter", function(e, ctrl, col, row){debugger
        if(idp.control.get("ACCOUNTUSE").getValue() == "基本建设资金专用账户"||idp.control.get("ACCOUNTUSE").getValue() == "更新改造资金专用账户"
            ||idp.control.get("ACCOUNTUSE").getValue() == "复垦押金专用账户"||idp.control.get("ACCOUNTUSE").getValue() == "农民工工资专用账户"
            ||idp.control.get("ACCOUNTUSE").getValue() == "存放同业专用账户"||idp.control.get("ACCOUNTUSE").getValue() == "其它专用账户"
            ||idp.control.get("ACCOUNTUSE").getValue() == "保证金") {
            var filters = [];
            filters.push({
                "Left": "",
                "Field": "CODE",
                "Operate": " =",
                "IsExpress": false,
                "Value": "03",
                "Right": "",
                "Logic": " and "
            });
            if (e.result == undefined || e.result == "" || e.result == null) {
                return filters;
            }
            if (e.result.length > 0) {
                for (i = 0; i < e.result.length; i++) {
                    filters.push(e.result[i]);
                }
            }
            return filters;
        }//// 03	贷款账户	账户性质=01基本存款账户 or 04 一般存款账户
        else if(idp.control.get("ACCOUNTUSE").getValue() == "贷款账户") {
            var filters = [];
            filters.push({
                "Left": "(",
                "Field": "CODE",
                "Operate": " =",
                "IsExpress": false,
                "Value": "01",
                "Right": "",
                "Logic": " or "
            },{
                "Left": "",
                "Field": "CODE",
                "Operate": " =",
                "IsExpress": false,
                "Value": "04",
                "Right": ")",
                "Logic": " and "
            });
            if (e.result == undefined || e.result == "" || e.result == null) {
                return filters;
            }
            if (e.result.length > 0) {
                for (i = 0; i < e.result.length; i++) {
                    filters.push(e.result[i]);
                }
            }
            return filters;
        }
        else if(idp.control.get("ACCOUNTUSE").getValue() == "日常结算账户") {
            var filters = [];
            filters.push({
                "Left": "(",
                "Field": "CODE",
                "Operate": " =",
                "IsExpress": false,
                "Value": "01",
                "Right": "",
                "Logic": " or "
            },{
                "Left": "",
                "Field": "CODE",
                "Operate": " =",
                "IsExpress": false,
                "Value": "04",
                "Right": "",
                "Logic": " and "
            },{
                "Left": "",
                "Field": "CODE",
                "Operate": " =",
                "IsExpress": false,
                "Value": "07",
                "Right": "",
                "Logic": " and "
            },{
                "Left": "",
                "Field": "CODE",
                "Operate": " =",
                "IsExpress": false,
                "Value": "08",
                "Right": ")",
                "Logic": " and "
            });
            if (e.result == undefined || e.result == "" || e.result == null) {
                return filters;
            }
            if (e.result.length > 0) {
                for (i = 0; i < e.result.length; i++) {
                    filters.push(e.result[i]);
                }
            }
            return filters;
        }
        //账户性质=01基本存款账户 or 04 一般存款账户
        else if(idp.control.get("ACCOUNTUSE").getValue() == "电费收入质押账户"||idp.control.get("ACCOUNTUSE").getValue() == "共管账户（含监管户）") {
            var filters = [];
            filters.push({
                "Left": "",
                "Field": "CODE",
                "Operate": " =",
                "IsExpress": false,
                "Value": "04",
                "Right": "",
                "Logic": " and "
            });
            if (e.result == undefined || e.result == "" || e.result == null) {
                return filters;
            }
            if (e.result.length > 0) {
                for (i = 0; i < e.result.length; i++) {
                    filters.push(e.result[i]);
                }
            }
            return filters;
        }
        //账户用途为其他，账户性质为人民币非结算账户
        else if(idp.control.get("ACCOUNTUSE").getValue() == "其他") {
            var filters = [];
            filters.push({
                "Left": "(",
                "Field": "CODE",
                "Operate": " =",
                "IsExpress": false,
                "Value": "05",
                "Right": "",
                "Logic": " or "
            },{
                "Left": "",
                "Field": "CODE",
                "Operate": " =",
                "IsExpress": false,
                "Value": "07",
                "Right": "",
                "Logic": " or "
            },{
                "Left": "",
                "Field": "CODE",
                "Operate": " =",
                "IsExpress": false,
                "Value": "08",
                "Right": ")",
                "Logic": " and "
            });
            if (e.result == undefined || e.result == "" || e.result == null) {
                return filters;
            }
            if (e.result.length > 0) {
                for (i = 0; i < e.result.length; i++) {
                    filters.push(e.result[i]);
                }
            }
            return filters;
        }//账户用途为其他，账户性质为人民币非结算账户
        else if(idp.control.get("ACCOUNTUSE").getValue() == "注资账户") {
            var filters = [];
            filters.push({
                "Left": "",
                "Field": "CODE",
                "Operate": " =",
                "IsExpress": false,
                "Value": "02",
                "Right": "",
                "Logic": " and  "
            });
            if (e.result == undefined || e.result == "" || e.result == null) {
                return filters;
            }
            if (e.result.length > 0) {
                for (i = 0; i < e.result.length; i++) {
                    filters.push(e.result[i]);
                }
            }
            return filters;
        }
        //账户用途为 ，账户性质为外汇账户
        else if(idp.control.get("ACCOUNTUSE").getValue() == "经常项目账户"||idp.control.get("ACCOUNTUSE").getValue() == "资本项目账户"
        ||idp.control.get("ACCOUNTUSE").getValue() == "其他外汇账户") {
            var filters = [];
            filters.push({
                "Left": "",
                "Field": "CODE",
                "Operate": " =",
                "IsExpress": false,
                "Value": "06",
                "Right": "",
                "Logic": " and "
            });
            if (e.result == undefined || e.result == "" || e.result == null) {
                return filters;
            }
            if (e.result.length > 0) {
                for (i = 0; i < e.result.length; i++) {
                    filters.push(e.result[i]);
                }
            }
            return filters;
        }
    })

    idp.event.register("ACCOUNTUSE", "beforeHelpFilter", function(e, ctrl, col, row){
        if(idp.control.get("ACCOUNTPROPERTY").getValue() == "基本存款账户") {
            var filters = [];
            filters.push({
                "Left": "(",
                "Field": "CODE",
                "Operate": " =",
                "IsExpress": false,
                "Value": "001",
                "Right": "",
                "Logic": " or "
            },{
                "Left": "",
                "Field": "CODE",
                "Operate": " =",
                "IsExpress": false,
                "Value": "003",
                "Right": ")",
               "Logic": " and "
            }
            )
            if (e.result == undefined || e.result == "" || e.result == null) {
                return filters;
            }
            if (e.result.length > 0) {
                for (i = 0; i < e.result.length; i++) {
                    filters.push(e.result[i]);
                }
            }
            return filters;
        }
        if(idp.control.get("ACCOUNTPROPERTY").getValue() == "一般存款账户") {
            var filters = [];
            filters.push({
                    "Left": "(",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "001",
                    "Right": "",
                    "Logic": " or "
                },{
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "002",
                    "Right": "",
                    "Logic": " or "
                },{
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "003",
                    "Right": "",
                    "Logic": " or "
                },{
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "004",
                    "Right": ")",
                    "Logic": " and "
                }
            )
            if (e.result == undefined || e.result == "" || e.result == null) {
                return filters;
            }
            if (e.result.length > 0) {
                for (i = 0; i < e.result.length; i++) {
                    filters.push(e.result[i]);
                }
            }
            return filters;
        }
        if(idp.control.get("ACCOUNTPROPERTY").getValue() == "专用存款账户") {
            var filters = [];
            filters.push({
                    "Left": "(",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "005",
                    "Right": "",
                    "Logic": " or "
                },{
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "006",
                    "Right": "",
                    "Logic": " or "
                },{
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "007",
                    "Right": "",
                    "Logic": " or "
                },{
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "008",
                    "Right": "",
                    "Logic": " or "
                },{
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "009",
                    "Right": "",
                    "Logic": " or "
                },{
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "010",
                    "Right": "",
                    "Logic": " or "
                },{
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "012",
                    "Right": ")",
                    "Logic": " and "
                }
            )
            if (e.result == undefined || e.result == "" || e.result == null) {
                return filters;
            }
            if (e.result.length > 0) {
                for (i = 0; i < e.result.length; i++) {
                    filters.push(e.result[i]);
                }
            }
            return filters;
        }
        if(idp.control.get("ACCOUNTPROPERTY").getValue() == "人民币非结算账户") {
            var filters = [];
            filters.push({
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "016",
                    "Right": "",
                    "Logic": " and "
                }
            )
            if (e.result == undefined || e.result == "" || e.result == null) {
                return filters;
            }
            if (e.result.length > 0) {
                for (i = 0; i < e.result.length; i++) {
                    filters.push(e.result[i]);
                }
            }
            return filters;
        }
        if(idp.control.get("ACCOUNTPROPERTY").getValue() == "外汇账户") {
            var filters = [];
            filters.push({
                    "Left": "(",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "013",
                    "Right": "",
                    "Logic": " or "
                },{
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "014",
                    "Right": "",
                    "Logic": " or "
                },{
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "015",
                    "Right": ")",
                    "Logic": " and "
                }
            )
            if (e.result == undefined || e.result == "" || e.result == null) {
                return filters;
            }
            if (e.result.length > 0) {
                for (i = 0; i < e.result.length; i++) {
                    filters.push(e.result[i]);
                }
            }
            return filters;
        }
        if(idp.control.get("ACCOUNTPROPERTY").getValue() == "临时存款账户") {
            var filters = [];
            filters.push({
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "011",
                    "Right": "",
                    "Logic": " and "
                }
            )
            if (e.result == undefined || e.result == "" || e.result == null) {
                return filters;
            }
            if (e.result.length > 0) {
                for (i = 0; i < e.result.length; i++) {
                    filters.push(e.result[i]);
                }
            }
            return filters;
        }
        if(idp.control.get("ACCOUNTPROPERTY").getValue() == "境内离岸账户"||idp.control.get("ACCOUNTPROPERTY").getValue() == "境外账户") {
            var filters = [];
            filters.push({
                    "Left": "(",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "001",
                    "Right": "",
                    "Logic": " or "
                },{
                    "Left": "",
                    "Field": "CODE",
                    "Operate": " =",
                    "IsExpress": false,
                    "Value": "016",
                    "Right": ")",
                    "Logic": " and "
                }
            )
            if (e.result == undefined || e.result == "" || e.result == null) {
                return filters;
            }
            if (e.result.length > 0) {
                for (i = 0; i < e.result.length; i++) {
                    filters.push(e.result[i]);
                }
            }
            return filters;
        }


    })


    idp.event.register("FK11", "beforeHelpFilter", function(e, ctrl, col, row){

        debugger
        var currentData = idp.uiview.modelController.getMainRowObj();
        var treePath = currentData.APPUNIT_TREEINFO_PATH;
        if(treePath!=null) {
            if (treePath.length > 9) {
                treePath = treePath.substring(0, 9);
            }
            var treePathList = [];
            var item = "";
            filters = [];
            for (var i = 0; i < treePath.length / 4 - 1; i++) {
                item += treePath.substring(i * 4, (i + 1) * 4);
                treePathList.push(item);
            }

            if (treePath == '0001') {

            } else {

                for (let i = 0; i <= treePathList.length; i++) {
                    if (i == treePathList.length) {
                        filters.push({
                            "Left": "",
                            "Field": "TREEINFO_PATH",
                            "Operate": "leftlike",
                            "IsExpress": false,
                            "Value": treePath,
                            "Right": "",
                            "Logic": ""
                        });
                        continue;
                    }

                }
            }
            return filters;
        }

    });

    idp.event.register("APPUNIT", "selected", function(e, value, name, obj) {
        var unit = value.curData[0].ID;
        let sqlId1 = 'ffed18ac-3a4e-33a8-4b7c-fbe7f62e35b2';
        let filters3 = [];
        filters3.push({
            Left: "",
            Field: 'id',
            Operate: " = ",
            IsExpress: false,
            Value: unit,
            Right: "",
            Logic: "AND"
        });
        let result3 = [];
        getQueryData(sqlId1, filters3, [], {})
            .done(function(data) {
                if (data.Code !== 'ok') {
                    return;
                }
                result3 = data.Data.Rows;
            }).fail(function(data) {});
        if(result3.length > 0){debugger
            let YWDYBH =result3[0].YWDYBH ;
            let YWDYMC =result3[0].YWDYMC ;
            idp.control.get("TXT10").setValue(YWDYBH);//业务单元编号
            idp.control.get("TXT05").setValue(YWDYMC);//业务单元名称
            idp.loaded();
        }
    })



})
idp.event.bind("loadData", function(e, context) {
    const data=idp.uiview.modelController.getMainRowObj();
    if(data.DOCSTATUS!==-2&&data.DOCSTATUS!==-3){
        idp.control.get("RETURNREASON").setValue("");
        idp.loaded();
    }else{
        idp.control.get("RETURNREASON").setValue(data.RETURNREASON);
        idp.loaded();
    }
    if(idp.control.get("ONLINEBANKOPENSTATUS").getValue()==""||idp.control.get("ONLINEBANKOPENSTATUS").getValue()==null||idp.control.get("ONLINEBANKOPENSTATUS").getValue()==undefined){
        idp.control.get("ONLINEBANKOPENSTATUS").setValue(1);
    }
    let sqrid=idp.uiview.modelController.getMainRowObj().APPLICANTID;
    if(sqrid==null||sqrid==''||sqrid==undefined){
        sqrid=  idp.context.get("UserId");
    }
    let sqlId1 = 'b59820f9-554f-2be1-8585-356b14031c5c';
    let filters1 = [];
    filters1.push({
        Left: "",
        Field: 'gspuser.id',
        Operate: " = ",
        IsExpress: false,
        Value: sqrid,
        Right: "",
        Logic: "AND"
    });
    let result1 = [];
    getQueryData(sqlId1, filters1, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result1 = data.Data.Rows;
        }).fail(function(data) {});
    if(result1.length > 0){debugger
        let zrr =result1[0].SJINCHARGE ;
        let fgld =result1[0].INCHARGE ;
        let xzzz =result1[0].XZZZ ;
        let bjzz =result1[0].BJZZ ;
        let sjfzr =result1[0].SJHEAD ;
        idp.control.get("TXT06").setValue(xzzz);//发起人的行政单位
        idp.control.get("TXT08").setValue(zrr);//上级分管领导
        idp.control.get("TXT09").setValue(fgld);//本级负责人
        idp.control.get("TXT01").setValue(bjzz);//本级单位分管领导
        idp.control.get("TXT03").setValue(sjfzr);//上级负责人
        idp.loaded();
    }
    let sqlId = '34777655-e68e-4647-5aa2-1a9988f4eb37';
    let filters = [];
    filters.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "10082361",
        Right: "",
        Logic: "AND"
    });
    let result = [];
    getQueryData(sqlId, filters, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT11").setValue(id);//发起人的行政单位
        idp.loaded();
    }
    let filters2 = [];
    filters2.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "10082362",
        Right: "",
        Logic: "AND"
    });
    getQueryData(sqlId, filters2, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT12").setValue(id);//发起人的行政单位
        idp.loaded();
    }
    let filters3 = [];
    filters3.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "40003643",
        Right: "",
        Logic: "AND"
    });
    getQueryData(sqlId, filters3, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT13").setValue(id);
        idp.loaded();
    }
    let filter4 = [];
    filter4.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "01007021",
        Right: "",
        Logic: "AND"
    });
    getQueryData(sqlId, filter4, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("FK14").setValue(id);
        idp.loaded();
    }
    let filter5 = [];
    filter5.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "01001046",
        Right: "",
        Logic: "AND"
    });
    getQueryData(sqlId, filter5, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("FK16").setValue(id);
        idp.loaded();
    }


})

idp.event.bind("viewReady", function(e, context) {//APPLICANTID
    const data = idp.uiview.modelController.getMainRowObj();
    if(data.DOCSTATUS!=="-2"&&data.DOCSTATUS!=="-3"){
        idp.control.get("RETURNREASON").setValue("");
        idp.loaded();
    }else{
        idp.control.get("RETURNREASON").setValue(data.RETURNREASON);
        idp.loaded();
    }
    if(idp.control.get("ONLINEBANKOPENSTATUS").getValue()==""||idp.control.get("ONLINEBANKOPENSTATUS").getValue()==null||idp.control.get("ONLINEBANKOPENSTATUS").getValue()==undefined){
        idp.control.get("ONLINEBANKOPENSTATUS").setValue(1);
    }
    let sqlId1 = 'b59820f9-554f-2be1-8585-356b14031c5c';
    let filters1 = [];
    filters1.push({
        Left: "",
        Field: 'gspuser.id',
        Operate: " = ",
        IsExpress: false,
        Value: idp.context.get("UserId"),
        Right: "",
        Logic: "AND"
    });
    let result1 = [];
    getQueryData(sqlId1, filters1, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result1 = data.Data.Rows;
        }).fail(function(data) {});
    if(result1.length > 0){debugger
        let zrr =result1[0].SJINCHARGE ;
        let fgld =result1[0].INCHARGE ;
        let xzzz =result1[0].XZZZ ;
        let bjzz =result1[0].BJZZ ;
        let sjfzr =result1[0].SJHEAD ;
        idp.control.get("TXT06").setValue(xzzz);
        idp.control.get("TXT08").setValue(zrr);
        idp.control.get("TXT09").setValue(fgld);
        idp.control.get("TXT01").setValue(bjzz);
        idp.control.get("TXT03").setValue(sjfzr);
        idp.loaded();
    }
    let sqlId = '34777655-e68e-4647-5aa2-1a9988f4eb37';
    let filters = [];
    filters.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "10082361",
        Right: "",
        Logic: "AND"
    });
    let result = [];
    getQueryData(sqlId, filters, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT11").setValue(id);//发起人的行政单位
        idp.loaded();
    }
    let filters2 = [];
    filters2.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "10082362",
        Right: "",
        Logic: "AND"
    });
    getQueryData(sqlId, filters2, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT12").setValue(id);//发起人的行政单位
        idp.loaded();
    }
    let filters3 = [];
    filters3.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "40003643",
        Right: "",
        Logic: "AND"
    });
    getQueryData(sqlId, filters3, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("TXT13").setValue(id);
        idp.loaded();
    }

    let filter4 = [];
    filter4.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "01007021",
        Right: "",
        Logic: "AND"
    });
    getQueryData(sqlId, filter4, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("FK14").setValue(id);
        idp.loaded();
    }
    let filter5 = [];
    filter5.push({
        Left: "",
        Field: 'code',
        Operate: " = ",
        IsExpress: false,
        Value: "01001046",
        Right: "",
        Logic: "AND"
    });
    getQueryData(sqlId, filter5, [], {})
        .done(function(data) {
            if (data.Code !== 'ok') {
                return;
            }
            result = data.Data.Rows;
        }).fail(function(data) {});
    if(result.length > 0){debugger
        let id =result[0].ID ;
        idp.control.get("FK16").setValue(id);
        idp.loaded();
    }


})

//取数
function getQueryData(sqlId, filter, orders, bizinfo, callback, pageSize) {
    var params = {
        sqlId: sqlId,
        orders: orders,
        fields: filter,
        page: 0,
        pageSize: pageSize || 0,
        bizId: bizinfo.bizId,
        bizOpId: bizinfo.bizOpId
    };
    if (!callback)
        return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false);
    return idp.service.requestApi("/MgrList/getListPage", JSON.stringify(params), false).done(function(data) {
        callback(data);
    }).fail(function(data) {
    });
}


//二开代理
idp.custom.delegate({
    "controls":{
        // 工具栏Id
        "toolbar1":{
            //工具栏点击事件
            "toolbarClick":{
                "before":function(e,btn,toolbar){
                    if(e.id == "baritem_save"){debugger;
                        //var data=idp.uiview.gridController.mainGrid.data.Rows;
                        //var data=idp.uiview.modelController.deafaultData[1].data;//
                        var data=idp.control.get("grid_TMBANKACCOUNTOPENITEMS").rows;
                        var msg="";
                        if(idp.control.get("ACCOUNTPROPERTY").getValue() == "境内离岸账户"&&idp.control.get("COUNTRY_NAME").getText()!="中国") {
                            msg+= "境内离岸账户开户地国家需为中国！<br>";
                        }
                        if(idp.control.get("BOUNDARYTYPE").getValue() == "1"&&idp.control.get("COUNTRY_NAME").getText()!="中国") {
                            msg+= "境内开户地国家需为中国！<br>";
                        }
                        if(idp.control.get("BOUNDARYTYPE").getValue() == "2"&&idp.control.get("COUNTRY_NAME").getText()=="中国") {
                            msg+= "境外开户地国家不能为中国！<br>";
                        }
                        if((idp.control.get("ACCOUNTPROPERTY").getValue() == "基本存款账户"||idp.control.get("ACCOUNTPROPERTY").getValue() == "一般存款账户"
                            ||idp.control.get("ACCOUNTPROPERTY").getValue() == "专用存款账户"||idp.control.get("ACCOUNTPROPERTY").getValue() == "临时存款账户"
                            ||idp.control.get("ACCOUNTPROPERTY").getValue() == "人民币非结算账户")&&idp.control.get("DEFAULTCURRENCY").getValue()!="人民币") {
                            msg+= idp.control.get("ACCOUNTPROPERTY").getValue()+"币种需为人民币！<br>";
                        }
                        if((idp.control.get("ACCOUNTUSE").getValue() == "基本建设资金专用账户"||idp.control.get("ACCOUNTUSE").getValue() == "更新改造资金专用账户"
                            ||idp.control.get("ACCOUNTUSE").getValue() == "复垦押金专用账户"||idp.control.get("ACCOUNTUSE").getValue() == "农民工工资专用账户"
                            ||idp.control.get("ACCOUNTUSE").getValue() == "存放同业专用账户"||idp.control.get("ACCOUNTUSE").getValue() == "其它专用账户")&&idp.control.get("ACCOUNTPROPERTY").getValue()!="专用存款账户"){
                            msg+= idp.control.get("ACCOUNTUSE").getValue()+"账户性质需为专用存款账户！<br>";
                        }
                        if(idp.control.get("ACCOUNTUSE").getValue() == "贷款账户"){
                                if(idp.control.get("ACCOUNTPROPERTY").getValue()=="基本存款账户"||idp.control.get("ACCOUNTPROPERTY").getValue()=="一般存款账户"){
                                }else{
                                    msg+= idp.control.get("ACCOUNTUSE").getValue()+"账户性质需为基本存款账户或一般存款账户！<br>";
                                }
                        }
                        if(idp.control.get("ACCOUNTUSE").getValue() == "保证金"){
                            if(idp.control.get("ACCOUNTPROPERTY").getValue()=="境外账户"||idp.control.get("ACCOUNTPROPERTY").getValue()=="境内离岸账户"
                                ||idp.control.get("ACCOUNTPROPERTY").getValue()=="人民币非结算账户"){
                            }else{
                                msg+= idp.control.get("ACCOUNTUSE").getValue()+"账户性质需为境外账户或境内离岸账户或人民币非结算账户！<br>";
                            }
                        }
                        for (var i = 0; i < data.length; i++){
                            if((idp.control.get("ACCOUNTPROPERTY").getValue() == "基本存款账户"||idp.control.get("ACCOUNTPROPERTY").getValue() == "一般存款账户"
                                ||idp.control.get("ACCOUNTPROPERTY").getValue() == "专用存款账户"||idp.control.get("ACCOUNTPROPERTY").getValue() == "临时存款账户"
                                ||idp.control.get("ACCOUNTPROPERTY").getValue() == "人民币非结算账户")&&data[i].CURRENCY_NAME$LANGUAGE$!="人民币" ) {
                                msg+= idp.control.get("ACCOUNTPROPERTY").getValue()+"币种需为人民币！<br>";
                            }
                            if(idp.control.get("ACCOUNTUSE").getValue() == "日常结算账户" && data[i].ACCOUNTTYPE_NAME$LANGUAGE$!="活期")
                            {
                                msg+= idp.control.get("ACCOUNTUSE").getValue()+"账户类型需为活期！<br>";
                            }
                        }
                        if(msg!=""){
                            idp.warn(msg);
                            throw(msg);
                        }
                    }
                    else if(e.id == "baritem_submitparent"){
                        var err="";
                        var data1=idp.uiview.modelController.deafaultData[0].data;//主表
                        if(data1[0].DOCSTATUS!='-3') {
                            idp.service.fetch("/api/jtgk/jfkjsapgetgscloud/geteisnum", {

                                billid: data1[0].ID
                            }, false, "POST", false).done(function (data) {
                                if (data.code == '200') {
                                } else {
                                    err = data.message;
                                }
                            })
                            if (err != "") {
                                idp.warn(err);
                                throw (err);
                            }
                        }
                    }else if (e.id == "baritem_genaccount"){
                        var err="";
                        var data1=idp.uiview.modelController.deafaultData[0].data;//主表
                        idp.service.fetch("/api/jtgk/jfkjsapgetgscloud/getkhbleisnum",{

                            billid: data1[0].ID
                        }, false, "POST",false).done(function (data) {
                            if (data.code == '200') {
                            }else{
                                err=data.message;
                            }
                        })
                        if(err!=""){
                            idp.warn(err);
                            throw(err);
                        }
                    }

                    if (e.id == "baritem_genaccount"){
                        var err="";
                        var data1=idp.uiview.modelController.deafaultData[0].data;//主表
                        idp.service.fetch("/api/jtgk/jfkjsapgetgscloud/getocrflag",{

                            billid: data1[0].ID
                        }, false, "POST",false).done(function (data) {
                            if (data.code == '200') {
                            }else{
                                err=data.message;
                            }
                        })
                        if(err!=""){
                            idp.warn(err);
                            throw(err);
                        }}

                }
            },
        },
    }
})

//获取账户直联信息
function getBankLinkInfos(bank) {
    //#region 带出直联银行，联行信息等
    var paramHandle = 'bankid=' + bank[0].ID;
    var urlHandle = '/api/bf/df/bankaccounts/v1.0/bankaccounts/afterBank?' + paramHandle;
    $.ajax({
        type: "GET",
        dataType: "json",
        url: urlHandle,
        success: function (result) {
            //联行号
            if(result["bankIdentityfier"]=="CWGS_GFS_001"){
                idp.control.get("ONLINEBANKOPENSTATUS").setValue(3);
            }else{
                idp.control.get("ONLINEBANKOPENSTATUS").setValue(1);
            }
        },
        error: function (e) {
        }
    });
    //#endregion
}


