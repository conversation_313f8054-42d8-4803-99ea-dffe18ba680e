//  /apps/jtgk/jfkj/extend/khsqlistexents.js
//账户开户申请界面，系统自动判断账户类型为基本户时，在开立商业银行账户的同时，系统默认自动勾选“开立财务公司账户FK03”（支持人工去掉勾选）
idp.custom.delegate({
    "controls":{
        // 工具栏Id
        "toolbar1":{
            //工具栏点击事件
            "toolbarClick":{
                "before":function(e,btn,toolbar){

                    if(e.id == "baritem_submitparent"){
                        var err="";
                        var row = idp.func.getGridSelected("grid_main");
                        if(row.DOCSTATUS!='-3') {
                            idp.service.fetch("/api/jtgk/jfkjsapgetgscloud/geteisnum", {

                                billid: row.ID
                            }, false, "POST", false).done(function (data) {
                                if (data.code == '200') {
                                } else {
                                    err = data.message;
                                }
                            })
                            if (err != "") {
                                idp.warn(err);
                                throw (err);
                            }
                        }
                    }
                    else if (e.id == "baritem_genaccount"){
                        var err="";
                        var row = idp.func.getGridSelected("grid_main");
                        idp.service.fetch("/api/jtgk/jfkjsapgetgscloud/getkhbleisnum",{

                            billid: row.ID
                        }, false, "POST",false).done(function (data) {
                            if (data.code == '200') {
                            }else{
                                err='请上传开户办理环节所需的附件材料';
                            }
                        })
                        if(err!=""){
                            idp.warn(err);
                            throw(err);
                        }
                    }
                     if (e.id == "baritem_genaccount"){
                        var err="";
                        var row = idp.func.getGridSelected("grid_main");
                        idp.service.fetch("/api/jtgk/jfkjsapgetgscloud/getocrflag",{

                            billid: row.ID
                        }, false, "POST",false).done(function (data) {
                            if (data.code == '200') {
                            }else{
                                err='请识别电子附件信息';
                            }
                        })
                        if(err!=""){
                            idp.warn(err);
                            throw(err);
                        }
                    }
                }
            },
        },
    }
})

//开户办理列表增加当前审批人
