//账户卡片 配置 账户科目维护按钮隐藏
//  /apps/jtgk/jfkj/extend/zhglexents.js
idp.event.bind("viewReady", function(e, context) {
    //审批中的状态，提交按钮置灰，仅退回或者制单状态允许点击提交按钮
    const current = idp.uiview.modelController.getMainRowObj();
    var opentype = idp.utils.getQuery("opentype");//opentype=query
    var fk01=$("#FK01").val();
    // if(fk01 != "1"||opentype!="query" ){
    //     // idp.control.toolbar.setDisabled("toolbar1", ['baritem_zhkmwh']);
    //     idp.control.toolbar.toggleBtns("toolbar1", ["baritem_zhkmwh"], false);
    // }else{
    //     idp.control.toolbar.toggleBtns("toolbar1", ["baritem_zhkmwh"], true);
    // }
    if(fk01 == "1"&&opentype=="query" ){
        // idp.control.toolbar.setDisabled("toolbar1", ['baritem_zhkmwh']);
        idp.control.toolbar.toggleBtns("toolbar1", ["baritem_zhkmwh"],true );
    }else{
        idp.control.toolbar.toggleBtns("toolbar1", ["baritem_zhkmwh"], false);
    }
});
idp.event.bind("loadData", function(e, context) {
    //审批中的状态，提交按钮置灰，仅退回或者制单状态允许点击提交按钮
    const data = idp.uiview.modelController.getMainRowObj();
    var opentype = idp.utils.getQuery("opentype");//opentype=query
    var fk01=data.FK01;
    // if(fk01 != "1"||opentype!="query" ){
    //     // idp.control.toolbar.setDisabled("toolbar1", ['baritem_zhkmwh']);
    //     idp.control.toolbar.toggleBtns("toolbar1", ["baritem_zhkmwh"], false);
    // }else{
    //     idp.control.toolbar.toggleBtns("toolbar1", ["baritem_zhkmwh"], true);
    // }
    if(fk01 == "1"&&opentype=="query" ){
        // idp.control.toolbar.setDisabled("toolbar1", ['baritem_zhkmwh']);
        idp.control.toolbar.toggleBtns("toolbar1", ["baritem_zhkmwh"],true );
    }else{
        idp.control.toolbar.toggleBtns("toolbar1", ["baritem_zhkmwh"], false);
    }
});



idp.event.bind("domReady", function () {
    idp.event.register("FK11", "beforeHelpFilter", function(e, ctrl, col, row){

        debugger
        var currentData = idp.uiview.modelController.getMainRowObj();
        var treePath = currentData.APPUNIT_TREEINFO_PATH;
        if(treePath!=null) {
            if (treePath.length > 9) {
                treePath = treePath.substring(0, 9);
            }
            var treePathList = [];
            var item = "";
            filters = [];
            for (var i = 0; i < treePath.length / 4 - 1; i++) {
                item += treePath.substring(i * 4, (i + 1) * 4);
                treePathList.push(item);
            }

            if (treePath == '0001') {

            } else {

                for (let i = 0; i <= treePathList.length; i++) {
                    if (i == treePathList.length) {
                        filters.push({
                            "Left": "",
                            "Field": "TREEINFO_PATH",
                            "Operate": "leftlike",
                            "IsExpress": false,
                            "Value": treePath,
                            "Right": "",
                            "Logic": ""
                        });
                        continue;
                    }

                }
            }
            return filters;
        }


    });
})