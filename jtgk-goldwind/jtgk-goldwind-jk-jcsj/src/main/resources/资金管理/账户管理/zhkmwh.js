//账户 表单模版定制增加  账户科目维护  按钮 脚本
//  /apps/jtgk/jfkj/extend/zhkmwh.js
const current = idp.uiview.modelController.getMainRowObj();
var url="/apps/fastdweb/views/runtime/page/card/cardpreview.html?dataid="+current.ID+"&status=edit&styleid=3b1dd97b-1bec-46c1-b55f-008df3224374&runtime=true&fdim=zhxxdj&formState=&j=&prefix=&FormStatePage=IDP_PAGES_3b1dd97b-1bec-46c1-b55f-008df3224374";
//idp.utils.openurl(current.ID, "账户科目维护", url, true);//'电子影像'

options = {
    name: 'ZHKMWH',
    title: '账户科目维护',
    url: url,
    width: 680,
    height: 400,
    buttons: [
        // { id: "LV_ok", text: '取回', cls: 'lee-btn-primary lee-dialog-btn-ok', onclick: onConfirm },
        { text: '关闭', cls: 'lee-dialog-btn-cancel ', onclick: onCancel }
    ]
};

// 打开界面
idp.dialogUrl(options, function () { });
// 取消回调
function onCancel(item, dialog) {
    dialog.close();
    // idp.uiview.refreshGrid("grid_BFBANKACCOUNTITEMS");
    idp.control.get("grid_BFBANKACCOUNTITEMS").loadData();
}