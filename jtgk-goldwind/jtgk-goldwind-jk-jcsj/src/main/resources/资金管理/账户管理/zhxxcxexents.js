//  /apps/jtgk/jfkj/extend/zhxxcxexents.js
idp.event.bind("domReady", function(e) {

    //列表过滤前
    idp.event.register("grid_main", "beforeGridFilter", function(e, filter) {
        if($("#sfbb").val().length>0&&$("#bbqj").val().length<=0) {
            var sfbb = idp.control.get("sfbb").getValue();
            if (filter.length > 0) {
                filter[filter.length - 1].Logic = "and";
                filter[0].Left = "";
                filter[filter.length - 1].Right = "";
            }
            filter.push({
                "Left": "",
                "Field": "BFMASTERORGANIZATION.EXTVARCHAR3",
                "Operate": "=",
                "IsExpress": false,
                "Value": sfbb,
                "Right": "",
                "Logic": ""
            });
        }
        else if($("#sfbb").val().length>0&&$("#bbqj").val().length>0) {
            var sfbb = idp.control.get("sfbb").getValue();
            var bbqj=$("#bbqj").val();
            if (filter.length > 0) {
                filter[filter.length - 1].Logic = "and";
                filter[0].Left = "";
                filter[filter.length - 1].Right = "";
            }
            if(sfbb=="1") {
                filter.push({
                    "Left": "",
                    "Field": "BFMASTERORGANIZATION.code",
                    "Operate": "in",
                    "IsExpress": true,
                    "Value": {
                        sqlId: "jfkjaccountQry-getsfbb",
                        params: {bbqj: bbqj}
                    },
                    "Right": "",
                    "Logic": ""
                });
            }else{
                filter.push({
                    "Left": "",
                    "Field": "BFMASTERORGANIZATION.code",
                    "Operate": "not in",
                    "IsExpress": true,
                    "Value": {
                        sqlId: "jfkjaccountQry-getsfbb",
                        params: {bbqj: bbqj}
                    },
                    "Right": "",
                    "Logic": ""
                });
            }
        }
        console.log(filter);
        return filter;
    });


});
