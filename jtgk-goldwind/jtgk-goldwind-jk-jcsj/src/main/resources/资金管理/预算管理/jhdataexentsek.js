///apps/jtgk/jfkj/extend/jhdataexentsek.js
//计划编制 合并付款  脚本路径里不要有大写
var jhrq="";
var ysts="";
var ksrq="";
var dynm="";
var zznm="";
var slnm="";
var sessionzz="";
var mkid="";
var filter = [];
idp.event.bind("domReady", function() {
    debugger //需要在界面加载后绑定
    var jhdata = idp.utils.getQuery("JHDATA");
    if (jhdata) {
        var isJson = jhUtil.isJSON(jhdata);
        if (!isJson) {
            jhdata = tb.base.getParam(jhdata);
            //审批加密
            if (!jhdata) {
                jhdata = idp.utils.jsd(idp.utils.getQuery("JHDATA"));
            }
            ksrq = $.parseJSON(jhdata).ksrq;
            dynm = $.parseJSON(jhdata).dynm;
            zznm = $.parseJSON(jhdata).zznm;
            slnm = $.parseJSON(jhdata).slnm;
            mkid = $.parseJSON(jhdata).mkid;
            //sessionzz = $.parseJSON(jhdata).sessionzz;//计划编报页面当前组织，不能用作获取时区，因为他可能是二级单位
            //jhrq = idp.store.get("ksrq");
        }
    }
    if (idp.utils.getQuery('MODEL') == 'BZ'||idp.utils.getQuery('func') == 'YSTZ'){
        idp.event.register("grid_main", "beforeHelpFilter", function(e, ctrl, col, row) {
            if (col == "JHJHDATA_ZDYXM8") {
                var filters = [];
                filters.push({
                    "Left": "",
                    "Field": "tbyszz_zzbh",
                    "Operate": " in ",
                    "IsExpress": true,
                    "Value": {
                        sqlId: "JHJHDATA-JHJHDATAHELPDWID",  // sql元数据id
                        params: {zznm:zznm}//pjbh:dwbh,cprid:cprid
                    },
                    "Right": "",
                    "Logic": " and "
                });
                if(e.result == undefined || e.result == "" || e.result == null ){
                    return filters;
                }
                if(e.result.length > 0){
                    for(i=0;i<e.result.length;i++){
                        filters.push(e.result[i]);
                    }
                }

                return filters;
            }
        })
    }

})



//二开代理
// idp.custom.delegate({
//     "controls":{
//         // 工具栏Id
//         "toolbar2":{
//             //工具栏点击事件
//             "toolbarClick":{
//                 "before":function(e,btn,toolbar){
//                     if(e.id == "baritem_173318"){
//                           //  idp.("“计划支出金额单位为“万元”，请确认填写金额无误】");
//                         idp.confirm("“计划支出金额单位为“万元”，请确认填写金额无误】", function() {},function (){});
//                            // idp.loading("正在处理");
//                     }
//                 }
//             },
//         },
//     }
// })