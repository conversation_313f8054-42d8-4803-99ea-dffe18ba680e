//import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.jk.jcsj.dao.BooleanReturnValue;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.junit.Test;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.json.JSONObject;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.Date;
import java.util.HashMap;

public class test01 {
    @Test
    public void aa() throws ParseException, JSONException, NoSuchAlgorithmException, UnsupportedEncodingException {
//        String aa = "20190101";

//        // 定义输入和输出的日期格式a
//        DateTimeFormatter inputFormat = DateTimeFormatter.ofPattern("yyyyMMdd");
//        DateTimeFormatter outputFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//
//        // 将输入字符串解析为 LocalDate
//        LocalDate date = LocalDate.parse(aa, inputFormat);
//
//        // 格式化为目标字符串
//        String formattedDate = date.format(outputFormat);
//
//        // 输出结果
//        System.out.println(formattedDate); // 输出: 2019-01-01
//
//        // 获取当前日期
//        LocalDate currentDate = LocalDate.now();
//
//        // 获取当前年份
//        int currentYear = currentDate.getYear();
//        // 获取当前月份
//        int currentMonth = currentDate.getMonthValue();
//
//        // 输出结果
//        System.out.println("当前年: " + currentYear);
//        System.out.println("当前月: " + currentMonth);
//        Map<String, Object> req = new HashMap<>();
//        List<Map<String, Object>> mapList=new ArrayList<>();
//        Map<String, Object> maps = new HashMap<>();
//        maps.put("BUKRS", "");//公司代码
//        maps.put("HKONT", "");//会计科目
//        maps.put("year", "");//年份
//        maps.put("MOUTH", "");//月份
//        mapList.add(maps);
//        req.put("IT_QUERY",mapList);
//        String mapls = JSONSerializer.serialize(req);
//        String aa="{\n" +
//                "    \"success\": true,\n" +
//                "    \"data\": {\n" +
//                "        \"ET_RESULT\": [\n" +
//                "            {\n" +
//                "                \"BUKRS\": \"1001\",\n" +
//                "                \"BALANCE_CUM\": \"-121500.00\",\n" +
//                "                \"WAERS\": \"CNY\",\n" +
//                "                \"HKONT\": \"6603030101\"\n" +
//                "            }\n" +
//                "        ]\n" +
//                "    }\n" +
//                "}";
//        List<Map> resultList=new ArrayList<>();
//        JSONObject resultJson = JSONObject.parseObject(aa);
//        System.out.println("入参1: " + resultJson.getString("success"));
//        System.out.println("入参11: " +  new Boolean(resultJson.getString("success")));
//        if (resultJson != null &&  new Boolean(resultJson.getString("success"))) {
//            String data=resultJson.getString("data");
//            System.out.println("入参2: " + resultJson.getString("data"));
//            JSONObject dataJson = JSONObject.parseObject(data);
//            System.out.println("入参3: " + dataJson.getString("ET_RESULT"));
//            resultList = JSON.parseArray(dataJson.getString("ET_RESULT"), Map.class);
//        }
//        System.out.println("入参: " + resultList);
//        String str = "0001001";
//        str = str.replaceAll("^0+", "");
//        if (str.isEmpty()) {
//            str = "0";
//        }
//        String aa="{\n" +
//                "    \"code\": \"000\",\n" +
//                "    \"message\": \"PageNumber 50 PageSize 1000 查询成功\",\n" +
//                "    \"employees\": []\n" +
//                "}";
//        JSONObject resultJson = JSONObject.parseObject(aa);
//        System.out.println("[]".equals(resultJson.getString("employees")));  // 输出: 0

//        String input = "佘德浩!@#$%^&*()_+-={}[]:;\"'<>,.?/~`|\\";
//        // 定义非法字符的正则表达式
//        String illegalCharacters = "[~`!@#\\$%\\^&*()_+\\-=\\{\\}\\[\\]:;'\",\\./<>\\?/~`|\\\\]+";
//
//        // 使用正则表达式替换非法字符
//        String output = input.replaceAll(illegalCharacters, "");
//
//        System.out.println("Original: " + input);
//        System.out.println("Cleaned: " + output);
//        String param="{ \n" +
//                "\"CLTNO\":\"1001\",\n" +
//                "\"DOCSTATUS\":\"1\",\n" +
//                "\"ACCOUNT_NO\":\"1001001\",\n" +
//                "\"APP_DATE\":\"2024/11/14\",\n" +
//                "\"APP_USERNAME\":\"李倩\",\n" +
//                "\"CANCELLATIONNOTE\":\"测试\",\n" +
//                "\"SRCBIZSYS\":\"\",\n" +
//                "\"SRCDOCTYPEID\":\"\",\n" +
//                "\"SRCDOCTYPECODE\":\"\",\n" +
//                "\"SRCDOCID\":\"\",\n" +
//                "\"SRCDOCNO\":\"\"}\n";
//        LinkedHashMap DataStructu = new LinkedHashMap();
//        DataStructu.put("generateParam", param);
//        System.out.println("DataStructu: " + DataStructu);
//        Map<String, String> exceptionuMap = new HashMap<>();
//        String updateStateResultMsg = RpcUtilek.rpcCall("com.inspur.gs.tm.am.bankaccountcancelmobile.core.service.BankAccountCancelComponetService.genBankAccCloseingDoc",
//                "AM", DataStructu, exceptionuMap, String.class);


//        String name="MT940_PK_********193200";
//        String rq="********";
//        int index=name.indexOf(rq);
//        String sj=name.split("********")[1];
//        SimpleDateFormat sd=new SimpleDateFormat("HH:mm:ss");
//        System.out.println("sj: " + sj);
//        Date aa=sd.parse(sj);
//        System.out.println("updateStateResultMsg: " + aa);

//        public class Main {
//            public static void main(String[] args) {
        // 获取当前日期
//        LocalDate currentDate = LocalDate.now();
//
//        // 获取当前年份
//        int currentYear = currentDate.getYear();
//
//        // 获取当前月份
//        int currentMonth = currentDate.getMonthValue();
//        // 获取下一个日期
//        LocalDate nextDate = currentDate.plusDays(1);
//
//        // 判断是否为月底
//        boolean isEndOfMonth = currentDate.getMonth() != nextDate.getMonth();
//        if(!isEndOfMonth){
//            //不是月底的话，取上个月加这个月的
//            //月份为1 的话，取去年12月的
//            if(currentMonth==1){
//                // 获取上个月的日期
//                LocalDate lastMonthDate = currentDate.minusMonths(1);
//                // 获取上个月的年份和月份
//                currentYear= lastMonthDate.getYear();
//                currentMonth = lastMonthDate.getMonthValue();
//            }
//        }
//        String mapString="{\"data\":{\"data\":[{\"sqbh\":\"KHSQ2024113000003\",\"applicantname\":\"李倩\",\"applicantid\":\"52d24be4-2903-2dc1-6182-f41ff1360475\",\"docstatus\":-3},{\"sqbh\":\"KHSQ2024120600002\",\"applicantname\":\"施玉玲\",\"applicantid\":\"06986981-c6ff-4618-aadf-aef7a6b078d4\",\"docstatus\":-3},{\"sqbh\":\"KHSQ2025010800002\",\"applicantname\":\"施玉玲\",\"applicantid\":\"06986981-c6ff-4618-aadf-aef7a6b078d4\",\"docstatus\":-2},{\"sqbh\":\"KHSQ2024103000002\",\"applicantname\":\"李倩\",\"applicantid\":\"52d24be4-2903-2dc1-6182-f41ff1360475\",\"docstatus\":-3}],\"hasData\":true,\"masterName\":\"schema\",\"sysUsers\":[\"52d24be4-2903-2dc1-6182-f41ff1360475\",\"06986981-c6ff-4618-aadf-aef7a6b078d4\"]},\"msgid\":[\"d3b21adb-5020-4e86-9aca-f39ff804db82\"],\"logid\":\"b0efc1cb-399b-489b-a17d-21d1d28d6f12\"}";
//        JSONObject yjxxOb = JSONObject.parseObject(mapString);
//        JSONObject dataOb = yjxxOb.getJSONObject("data");
//        JSONArray dataArray = dataOb.getJSONArray("data");
//        JSONArray userArray = dataOb.getJSONArray("sysUsers") ;
//        String wxuserid="";
//        for (Object obj : userArray) {
//            wxuserid+=obj+"|";
//
//        }
//        wxuserid = wxuserid.substring(0, wxuserid.length() - 1);
//        System.out.println(wxuserid);
//        int zs = dataArray.size();String sqbh="";
//        for(int i =0;i<zs;i++) {
//            JSONObject object = (JSONObject) dataArray.get(i);
//            sqbh+=object.getString("sqbh")+",";
//        }
//        sqbh = sqbh.substring(0, sqbh.length() - 1);
//        System.out.println(sqbh);
//        String wxuserid ="AAA||BBB||CCC||";
//          wxuserid = wxuserid.substring(0, wxuserid.length() - 2);
        // 输出结果
//        String sqlxmc="开户申请流程{}的单据已被退回，请查收关注。司库路径：";
//        String sqbh="123";
//        sqlxmc=sqlxmc.replace("{}",sqbh);
//        System.out.println("当前年份: " + sqlxmc);
        //System.out.println("当前月份: " + currentMonth);
//        String jsonString = "{\"parameters\":null,\"content\":null,\"textcardContent\":{\"title\":null,\"description\":null,\"url\":null},\"textContent\":{\"content\":\"<html>\\n <head></head>\\n <body>\\n  <p>测试</p>\\n </body>\\n</html>\"}}";
//
//        // 解析 JSON
//        JSONObject jsonObject = new JSONObject(jsonString);
//        String contentWithHtml = jsonObject.getJSONObject("textContent").getString("content");
//
//        // 将 HTML 内容解析为 Document
//        Document document = Jsoup.parse(contentWithHtml);
//        // 获取不带 HTML 的文本
//        String contentWithoutHtml = document.text();
        // 获取当前日期
//        LocalDate currentDate = LocalDate.now();
//        // 获取当前年份
//        int currentYear = currentDate.getYear();
//        // 获取当前月份
//        int currentMonth = currentDate.getMonthValue();
//        // 获取下一个日期
//        LocalDate nextDate = currentDate.plusDays(1);
//
//        // 判断是否为月底
//        boolean isEndOfMonth = currentDate.getMonth() != nextDate.getMonth();
//        // 获取上个月的日期
//        LocalDate lastMonthDate = currentDate.minusMonths(1);
//        // 获取上个月的年份和月份
//        currentYear= lastMonthDate.getYear();
//        currentMonth = lastMonthDate.getMonthValue();
//        uat环境的密钥
        String secretKey = "a6a482f34fa245b5b1862c55d9257ca8";
        Long timeStamp = System.currentTimeMillis();// 1742276542668L;
//        HashMap<String, String> map = new HashMap<>();
//        map.put("name", "zhagnsan");
        String data =  "{\"custSN\":\"****************\",\"totalNumber\":\"1\",\"totalAmount\":\"0.03\",\"applyDate\":\"2025-03-18\",\"endrseAcctId\":\"65001611600050000954\",\"endrseAcct\":{\"acctId\":\"65001611600050000954\",\"accountNo\":\"65001611600050000954\",\"name\":\"金风\",\"bankNum\":\"************\"},\"negtbl\":\"EM00\",\"memo\":\"\",\"contents\":[{\"indx\":\"1\",\"draftNo\":\"**************\",\"cdRange\":\"0\",\"billAmt\":\"0.03\",\"endrseAmt\":\"0.03\",\"endrseAcct\":{\"acctId\":\"65001611600050000954\",\"accountNo\":\"65001611600050000954\",\"name\":\"金风\",\"bankNum\":\"************\"}}]}";
        System.out.println(timeStamp);
        String msg = secretKey + timeStamp + data + secretKey;
        System.out.println(msg);
        MessageDigest digest = MessageDigest.getInstance("MD5");
        byte[] md5 = digest.digest(msg.getBytes("UTF-8"));
        String sign = Hex.encodeHexString(md5).toUpperCase();
        String fk01="**********";
        String hktid = StringUtils.isEmpty(fk01) ? "" : fk01.substring(fk01.length() - 5, fk01.length());
        System.out.println(sign);
        //String aa="{\"dwrName\":\"\",\"maxCdAmt\":\"\",\"SU01\":\"\",\"busiKind\":\"reply\",\"busiDtEnd\":\"2025-02-28\",\"acptrAcct\":\"\",\"cdNo\":\"\",\"acptrName\":\"\",\"busiDtStart\":\"2025-02-01\",\"size\":\"1000\",\"sgnUpMk\":\"SU00\",\"dwrAcct\":\"\",\"minCdAmt\":\"\",\"custSN\":\"***********\",\"billStatus\":\"successful\",\"pyName\":\"\",\"pyAcct\":\"\",\"page\":\"2\",\"businessType\":\"BC03\",\"billNo\":\"\"}";
    }
}
