package com.inspur.cloud.jtgk.goldwind.rpa.config;

import com.inspur.cloud.jtgk.goldwind.rpa.controller.JFKJGoldwindRpaController;
import io.iec.edp.caf.rest.RESTEndpoint;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * <AUTHOR>
 * @version 1.0
 * @project jtgk-jxjt-financingextend
 * @description
 * @date 2024/12/17
 */
@Configuration
@ComponentScan("com.inspur.cloud.jtgk.goldwind.rpa")
@EnableJpaRepositories("com.inspur.cloud.jtgk.goldwind.rpa.repository")
@EntityScan("com.inspur.cloud.jtgk.goldwind.rpa.entity")
public class JfkjRpaConfig {

    @Bean
    public RESTEndpoint paymentClaimAEndpoint(JFKJGoldwindRpaController jfkjGoldwindRpaController){
        return new RESTEndpoint("/jtgk/jfkj/goldwind/sfzz/v1.0", jfkjGoldwindRpaController);
    }


}
