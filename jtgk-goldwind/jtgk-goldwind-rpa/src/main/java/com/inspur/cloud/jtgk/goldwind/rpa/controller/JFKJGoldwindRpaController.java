package com.inspur.cloud.jtgk.goldwind.rpa.controller;

import com.alibaba.fastjson.JSONObject;
import com.inspur.cloud.jtgk.goldwind.rpa.dto.AccountBalanceDto;
import com.inspur.cloud.jtgk.goldwind.rpa.dto.BankTransDetailsQueryRenDto;
import com.inspur.cloud.jtgk.goldwind.rpa.dto.HisAccountBalanceDto;
import com.inspur.cloud.jtgk.goldwind.rpa.dto.TransDetailsQueryRenDto;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.BankQueryTransDetailsReq;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.QueryReq;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.QueryReqHis;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.QueryTransDetailsReq;
import com.inspur.cloud.jtgk.goldwind.rpa.service.QueryBankAccountBalance;
import com.inspur.cloud.jtgk.goldwind.rpa.service.TerminationBaseDateService;
import com.inspur.cloud.jtgk.goldwind.rpa.utils.XmlGenerator;
import com.inspur.cloud.jtgk.goldwind.rpa.utils.XmlParser;
import com.inspur.cloud.jtgk.goldwind.rpa.xml.*;
import com.inspur.idd.log.api.controller.LogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import java.util.Map;


import javax.xml.bind.JAXBException;

/**TERMINATIONBASEDATE
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/3/26
 */
@Controller
public class JFKJGoldwindRpaController {

    @Autowired
    private TerminationBaseDateService terminationBaseDateService;



    @Autowired
    private LogService logService;

    @Autowired
    private QueryBankAccountBalance queryBankAccountBalance;

    @Path("/getTerminationBaseDate")
    @POST
    @ResponseBody
    public Map<String,Object> getTerminationBaseDate(){

        Map<String, Object> terminationBaseDate = terminationBaseDateService.getTerminationBaseDate();
        return terminationBaseDate;


    }


    //查询内部银行流水
    @Path("/queryTransDetails")
    @POST
    @ResponseBody
    public String queryTransDetails(String queryReqStr)  {
        String name="queryTransDetails";
        logService.init(name);
        QueryTransDetailsReq queryReq = null;
        String resultXML = "";
        try {
            logService.info(name,"queryTransDetails入参："+queryReqStr);
            queryReq = XmlParser.InBankStatementParseXmlToQueryReq(queryReqStr);
            logService.info(name,"queryTransDetails-queryReq："+queryReq);
            TransDetailsQueryRenDto transDetailsQueryRenDto = queryBankAccountBalance.queryTransDetails(queryReq);
            InBankStatementIssItreasuryRes bankStatementIssItreasury = new InBankStatementIssItreasuryRes();
            bankStatementIssItreasury.setQueryReq(transDetailsQueryRenDto);
            resultXML = XmlGenerator.convertToXml(bankStatementIssItreasury);
            logService.info(name,"queryTransDetails-出参："+resultXML);
        } catch (JAXBException e) {
            logService.error(name,"queryTransDetails异常",e);
            throw new RuntimeException(e);
        }finally {
            logService.flush();
        }

        return resultXML;
    }

    /**
     * 查询外部银行流水
     * @param queryReqStr
     * @return
     */
    @Path("/queryBankTransDetails")
    @POST
    @ResponseBody
    public String queryBankTransDetails(String queryReqStr)  {
        String name="queryBankTransDetails";
        logService.init(name);
        BankQueryTransDetailsReq queryReq = null;
        String resultXML = "";
        try {
            logService.info(name,"queryTransDetails入参："+queryReqStr);
            queryReq = XmlParser.OutBankStatementParseXmlToQueryReq(queryReqStr);
            logService.info(name,"queryTransDetails-queryReq："+queryReq);
            BankTransDetailsQueryRenDto bankTransDetailsQueryRenDto = queryBankAccountBalance.queryBankTransDetails(queryReq);
            OutBankStatementIssItreasuryRes bankStatementIssItreasury = new OutBankStatementIssItreasuryRes();
            bankStatementIssItreasury.setQueryReq(bankTransDetailsQueryRenDto);
            resultXML = XmlGenerator.convertToXml(bankStatementIssItreasury);
            logService.info(name,"queryTransDetails-出参："+resultXML);
        }  catch (JAXBException e) {
            logService.error(name,"queryBankTransDetails异常",e);
            throw new RuntimeException(e);
        }finally {
            logService.flush();
        }

        return resultXML;
    }


    //查询内部账户当日余额
    @Path("/queryInternalBankAccountBalance")
    @POST
    @ResponseBody
    public String queryInternalBankAccountBalance(String queryReqStr){

        QueryReq queryReq = null;
        logService.init("queryInternalBankAccountBalance");
        String resultXML="";
        try {
            queryReq = XmlParser.parseXmlToQueryReq(queryReqStr);
            logService.info("queryInternalBankAccountBalance","查询内部账户当日余额入参："+ JSONObject.toJSONString(queryReq));
            AccountBalanceDto accountBalanceDto = queryBankAccountBalance.queryInternalBankAccountBalance(queryReq);
            DayInBankAccountIssItreasuryRes dayBankAccountIssItreasuryRes = new DayInBankAccountIssItreasuryRes();
            dayBankAccountIssItreasuryRes.setQueryReq(accountBalanceDto);
            resultXML = XmlGenerator.convertToXml(dayBankAccountIssItreasuryRes);
        } catch (JAXBException e) {
            throw new RuntimeException(e);
        }finally {
            logService.flush();
        }

        return resultXML;
    }


    // 查询内部账户历史每日余额
    @Path("/queryInternalBankAccountBalanceHis")
    @POST
    @ResponseBody
    public String queryInternalBankAccountBalanceHis(String queryReqStr){
        QueryReq queryReq = null;
        logService.init("queryInternalBankAccountBalanceHis");
        String resultXML="";
        try {
            queryReq = XmlParser.HisParseXmlToQueryReq(queryReqStr);
            logService.info("queryInternalBankAccountBalanceHis","查询内部账户历史每日余额入参："+ JSONObject.toJSONString(queryReq));
            HisAccountBalanceDto hisAccountBalanceDto = queryBankAccountBalance.queryInternalBankAccountBalanceHis(queryReq);
            HisInBankAccountIssItreasuryRes hisBankAccountIssItreasuryRes = new HisInBankAccountIssItreasuryRes();
            hisBankAccountIssItreasuryRes.setQueryReq(hisAccountBalanceDto);
            resultXML = XmlGenerator.convertToXml(hisBankAccountIssItreasuryRes);
        } catch (JAXBException e) {
            throw new RuntimeException(e);
        }finally {
            logService.flush();
        }

        return resultXML;
    }


    // 查询银行账户当日余额
    @Path("/queryExternalBankAccountBalance")
    @POST
    @ResponseBody
    public String queryExternalBankAccountBalance(String queryReqStr){
        QueryReq queryReq = null;
        String resultXML="";
        try {
            queryReq = XmlParser.parseXmlToQueryReq(queryReqStr);
            AccountBalanceDto accountBalanceDto = queryBankAccountBalance.queryExternalBankAccountBalance(queryReq);
            DayInBankAccountIssItreasuryRes  BankAccountIssItreasuryRes = new DayInBankAccountIssItreasuryRes();
            BankAccountIssItreasuryRes.setQueryReq(accountBalanceDto);
            resultXML = XmlGenerator.convertToXml(BankAccountIssItreasuryRes);
        } catch (JAXBException e) {
            throw new RuntimeException(e);
        }

        return resultXML;
    }

    // 查询银行账户历史每日余额
    @Path("/queryExternalBankAccountBalanceHis")
    @POST
    @ResponseBody
    public String queryExternalBankAccountBalanceHis(String queryReqStr){
        QueryReqHis queryReq = null;
        String resultXML="";
        try {
            queryReq = XmlParser.HisBankParseXmlToQueryReq(queryReqStr);
            HisAccountBalanceDto hisAccountBalanceDto = queryBankAccountBalance.queryExternalBankAccountBalanceHis(queryReq);
            HisBankAccountIssItreasuryRes  bankAccountIssItreasuryRes = new HisBankAccountIssItreasuryRes();
            bankAccountIssItreasuryRes.setQueryReq(hisAccountBalanceDto);
            resultXML = XmlGenerator.convertToXml(bankAccountIssItreasuryRes);
        } catch (JAXBException e) {
            throw new RuntimeException(e);
        }

        return resultXML;
    }

}
