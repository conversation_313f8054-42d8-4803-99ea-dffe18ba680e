package com.inspur.cloud.jtgk.goldwind.rpa.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/3/10
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class BankTransDetailsRenContent {

    @JsonProperty("BanktransNo")
    @XmlElement(name = "BanktransNo")
    private String banktransNo;



    @JsonProperty("ExcutDate")
    @XmlElement(name = "ExcutDate")
    private String ExcutDate;



    @JsonProperty("AccountNo")
    @XmlElement(name = "AccountNo")
    private String AccountNo;

    @JsonProperty("AccountName")
    @XmlElement(name = "AccountName")
    private String AccountName;

    @JsonProperty("OppAccountNo")
    @XmlElement(name = "OppAccountNo")
    private String OppAccountNo;

    @JsonProperty("OppAccountName")
    @XmlElement(name = "OppAccountName")
    private String OppAccountName;


    @JsonProperty("OppOpenBankName")
    @XmlElement(name = "OppOpenBankName")
    private String OppOpenBankName;

    @JsonProperty("InterestStart")
    @XmlElement(name = "InterestStart")
    private String InterestStart;

    @JsonProperty("Amount")
    @XmlElement(name = "Amount")
    private String Amount;

    @JsonProperty("TransDirection")
    @XmlElement(name = "TransDirection")
    private String TransDirection;

    @JsonProperty("AbstractContent")
    @XmlElement(name = "AbstractContent")
    private String AbstractContent;

    @JsonProperty("UseInfo")
    @XmlElement(name = "UseInfo")
    private String UseInfo;

    // ------------------------ 备用字段 ------------------------

    @JsonProperty("REVERSE1")
    private String reverse1;               // 银行交易号

    @JsonProperty("REVERSE2")
    private String reverse2;               // 备用字段2

    @JsonProperty("REVERSE3")
    private String reverse3;               // 备用字段3

    @JsonProperty("REVERSE4")
    private String reverse4;               // 备用字段4

    @JsonProperty("REVERSE5")
    private String reverse5;               // 备用字段5

    // Getter and Setter methods


    public String getBanktransNo() {
        return banktransNo;
    }

    public void setBanktransNo(String banktransNo) {
        this.banktransNo = banktransNo==null ? "" : banktransNo;
    }

    public String getExcutDate() {
        return ExcutDate;
    }

    public void setExcutDate(String excutDate) {
        ExcutDate = excutDate==null ? "" : excutDate;
    }

    public String getAccountNo() {
        return AccountNo;
    }

    public void setAccountNo(String accountNo) {
        AccountNo = accountNo==null ? "" : accountNo;
    }

    public String getAccountName() {
        return AccountName;
    }

    public void setAccountName(String accountName) {
        AccountName = accountName==null ? "" : accountName;
    }

    public String getOppAccountNo() {
        return OppAccountNo;
    }

    public void setOppAccountNo(String oppAccountNo) {
        OppAccountNo = oppAccountNo==null ? "" : oppAccountNo;
    }

    public String getOppAccountName() {
        return OppAccountName;
    }

    public void setOppAccountName(String oppAccountName) {
        OppAccountName = oppAccountName==null ? "" : oppAccountName;
    }

    public String getOppOpenBankName() {
        return OppOpenBankName;
    }

    public void setOppOpenBankName(String oppOpenBankName) {
        OppOpenBankName = oppOpenBankName==null ? "" : oppOpenBankName;
    }

    public String getInterestStart() {
        return InterestStart;
    }

    public void setInterestStart(String interestStart) {
        InterestStart = interestStart==null ? "" : interestStart;
    }

    public String getAmount() {
        return Amount;
    }

    public void setAmount(String amount) {
        Amount = amount==null ? "" : amount;
    }

    public String getTransDirection() {
        return TransDirection;
    }

    public void setTransDirection(String transDirection) {
        TransDirection = transDirection==null ? "" : transDirection;
    }

    public String getAbstractContent() {
        return AbstractContent;
    }

    public void setAbstractContent(String abstractContent) {
        AbstractContent = abstractContent==null ? "" : abstractContent;
    }

    public String getUseInfo() {
        return UseInfo;
    }

    public void setUseInfo(String useInfo) {
        UseInfo = useInfo==null ? "" : useInfo;
    }

    public String getReverse1() {
        return reverse1;
    }

    public void setReverse1(String reverse1) {
        this.reverse1 = (reverse1 == null) ? "" : reverse1;
    }

    public String getReverse2() {
        return reverse2;
    }

    public void setReverse2(String reverse2) {
        this.reverse2 = (reverse2 == null) ? "" : reverse2;
    }

    public String getReverse3() {
        return reverse3;
    }

    public void setReverse3(String reverse3) {
        this.reverse3 = (reverse3 == null) ? "" : reverse3;
    }

    public String getReverse4() {
        return reverse4;
    }

    public void setReverse4(String reverse4) {
        this.reverse4 = (reverse4 == null) ? "" : reverse4;
    }

    public String getReverse5() {
        return reverse5;
    }

    public void setReverse5(String reverse5) {
        this.reverse5 = (reverse5 == null) ? "" : reverse5;
    }
}
