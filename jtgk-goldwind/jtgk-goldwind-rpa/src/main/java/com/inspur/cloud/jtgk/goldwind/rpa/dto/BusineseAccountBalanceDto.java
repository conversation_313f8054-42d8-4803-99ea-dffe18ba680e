package com.inspur.cloud.jtgk.goldwind.rpa.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description 银行账户余额返回体实体
 * @date 2025/3/4
 */

public class BusineseAccountBalanceDto {

    @JsonProperty("OperationType")
    private String OperationType;//操作类型

    @JsonProperty("ProcessCode")
    private String ProcessCode;//接口调用结果编码 0000：标识接口调用成功； 存在错误账号则整个报文返回9999

    @JsonProperty("ProcessDesc")
    private String ProcessDesc;//接口调用结果描述

    @JsonProperty("RenContent")
    private List<HisRenContentDto> RenContent;

    public String getOperationType() {
        return OperationType;
    }

    public void setOperationType(String operationType) {
        OperationType = operationType==null?"":operationType;
    }

    public String getProcessCode() {
        return ProcessCode;
    }

    public void setProcessCode(String processCode) {
        ProcessCode = processCode==null?"":processCode;
    }

    public String getProcessDesc() {
        return ProcessDesc;
    }

    public void setProcessDesc(String processDesc) {
        ProcessDesc = processDesc==null?"":processDesc;
    }

    public List<HisRenContentDto> getRenContent() {
        return RenContent;
    }

    public void setRenContent(List<HisRenContentDto> renContent) {
        RenContent = renContent;
    }
}
