package com.inspur.cloud.jtgk.goldwind.rpa.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/3/4
 */
public class BusineseRenContentDto {


    @JsonProperty("AccountNo")
    private String AccountNo;//账户号

    @JsonProperty("Balance")
    private String Balance;//余额


    @JsonProperty("REVERSE1")
    private String REVERSE1;//备用字段1

    @JsonProperty("REVERSE2")
    private String REVERSE2;//账户可用余额


    public String getAccountNo() {
        return AccountNo;
    }

    public void setAccountNo(String accountNo) {
        AccountNo = accountNo==null?"":accountNo;
    }

    public String getBalance() {
        return Balance;
    }

    public void setBalance(String balance) {
        Balance = balance==null?"":balance;
    }

    public String getREVERSE1() {
        return REVERSE1;
    }

    public void setREVERSE1(String REVERSE1) {
        this.REVERSE1 = REVERSE1==null?"":REVERSE1;
    }

    public String getREVERSE2() {
        return REVERSE2;
    }

    public void setREVERSE2(String REVERSE2) {
        this.REVERSE2 = REVERSE2==null?"":REVERSE2;
    }
}
