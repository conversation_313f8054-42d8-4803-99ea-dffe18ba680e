package com.inspur.cloud.jtgk.goldwind.rpa.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description 银行账户余额返回体实体
 * @date 2025/3/4
 */

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class HisAccountBalanceDto {

    @JsonProperty("OperationType")
    @XmlElement(name = "OperationType")
    private String OperationType;//操作类型

    @JsonProperty("ProcessCode")
    @XmlElement(name = "ProcessCode")
    private String ProcessCode;//接口调用结果编码 0000：标识接口调用成功； 存在错误账号则整个报文返回9999

    @JsonProperty("ProcessDesc")
    @XmlElement(name = "ProcessDesc")
    private String ProcessDesc;//接口调用结果描述

    @JsonProperty("RenContent")
    @XmlElement(name = "RenContent")
    private List<HisRenContentDto> RenContent;

}
