package com.inspur.cloud.jtgk.goldwind.rpa.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/3/4
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class RenContentDto {


    @JsonProperty("AccountNo")
    @XmlElement(name = "AccountNo")
    private String AccountNo;//账户号

    @JsonProperty("Balance")
    @XmlElement(name = "Balance")
    private String Balance;//余额

    @JsonProperty("REVERSE1")
    @XmlElement(name = "REVERSE1")
    private String REVERSE1;//备用字段1

    @JsonProperty("REVERSE2")
    @XmlElement(name = "REVERSE2")
    private String REVERSE2;//账户可用余额
}
