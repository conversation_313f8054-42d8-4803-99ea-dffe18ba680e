package com.inspur.cloud.jtgk.goldwind.rpa.dto;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/2/25
 */

public class ResponseDto {

    @JsonProperty("EV_TYPE")
    private String EV_TYPE;// 消息类型: S 成功,E 错误,W 警告,I 信息,A 中断

    @JsonProperty("EV_MESSAGE")
    private String EV_MESSAGE;



    public String getType() {
        return EV_TYPE;
    }

    public void setType(String type) {
        this.EV_TYPE = type==null?"":EV_TYPE;
    }

    public String getMassage() {
        return EV_MESSAGE;
    }

    public void setMassage(String massage) {
        this.EV_MESSAGE = massage==null?"":EV_MESSAGE;
    }

}
