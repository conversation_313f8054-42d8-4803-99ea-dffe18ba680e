package com.inspur.cloud.jtgk.goldwind.rpa.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.TransDetailQueryContent;
import lombok.Data;

import javax.ws.rs.core.Link;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/3/10
 */

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class TransDetailsQueryRenDto {

    @XmlElement(name = "OperationType")
    private String OperationType;
    @XmlElement(name = "ProcessCode")
    private String ProcessCode;
    @XmlElement(name = "ProcessDesc")
    private String ProcessDesc;
    @XmlElement(name = "TotalCount")
    private String TotalCount;
    @XmlElement(name = "CurCount")
    private String CurCount;
    @XmlElement(name = "QueryNextPage")
    private String QueryNextPage;
    @XmlElement(name = "QueryContent")
    private TransDetailQueryContent QueryContent;
    @XmlElement(name = "RenContent")
    private List<TransDetailsRenContent> RenContent;

}
