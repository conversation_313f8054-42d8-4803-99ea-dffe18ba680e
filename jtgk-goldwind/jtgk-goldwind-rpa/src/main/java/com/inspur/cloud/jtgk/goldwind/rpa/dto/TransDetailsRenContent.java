package com.inspur.cloud.jtgk.goldwind.rpa.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/3/10
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class TransDetailsRenContent {

    @JsonProperty("BankTransNo")
    @XmlElement(name = "BankTransNo")
    private String banktransNo;

    // ------------------------ 交易基础信息 ------------------------

    @JsonProperty("TransNo")
    @XmlElement(name = "TransNo")
    private String transno;                // 交易编号

    @JsonProperty("TransactionTypeCode")
    @XmlElement(name = "TransactionTypeCode")
    private String transactiontypecode;    // 交易类型编码

    @JsonProperty("TransactionTypeName")
    @XmlElement(name = "TransactionTypeName")
    private String transactiontypename;    // 交易类型名称

    @JsonProperty("ApplyCode")
    @XmlElement(name = "ApplyCode")
    private String applycode;              // 业务申请编号

    @JsonProperty("ExcutDate")
    @XmlElement(name = "ExcutDate")
    private String excutdate;              // 日期（建议使用 Date 类型，如 LocalDate）

    // ------------------------ 账户信息 ------------------------

    @JsonProperty("AccountNo")
    @XmlElement(name = "AccountNo")
    private String accountno;              // 本方账户号

    @JsonProperty("AccountName")
    @XmlElement(name = "AccountName")
    private String accountname;            // 本方账户名称

    @JsonProperty("OppAccountNo")
    @XmlElement(name = "OppAccountNo")
    private String oppaccountno;           // 对方账户号（XML 标签原为 `<Opp AccountNo>`，已修正为驼峰命名）

    @JsonProperty("OppAccountName")
    @XmlElement(name = "OppAccountName")
    private String oppaccountname;         // 对方账户名称

    // ------------------------ 交易详情 ------------------------

    @JsonProperty("AbstractContent")
    @XmlElement(name = "AbstractContent")
    private String abstractcontent;        // 备注（摘要）

    @JsonProperty("UseInfo")
    @XmlElement(name = "UseInfo")
    private String useinfo;

    @JsonProperty("InterestStart")
    @XmlElement(name = "InterestStart")
    private String intereststart;          // 起息日（建议使用 Date 类型）

    @JsonProperty("TransDirection")
    @XmlElement(name = "TransDirection")
    private String transdirection;         // 交易方向（如：IN-收入，OUT-支出）

    @JsonProperty("Amount")
    @XmlElement(name = "Amount")
    private String amount;                 // 交易金额（建议使用 BigDecimal 类型）

    // ------------------------ 备用字段 ------------------------

    @JsonProperty("REVERSE1")
    @XmlElement(name = "REVERSE1")
    private String reverse1;               // 银行交易号

    @JsonProperty("REVERSE2")
    @XmlElement(name = "REVERSE2")
    private String reverse2;               // 备用字段2

    @JsonProperty("REVERSE3")
    @XmlElement(name = "REVERSE3")
    private String reverse3;               // 备用字段3

    @JsonProperty("REVERSE4")
    @XmlElement(name = "REVERSE4")
    private String reverse4;               // 备用字段4

    @JsonProperty("REVERSE5")
    @XmlElement(name = "REVERSE5")
    private String reverse5;               // 备用字段5


}
