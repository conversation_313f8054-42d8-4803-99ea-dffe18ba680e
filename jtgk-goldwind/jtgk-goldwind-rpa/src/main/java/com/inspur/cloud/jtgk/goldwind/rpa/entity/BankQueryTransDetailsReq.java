package com.inspur.cloud.jtgk.goldwind.rpa.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description 查询交易流水
 * @date 2025/3/10
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class BankQueryTransDetailsReq {

    @JsonProperty("OperationType")
    @XmlElement(name = "OperationType")
    private String OperationType;
    @JsonProperty("SystemID")
    @XmlElement(name = "SystemID")
    private String SystemID;
    @JsonProperty("SendTime")
    @XmlElement(name = "SendTime")
    private String SendTime;
    @JsonProperty("QueryContent")
    @XmlElement(name = "QueryContent")
    private BankTransDetailQueryContent QueryContent;
}
