package com.inspur.cloud.jtgk.goldwind.rpa.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/3/10
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class BankTransDetailQueryContent {


    @XmlElement(name = "AccountNo")
    private String AccountNo;

    @XmlElement(name = "ExcuteDateStart")
    private String ExcuteDateStart;

    @XmlElement(name = "ExcuteDateEnd")
    private String ExcuteDateEnd;

    @XmlElement(name = "QueryPageNumber")
    private String QueryPageNumber;

    @XmlElement(name = "REVERSE1")
    private String REVERSE1;

    @XmlElement(name = "REVERSE2")
    private String REVERSE2;

    @XmlElement(name = "REVERSE3")
    private String REVERSE3;

    @XmlElement(name = "REVERSE4")
    private String REVERSE4;

    @XmlElement(name = "REVERSE5")
    private String REVERSE5;

}
