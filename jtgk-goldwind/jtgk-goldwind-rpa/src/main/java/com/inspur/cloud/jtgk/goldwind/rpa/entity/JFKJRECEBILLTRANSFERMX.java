package com.inspur.cloud.jtgk.goldwind.rpa.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import javax.persistence.*;
import java.sql.Date;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/4/1
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@Entity
@Table(name = "JFKJRECEBILLTRANSFERMX")
public class JFKJRECEBILLTRANSFERMX {

    @JsonProperty("TIMESTAMPS_CREATEDBY")
    private String timestamps_CreatedBy; // 创建人

    @JsonProperty("TIMESTAMPS_CREATEDON")
    private Date timestamps_CreatedOn; // 创建时间

    @JsonProperty("TIMESTAMPS_LASTCHANGEDBY")
    private String timestamps_LastChangedBy; // 最后修改人

    @JsonProperty("TIMESTAMPS_LASTCHANGEDON")
    private Date timestamps_LastChangedOn; // 最后修改时间

    @Id
    @JsonProperty("ID")
    private String id; // 主键

    @JsonProperty("PARENTID")
    private String parentId; // 应收票据承接表ID

    @JsonProperty("ENDORSEMENTNUMBER")
    private String endorsementNumber; // 背书序号

    @JsonProperty("BILLNO")
    private String billNo; // 票据号

    @JsonProperty("ENDORSEMENTTYPE")
    private String endorsementType; // 背书类型

    @JsonProperty("ENDORSEMENTUNIT")
    private String endorsementUnit; // 背书单位

    @JsonProperty("ENDORSEMENTUNITCODE")
    private String endorsementUnitCode; // 背书单位编号

    @JsonProperty("ENDORSERACCOUNT")
    private String endorserAccount; // 背书人账户

    @JsonProperty("ENDORSEMENTAMOUNT")
    private String endorsementAmount; // 背书金额

    @JsonProperty("RECEIVINGUNIT")
    private String receivingUnit; // 收票单位

    @JsonProperty("RECEIPTBANKACCOUNT")
    private String receiptBankAccount; // 收票银行账号

    @JsonProperty("TICKETRECIPIENTNAME")
    private String ticketRecipientName; // 收票户名

    @JsonProperty("BILLOPENBANK")
    private String billOpenBank; // 收票开户银行

    @JsonProperty("ENDORSERPROFITCENTER")
    private String endorserProfitCenter; // 背书人利润中心

    @JsonProperty("ENDORSERPROFITCENTERNAME")
    private String endorserProfitCenterName; // 背书人利润中心

    @JsonProperty("BILLSTATUS")
    private String billStatus; // 票据状态

    @JsonProperty("APPLICATIONNO")
    private String applicationNo; // 背书申请单号

    @JsonProperty("APPLICATIONID")
    private String applicationID; // 背书申请单号

    @JsonProperty("POSTSCRIPT")
    private String postscript; // 附言

    @JsonProperty("BUSINESSUNITNO")
    private String businessUnitNo; // 二级业务单元编号

    @JsonProperty("BUSINESSUNITNAME")
    private String businessUnitName; // 二级业务单元名称

    @JsonProperty("BUSINESSUNITID")
    private String businessUnitId; // 二级业务单元ID

    @JsonProperty("RECEIVINGUNITPROFITCENTER")
    private String receivingUnitProfitCenter; // 收票单位利润中心

    @JsonProperty("RECEIVINGUNITPROFITCENTERNAME")
    private String receivingUnitProfitCenterName; // 收票单位利润中心

    @JsonProperty("ISDETAIL")
    private String isDetail; // 分级明细

    @JsonProperty("LAYER")
    private String layer; // 级数

    @JsonProperty("PATH")
    private String path; // 分级码

    @JsonProperty("HIGHERLEVELID")
    private String higherLevelId; // 父级id

    @JsonProperty("SUBBILLSTARTSN")
    private String subBillStartSn; // 子票区间起

    @JsonProperty("SUBBILLENDSN")
    private String subBillEndSn; // 子票区间止

    @JsonProperty("AVAENDORSE")
    private String avaEndorse; // 后手可转让标记

    @JsonProperty("SPLITFLAG")
    private String splitFlag; // 可分包流转的标志

    @JsonProperty("NEWBILLFLAG")
    private String newBillFlag; // 新一代票据

    @JsonProperty("RECEIVINGUNITNAME")
    private String receivingUnitName; // 收票单位名称

    @JsonProperty("EXPECTDATE")
    private String expectDate; // 期望背书日期

    @JsonProperty("COSTCENTERNO")
    private String costCenterNo; // 成本中心编号

    @JsonProperty("COSTCENTERNAME")
    private String costCenterName; // 成本中心名称

    @JsonProperty("BELONGINGSUBJECT")
    private String belongingSubject; // 归属主体

    @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @JoinColumn(name = "applicationID",updatable = false,insertable = false)
    @JsonProperty(value = "TMBILLENDORSEREQUEST")
    private JFKJ_TMBILLENDORSEREQUEST jfkj_tmbillendorserequest;


}