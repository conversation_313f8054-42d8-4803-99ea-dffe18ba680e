package com.inspur.cloud.jtgk.goldwind.rpa.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/4/1
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@Entity
@Table(name = "JFKJRECEIVABLEBILLTRANSFER")
public class JFKJRECEIVABLEBILLTRANSFER {

    @Id
    @JsonProperty("ID")
    private String id; // 主键

    @JsonProperty("TIMESTAMPS_CREATEDBY")
    private String timestamps_CreatedBy; // 创建人

    @JsonProperty("TIMESTAMPS_CREATEDON")
    private String timestamps_CreatedOn; // 创建时间

    @JsonProperty("TIMESTAMPS_LASTCHANGEDBY")
    private String timestamps_LastChangedBy; // 最后修改人

    @JsonProperty("TIMESTAMPS_LASTCHANGEDON")
    private String timestamps_LastChangedOn; // 最后修改时间

    @JsonProperty("DOCUMENTNO")
    private String documentNo; // 单据编号

    @JsonProperty("STATUS")
    private String status; // 单据状态

    @JsonProperty("APPLICANT")
    private String applicant; // 申请人

    @JsonProperty("APPLICATIONDATE")
    private String applicationDate; // 申请日期

    @JsonProperty("NOTES")
    private String notes; // 备注

    @JsonProperty("BUSINESSUNITNO")
    private String businessUnitNo; // 二级业务单元编号

    @JsonProperty("BUSINESSUNITNAME")
    private String businessUnitName; // 二级业务单元名称

    @JsonProperty("BUSINESSUNITID")
    private String businessUnitId; // 二级业务单元ID

    @JsonProperty("APPROVALPROCESS")
    private String approvalProcess; // 审批流

    @JsonProperty("APPROVALSTATUS")
    private String approvalStatus;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    @Fetch(FetchMode.SUBSELECT)
    @JoinColumn(name = "parentId",updatable = false,insertable = false)
    @JsonProperty(value = "JFKJRECEBILLTRANSFERMX")
    private List<JFKJRECEBILLTRANSFERMX> jfkjReceivableBillTransferMxList;

}