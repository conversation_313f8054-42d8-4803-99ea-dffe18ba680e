package com.inspur.cloud.jtgk.goldwind.rpa.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description 票据
 * @date 2025/4/14
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@Entity
@Table(name = "TMBILLRECEIVABLEINVENTORY")
public class JFKJTMBILLRECEIVABLEINVENTORYEnt {

    @Id
    private String id;

    @Column(name = "BILLNO")
    private String billNo;//票据号码

    private String SUBBILLSTARTSN;//子票区间起

    private String SUBBILLENDSN;//子票区间止

    private String AVAENDORSE;//后手可转让标记

    private String SPLITFLAG;//可分包流转的标志

    private String NEWBILLFLAG;//新一代票据



}
