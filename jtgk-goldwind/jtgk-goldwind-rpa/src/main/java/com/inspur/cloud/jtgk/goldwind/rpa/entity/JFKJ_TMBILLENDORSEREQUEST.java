package com.inspur.cloud.jtgk.goldwind.rpa.entity;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSKf
 * @description
 * @date 2025/4/9
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@Entity
@Table(name = "TMBILLENDORSEREQUEST")
public class JFKJ_TMBILLENDORSEREQUEST {

    @Id
    private String ID;

    private String DOCSTATUS;

    private String DOCTYPE;
}
