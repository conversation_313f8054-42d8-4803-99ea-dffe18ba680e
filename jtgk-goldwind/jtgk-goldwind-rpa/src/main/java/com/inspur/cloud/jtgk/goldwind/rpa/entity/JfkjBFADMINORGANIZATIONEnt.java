package com.inspur.cloud.jtgk.goldwind.rpa.entity;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/4/14
 */

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@NoArgsConstructor
@Accessors(chain = true)
@Entity
@Table(name = "BFADMINORGANIZATION")
public class JfkjBFADMINORGANIZATIONEnt {

    @Id
    private String id;

    private String CODE;

    @Column(name = "NAME_CHS")
    private String nameChs;
}
