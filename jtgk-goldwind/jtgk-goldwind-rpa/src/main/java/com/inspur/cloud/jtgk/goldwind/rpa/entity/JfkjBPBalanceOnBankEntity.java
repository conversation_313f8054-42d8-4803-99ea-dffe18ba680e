package com.inspur.cloud.jtgk.goldwind.rpa.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/3/4
 */

@Data
public class JfkjBPBalanceOnBankEntity {


    private String accountNo;
    private Date BalanceDate;
    private BigDecimal currentBalance;//CURRENTBALANCE 当前余额-当日
    private BigDecimal currentBalanceHis;//CURRENTBALANCEHIS 当前余额-历史

}
