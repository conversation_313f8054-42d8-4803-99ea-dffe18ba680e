package com.inspur.cloud.jtgk.goldwind.rpa.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description 银行账户余额查询入参实体
 * @date 2025/3/4
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryContentEntity {

    @XmlElement(name = "AccountNo")
    private String accountNo;

    @XmlElement(name = "AccountCode")
    private String AccountCode;

    @XmlElement(name = "QueryDateStart")
    private Date QueryDateStart;

    @XmlElement(name = "QueryDateEnd")
    private Date QueryDateEnd;

    @XmlElement(name = "REVERSE1")
    private String reverse1;

    @XmlElement(name = "REVERSE2")
    private String reverse2;

    @XmlElement(name = "REVERSE3")
    private String reverse3;

    @XmlElement(name = "REVERSE4")
    private String reverse4;

    @XmlElement(name = "REVERSE5")
    private String reverse5;
}
