package com.inspur.cloud.jtgk.goldwind.rpa.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description 银行账户余额查询入参实体
 * @date 2025/3/4
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryContentHisEntity {

    @JsonProperty("AccountCode")
    @XmlElement(name = "AccountCode")
    private String AccountCode;


    @JsonProperty("QueryDateStart")
    @XmlElement(name = "QueryDateStart")
    private Date QueryDateStart;

    @JsonProperty("QueryDateEnd")
    @XmlElement(name = "QueryDateEnd")
    private Date QueryDateEnd;

    @JsonProperty("REVERSE1")
    @XmlElement(name = "REVERSE1")
    private String REVERSE1;

    @JsonProperty("REVERSE2")
    @XmlElement(name = "REVERSE2")
    private String REVERSE2;

    @JsonProperty("REVERSE3")
    @XmlElement(name = "REVERSE3")
    private String REVERSE3;

    @JsonProperty("REVERSE4")
    @XmlElement(name = "REVERSE4")
    private String REVERSE4;

    @JsonProperty("REVERSE5")
    @XmlElement(name = "REVERSE5")
    private String REVERSE5;
}
