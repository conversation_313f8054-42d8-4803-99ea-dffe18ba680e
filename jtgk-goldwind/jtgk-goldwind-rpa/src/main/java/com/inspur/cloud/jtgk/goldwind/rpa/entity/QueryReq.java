package com.inspur.cloud.jtgk.goldwind.rpa.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import javax.ws.rs.core.Link;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/3/4
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryReq {

    @XmlElement(name = "OperationType")
    private String operationType;

    @XmlElement(name = "SystemID")
    private String systemID;

    @XmlElement(name = "SendTime")
    private String sendTime;

    @XmlElement(name = "QueryContent")
    private List<QueryContentEntity> queryContents;  // 改为List类型
}
