package com.inspur.cloud.jtgk.goldwind.rpa.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/3/4
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryReqHis {

    @JsonProperty("OperationType")
    @XmlElement(name = "OperationType")
    private String OperationType;//操作类型

    @JsonProperty("ProcessCode")//系统标识
    @XmlElement(name = "ProcessCode")
    private String SystemID;

    @JsonProperty("ProcessDesc")
    @XmlElement(name = "ProcessDesc")
    private String SendTime;//系统标识

    @JsonProperty("QueryContent")
    @XmlElement(name = "QueryContent")
    private QueryContentHisEntity QueryContent;//请求详情
}
