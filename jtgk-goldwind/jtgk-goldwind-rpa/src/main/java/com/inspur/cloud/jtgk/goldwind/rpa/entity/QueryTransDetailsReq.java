package com.inspur.cloud.jtgk.goldwind.rpa.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.ws.rs.core.Link;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description 查询交易流水
 * @date 2025/3/10
 */
@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class QueryTransDetailsReq {

    @XmlElement(name = "OperationType")
    private String OperationType;
    @XmlElement(name = "SystemID")
    private String SystemID;
    @XmlElement(name = "SendTime")
    private String SendTime;
    @XmlElement(name = "QueryContent")
    private TransDetailQueryContent QueryContent;
}
