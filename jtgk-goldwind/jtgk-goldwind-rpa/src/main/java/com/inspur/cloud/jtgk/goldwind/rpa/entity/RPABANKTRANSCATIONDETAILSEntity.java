package com.inspur.cloud.jtgk.goldwind.rpa.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/2/24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "RPABANKTRANSCATIONDETAILS")
public class RPABANKTRANSCATIONDETAILSEntity {

    @Id
    private String NM;        //内码
    private String ZBANKT;    //银行名称
    private String BNKTIM;    //交易日期时间
    private String BUTXT;     //公司名称
    private String ACTNBR;    //银行账号
    private BigDecimal ZZC;       //支出
    private BigDecimal ZSR;       //收入
    private BigDecimal ACTBAL;    //账户余额
    private String ZJYBZ;     //币种
    private String ZOTHAC;    //对方账号
    private String ZOTHNAM;   //对方户名
    private String ZOTHOPN;   //对方开户行
    private String BNKFLW;    //流水号
    private String ZPZZL;     //凭证种类
    private String ZPZH;      //凭证号
    private String ZYWBH;     //企业业务编号
    private String ZYWZL;     //业务种类
    private String ZSGTX;     //摘要
    private String ZFY;       //附言
    private String NUSAGE;    //用途
    private String ZBZ;       //备注
    private String ZJYXX;     //交易信息
    private String ZSTA;      //交易状态
    private String ZJYLX;     //交易类型
    private String ZYJQD;     //交易渠道
    private String ZKH;       //卡号
    private String ZDWKH;     //单位结算卡号
    private String ZLHH;      //对方开户行联行号
    private String ZZBANK;    //子账户账号
    private String ZZBANKT;   //子账户名称
    private String ZYL1;      // 备用字段1
    private String ZYL2;      // 备用字段2
    private String ZYL3;      // 备用字段3
    private String ZYL4;      // 备用字段4
    private String ZYL5;      // 备用字段5
    private String ZYL6;      // 备用字段6
    private String ZYL7;      // 备用字段7
    private String ZYL8;      // 备用字段8
    private String ZYL9;      // 备用字段9
    private String ZYL10;     // 备用字段10
    private Date CREATTIME; //接收时间


}
