package com.inspur.cloud.jtgk.goldwind.rpa.repository;

import com.inspur.cloud.jtgk.goldwind.rpa.entity.JFKJRECEBILLTRANSFERMX;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.JFKJRECEIVABLEBILLTRANSFER;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/4/1
 */
@Repository
public interface JFKJRECEBILLTRANSFERMXRep extends JpaRepository<JFKJRECEBILLTRANSFERMX, String> {


    List<JFKJRECEBILLTRANSFERMX> findAllByParentId(String parentId);
}
