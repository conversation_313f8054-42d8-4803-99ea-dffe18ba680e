package com.inspur.cloud.jtgk.goldwind.rpa.repository;

import com.alibaba.fastjson.JSONObject;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.JFKJRECEBILLTRANSFERMX;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.JFKJRECEIVABLEBILLTRANSFER;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/4/1
 */


@Repository
public interface JFKJRECEIVABLEBILLTRANSFERRep extends JpaRepository<JFKJRECEIVABLEBILLTRANSFER, String> , JpaSpecificationExecutor<JFKJRECEIVABLEBILLTRANSFER> {

    // 扩展默认findAll方法，添加ID条件
    default List<JFKJRECEIVABLEBILLTRANSFER> findAllWithSubCondition() {
        return findAll((root, query, cb) -> {
            // 创建子查询：从JFKJRECEBILLTRANSFERMX表中获取applicationID为null的parentid
            Subquery<Long> subquery = query.subquery(Long.class);
            Root<JFKJRECEBILLTRANSFERMX> subRoot = subquery.from(JFKJRECEBILLTRANSFERMX.class);
            subquery.select(cb.literal(1L))  // 返回固定值 1（仅用于 EXISTS 判断）
                    .where(
                            cb.equal(subRoot.get("parentId"), root.get("id")), // 关联条件
                            cb.isNull(subRoot.get("applicationID"))           // 筛选条件
                    );

            // 组合条件：EXISTS 子查询 AND status = '2'
            return cb.and(
                    cb.exists(subquery),
                    cb.equal(root.get("approvalStatus"), "2")
            );
        });
    }

    @Override
    Optional<JFKJRECEIVABLEBILLTRANSFER> findById(String s);

}
