package com.inspur.cloud.jtgk.goldwind.rpa.repository;

import com.inspur.cloud.jtgk.goldwind.rpa.entity.JFKJRECEBILLTRANSFERMX;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.JfkjBFADMINORGANIZATIONEnt;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/4/14
 */
@Repository
public interface JfkjBFADMINORGANIZATIONRep  extends JpaRepository<JfkjBFADMINORGANIZATIONEnt, String> {


    Optional<JfkjBFADMINORGANIZATIONEnt> findByNameChs(String name_CHS);
}
