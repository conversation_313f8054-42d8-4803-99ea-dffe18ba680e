package com.inspur.cloud.jtgk.goldwind.rpa.repository;

import com.inspur.cloud.jtgk.goldwind.rpa.entity.JFKJTMBILLRECEIVABLEINVENTORYEnt;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.JFKJ_TMBILLENDORSEREQUEST;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/4/14
 */
@Repository
public interface JfkjTMBILLRECEIVABLEINVENTORYRep extends JpaRepository<JFKJTMBILLRECEIVABLEINVENTORYEnt, String> {


    Optional<JFKJTMBILLRECEIVABLEINVENTORYEnt> findByBillNo(String billNo) ;
}
