package com.inspur.cloud.jtgk.goldwind.rpa.repository;

import com.inspur.cloud.jtgk.goldwind.rpa.entity.RPABANKTRANSCATIONDETAILSEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/2/24
 */
//@Repository
public interface RpaBankTranscationDetailsRepository extends JpaRepository<RPABANKTRANSCATIONDETAILSEntity, String> {

    RPABAN<PERSON>TRANSCATIONDETAILSEntity findByBNKFLW(String BNKFLW);
}
