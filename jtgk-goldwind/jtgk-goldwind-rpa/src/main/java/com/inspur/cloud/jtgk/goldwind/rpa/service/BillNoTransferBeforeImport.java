package com.inspur.cloud.jtgk.goldwind.rpa.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.JFKJRECEBILLTRANSFERMX;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.JFKJRECEIVABLEBILLTRANSFER;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.JFKJTMBILLRECEIVABLEINVENTORYEnt;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.JfkjBFADMINORGANIZATIONEnt;
import com.inspur.cloud.jtgk.goldwind.rpa.repository.JFKJRECEBILLTRANSFERMXRep;
import com.inspur.cloud.jtgk.goldwind.rpa.repository.JFKJRECEIVABLEBILLTRANSFERRep;
import com.inspur.cloud.jtgk.goldwind.rpa.repository.JfkjBFADMINORGANIZATIONRep;
import com.inspur.cloud.jtgk.goldwind.rpa.repository.JfkjTMBILLRECEIVABLEINVENTORYRep;
import com.inspur.fastdweb.model.excel.ExcelObject;
import com.inspur.fastdweb.model.excel.ExcelResult;
import com.inspur.fastdweb.service.excel.IExcelImportEvent;
import com.inspur.idd.log.api.controller.LogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/4/11
 */
@Service
public class BillNoTransferBeforeImport implements IExcelImportEvent {



    @Autowired
    private JFKJRECEIVABLEBILLTRANSFERRep jfkjreceivablebilltransferRep;

    @Autowired
    private JFKJRECEBILLTRANSFERMXRep jfkjrecebilltransfermxRep;

    @Autowired
    private JfkjTMBILLRECEIVABLEINVENTORYRep jfkjTMBILLRECEIVABLEINVENTORYRep;

    @Autowired
    private JfkjBFADMINORGANIZATIONRep jfkjBFADMINORGANIZATIONRep;

    @Autowired
    private LogService logService;



    //全部导入后事件
    public void finalImport(ExcelObject excelObject) {

        logService.init("finalImport");
        logService.info("finalImport",JSONObject.toJSONString(excelObject));
        //logService.flush();

        Object parentida = excelObject.dynMap.get("PARENTIDA");
        //检查主表是否保存
        Optional<JFKJRECEIVABLEBILLTRANSFER> byId = jfkjreceivablebilltransferRep.findById(parentida.toString());
        JFKJRECEIVABLEBILLTRANSFER jfkjreceivablebilltransfer = byId.get();
        if (jfkjreceivablebilltransfer == null){
            logService.info("finalImport","主表未保存");
        }
        //获取导入的所有明细
        List<JFKJRECEBILLTRANSFERMX> allByParentIdList = jfkjrecebilltransfermxRep.findAllByParentId(parentida.toString());

        logService.info("finalImport","allByParentIdList:"+JSON.toJSONString(allByParentIdList));
        for (JFKJRECEBILLTRANSFERMX jfkjrecebilltransfermx : allByParentIdList){
            //处理当前级数
            int layer =(jfkjrecebilltransfermx.getPath().length())/4;
            jfkjrecebilltransfermx.setLayer(String.valueOf(layer));
            //处理判断是否有子级
            Map<String,JFKJRECEBILLTRANSFERMX> map = new HashMap<>();
            for (JFKJRECEBILLTRANSFERMX sj : allByParentIdList){

                if (jfkjrecebilltransfermx.getBillNo().equals(sj.getBillNo())&&
                    //jfkjrecebilltransfermx.getPath().length()<sj.getPath().length() &&
                        jfkjrecebilltransfermx.getPath().length()+4==(sj.getPath().length())&&
                        jfkjrecebilltransfermx.getPath().equals(sj.getPath().substring(0,jfkjrecebilltransfermx.getPath().length()))
                ){
                    map.put(sj.getEndorsementNumber(),sj);
                }
            }
            if (map.size()>0){
                jfkjrecebilltransfermx.setIsDetail("0");
            }else {
                jfkjrecebilltransfermx.setIsDetail("1");
            }

            //处理票据的信息
            Optional<JFKJTMBILLRECEIVABLEINVENTORYEnt> optionalBillNo = jfkjTMBILLRECEIVABLEINVENTORYRep.findByBillNo(jfkjrecebilltransfermx.getBillNo());
            JFKJTMBILLRECEIVABLEINVENTORYEnt byBillNo = optionalBillNo.get();
            if (byBillNo!=null){
                jfkjrecebilltransfermx.setSubBillStartSn(byBillNo.getSUBBILLSTARTSN());
                jfkjrecebilltransfermx.setSubBillEndSn(byBillNo.getSUBBILLENDSN());
                jfkjrecebilltransfermx.setAvaEndorse(byBillNo.getAVAENDORSE());
                jfkjrecebilltransfermx.setSplitFlag(byBillNo.getSPLITFLAG());
                jfkjrecebilltransfermx.setNewBillFlag(byBillNo.getNEWBILLFLAG());
            }
            //处理背书单位
            Optional<JfkjBFADMINORGANIZATIONEnt> optionalBF = jfkjBFADMINORGANIZATIONRep.findByNameChs(jfkjrecebilltransfermx.getEndorsementUnit());
            JfkjBFADMINORGANIZATIONEnt byName_chs = optionalBF.get();
            if (byName_chs!=null){
                jfkjrecebilltransfermx.setEndorsementUnit(byName_chs.getId());
                jfkjrecebilltransfermx.setEndorsementUnitCode(byName_chs.getCODE());
            }


            jfkjrecebilltransfermxRep.save(jfkjrecebilltransfermx);
        }

    }

}
