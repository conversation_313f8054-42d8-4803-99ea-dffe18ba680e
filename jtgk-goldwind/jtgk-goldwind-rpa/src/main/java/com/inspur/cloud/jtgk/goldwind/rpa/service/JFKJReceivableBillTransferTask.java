package com.inspur.cloud.jtgk.goldwind.rpa.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.JFKJRECEBILLTRANSFERMX;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.JFKJRECEIVABLEBILLTRANSFER;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.JFKJ_TMBILLENDORSEREQUEST;
import com.inspur.cloud.jtgk.goldwind.rpa.repository.JFKJRECEBILLTRANSFERMXRep;
import com.inspur.cloud.jtgk.goldwind.rpa.repository.JFKJRECEIVABLEBILLTRANSFERRep;
import com.inspur.cloud.jtgk.goldwind.rpa.repository.JFKJTMBILLENDORSEREQUESTrep;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.commons.utils.StringUtils;
import io.iec.edp.caf.rpc.api.service.RpcClient;
//import javafx.embed.swing.JFXPanel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description 应收票据转承审批后生成背书申请调度
 * @date 2025/4/8
 */
@Service("58eeb837-a215-e753-3c82-8855c0b680c8")
@Scope(ConfigurableListableBeanFactory.SCOPE_SINGLETON)//单例
public class JFKJReceivableBillTransferTask {

    @Autowired
    private JFKJRECEIVABLEBILLTRANSFERRep jfkjReceivableBillTransferRep;

    @Autowired
    private JFKJRECEBILLTRANSFERMXRep jfkjReceivableBillTransferMxRep;

    @Autowired
    private JFKJTMBILLENDORSEREQUESTrep jfkjtmbillendorserequesTrep;

    @Autowired
    private EntityManager entityManager;
    private String logCode = "billTransferTask";

    @Autowired
    private RpcClient rpcClient;

    @Autowired
    private LogService logService;


    public void billTransferTask() {

        logService.init(logCode);


        List<JFKJRECEIVABLEBILLTRANSFER> allList = jfkjReceivableBillTransferRep.findAllWithSubCondition();
        //List<Object[]> results = entityManager.createNativeQuery(sql).getResultList();
        logService.info(logCode,"查询结果："+ JSONObject.toJSONString(allList));
        
        if (allList.size() <= 0){
            logService.info(logCode,"未查询到数据有效");
            logService.flush();
            return;
        }

        for (JFKJRECEIVABLEBILLTRANSFER jfkjReceivableBillTransfer : allList){
            List<JFKJRECEBILLTRANSFERMX> TransferMxList = jfkjReceivableBillTransfer.getJfkjReceivableBillTransferMxList();
            TransferMxList.sort(Comparator.comparing(JFKJRECEBILLTRANSFERMX::getEndorsementNumber));
            JFKJRECEBILLTRANSFERMX flagTransferMx =TransferMxList.get(0);
            for (JFKJRECEBILLTRANSFERMX transferMx :  TransferMxList) {
                String docStatus = flagTransferMx.getJfkj_tmbillendorserequest()==null ?"0":transferMx.getJfkj_tmbillendorserequest().getDOCSTATUS();
                if (StringUtils.isEmpty(transferMx.getApplicationNo()) && !docStatus.equals("5")) {

                    //组织背书申请接口入参
                    LinkedHashMap params = new LinkedHashMap();
                    // 创建一个Map集合，键为字符串类型，值为Object类型（可以存储null）
                    Map<String, Object> billValue = new HashMap<>();

                    // 添加字段名和对应的null值到Map集合中
                    billValue.put("EndorseWAY", "1"); // 背书方式
                    //billValue.put("DOCNO", null); // 单据编号
                    billValue.put("DOCTYPE", "2"); // 单据类型
                    billValue.put("CLTNO", transferMx.getEndorsementUnitCode()); // 背书单位编号
                    billValue.put("USINGUNIT", transferMx.getReceivingUnit()); // 用票单位编号
                    billValue.put("DOCSRC", "4"); // 单据来源
                    billValue.put("SRCDOCID", transferMx.getId()); // 来源单据ID
                    billValue.put("SRCDOCNO", jfkjReceivableBillTransfer.getDocumentNo()); // 来源单据编号
                    billValue.put("IFSUBMIT", "1"); // 是否提交启动流程
                    billValue.put("RECUNITNAME", transferMx.getReceivingUnitName()); // 收款单位名称
                    billValue.put("RECACCNO", transferMx.getReceiptBankAccount()); // 收款银行账号
                    billValue.put("RECBANKNAME", transferMx.getBillOpenBank()); // 收款账号开户行名称
                    //billValue.put("RECBANKNO", null); // 收款账号开户行行号
                    billValue.put("ENDORSEAMOUNT", transferMx.getEndorsementAmount()); // 背书金额
                    billValue.put("EXPECTDATE", transferMx.getExpectDate()); // 期望背书日期
                    //billValue.put("CONTRACTNO", null); // 合同号
                    //billValue.put("ENDORSEREASON", null); // 背书事由
                    billValue.put("REMARK", transferMx.getPostscript()); // 背书附言
                    //billValue.put("TREASUREORGCODE", null); // 资金组织编号
                    //billValue.put("INNERACCNO", null); // 内部票据户编号
                    billValue.put("APPLYDATE", CAFContext.current.getCurrentDateTime()); // 申请日期
                    billValue.put("APPLICANT", CAFContext.current.getUserId()); // 申请人名称
                    //billValue.put("PLANNINGNO", null); // 资金计划编号
                    //billValue.put("PLANNINGID", null); // 资金计划ID
                    //billValue.put("recproname", null); // 收款省名
                    //billValue.put("reccityname", null); // 收款城市名
                    billValue.put("TXT01", transferMx.getBusinessUnitName());//业务单元名称
                    billValue.put("TXT02", transferMx.getBusinessUnitNo());//业务单元编号
                    billValue.put("TXT03", transferMx.getEndorserProfitCenter());//利润中心
                    billValue.put("TXT04", transferMx.getEndorserProfitCenterName());//利润中心名称
                    billValue.put("TXT05", transferMx.getCostCenterNo());//成本中心
                    billValue.put("TXT06", transferMx.getCostCenterName());//成本中心名称
                    billValue.put("TXT07", transferMx.getEndorsementType());//背书类型
                    //billValue.put("TXT08", null);//还票信息
                    billValue.put("TXT10", transferMx.getBelongingSubject());//归属主题

                    List<Map<String, Object>> BillEndorseDetailList = new ArrayList<>();
                    Map<String, Object> billEndorseDetail = new HashMap<>();
                    billEndorseDetail.put("BILLNO", transferMx.getBillNo()); // 票据号码
                    billEndorseDetail.put("SUBBILLSTARTSN", transferMx.getSubBillStartSn()); // 子票区间起
                    billEndorseDetail.put("SUBBILLENDSN", transferMx.getSubBillEndSn()); // 子票区间止
                    billEndorseDetail.put("AVAENDORSE", transferMx.getAvaEndorse()); // 后手可转让标记
                    billEndorseDetail.put("SPLITFLAG", transferMx.getSplitFlag()); // 可分包流转的标志
                    billEndorseDetail.put("NEWBILLFLAG", transferMx.getNewBillFlag()); // 新一代票据
                    billEndorseDetail.put("USEAMOUNT", transferMx.getEndorsementAmount()); // 本次背书金额
                    BillEndorseDetailList.add(billEndorseDetail);
                    billValue.put("generaBillEndorseDetailList", BillEndorseDetailList);
                    Map<String, Object> TMBILLENDORSEREQUEST = new HashMap<>();

                    List<Map<String, Object>> TMBILLENDORSEREQUESTlist = new ArrayList<>();
                    TMBILLENDORSEREQUESTlist.add(billValue);

                    TMBILLENDORSEREQUEST.put("TMBILLENDORSEREQUEST", TMBILLENDORSEREQUESTlist);

                    params.put("params", TMBILLENDORSEREQUEST);

                    logService.info(logCode, "调用RPC入参：" + params.toString());
                    String invokeresult = rpcClient.invoke(String.class,
                            "com.inspur.gs.tm.bm.bmbillreceipt.core.service.BMBillReceivableRPCService.generateBillEndorseRequest",
                            "BM",
                            params,
                            null
                    );
                    logService.info(logCode, "调用RPC返回值：" + invokeresult.toString());
                    Map invoke = JSON.parseObject(invokeresult, Map.class);
                    logService.info(logCode, "调用RPC返回值转化后：" + invoke.toString());
                    if (null!=invoke && "1".equals(invoke.get("RET_CODE"))) {
                        List<Map<String, String>> ret_body = (List<Map<String, String>>) invoke.get("RET_BODY");
                        transferMx.setApplicationNo(ret_body.get(0).get("DOCNO"));
                        transferMx.setApplicationID(ret_body.get(0).get("DOC_ID"));
                        transferMx.setBillStatus("2");
                        jfkjReceivableBillTransferMxRep.save(transferMx);
                        if ("4".equals(transferMx.getEndorsementType())) {
                            JFKJ_TMBILLENDORSEREQUEST tm = new JFKJ_TMBILLENDORSEREQUEST();
                            tm.setID(ret_body.get(0).get("DOC_ID"));
                            tm.setDOCTYPE("4");
                            jfkjtmbillendorserequesTrep.save(tm);
                        }
                    }

                    Map<String, Object> map = new HashMap<>();
                    map.put("LYDJLX", "BM_ZGBS"); //业务类型：BM_ZGBS，BM_TGBS
                    map.put("FORMTYPE", "BM_ZGBS"); //业务类型：BM_ZGBS，BM_TGBS
                    map.put("BILLID",transferMx.getApplicationID() ); //单据ID
                    LinkedHashMap<String, Object> subParams = new LinkedHashMap<>();
                    subParams.put("map", map);
                    String fsspResultRet = rpcClient.invoke(String.class,
                            "com.inspur.gs.tm.bm.bmbillreceipt.core.service.BMBillReceivableRPCService.SubmitBillEndorseRequest",
                            "BM", subParams, null);
                    logService.info( logCode,"自动提交-返回：" + fsspResultRet);
                    break;
                }
                flagTransferMx=transferMx;

            }
        }





       /* for (JFKJRECEIVABLEBILLTRANSFER jfkjReceivableBillTransfer : allList) {
            List<JFKJRECEBILLTRANSFERMX> billTransferMxList = jfkjReceivableBillTransfer.getJfkjReceivableBillTransferMxList();

            Map<String, Map<String, List<JFKJRECEBILLTRANSFERMX>>> collectMap = billTransferMxList.stream().collect(
                    Collectors.groupingBy(JFKJRECEBILLTRANSFERMX::getBillNo,
                            Collectors.groupingBy(JFKJRECEBILLTRANSFERMX::getEndorsementAmount))
            );
            Set<String> keySet = collectMap.keySet();
            for (String key : keySet) {
                Map<String, List<JFKJRECEBILLTRANSFERMX>> mxMap = collectMap.get(key);
                for (String key1 : mxMap.keySet()) {
                    List<JFKJRECEBILLTRANSFERMX> TransferMxList = mxMap.get(key1);
                    TransferMxList.sort(Comparator.comparing(JFKJRECEBILLTRANSFERMX::getEndorsementNumber));
                    logService.info(logCode, key1+"分组结果：" + JSONObject.toJSONString(TransferMxList));
                    JFKJRECEBILLTRANSFERMX flagTransferMx = TransferMxList.get(0);
                    for (JFKJRECEBILLTRANSFERMX transferMx : TransferMxList) {
                        flagTransferMx=transferMx;
                        if (StringUtils.isEmpty(transferMx.getApplicationNo()) &&
                                (null!=flagTransferMx.getJfkj_tmbillendorserequest() &&
                                "5".equals(flagTransferMx.getJfkj_tmbillendorserequest().getDOCSTATUS()))) {

                            //组织背书申请接口入参
                            LinkedHashMap params = new LinkedHashMap();
                            // 创建一个Map集合，键为字符串类型，值为Object类型（可以存储null）
                            Map<String, Object> billValue = new HashMap<>();

                            // 添加字段名和对应的null值到Map集合中
                            billValue.put("EndorseWAY", "1"); // 背书方式
                            //billValue.put("DOCNO", null); // 单据编号
                            billValue.put("DOCTYPE", "2"); // 单据类型
                            billValue.put("CLTNO", transferMx.getEndorsementUnitCode()); // 背书单位编号
                            billValue.put("USINGUNIT", transferMx.getReceivingUnit()); // 用票单位编号
                            billValue.put("DOCSRC", "4"); // 单据来源
                            billValue.put("SRCDOCID", jfkjReceivableBillTransfer.getId()); // 来源单据ID
                            billValue.put("SRCDOCNO", transferMx.getEndorsementNumber()); // 来源单据编号
                            billValue.put("IFSUBMIT", "1"); // 是否提交启动流程
                            billValue.put("RECUNITNAME", transferMx.getReceivingUnitName()); // 收款单位名称
                            billValue.put("RECACCNO", transferMx.getReceiptBankAccount()); // 收款银行账号
                            billValue.put("RECBANKNAME", transferMx.getBillOpenBank()); // 收款账号开户行名称
                            //billValue.put("RECBANKNO", null); // 收款账号开户行行号
                            billValue.put("ENDORSEAMOUNT", transferMx.getEndorsementAmount()); // 背书金额
                            billValue.put("EXPECTDATE", transferMx.getExpectDate()); // 期望背书日期
                            //billValue.put("CONTRACTNO", null); // 合同号
                            //billValue.put("ENDORSEREASON", null); // 背书事由
                            billValue.put("REMARK", transferMx.getPostscript()); // 背书附言
                            //billValue.put("TREASUREORGCODE", null); // 资金组织编号
                            //billValue.put("INNERACCNO", null); // 内部票据户编号
                            billValue.put("APPLYDATE", CAFContext.current.getCurrentDateTime()); // 申请日期
                            billValue.put("APPLICANT", CAFContext.current.getUserId()); // 申请人名称
                            //billValue.put("PLANNINGNO", null); // 资金计划编号
                            //billValue.put("PLANNINGID", null); // 资金计划ID
                            //billValue.put("recproname", null); // 收款省名
                            //billValue.put("reccityname", null); // 收款城市名
                            billValue.put("TXT01", transferMx.getBusinessUnitName());//业务单元名称
                            billValue.put("TXT02", transferMx.getBusinessUnitNo());//业务单元编号
                            billValue.put("TXT03", transferMx.getEndorserProfitCenter());//利润中心
                            billValue.put("TXT04", transferMx.getEndorserProfitCenterName());//利润中心名称
                            billValue.put("TXT05", transferMx.getCostCenterNo());//成本中心
                            billValue.put("TXT06", transferMx.getCostCenterName());//成本中心名称
                            billValue.put("TXT07", transferMx.getEndorsementType());//背书类型
                            //billValue.put("TXT08", null);//还票信息
                            billValue.put("TXT10", transferMx.getBelongingSubject());//归属主题

                            List<Map<String, Object>> BillEndorseDetailList = new ArrayList<>();
                            Map<String, Object> billEndorseDetail = new HashMap<>();
                            billEndorseDetail.put("BILLNO", transferMx.getBillNo()); // 票据号码
                            billEndorseDetail.put("SUBBILLSTARTSN", transferMx.getSubBillStartSn()); // 子票区间起
                            billEndorseDetail.put("SUBBILLENDSN", transferMx.getSubBillEndSn()); // 子票区间止
                            billEndorseDetail.put("AVAENDORSE", transferMx.getAvaEndorse()); // 后手可转让标记
                            billEndorseDetail.put("SPLITFLAG", transferMx.getSplitFlag()); // 可分包流转的标志
                            billEndorseDetail.put("NEWBILLFLAG", transferMx.getNewBillFlag()); // 新一代票据
                            billEndorseDetail.put("USEAMOUNT", transferMx.getEndorsementAmount()); // 本次背书金额
                            BillEndorseDetailList.add(billEndorseDetail);
                            billValue.put("generaBillEndorseDetailList", BillEndorseDetailList);
                            Map<String, Object> TMBILLENDORSEREQUEST = new HashMap<>();

                            List<Map<String, Object>> TMBILLENDORSEREQUESTlist = new ArrayList<>();
                            TMBILLENDORSEREQUESTlist.add(billValue);

                            TMBILLENDORSEREQUEST.put("TMBILLENDORSEREQUEST", TMBILLENDORSEREQUESTlist);

                            params.put("params", TMBILLENDORSEREQUEST);

                            logService.info(logCode, "调用RPC入参：" + params.toString());
                            String invokeresult = rpcClient.invoke(String.class,
                                    "com.inspur.gs.tm.bm.bmbillreceipt.core.service.BMBillReceivableRPCService.generateBillEndorseRequest",
                                    "BM",
                                    params,
                                    null
                            );
                            logService.info(logCode, "调用RPC返回值：" + invokeresult.toString());
                            Map invoke = JSON.parseObject(invokeresult, Map.class);
                            logService.info(logCode, "调用RPC返回值转化后：" + invoke.toString());
                            if (null!=invoke && "1".equals(invoke.get("RET_CODE"))) {
                                List<Map<String, String>> ret_body = (List<Map<String, String>>) invoke.get("RET_BODY");
                                transferMx.setApplicationNo(ret_body.get(0).get("DOCNO"));
                                transferMx.setApplicationID(ret_body.get(0).get("DOC_ID"));
                                transferMx.setBillStatus("2");
                                jfkjReceivableBillTransferMxRep.save(transferMx);
                                if ("4".equals(transferMx.getEndorsementType())) {
                                    JFKJ_TMBILLENDORSEREQUEST tm = new JFKJ_TMBILLENDORSEREQUEST();
                                    tm.setID(ret_body.get(0).get("DOC_ID"));
                                    tm.setDOCTYPE("4");
                                    jfkjtmbillendorserequesTrep.save(tm);
                                }
                            }

                            Map<String, Object> map = new HashMap<>();
                            map.put("LYDJLX", "BM_ZGBS"); //业务类型：BM_ZGBS，BM_TGBS
                            map.put("FORMTYPE", "BM_ZGBS"); //业务类型：BM_ZGBS，BM_TGBS
                            map.put("BILLID",transferMx.getApplicationID() ); //单据ID
                            LinkedHashMap<String, Object> subParams = new LinkedHashMap<>();
                            subParams.put("map", map);
                            String fsspResultRet = rpcClient.invoke(String.class,
                                    "com.inspur.gs.tm.bm.bmbillreceipt.core.service.BMBillReceivableRPCService.SubmitBillEndorseRequest",
                                    "BM", subParams, null);
                            logService.info( logCode,"自动提交-返回：" + fsspResultRet);
                            break;
                        }

                        }

                    }
                }


            }*/
        logService.flush();

    }

}
