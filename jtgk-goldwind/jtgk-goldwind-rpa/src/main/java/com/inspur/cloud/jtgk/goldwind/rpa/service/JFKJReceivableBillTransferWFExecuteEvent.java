package com.inspur.cloud.jtgk.goldwind.rpa.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.JFKJRECEBILLTRANSFERMX;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.JFKJRECEIVABLEBILLTRANSFER;
import com.inspur.cloud.jtgk.goldwind.rpa.repository.JFKJRECEBILLTRANSFERMXRep;
import com.inspur.cloud.jtgk.goldwind.rpa.repository.JFKJRECEIVABLEBILLTRANSFERRep;
import com.inspur.fastdweb.wf.WFEvent;
import com.inspur.fastdweb.wf.WFExecuteEvent;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.commons.transaction.JpaTransaction;
import io.iec.edp.caf.commons.transaction.TransactionPropagation;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.commons.utils.StringUtils;
import io.iec.edp.caf.rpc.api.service.RpcClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/4/1
 */
@Service
public class JFKJReceivableBillTransferWFExecuteEvent implements WFExecuteEvent {

    @Autowired
    private LogService logService;

    @Autowired
    private RpcClient rpcClient;

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private JFKJRECEIVABLEBILLTRANSFERRep jfkjreceivablebilltransferRep;

    @Autowired
    private JFKJRECEBILLTRANSFERMXRep jfkjrecebilltransfermxRep;

    @Override
    //@Transactional(rollbackFor = Throwable.class)
    public void pass(WFEvent event) {
/*
        JpaTransaction transaction = JpaTransaction.getTransaction();
        transaction.begin(TransactionPropagation.REQUIRES_NEW);
        try {
            logService.init("executeWF");
            logService.info("executeWF", event.toString());
            String docid = "";

            //根据id获取转承数据
            Optional<JFKJRECEIVABLEBILLTRANSFER> byId = jfkjreceivablebilltransferRep.findById(event.dataId);
            JFKJRECEIVABLEBILLTRANSFER jfkjreceivablebilltransfer = byId.get();

            //转承明细集合
            List<JFKJRECEBILLTRANSFERMX> jfkjReceivableBillTransferMxList = jfkjreceivablebilltransfer.getJfkjReceivableBillTransferMxList();

            logService.info("executeWF", "转承明细集合："+jfkjReceivableBillTransferMxList);

            for (JFKJRECEBILLTRANSFERMX jfkjReceivableBillTransferMx : jfkjReceivableBillTransferMxList){
                //组织背书申请接口入参
                LinkedHashMap params = new LinkedHashMap();
                // 创建一个Map集合，键为字符串类型，值为Object类型（可以存储null）
                Map<String, Object> billValue = new HashMap<>();

                // 添加字段名和对应的null值到Map集合中
                billValue.put("EndorseWAY", "1"); // 背书方式
                //billValue.put("DOCNO", null); // 单据编号
                billValue.put("DOCTYPE", "2"); // 单据类型
                billValue.put("CLTNO", jfkjReceivableBillTransferMx.getB_endorsementUnitCode()); // 背书单位编号
                billValue.put("USINGUNIT", jfkjReceivableBillTransferMx.getB_receivingUnit()); // 用票单位编号
                billValue.put("DOCSRC", "4"); // 单据来源
                billValue.put("SRCDOCID", jfkjreceivablebilltransfer.getId()); // 来源单据ID
                billValue.put("SRCDOCNO", jfkjreceivablebilltransfer.getDocumentNo()); // 来源单据编号
                billValue.put("IFSUBMIT", "1"); // 是否提交启动流程
                billValue.put("RECUNITNAME", jfkjReceivableBillTransferMx.getReceivingUnitName()); // 收款单位名称
                billValue.put("RECACCNO", jfkjReceivableBillTransferMx.getReceiptBankAccount()); // 收款银行账号
                billValue.put("RECBANKNAME", jfkjReceivableBillTransferMx.getBillOpenBank()); // 收款账号开户行名称
                //billValue.put("RECBANKNO", null); // 收款账号开户行行号
                billValue.put("ENDORSEAMOUNT", jfkjReceivableBillTransferMx.getEndorsementAmount()); // 背书金额
                billValue.put("EXPECTDATE", jfkjReceivableBillTransferMx.getExpectDate()); // 期望背书日期
                //billValue.put("CONTRACTNO", null); // 合同号
                //billValue.put("ENDORSEREASON", null); // 背书事由
                billValue.put("REMARK", jfkjReceivableBillTransferMx.getPostscript()); // 背书附言
                //billValue.put("TREASUREORGCODE", null); // 资金组织编号
                //billValue.put("INNERACCNO", null); // 内部票据户编号
                billValue.put("APPLYDATE", CAFContext.current.getCurrentDateTime()); // 申请日期
                billValue.put("APPLICANT", CAFContext.current.getUserId()); // 申请人名称
                //billValue.put("PLANNINGNO", null); // 资金计划编号
                //billValue.put("PLANNINGID", null); // 资金计划ID
                //billValue.put("recproname", null); // 收款省名
                //billValue.put("reccityname", null); // 收款城市名
                billValue.put("TXT01", jfkjReceivableBillTransferMx.getBusinessUnitName());//业务单元名称
                billValue.put("TXT02", jfkjReceivableBillTransferMx.getBusinessUnitNo());//业务单元编号
                billValue.put("TXT03", jfkjReceivableBillTransferMx.getEndorserProfitCenter());//利润中心
                billValue.put("TXT04", jfkjReceivableBillTransferMx.getEndorserProfitCenterName());//利润中心名称
                billValue.put("TXT05", jfkjReceivableBillTransferMx.getCostCenterNo());//成本中心
                billValue.put("TXT06", jfkjReceivableBillTransferMx.getCostCenterName());//成本中心名称
                billValue.put("TXT07", jfkjReceivableBillTransferMx.getEndorsementType());//背书类型
                //billValue.put("TXT08", null);//还票信息
                billValue.put("TXT10", jfkjReceivableBillTransferMx.getBelongingSubject());//归属主题

                List<Map<String, Object>> BillEndorseDetailList = new ArrayList<>();
                Map<String, Object> billEndorseDetail = new HashMap<>();
                billEndorseDetail.put("BILLNO", jfkjReceivableBillTransferMx.getBillNo()); // 票据号码
                billEndorseDetail.put("SUBBILLSTARTSN", jfkjReceivableBillTransferMx.getSubBillStartSn()); // 子票区间起
                billEndorseDetail.put("SUBBILLENDSN", jfkjReceivableBillTransferMx.getSubBillEndSn()); // 子票区间止
                billEndorseDetail.put("AVAENDORSE", jfkjReceivableBillTransferMx.getAvaEndorse()); // 后手可转让标记
                billEndorseDetail.put("SPLITFLAG", jfkjReceivableBillTransferMx.getSplitFlag()); // 可分包流转的标志
                billEndorseDetail.put("NEWBILLFLAG", jfkjReceivableBillTransferMx.getNewBillFlag()); // 新一代票据
                billEndorseDetail.put("USEAMOUNT", jfkjReceivableBillTransferMx.getEndorsementAmount()); // 本次背书金额
                BillEndorseDetailList.add(billEndorseDetail);
                billValue.put("generaBillEndorseDetailList", BillEndorseDetailList);
                Map<String, Object> TMBILLENDORSEREQUEST = new HashMap<>();

                List<Map<String, Object>> TMBILLENDORSEREQUESTlist = new ArrayList<>();
                TMBILLENDORSEREQUESTlist.add(billValue);

                TMBILLENDORSEREQUEST.put("TMBILLENDORSEREQUEST", TMBILLENDORSEREQUESTlist);

                params.put("params",TMBILLENDORSEREQUEST);

                logService.info("executeWF", "调用RPC入参："+params.toString());
                if (StringUtils.isEmpty(jfkjReceivableBillTransferMx.getApplicationID())){
                    String invokeresult = rpcClient.invoke(String.class,
                            "com.inspur.gs.tm.bm.bmbillreceipt.core.service.BMBillReceivableRPCService.generateBillEndorseRequest",
                            "BM",
                            params,
                            null
                    );
                    logService.info("executeWF", "调用RPC返回值："+invokeresult.toString());
                    Map invoke = JSON.parseObject(invokeresult, Map.class);
                    logService.info("executeWF", "调用RPC返回值转化后："+invoke.toString());
                    if (null!=invoke && "1".equals(invoke.get("RET_CODE"))){



                        List<Map<String, String>> ret_body = (List<Map<String, String>>) invoke.get("RET_BODY");
                        jfkjReceivableBillTransferMx.setApplicationNo(ret_body.get(0).get("DOCNO"));
                        jfkjReceivableBillTransferMx.setApplicationID(ret_body.get(0).get("DOC_ID"));
                        jfkjrecebilltransfermxRep.save(jfkjReceivableBillTransferMx);
                    *//*if ("4".equals(jfkjReceivableBillTransferMx.getEndorsementType())){
                        String updateSql = "update  set DOCTYPE='4' where id = '"+ret_body.get(0).get("DOC_ID")+"'";
                        Query nativeQuery = entityManager.createNativeQuery(updateSql);
                        int i = nativeQuery.executeUpdate();
                    }*//*

                    }
                }else {
                    logService.info("executeWF", "已经生成背书申请单："+jfkjReceivableBillTransferMx.getApplicationNo());
                }


            }
            //提交事务，保存生成的背书申请
            transaction.commit();

            //重新获取转承信息
            Optional<JFKJRECEIVABLEBILLTRANSFER> optional = jfkjreceivablebilltransferRep.findById(event.dataId);
            JFKJRECEIVABLEBILLTRANSFER jfkjreceivablebilltransfer1 = optional.get();
            List<JFKJRECEBILLTRANSFERMX> jfkjReceivableBillTransferMxList1 = jfkjreceivablebilltransfer1.getJfkjReceivableBillTransferMxList();
            for (JFKJRECEBILLTRANSFERMX jfkjReceivableBillTransferMx : jfkjReceivableBillTransferMxList1) {
                Map<String, Object> map = new HashMap<>();
                map.put("LYDJLX", "BM_ZGBS"); //业务类型：BM_ZGBS，BM_TGBS
                map.put("FORMTYPE", "BM_ZGBS"); //业务类型：BM_ZGBS，BM_TGBS
                map.put("BILLID",jfkjReceivableBillTransferMx.getApplicationID() ); //单据ID
//                    logService.info( DOCID,"自动提交-入参：" + JSONSerializer.serialize(map));
                LinkedHashMap<String, Object> subParams = new LinkedHashMap<>();
                subParams.put("map", map);
*//*
                    RpcClient rpcClient = SpringBeanUtils.getBean(RpcClient.class);
*//*
                String fsspResultRet = rpcClient.invoke(String.class,
                        "com.inspur.gs.tm.bm.bmbillreceipt.core.service.BMBillReceivableRPCService.SubmitBillEndorseRequest",
                        "BM", subParams, null);
                logService.info( "executeWF","自动提交-返回：" + fsspResultRet);
            }



            WFExecuteEvent.super.pass(event);
        }catch (Throwable e){
            transaction.rollback();
            logService.flush();
            logService.error("executeWF","自动提交-异常：" + e.getMessage());

        }*/


    }
}
