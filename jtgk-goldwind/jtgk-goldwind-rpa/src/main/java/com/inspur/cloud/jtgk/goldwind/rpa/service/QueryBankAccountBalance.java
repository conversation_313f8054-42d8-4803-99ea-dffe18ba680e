package com.inspur.cloud.jtgk.goldwind.rpa.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.Feature;
import com.alibaba.fastjson.parser.ParserConfig;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.inspur.cloud.jtgk.goldwind.rpa.dto.*;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.*;
import com.inspur.cloud.jtgk.goldwind.rpa.utils.DataBase;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.commons.utils.Page;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.rpc.api.annotation.GspServiceBundle;
import io.iec.edp.caf.rpc.api.annotation.RpcParam;
import io.iec.edp.caf.rpc.api.annotation.RpcServiceMethod;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.query.SortQuery;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;


/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description 银行账户余额查询
 * @date 2025/3/4
 */
@Slf4j
@Service
public class QueryBankAccountBalance {

    private String internalBankAccount = "internalBankAccount";
    private String internalBankAccountHis = "internalBankAccountHis";
    private String extrernalBankAccount = "extrernalBankAccount";
    private String extrernalBankAccountHis = "extrernalBankAccountHis";

    private String queryTransDetails = "queryTransDetails";

    private String queryBankTransDetails="queryBankTransDetails";

    @Autowired
    private LogService logService;

    /**
     * 查询内部账户当日余额
     * 内部账户  指 开户行为  新疆金风财务公司的账户
     * 新疆金风财务公司 联行号为  CWGS_GFS_001
     * @param queryReq
     * @return
     */
    public AccountBalanceDto queryInternalBankAccountBalance(QueryReq queryReq){
        logService.init(internalBankAccount);
        logService.info(internalBankAccount,"查询内部账户当日余额入参："+JSONObject.toJSONString(queryReq));
        AccountBalanceDto accountBalanceDto = new AccountBalanceDto();
        try {

            //数据校验
            if ("".equals(queryReq.getOperationType())){
                logService.error(internalBankAccount,"操作类型为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("操作类型为空");
                return accountBalanceDto;
            }
            if ("".equals(queryReq.getSystemID())){
                logService.error(internalBankAccount,"系统标识为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("系统标识为空");
                return accountBalanceDto;
            }
            if ("".equals(queryReq.getSendTime())){
                logService.error(internalBankAccount,"发送时间不能为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("发送时间不能为空");
                return accountBalanceDto;
            }

            List<RenContentDto> trueRenContents = new ArrayList<>();//成功的记录
            List<QueryContentEntity> data = queryReq.getQueryContents();
            StringBuilder processDesc = new StringBuilder();

            for (QueryContentEntity queryContent : data){
                RenContentDto renContentDto = queryInternalBankAccountBalanceOne(queryContent,processDesc);
                if (renContentDto!=null){
                    trueRenContents.add(renContentDto);
                }
            }
            if (processDesc.length()>0){
                logService.error(internalBankAccount,"错误："+processDesc.toString());
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc(processDesc.toString());
            }else {
                logService.info(internalBankAccount,"成功");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("0000");
                accountBalanceDto.setRenContent(trueRenContents);
            }
        }catch (Throwable throwable){
            logService.error(internalBankAccount,"查询内部账户当日余额异常："+JSONObject.toJSONString(throwable));
            accountBalanceDto.setOperationType(queryReq.getOperationType());
            accountBalanceDto.setProcessCode("9999");
            accountBalanceDto.setProcessDesc("异常："+throwable.getMessage());
        }finally {
            logService.flush();
        }



       return accountBalanceDto;
    }

    /**
     * 查询内部账户历史每日余额
     * @param queryReq
     * @return
     */
    public HisAccountBalanceDto queryInternalBankAccountBalanceHis(QueryReq queryReq){
        HisAccountBalanceDto accountBalanceDto = new HisAccountBalanceDto();
        logService.init(internalBankAccountHis);
        logService.info(internalBankAccountHis,"查询内部账户历史每日余额入参："+JSONObject.toJSONString(queryReq));

        try {
            //数据校验
            if ("".equals(queryReq.getOperationType())){
                logService.error(internalBankAccountHis,"操作类型为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("操作类型为空");
                return accountBalanceDto;
            }
            if ("".equals(queryReq.getSystemID())){
                logService.error(internalBankAccountHis,"系统标识为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("系统标识为空");
                return accountBalanceDto;
            }
            if ("".equals(queryReq.getSendTime())){
                logService.error(internalBankAccountHis,"发送时间不能为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("发送时间不能为空");
                return accountBalanceDto;
            }
            if ("".equals(queryReq.getQueryContents().get(0).getAccountCode())){
                logService.error(internalBankAccountHis,"账户编码不能为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("账户编码不能为空");
                return accountBalanceDto;
            }
            if ("".equals(queryReq.getQueryContents().get(0).getQueryDateStart())){
                logService.error(internalBankAccountHis,"开始日期不能为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("开始日期不能为空");
                return accountBalanceDto;
            }
            if ("".equals(queryReq.getQueryContents().get(0).getQueryDateEnd())){
                logService.error(internalBankAccountHis,"结束日期不能为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("结束日期不能为空");
                return accountBalanceDto;
            }
            StringBuilder processDesc = new StringBuilder();
            List<HisRenContentDto> renContentDto = queryInternalBankAccountBalanceAll(queryReq.getQueryContents().get(0),processDesc);
            accountBalanceDto.setRenContent(renContentDto);
            if (processDesc.length()>0){
                logService.info(internalBankAccountHis,"错误："+processDesc.toString());
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc(processDesc.toString());
            }else {
                logService.info(internalBankAccountHis,"成功");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("0000");
                accountBalanceDto.setRenContent(renContentDto);
            }
        }catch (Throwable throwable){
            logService.error(internalBankAccountHis,"查询内部账户当日余额异常："+JSONObject.toJSONString(throwable));
            accountBalanceDto.setOperationType(queryReq.getOperationType());
            accountBalanceDto.setProcessCode("9999");
            accountBalanceDto.setProcessDesc("异常："+throwable.getMessage());
        }finally {
            logService.flush();
        }


        return accountBalanceDto;
    }

    public RenContentDto queryInternalBankAccountBalanceOne(QueryContentEntity data,StringBuilder processDesc){
        RenContentDto renContentDto = new RenContentDto();

        if ("".equals(data.getAccountNo())){
            processDesc.append("账户编码不能为空");
            return renContentDto;
        }

        String checkSql = "select \n" +
                "bfbank.BANKIDENTIFIER as bankidentifier\n" +
                "from BFBankAccounts\n" +
                "left join bfbank on bfbank.id= BFBankAccounts.BANK  " +
                "where BFBankAccounts.accountno= '"+data.getAccountNo()+"'";
        logService.info(internalBankAccount,"账户校验sql："+checkSql);

        Map<String, Object> checkResult = DataBase.queryOne(checkSql);

        logService.info(internalBankAccount,"账户校验结果："+JSONObject.toJSONString(checkResult));
        if (checkResult.size()>0){
            if (!"CWGS_GFS_001".equals(checkResult.get("bankidentifier"))){
                processDesc.append("该账户"+data.getAccountNo()+"不是内部账户");
                return renContentDto;
            }
        }else {
            processDesc.append("该账户"+data.getAccountNo()+"不存在");
            return renContentDto;
        }


        String querySql = "SELECT\n" +
                "\t\t\tBFBankAccounts.AccountNo AS accountno,\n" +
                "\t\t\tBPBalanceOnBank.BalanceDate AS balancedate,\n" +
                "\t\t\tBPBalanceOnBank.CurrentBalance AS currentbalance,\n" +
                "\t\t\tBPBalanceOnBank.CurrentBalanceHis AS currentbalancehis\n" +
                "\t\tFROM\n" +
                "\t\t\tBPBalanceOnBank\n" +
                "\t\tLEFT OUTER JOIN BFBankAccounts ON\n" +
                "\t\t\tBPBalanceOnBank.Account = BFBankAccounts.ID\n" +
                "\t\tWHERE BFBankAccounts.AccountNo='" + data.getAccountNo() + "' "+
                "\t\tORDER BY BPBalanceOnBank.BalanceDate DESC \n" +
                "\t\tLIMIT 1";
        logService.info(internalBankAccount,"查询内部账户当日余额sql："+querySql);
        Map<String, Object> resultMap = DataBase.queryOne(querySql);
        logService.info(internalBankAccount,"查询内部账户当日余额结果："+JSONObject.toJSONString(resultMap));

        if (resultMap.size()>0){
            renContentDto.setAccountNo(String.valueOf(resultMap.get("accountno")));
            if (resultMap.get("currentbalance")!=null){
                renContentDto.setBalance(String.valueOf(resultMap.get("currentbalance")));
            }
            if (resultMap.get("currentbalancehis")!=null){
                renContentDto.setREVERSE2(String.valueOf(resultMap.get("currentbalancehis")));
            }
        }
        return renContentDto;
    }

    public List<HisRenContentDto> queryInternalBankAccountBalanceAll(QueryContentEntity data, StringBuilder processDesc){

        List<HisRenContentDto> renContentDtos = new ArrayList<>();

        String checkSql = "select \n" +
                "bfbank.BANKIDENTIFIER as bankidentifier\n" +
                "from BFBankAccounts\n" +
                "left join bfbank on bfbank.id= BFBankAccounts.BANK "+
                "where BFBankAccounts.accountno= '"+data.getAccountCode()+"'";
        logService.info(internalBankAccountHis,"账户校验sql："+checkSql);
        Map<String, Object> checkResult = DataBase.queryOne(checkSql);
        logService.info(internalBankAccountHis,"账户校验结果："+JSONObject.toJSONString(checkResult));
        if (checkResult.size()>0){
            if (!"CWGS_GFS_001".equals(checkResult.get("bankidentifier"))){
                processDesc.append("该账户"+data.getAccountCode()+"不是内部账户;");
                return renContentDtos;
            }
        }else {
            processDesc.append("该账户"+data.getAccountCode()+"不存在;");
            return renContentDtos;
        }


        String querySql = "SELECT\n" +
                "BFBankAccounts.AccountNo AS AccountNo,\n" +
                "BPBalanceOnBank.BalanceDate AS BalanceDate,\n" +
                "BPBalanceOnBank.CurrentBalance AS CurrentBalance,\n" +
                "BPBalanceOnBank.CurrentBalanceHis AS CurrentBalanceHis\n" +
                "FROM\n" +
                "BPBalanceOnBank\n" +
                "LEFT OUTER JOIN BFBankAccounts ON\n" +
                "BPBalanceOnBank.Account = BFBankAccounts.ID\n" +
                "WHERE BFBankAccounts.AccountNo=? " +
                "and BPBalanceOnBank.BalanceDate>=? and BPBalanceOnBank.BalanceDate <= ? " +
                "ORDER BY BPBalanceOnBank.BalanceDate DESC " ;
        EntityManager manager = SpringBeanUtils.getBean(EntityManager.class);
        Query nativeQuery = manager.createNativeQuery(querySql);
        nativeQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        nativeQuery.setParameter(1,data.getAccountCode());
        nativeQuery.setParameter(2,data.getQueryDateStart());
        nativeQuery.setParameter(3,data.getQueryDateEnd());
        List<Map<String, Object>> resultList = nativeQuery.getResultList();
        logService.info(internalBankAccountHis,"查询内部账户历史余额结果："+JSONObject.toJSONString(resultList));


        for (Map<String, Object> map : resultList){
            HisRenContentDto renContentDto = new HisRenContentDto();
            renContentDto.setAccountNo(String.valueOf(map.get("accountno")));
            if (map.get("currentbalance")!=null){
                renContentDto.setBalance(String.valueOf(map.get("currentbalance")));
            }
            if (map.get("balancedate")!=null){
                /*Date balanceDate = null;
                try {
                    balanceDate = DateUtils.parseDate(String.valueOf(map.get("balancedate")), "yyyy-MM-dd");
                } catch (Throwable e) {

                }*/
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String dateStr = sdf.format(map.get("balancedate"));
                renContentDto.setBalanceDate(dateStr);
            }
            renContentDtos.add(renContentDto);
        }

        return renContentDtos;
    }


    /**
     *  查询银行账户当日余额
     * @param queryReq
     * @return
     */
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.rpa.service.QueryBankAccountBalance.queryExternalBankAccountBalance")
    public AccountBalanceDto queryExternalBankAccountBalance( QueryReq queryReq){
        logService.init(extrernalBankAccount);
        logService.info(extrernalBankAccount,"查询银行账户当日余额入参："+JSONObject.toJSONString(queryReq));
        AccountBalanceDto accountBalanceDto = new AccountBalanceDto();

        try {

            //数据校验
            if ("".equals(queryReq.getOperationType())){
                logService.error(extrernalBankAccount,"操作类型为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("操作类型为空");
                return accountBalanceDto;
            }
            if ("".equals(queryReq.getSystemID())){
                logService.error(extrernalBankAccount,"系统标识为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("系统标识为空");
                return accountBalanceDto;
            }
            if ("".equals(queryReq.getSendTime())){
                logService.error(extrernalBankAccount,"发送时间不能为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("发送时间不能为空");
                return accountBalanceDto;
            }

            List<RenContentDto> trueRenContents = new ArrayList<>();//成功的记录
            List<QueryContentEntity> data = queryReq.getQueryContents();
            StringBuilder processDesc = new StringBuilder();

            for (QueryContentEntity queryContent : data){
                RenContentDto renContentDto = queryExternalBankAccountBalanceOne(queryContent,processDesc);
                if (renContentDto!=null){
                    trueRenContents.add(renContentDto);
                }
            }
            if (processDesc.length()>0){
                logService.error(extrernalBankAccount,"查询银行账户当日余额失败："+processDesc.toString());
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc(processDesc.toString());
            }else {
                logService.info(extrernalBankAccount,"查询银行账户当日余额成功：");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("0000");
                accountBalanceDto.setRenContent(trueRenContents);
            }

        }catch (Throwable throwable){
            logService.error(extrernalBankAccount,"查询银行账户当日余额异常："+JSONObject.toJSONString(throwable));
            accountBalanceDto.setOperationType(queryReq.getOperationType());
            accountBalanceDto.setProcessCode("9999");
            accountBalanceDto.setProcessDesc("异常："+throwable.getMessage());
        }finally {
            logService.flush();
        }


        return accountBalanceDto;
    }

    /**
     * 查询银行账户历史余额
     * @param queryReq
     * @return
     */

    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.rpa.service.QueryBankAccountBalance.queryExternalBankAccountBalanceHis")
    public HisAccountBalanceDto queryExternalBankAccountBalanceHis( QueryReqHis queryReq){
        HisAccountBalanceDto accountBalanceDto = new HisAccountBalanceDto();
        logService.init(extrernalBankAccountHis);
        logService.info(extrernalBankAccountHis,"查询银行账户历史余额入参："+JSONObject.toJSONString(queryReq));

        try {

            //数据校验
            if ("".equals(queryReq.getOperationType())){
                logService.error(extrernalBankAccountHis,"操作类型为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("操作类型为空");
                return accountBalanceDto;
            }
            if ("".equals(queryReq.getSystemID())){
                logService.error(extrernalBankAccountHis,"系统标识为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("系统标识为空");
                return accountBalanceDto;
            }
            if ("".equals(queryReq.getSendTime())){
                logService.error(extrernalBankAccountHis,"发送时间不能为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("发送时间不能为空");
                return accountBalanceDto;
            }
            if ("".equals(queryReq.getQueryContent().getAccountCode())){
                logService.error(extrernalBankAccountHis,"账户编码不能为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("账户编码不能为空");
                return accountBalanceDto;
            }
            if ("".equals(queryReq.getQueryContent().getQueryDateStart())){
                logService.error(extrernalBankAccountHis,"开始日期不能为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("开始日期不能为空");
                return accountBalanceDto;
            }
            if ("".equals(queryReq.getQueryContent().getQueryDateEnd())){
                logService.error(extrernalBankAccountHis,"结束日期不能为空");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc("结束日期不能为空");
                return accountBalanceDto;
            }

            StringBuilder processDesc = new StringBuilder();
            List<HisRenContentDto> renContentDto = queryExternalBankAccountBalanceAll(queryReq.getQueryContent(),processDesc);

            accountBalanceDto.setRenContent(renContentDto);
            if (processDesc.length()>0){
                logService.info(extrernalBankAccountHis,"查询银行账户历史余额失败："+processDesc.toString());
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("9999");
                accountBalanceDto.setProcessDesc(processDesc.toString());
            }else {
                logService.info(extrernalBankAccountHis,"查询银行账户历史余额成功：");
                accountBalanceDto.setOperationType(queryReq.getOperationType());
                accountBalanceDto.setProcessCode("0000");
                accountBalanceDto.setRenContent(renContentDto);
            }

        }catch (Throwable throwable){
            logService.error(extrernalBankAccountHis,"查询银行账户历史余额异常："+JSONObject.toJSONString(throwable));
            accountBalanceDto.setOperationType(queryReq.getOperationType());
            accountBalanceDto.setProcessCode("9999");
            accountBalanceDto.setProcessDesc("异常："+throwable.getMessage());
        }finally {
            logService.flush();
        }

        return accountBalanceDto;
    }


    public RenContentDto queryExternalBankAccountBalanceOne(QueryContentEntity data,StringBuilder processDesc){
        RenContentDto renContentDto = new RenContentDto();

        if ("".equals(data.getAccountNo())){
            processDesc.append("账户编码不能为空");
            return renContentDto;
        }

        String checkSql = "select \n" +
                "bfbank.BANKIDENTIFIER as bankidentifier\n" +
                "from BFBankAccounts\n" +
                "left join bfbank on bfbank.id= BFBankAccounts.BANK  " +
                "where BFBankAccounts.accountno= '"+data.getAccountNo()+"'";
        logService.info(extrernalBankAccount,"账户检查sql："+checkSql);
        Map<String, Object> checkResult = DataBase.queryOne(checkSql);
        logService.info(extrernalBankAccount,"账户检查结果："+JSONObject.toJSONString(checkResult));
        if (checkResult.size()>0){
            if ("CWGS_GFS_001".equals(checkResult.get("bankidentifier"))){
                processDesc.append("该账户"+data.getAccountNo()+"是内部账户");
                return renContentDto;
            }
        }else {
            processDesc.append("该账户"+data.getAccountNo()+"不存在");
            return renContentDto;
        }


        String querySql = "SELECT\n" +
                "\t\t\tBFBankAccounts.AccountNo AS accountno,\n" +
                "\t\t\tBPBalanceOnBank.BalanceDate AS balancedate,\n" +
                "\t\t\tBPBalanceOnBank.CurrentBalance AS currentbalance,\n" +
                "\t\t\tBPBalanceOnBank.CurrentBalanceHis AS currentbalancehis,\n" +
                "\t\t\tBFBankAccounts.ONLINEBANKOPENSTATUS AS onlinebankopenstatus\n" +
                "\t\tFROM\n" +
                "\t\t\tBPBalanceOnBank\n" +
                "\t\tLEFT OUTER JOIN BFBankAccounts ON\n" +
                "\t\t\tBPBalanceOnBank.Account = BFBankAccounts.ID\n" +
                "\t\tWHERE BFBankAccounts.AccountNo='" + data.getAccountNo() + "' "+
                "\t\tORDER BY BPBalanceOnBank.BalanceDate DESC \n" +
                "\t\tLIMIT 1";
        logService.info(extrernalBankAccount,"查询银行账户当日余额sql："+querySql);
        Map<String, Object> resultMap = DataBase.queryOne(querySql);
        logService.info(extrernalBankAccount,"查询银行账户当日余额结果："+JSONObject.toJSONString(resultMap));
        if (resultMap.size()>0){
            renContentDto.setAccountNo(String.valueOf(resultMap.get("accountno")));
            if (resultMap.get("currentbalance")!=null){
                renContentDto.setBalance( String.valueOf(resultMap.get("currentbalance")));
            }
            if (resultMap.get("currentbalancehis")!=null){
                renContentDto.setREVERSE2(String.valueOf(resultMap.get("currentbalancehis")));
            }
            if ("1".equals(resultMap.get("onlinebankopenstatus"))){
                renContentDto.setREVERSE1("0");
            }else {
                renContentDto.setREVERSE1("1");
            }

        }
        return renContentDto;
    }


    public List<HisRenContentDto> queryExternalBankAccountBalanceAll(QueryContentHisEntity data, StringBuilder processDesc){

        List<HisRenContentDto> renContentDtos = new ArrayList<>();

        String checkSql = "select \n" +
                "bfbank.BANKIDENTIFIER as bankidentifier\n" +
                "from BFBankAccounts\n" +
                "left join bfbank on bfbank.id= BFBankAccounts.BANK "+
                "where BFBankAccounts.accountno= '"+data.getAccountCode()+"'";;
        logService.info(extrernalBankAccountHis,"账户检查sql："+checkSql);
        Map<String, Object> checkResult = DataBase.queryOne(checkSql);
        logService.info(extrernalBankAccountHis,"账户检查结果："+JSONObject.toJSONString(checkResult));
        if (checkResult.size()>0){
            if ("CWGS_GFS_001".equals(checkResult.get("bankidentifier"))){
                processDesc.append("该账户"+data.getAccountCode()+"是内部账户;");
                return renContentDtos;
            }
        }else {
            processDesc.append("该账户"+data.getAccountCode()+"不存在;");
            return renContentDtos;
        }

        String querySql = "SELECT\n" +
                "BFBankAccounts.AccountNo AS AccountNo,\n" +
                "BPBalanceOnBank.BalanceDate AS BalanceDate,\n" +
                "BPBalanceOnBank.CurrentBalance AS CurrentBalance,\n" +
                "BPBalanceOnBank.CurrentBalanceHis AS CurrentBalanceHis\n" +
                "FROM\n" +
                "BPBalanceOnBank\n" +
                "LEFT OUTER JOIN BFBankAccounts ON\n" +
                "BPBalanceOnBank.Account = BFBankAccounts.ID\n" +
                "WHERE BFBankAccounts.AccountNo=? " +
                "and BPBalanceOnBank.BalanceDate>=? and BPBalanceOnBank.BalanceDate <= ? " +
                "ORDER BY BPBalanceOnBank.BalanceDate DESC " ;
        EntityManager manager = SpringBeanUtils.getBean(EntityManager.class);
        Query nativeQuery = manager.createNativeQuery(querySql);
        nativeQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        nativeQuery.setParameter(1,data.getAccountCode());
        nativeQuery.setParameter(2,data.getQueryDateStart());
        nativeQuery.setParameter(3,data.getQueryDateEnd());
        List<Map<String, Object>> resultList = nativeQuery.getResultList();
        logService.info(extrernalBankAccountHis,"查询银行账户历史余额结果："+JSONObject.toJSONString(resultList));


        for (Map<String, Object> map : resultList){
            HisRenContentDto renContentDto = new HisRenContentDto();
            renContentDto.setAccountNo(String.valueOf(map.get("accountno")));
            if (map.get("currentbalance")!=null){
                renContentDto.setBalance( String.valueOf(map.get("currentbalance")));
            }
            if (map.get("balancedate")!=null){
                /*Date balanceDate = null;
                try {
                    balanceDate = DateUtils.parseDate(String.valueOf(map.get("balancedate")), "yyyy-MM-dd");
                } catch (Throwable e) {

                }*/
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                String dateStr = sdf.format(map.get("balancedate"));
                renContentDto.setBalanceDate(dateStr);
            }
            renContentDtos.add(renContentDto);
        }

        return renContentDtos;
    }




    // 查询内部账户交易流水
    public TransDetailsQueryRenDto queryTransDetails(QueryTransDetailsReq queryReq) {
        TransDetailsQueryRenDto transDetailsQueryRen = new TransDetailsQueryRenDto();
        transDetailsQueryRen.setQueryContent(queryReq.getQueryContent());
        transDetailsQueryRen.setQueryContent(queryReq.getQueryContent());
        transDetailsQueryRen.setOperationType(queryReq.getOperationType());
        transDetailsQueryRen.setProcessCode("0000");
        transDetailsQueryRen.setProcessDesc("");


        logService.init(queryTransDetails);
        logService.info(queryTransDetails,"------>>>查询账户交易明细入参："+JSONObject.toJSONString(queryReq));
        List<TransDetailsRenContent> renContentDtos = new ArrayList<>();
        transDetailsQueryRen.setRenContent(renContentDtos);
        try {

            if ("".equals(queryReq.getOperationType())){
                logService.error(queryTransDetails,"操作类型为空");
                transDetailsQueryRen.setProcessCode("9999");
                transDetailsQueryRen.setProcessDesc("操作类型为空");
                return transDetailsQueryRen;
            }
            if ("".equals(queryReq.getSystemID())){
                logService.error(queryTransDetails,"系统标识为空");
                transDetailsQueryRen.setProcessCode("9999");
                transDetailsQueryRen.setProcessDesc("系统标识为空");
                return transDetailsQueryRen;
            }
            if ("".equals(queryReq.getSendTime())){
                logService.error(queryTransDetails,"发送时间不能为空");
                transDetailsQueryRen.setProcessCode("9999");
                transDetailsQueryRen.setProcessDesc("发送时间不能为空");
                return transDetailsQueryRen;
            }
            if (queryReq.getQueryContent()==null){
                logService.error(queryTransDetails,"查询条件不能为空");
                transDetailsQueryRen.setProcessCode("9999");
                transDetailsQueryRen.setProcessDesc("查询条件不能为空");
                return transDetailsQueryRen;
            }
            if (queryReq.getQueryContent().getAccountNo()!=null && "".equals(queryReq.getQueryContent().getAccountNo())){
                logService.error(queryTransDetails,"查询条件账户不能为空");
                transDetailsQueryRen.setProcessCode("9999");
                transDetailsQueryRen.setProcessDesc("查询条件账户不能为空");
                return transDetailsQueryRen;
            }
            if (queryReq.getQueryContent().getExcuteDateStart()!=null && "".equals(queryReq.getQueryContent().getExcuteDateStart())){
                logService.error(queryTransDetails,"查询条件开始日期不能为空");
                transDetailsQueryRen.setProcessCode("9999");
                transDetailsQueryRen.setProcessDesc("查询条件开始日期不能为空");
                return transDetailsQueryRen;
            }
            if (queryReq.getQueryContent().getExcuteDateEnd()!=null && "".equals(queryReq.getQueryContent().getExcuteDateEnd())){
                logService.error(queryTransDetails,"查询条件结束日期不能为空");
                transDetailsQueryRen.setProcessCode("9999");
                transDetailsQueryRen.setProcessDesc("查询条件结束日期不能为空");
                return transDetailsQueryRen;
            }

            //先计算分页信息
            PageBean pageBean = queryTransDetailsPage(queryReq.getQueryContent(),queryTransDetails);
            int start = (pageBean.getCurrentPage()-1)*pageBean.getPageSize();
            int pageEnd = pageBean.getPageSize();
            if (pageBean.getNextPage()!=null && pageBean.getNextPage()!=0){
                transDetailsQueryRen.setQueryNextPage(String.valueOf(pageBean.getNextPage()));
            }else {
                transDetailsQueryRen.setQueryNextPage("");
            }
            transDetailsQueryRen.setCurCount(String.valueOf(pageBean.getCount()));
            transDetailsQueryRen.setTotalCount(String.valueOf(pageBean.getTotal()));

            logService.info(queryTransDetails,"------>>>查询账户交易明细分页信息："+JSONObject.toJSONString(pageBean));


            //根据分页信息查询数据
            StringBuilder querySql = new StringBuilder();
            querySql.append("select \n" +
                    "BPSETTLEMENTANDRECEIPT.id as reverse1,\n" +
                    "BPBANKTRANSCATIONDETAILS.TXT03 as reverse2,\n" +
                    "BPBANKTRANSCATIONDETAILS.TXT04 as reverse3,\n" +
                    "BPBANKTRANSCATIONDETAILS.bankflowno as transno,\n" +
                    "BPSETTLEMENTANDRECEIPT.DOCNO as applycode,\n" +
                    "BPBANKTRANSCATIONDETAILS.TRANSACTIONDATE as excudate,\n" +
                    "bfbankaccounts.accountno as accountno,\n" +
                    "bfbankaccounts.accountname_chs as accountname,\n" +
                    "BPBANKTRANSCATIONDETAILS.RECIPROCALACCOUNTNO as oppaccountno,\n" +
                    "BPBANKTRANSCATIONDETAILS.RECIPROCALACCNAME as oppaccountname,\n" +
                    "BPBANKTRANSCATIONDETAILS.SUMMARY as abstractcontent,\n" +
                    "BPBANKTRANSCATIONDETAILS.INCOMEOREXPENDITURE as transdirection,\n" +
                    "BPBANKTRANSCATIONDETAILS.SETTLEMENTAMOUNT as amount --金额\n" +
                    "from BPBANKTRANSCATIONDETAILS \n" +
                    "left join BPSETTLEMENTANDRECEIPT on BPSETTLEMENTANDRECEIPT.BANKTRANSID = BPBANKTRANSCATIONDETAILS.id\n" +
                    "left join bfbankaccounts on bfbankaccounts.id = BPBANKTRANSCATIONDETAILS.BANKACCOUNT");
            querySql.append(" where BPBANKTRANSCATIONDETAILS.TRANSACTIONDATE >= '").append(queryReq.getQueryContent().getExcuteDateStart()).append("'");
            querySql.append(" and BPBANKTRANSCATIONDETAILS.TRANSACTIONDATE <= '").append(queryReq.getQueryContent().getExcuteDateEnd()).append("'");
            querySql.append(" and BPBANKTRANSCATIONDETAILS.BANKACCOUNTNO = '").append(queryReq.getQueryContent().getAccountNo()).append("'");
            querySql.append(" limit ").append(pageEnd).append(" OFFSET ").append(start);
            logService.info(queryTransDetails,"查询账户交易明细SQL："+querySql.toString());
            List<Map<String, Object>> mapList = DataBase.queryList(querySql.toString());
            logService.info(queryTransDetails,"查询账户交易明细结果："+JSONObject.toJSONString(mapList));

            String jsonString = JSON.toJSONString(mapList,
                    SerializerFeature.WriteDateUseDateFormat  // 启用日期格式化
            );
            logService.info(queryTransDetails,"查询账户交易明细结果1："+jsonString);
            // 设置全局日期格式（可选，默认格式为 yyyy-MM-dd HH:mm:ss）
            JSON.DEFFAULT_DATE_FORMAT = "yyyy-MM-dd";  // 覆盖默认格式

            logService.info(queryTransDetails,"查询账户交易明细结果2："+jsonString);
            renContentDtos = JSON.parseArray(jsonString, TransDetailsRenContent.class);
            logService.info(queryTransDetails,"查询账户交易明细结果3："+JSONObject.toJSONString(renContentDtos));


            transDetailsQueryRen.setRenContent(renContentDtos);
            logService.info(queryTransDetails,"返回结果："+JSONObject.toJSONString(transDetailsQueryRen));
        }catch (Throwable throwable){
            logService.error(queryTransDetails,"查询账户交易明细异常："+throwable.getMessage());
            logService.error(queryTransDetails,"查询账户交易明细异常："+JSONObject.toJSONString(throwable));
            transDetailsQueryRen.setProcessCode("9999");
            transDetailsQueryRen.setProcessDesc("查询账户交易明细异常:"+throwable.getMessage());
            log.error("查询账户交易明细接口异常",throwable);
        }finally {
            logService.flush();
        }

        return transDetailsQueryRen;

    }


    //查询银行账户交易流水
    public BankTransDetailsQueryRenDto queryBankTransDetails( BankQueryTransDetailsReq queryReq) {
        BankTransDetailsQueryRenDto transDetailsQueryRen = new BankTransDetailsQueryRenDto();
        transDetailsQueryRen.setQueryContent(queryReq.getQueryContent());
        transDetailsQueryRen.setOperationType(queryReq.getOperationType());
        transDetailsQueryRen.setProcessCode("0000");
        transDetailsQueryRen.setProcessDesc("");


        String logCode = "queryBankTransDetails";
        logService.init(queryBankTransDetails);
        logService.info(queryBankTransDetails,"------>>>查询账户交易明细入参："+JSONObject.toJSONString(queryReq));
        List<BankTransDetailsRenContent> renContentDtos = new ArrayList<>();
        transDetailsQueryRen.setRenContent(renContentDtos);

        try {
            if ("".equals(queryReq.getOperationType())){
                logService.error(queryBankTransDetails,"操作类型为空");
                transDetailsQueryRen.setProcessCode("9999");
                transDetailsQueryRen.setProcessDesc("操作类型为空");
                return transDetailsQueryRen;
            }
            if ("".equals(queryReq.getSystemID())){
                logService.error(queryBankTransDetails,"系统标识为空");
                transDetailsQueryRen.setProcessCode("9999");
                transDetailsQueryRen.setProcessDesc("系统标识为空");
                return transDetailsQueryRen;
            }
            if ("".equals(queryReq.getSendTime())){
                logService.error(queryBankTransDetails,"发送时间不能为空");
                transDetailsQueryRen.setProcessCode("9999");
                transDetailsQueryRen.setProcessDesc("发送时间不能为空");
                return transDetailsQueryRen;
            }
            if (queryReq.getQueryContent()==null){
                logService.error(queryBankTransDetails,"查询条件不能为空");
                transDetailsQueryRen.setProcessCode("9999");
                transDetailsQueryRen.setProcessDesc("查询条件不能为空");
                return transDetailsQueryRen;
            }
            if (queryReq.getQueryContent().getAccountNo()!=null && "".equals(queryReq.getQueryContent().getAccountNo())){
                logService.error(queryBankTransDetails,"查询条件账户不能为空");
                transDetailsQueryRen.setProcessCode("9999");
                transDetailsQueryRen.setProcessDesc("查询条件账户不能为空");
                return transDetailsQueryRen;
            }
            if (queryReq.getQueryContent().getExcuteDateStart()!=null && "".equals(queryReq.getQueryContent().getExcuteDateStart())){
                logService.error(queryBankTransDetails,"查询条件开始日期不能为空");
                transDetailsQueryRen.setProcessCode("9999");
                transDetailsQueryRen.setProcessDesc("查询条件开始日期不能为空");
                return transDetailsQueryRen;
            }
            if (queryReq.getQueryContent().getExcuteDateEnd()!=null && "".equals(queryReq.getQueryContent().getExcuteDateEnd())){
                logService.error(queryBankTransDetails,"查询条件结束日期不能为空");
                transDetailsQueryRen.setProcessCode("9999");
                transDetailsQueryRen.setProcessDesc("查询条件结束日期不能为空");
                return transDetailsQueryRen;
            }
            //先计算分页信息
            PageBean pageBean = queryTransDetailsBankPage(queryReq.getQueryContent(),queryBankTransDetails);
            if (pageBean==null){
                transDetailsQueryRen.setProcessCode("9999");
                transDetailsQueryRen.setProcessDesc("分页信息处理失败！");
                return transDetailsQueryRen;
            }
            int start = (pageBean.getCurrentPage()-1)*pageBean.getPageSize();
            int pageEnd = pageBean.getPageSize();

            if (pageBean.getNextPage()!=null && pageBean.getNextPage()!=0){
                transDetailsQueryRen.setQueryNextPage(String.valueOf(pageBean.getNextPage()));
            }else {
                transDetailsQueryRen.setQueryNextPage("");
            }
            transDetailsQueryRen.setCurCount(String.valueOf(pageBean.getCount()));
            transDetailsQueryRen.setTotalCount(String.valueOf(pageBean.getTotal()));

            logService.info(queryBankTransDetails,"------>>>查询账户交易明细分页信息："+JSONObject.toJSONString(pageBean));

            //根据分页信息查询数据
            StringBuilder querySql = new StringBuilder();
            querySql.append("select \n" +
                    "BPSETTLEMENTANDRECEIPT.id as reverse1,\n" +
                    "BPBANKTRANSCATIONDETAILS.TXT03 as reverse2,\n" +
                    "BPBANKTRANSCATIONDETAILS.TXT04 as reverse3,\n" +
                    "BPBANKTRANSCATIONDETAILS.BANKRECEIPTID as banktransno,\n" +
                    "BPBANKTRANSCATIONDETAILS.bankflowno as transno,\n" +
                    "BPSETTLEMENTANDRECEIPT.DOCNO as applycode,\n" +
                    "BPBANKTRANSCATIONDETAILS.TRANSACTIONDATE as excudate,\n" +
                    "bfbankaccounts.accountno as accountno,\n" +
                    "bfbankaccounts.accountname_chs as accountname,\n" +
                    "BPBANKTRANSCATIONDETAILS.RECIPROCALACCOUNTNO as oppaccountno,\n" +
                    "BPBANKTRANSCATIONDETAILS.RECIPROCALACCNAME as oppaccountname,\n" +
                    "BPBANKTRANSCATIONDETAILS.SUMMARY as abstractcontent,\n" +
                    "BPBANKTRANSCATIONDETAILS.INCOMEOREXPENDITURE as transdirection,\n" +
                    "BPBANKTRANSCATIONDETAILS.SETTLEMENTAMOUNT as amount \n" +
                    "from BPBANKTRANSCATIONDETAILS \n" +
                    "left join BPSETTLEMENTANDRECEIPT on BPSETTLEMENTANDRECEIPT.BANKTRANSID = BPBANKTRANSCATIONDETAILS.id\n" +
                    "left join bfbankaccounts on bfbankaccounts.id = BPBANKTRANSCATIONDETAILS.BANKACCOUNT");
            querySql.append(" where BPBANKTRANSCATIONDETAILS.TRANSACTIONDATE >= '").append(queryReq.getQueryContent().getExcuteDateStart()).append("'");
            querySql.append(" and BPBANKTRANSCATIONDETAILS.TRANSACTIONDATE <= '").append(queryReq.getQueryContent().getExcuteDateEnd()).append("'");
            querySql.append(" and BPBANKTRANSCATIONDETAILS.BANKACCOUNTNO = '").append(queryReq.getQueryContent().getAccountNo()).append("'");
            querySql.append(" limit ").append(pageEnd).append(" OFFSET ").append(start);
            logService.info(queryBankTransDetails,"查询账户交易明细SQL："+querySql.toString());
            List<Map<String, Object>> mapList = DataBase.queryList(querySql.toString());
            logService.info(queryBankTransDetails,"查询账户交易明细结果："+JSONObject.toJSONString(mapList));

            String jsonString = JSON.toJSONString(mapList,
                    SerializerFeature.WriteDateUseDateFormat  // 启用日期格式化
            );
            logService.info(queryBankTransDetails,"查询账户交易明细结果1："+jsonString);

            // 设置全局日期格式（可选，默认格式为 yyyy-MM-dd HH:mm:ss）
            JSON.DEFFAULT_DATE_FORMAT = "yyyy-MM-dd";  // 覆盖默认格式

            logService.info(queryBankTransDetails,"查询账户交易明细结果2："+jsonString);
            renContentDtos = JSON.parseArray(jsonString, BankTransDetailsRenContent.class);
            logService.info(queryBankTransDetails,"查询账户交易明细结果3："+JSONObject.toJSONString(renContentDtos));
            transDetailsQueryRen.setRenContent(renContentDtos);
            logService.info(queryBankTransDetails,"返回结果："+JSONObject.toJSONString(transDetailsQueryRen));
        }catch (Throwable throwable){
            logService.error(queryBankTransDetails,"查询账户交易明细异常："+throwable.getMessage());
            logService.error(queryBankTransDetails,"查询账户交易明细异常明细："+JSONObject.toJSONString(throwable));
            transDetailsQueryRen.setProcessCode("9999");
            transDetailsQueryRen.setProcessDesc("查询账户交易明细异常:"+throwable.getMessage());
            log.error("查询账户交易明细接口异常",throwable);
        }finally {
            logService.flush();
        }

        return transDetailsQueryRen;

    }

    public PageBean queryTransDetailsPage(TransDetailQueryContent content,String logCode){
        PageBean pageBean = new PageBean();

        try {
            Integer queryPageNumber = 0;

            if ("".equals(content.getQueryPageNumber())){
                queryPageNumber=1;
            }else {
                queryPageNumber = Integer.parseInt(content.getQueryPageNumber());
            }
            logService.info(logCode,"查询账户交易明细分页信息入参："+JSONObject.toJSONString(content));
            int count = 0;
            StringBuilder countSql = new StringBuilder("select count(1) as coun from BPBANKTRANSCATIONDETAILS ");
            countSql.append(" where BPBANKTRANSCATIONDETAILS.TRANSACTIONDATE >= '").append(content.getExcuteDateStart()).append("'");
            countSql.append(" and BPBANKTRANSCATIONDETAILS.TRANSACTIONDATE <= '").append(content.getExcuteDateEnd()).append("'");
            countSql.append(" and BPBANKTRANSCATIONDETAILS.BANKACCOUNTNO = '").append(content.getAccountNo()).append("'");

            logService.info(logCode,"查询账户交易明细分页信息SQL："+countSql.toString());
            Map<String, Object> stringObjectMap = DataBase.queryOne(countSql.toString());
            logService.info(logCode,"查询账户交易明细分页信息结果："+JSONObject.toJSONString(stringObjectMap));
            if (stringObjectMap.size()>0){
                count=Integer.parseInt(String.valueOf(stringObjectMap.get("coun")));
            }
            pageBean.setCurrentPage(queryPageNumber);
            pageBean.setPageSize(1000);
            pageBean.setTotal(count);
            logService.info(logCode,"分页信息计算入参："+JSONObject.toJSONString(pageBean));
            pageBean.setCount(count%pageBean.getPageSize()==0?count/pageBean.getPageSize():count/pageBean.getPageSize()+1);
            logService.info(logCode,"分页信息计算入参："+JSONObject.toJSONString(pageBean));
            if (pageBean.getCurrentPage()<pageBean.getCount()){
                pageBean.setNextPage(pageBean.getCurrentPage()+1);
            }
            //pageBean.setNextPage(pageBean.getCurrentPage()<pageBean.getCount()?pageBean.getCurrentPage()+1:null);
            logService.info(logCode,"分页信息计算结果："+JSONObject.toJSONString(pageBean));
            int start = (pageBean.getCurrentPage()-1)*pageBean.getPageSize();
            int pageEnd = pageBean.getPageSize();

            StringBuilder querySql = new StringBuilder();
            querySql.append("select count(1) coun \n" +
                    "from BPBANKTRANSCATIONDETAILS \n" +
                    "left join BPSETTLEMENTANDRECEIPT on BPSETTLEMENTANDRECEIPT.BANKTRANSID = BPBANKTRANSCATIONDETAILS.id\n" +
                    "left join bfbankaccounts on bfbankaccounts.id = BPBANKTRANSCATIONDETAILS.BANKACCOUNT");
            querySql.append(" where BPBANKTRANSCATIONDETAILS.TRANSACTIONDATE >= '").append(content.getExcuteDateStart()).append("'");
            querySql.append(" and BPBANKTRANSCATIONDETAILS.TRANSACTIONDATE <= '").append(content.getExcuteDateEnd()).append("'");
            querySql.append(" and BPBANKTRANSCATIONDETAILS.BANKACCOUNTNO = '").append(content.getAccountNo()).append("'");
            querySql.append(" limit ").append(pageEnd).append(" OFFSET ").append(start);
            logService.info(logCode,"查询账户交易明细当前页条数SQL："+querySql.toString());
            Map<String, Object> stringObjectMap1 = DataBase.queryOne(querySql.toString());
            logService.info(logCode,"查询账户交易明细当前页条数结果："+JSONObject.toJSONString(stringObjectMap1));
            if (stringObjectMap1.size()>0){
                pageBean.setCount(Integer.parseInt(String.valueOf(stringObjectMap1.get("coun"))));
            }
        }catch (Throwable throwable){
            logService.error(logCode,"查询账户交易明细分页信息异常："+throwable.getMessage());
            logService.error(logCode,"查询账户交易明细分页信息异常："+JSONObject.toJSONString(throwable));
            return null;
        }
        return pageBean;
    }

    public PageBean queryTransDetailsBankPage(BankTransDetailQueryContent content,String logCode){
        PageBean pageBean = new PageBean();

        try {
            Integer queryPageNumber = 0;

            if ("".equals(content.getQueryPageNumber())){
                queryPageNumber=1;
            }else {
                queryPageNumber = Integer.parseInt(content.getQueryPageNumber());
            }
            logService.info(logCode,"查询账户交易明细分页信息入参："+JSONObject.toJSONString(content));
            int count = 0;
            StringBuilder countSql = new StringBuilder("select count(1) as coun from BPBANKTRANSCATIONDETAILS ");
            countSql.append(" where BPBANKTRANSCATIONDETAILS.TRANSACTIONDATE >= '").append(content.getExcuteDateStart()).append("'");
            countSql.append(" and BPBANKTRANSCATIONDETAILS.TRANSACTIONDATE <= '").append(content.getExcuteDateEnd()).append("'");
            countSql.append(" and BPBANKTRANSCATIONDETAILS.BANKACCOUNTNO = '").append(content.getAccountNo()).append("'");

            logService.info(logCode,"查询账户交易明细分页信息SQL："+countSql.toString());
            Map<String, Object> stringObjectMap = DataBase.queryOne(countSql.toString());
            logService.info(logCode,"查询账户交易明细分页信息结果："+JSONObject.toJSONString(stringObjectMap));
            if (stringObjectMap.size()>0){
                count=Integer.parseInt(String.valueOf(stringObjectMap.get("coun")));
            }
            pageBean.setCurrentPage(queryPageNumber);
            pageBean.setPageSize(1000);
            pageBean.setTotal(count);
            pageBean.setCount(count%pageBean.getPageSize()==0?count/pageBean.getPageSize():count/pageBean.getPageSize()+1);
            if (pageBean.getCurrentPage()<pageBean.getCount()){
                pageBean.setNextPage(pageBean.getCurrentPage()+1);
            }
            //pageBean.setNextPage(pageBean.getCurrentPage()<pageBean.getCount()?pageBean.getCurrentPage()+1:null);
            logService.info(logCode,"分页信息计算结果："+JSONObject.toJSONString(pageBean));
            int start = (pageBean.getCurrentPage()-1)*pageBean.getPageSize();
            int pageEnd = pageBean.getPageSize();

            StringBuilder querySql = new StringBuilder();
            querySql.append("select count(1) coun \n" +
                    "from BPBANKTRANSCATIONDETAILS \n" +
                    "left join BPSETTLEMENTANDRECEIPT on BPSETTLEMENTANDRECEIPT.BANKTRANSID = BPBANKTRANSCATIONDETAILS.id\n" +
                    "left join bfbankaccounts on bfbankaccounts.id = BPBANKTRANSCATIONDETAILS.BANKACCOUNT");
            querySql.append(" where BPBANKTRANSCATIONDETAILS.TRANSACTIONDATE >= '").append(content.getExcuteDateStart()).append("'");
            querySql.append(" and BPBANKTRANSCATIONDETAILS.TRANSACTIONDATE <= '").append(content.getExcuteDateEnd()).append("'");
            querySql.append(" and BPBANKTRANSCATIONDETAILS.BANKACCOUNTNO = '").append(content.getAccountNo()).append("'");
            querySql.append(" limit ").append(pageEnd).append(" OFFSET ").append(start);
            logService.info(logCode,"查询账户交易明细当前页条数SQL："+querySql.toString());
            Map<String, Object> stringObjectMap1 = DataBase.queryOne(querySql.toString());
            logService.info(logCode,"查询账户交易明细当前页条数结果："+JSONObject.toJSONString(stringObjectMap1));
            if (stringObjectMap1.size()>0){
                pageBean.setCount(Integer.parseInt(String.valueOf(stringObjectMap1.get("coun"))));
            }
        }catch (Throwable throwable){
            logService.error(logCode,"查询账户交易明细分页信息异常："+throwable.getMessage());
            logService.error(logCode,"查询账户交易明细分页信息异常："+JSONObject.toJSONString(throwable));
            return null;
        }
        return pageBean;
    }








}
