package com.inspur.cloud.jtgk.goldwind.rpa.service;

import com.alibaba.fastjson.JSONObject;
import com.inspur.cloud.jtgk.goldwind.rpa.dto.ResponseDto;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.JfskOfflineTransDetailsEntity;
import com.fasterxml.jackson.core.type.TypeReference;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.RPABANKTRANSCATIONDETAILSEntity;
import com.inspur.cloud.jtgk.goldwind.rpa.repository.RpaBankTranscationDetailsRepository;
import com.inspur.cloud.jtgk.goldwind.rpa.utils.DataBase;
import com.inspur.edp.internalservice.api.proxy.InternalServiceProxy;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.commons.transaction.JpaTransaction;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.commons.utils.StringUtils;
import io.iec.edp.caf.rpc.api.annotation.GspServiceBundle;
import io.iec.edp.caf.rpc.api.annotation.RpcParam;
import io.iec.edp.caf.rpc.api.annotation.RpcServiceMethod;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/2/24
 */

@Slf4j
@Service
@GspServiceBundle(applicationName = "jtgk",serviceUnitName = "AM",serviceName = "RpaToGtsBankFlowRpc")
public class RpaToGtsBankFlowRpc {


    private String logCode="offLineTransDetails";

    /**
     * 离线账户交易明细生单
     * return String errMsg:空字符串或者报错信息
     */
    @RpcServiceMethod(serviceId = "com.inspur.cloud.jtgk.goldwind.rpa.service.RpaToGtsBankFlowRpc.offlineTransDetailsRev")
    public ResponseDto offlineTransDetailsRev(@RpcParam(paramName = "data") List<RPABANKTRANSCATIONDETAILSEntity> data){

        LogService logService = SpringBeanUtils.getBean(LogService.class);
        logService.init(logCode);
        logService.info(logCode,"------>>>账户交易明细入参："+JSONObject.toJSONString(data));
        ResponseDto responseDto = new ResponseDto();
        responseDto.setType("S");
        JpaTransaction transaction = JpaTransaction.getTransaction();
        transaction.begin();

        for (RPABANKTRANSCATIONDETAILSEntity data1 : data){

            try {
                RpaBankTranscationDetailsRepository rpaBank = SpringBeanUtils.getBean(RpaBankTranscationDetailsRepository.class);
                logService.info(logCode,"------>>>账户交易明细生单转换后："+ JSONObject.toJSONString(data1));
                RPABANKTRANSCATIONDETAILSEntity byBNKFLW = rpaBank.findByBNKFLW(data1.getBNKFLW());
                if (byBNKFLW!=null){
                    transaction.rollback();
                    responseDto.setMassage(data1.getBNKFLW()+"流水号已存在");
                    return responseDto;
                }
                data1.setNM(UUID.randomUUID().toString());
                RPABANKTRANSCATIONDETAILSEntity save = rpaBank.save(data1);
                logService.info(logCode,"------>>>保存二开表成功："+JSONObject.toJSONString(save));

                //校验直连开通状态及开通日期；账户开通直连日期及之后不允许RPA导入流水
                String checkSql = "Select ONLINEBANKOPENSTATUS,DIRECTTIME from BFBANKACCOUNTS where ACCOUNTNO='"+data1.getACTNBR()+"'";
                Map<String, Object> stringObjectMap = DataBase.queryOne(checkSql);
                logService.info(logCode,"------>>>校验直连开通状态及开通日期查询结果："+ JSONObject.toJSONString(stringObjectMap));
                if (stringObjectMap.size()>0){
                    if ("2".equals(stringObjectMap.get("ONLINEBANKOPENSTATUS")) || "3".equals(stringObjectMap.get("ONLINEBANKOPENSTATUS"))){
                        //如果开通了直联，判断交易日期
                        Date onLineTime = getDate(String.valueOf(stringObjectMap.get("DIRECTTIME")));
                        Date transactionDate = getDate(data1.getBNKTIM());
                        if (transactionDate.after(onLineTime)){
                            transaction.rollback();
                            responseDto.setMassage(data1.getBNKFLW()+"交易日期大于直联开通日期不允许RPA导入流水");
                            responseDto.setType("E");
                            return responseDto;
                        }
                    }
                }else {
                    transaction.rollback();
                    responseDto.setMassage("未找到账户信息："+data1.getACTNBR());
                    responseDto.setType("E");
                    return responseDto;
                }

                logService.info(logCode,"------>>>账户交易明细校验直连开通状态及开通日期：通过");


                List<JfskOfflineTransDetailsEntity> InParam = new ArrayList<>();
                JfskOfflineTransDetailsEntity offlineTransDetails = new JfskOfflineTransDetailsEntity();
                Integer incomeOrExpenditure = null;
                BigDecimal settlementAmount = null;
                if (data1.getZZC().compareTo(BigDecimal.ZERO)!=0){
                    incomeOrExpenditure = 1;
                    settlementAmount = data1.getZZC();
                }
                if (data1.getZSR().compareTo(BigDecimal.ZERO)!=0){
                    incomeOrExpenditure = 2;
                    settlementAmount = data1.getZSR();
                }

                offlineTransDetails.setBankFlowNo(data1.getBNKFLW());//交易流水号
                offlineTransDetails.setIncomeOrExpenditure(incomeOrExpenditure);//收支属性必填：1付款/2收款
                offlineTransDetails.setAccountUnit(getOrgId(data1.getBUTXT()));//账户开户单位id必填
                offlineTransDetails.setBankAccountID(getAccountId(data1.getACTNBR()));//账户id必填；如没有用账号查账户表，找到传过来：BFBANKACCOUNTS
                offlineTransDetails.setBankAccountNo(data1.getACTNBR());//银行账号必填
                String currency = getCurrency(data1.getZJYBZ());
                if (StringUtils.isEmpty(currency)){
                    transaction.rollback();
                    responseDto.setMassage(data1.getBNKFLW()+"未找到对应币种信息："+data1.getZJYBZ());
                    responseDto.setType("E");
                    return responseDto;
                }
                offlineTransDetails.setCurrency(getCurrency(data1.getZJYBZ()));//币种id；
                offlineTransDetails.setSettlementAmount(settlementAmount);//交易金额
                offlineTransDetails.setReciprocalAccountNo(data1.getZOTHAC());//对方账号-调拨类必填
                offlineTransDetails.setReciprocalAccName(data1.getZOTHNAM());//对方账号名称
                offlineTransDetails.setBankCodeOfReciprocalAccount(data1.getZLHH());//对方开户行行号BANKCODEOFRECIPROCALACCOUNT
                //offlineTransDetails.setProvice();//对方省
                //offlineTransDetails.setCity();//对方市
                offlineTransDetails.setSummary(data1.getZSGTX());//摘要 必填
                offlineTransDetails.setTransStatus(1);//交易状态必填 1未生成2已生成3已核销4无需生成5作废
                offlineTransDetails.setTransactionDate(getDate(data1.getBNKTIM()));//交易日期 必填
                offlineTransDetails.setTransactionTime(getDate(data1.getBNKTIM()));//交易时间
                offlineTransDetails.setImmediateAmount(data1.getACTBAL());//即时金额，这笔交易明细发生之后，账户的实时余额，一般为直联调度*使用
                offlineTransDetails.setDataSrc("03");//单据来源必填 01: 直联程序 02: 手工导入03: 财务公司采集 04：手工录入
                //offlineTransDetails.setOrigBankFlowNo();//原始银行流水号

                InParam.add(offlineTransDetails);

                List<Object> parameters=new ArrayList<Object>();
                parameters.add(InParam);
                logService.info(logCode,"------>>>调用账户交易明细调用内部服务入参："+ JSONObject.toJSONString(parameters));
                String offlineTransDetailsRev = InternalServiceProxy.invoke("BP/Bebc/v1.0/BankTransactionDetailsInternalApi", "OfflineTransDetailsRev", parameters, new TypeReference<String>() {
                });
                logService.info(logCode,"------>>>调用账户交易明细调用内部服务结果："+offlineTransDetailsRev);

            }catch (Throwable throwable){
                transaction.rollback();
                logService.error(logCode,"------>>>交易明细同步异常："+throwable.getMessage());
                logService.error(logCode,"------>>>异常明细："+JSONObject.toJSONString(throwable));
                responseDto.setType("E");
                responseDto.setMassage("交易明细同步异常："+JSONObject.toJSONString(throwable.getMessage()));
                return responseDto;
            }finally {
                logService.flush();
            }
        }
        transaction.commit();
        return responseDto;
    }


    //根据机构编码获取机构id
    private String getOrgId(String orgCode){

        String orgIdSql = "select id FROM BFMASTERORGANIZATION  WHERE CODE='"+ orgCode+"'";
        log.error("查询组织idsq："+orgIdSql);
        Map<String, Object> stringObjectMap = DataBase.queryOne(orgIdSql);
        log.error("查询组织id结果："+JSONObject.toJSONString(stringObjectMap));
        if (stringObjectMap==null){
            log.error("查询组织id结果：null");
            return null;
        }else {
            log.error("查询组织id结果true："+stringObjectMap.get("id"));
            return stringObjectMap.get("id").toString();
        }
    }


    private String getAccountId(String accountNo){

        String accountIdSql = "select id FROM BFBANKACCOUNTS  WHERE ACCOUNTNO='"+ accountNo+"'";
        log.error("查询账户idsq："+accountIdSql);
        Map<String, Object> stringObjectMap = DataBase.queryOne(accountIdSql);
        log.error("查询账户id结果："+JSONObject.toJSONString(stringObjectMap));
        if (stringObjectMap==null){
            log.error("查询账户id结果：null");
            return null;
        }else {
            log.error("查询账户id结果true："+stringObjectMap.get("id"));
            return stringObjectMap.get("id").toString();
        }
    }

    private String getCurrency(String currency){
        if (currency==null){
            currency="CNY";
        }

        String currencySql = "select id FROM BFCURRENCY  WHERE name_chs='"+ currency+"' or code ='"+ currency+"'";
        Map<String, Object> stringObjectMap = DataBase.queryOne(currencySql);
        if (stringObjectMap==null || stringObjectMap.size()==0){
            return null;
        }else {
            return stringObjectMap.get("id").toString();
        }
    }


    private Date getDate(String date){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date1 = null;
        try {
            date1 = sdf.parse(date);
        } catch (Throwable e) {

        }
        return date1;
    }


    private RPABANKTRANSCATIONDETAILSEntity getRpaBankTranscationDetailsEntity(RPABANKTRANSCATIONDETAILSEntity data){

        RPABANKTRANSCATIONDETAILSEntity rpaBankTranscationDetailsEntity = new RPABANKTRANSCATIONDETAILSEntity();
        rpaBankTranscationDetailsEntity.setNM(data.getNM());
        rpaBankTranscationDetailsEntity.setBUTXT(data.getBUTXT());
        rpaBankTranscationDetailsEntity.setACTNBR(data.getACTNBR());
        rpaBankTranscationDetailsEntity.setZJYBZ(data.getZJYBZ());
        rpaBankTranscationDetailsEntity.setZOTHAC(data.getZOTHAC());
        rpaBankTranscationDetailsEntity.setZOTHNAM(data.getZOTHNAM());
        rpaBankTranscationDetailsEntity.setZOTHOPN(data.getZOTHOPN());
        rpaBankTranscationDetailsEntity.setBNKFLW(data.getBNKFLW());
        rpaBankTranscationDetailsEntity.setZPZZL(data.getZPZZL());
        rpaBankTranscationDetailsEntity.setZPZH(data.getZPZH());
        rpaBankTranscationDetailsEntity.setZYWBH(data.getZYWBH());
        rpaBankTranscationDetailsEntity.setZYWZL(data.getZYWZL());
        rpaBankTranscationDetailsEntity.setZSGTX(data.getZSGTX());
        rpaBankTranscationDetailsEntity.setZFY(data.getZFY());
        rpaBankTranscationDetailsEntity.setNUSAGE(data.getNUSAGE());
        rpaBankTranscationDetailsEntity.setZBZ(data.getZBZ());
        rpaBankTranscationDetailsEntity.setZJYXX(data.getZJYXX());
        rpaBankTranscationDetailsEntity.setZSTA(data.getZSTA());
        rpaBankTranscationDetailsEntity.setZJYLX(data.getZJYLX());
        rpaBankTranscationDetailsEntity.setZYJQD(data.getZYJQD());
        rpaBankTranscationDetailsEntity.setZKH(data.getZKH());
        rpaBankTranscationDetailsEntity.setZDWKH(data.getZDWKH());
        rpaBankTranscationDetailsEntity.setZLHH(data.getZLHH());
        rpaBankTranscationDetailsEntity.setZZBANK(data.getZZBANK());
        rpaBankTranscationDetailsEntity.setZZBANKT(data.getZZBANKT());
        rpaBankTranscationDetailsEntity.setZYL1(data.getZYL1());
        rpaBankTranscationDetailsEntity.setZYL2(data.getZYL2());
        rpaBankTranscationDetailsEntity.setZYL3(data.getZYL3());
        rpaBankTranscationDetailsEntity.setZYL4(data.getZYL4());
        rpaBankTranscationDetailsEntity.setZYL5(data.getZYL5());
        rpaBankTranscationDetailsEntity.setZYL6(data.getZYL6());
        rpaBankTranscationDetailsEntity.setZYL7(data.getZYL7());
        rpaBankTranscationDetailsEntity.setZYL8(data.getZYL8());
        rpaBankTranscationDetailsEntity.setZYL9(data.getZYL9());
        rpaBankTranscationDetailsEntity.setZYL10(data.getZYL10());

        return rpaBankTranscationDetailsEntity;
    }

    private String  getNewDate(){
        LocalDate date = LocalDate.now(); // 获取当前日期
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String formattedDate = date.format(formatter); // 格式化日期
        return formattedDate;
    }

}
