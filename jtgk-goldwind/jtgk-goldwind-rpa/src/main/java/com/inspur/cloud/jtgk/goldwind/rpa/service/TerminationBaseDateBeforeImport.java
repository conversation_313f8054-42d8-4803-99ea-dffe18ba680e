package com.inspur.cloud.jtgk.goldwind.rpa.service;

import com.alibaba.fastjson.JSON;
import com.inspur.fastdweb.exception.ResultException;
import com.inspur.fastdweb.model.excel.ExcelObject;
import com.inspur.fastdweb.service.excel.IExcelImportEvent;
import lombok.extern.log4j.Log4j;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/3/27
 */
@Service
@Slf4j
public class TerminationBaseDateBeforeImport implements IExcelImportEvent {

    public ExcelObject beforeImport(ExcelObject excelObject) {

        log.error("导入前校验:"+ JSON.toJSONString(excelObject));
        log.error("导入前校验:"+  excelObject.toString());
        throw new ResultException("check", "导入前校验");

    }

    //插入数据库前
    public ExcelObject beforeInsertImport(ExcelObject excelObject) {
        log.error("插入数据库前:"+ JSON.toJSONString(excelObject));
        log.error("插入数据库前:"+  excelObject.toString());
        return excelObject;

    }
}
