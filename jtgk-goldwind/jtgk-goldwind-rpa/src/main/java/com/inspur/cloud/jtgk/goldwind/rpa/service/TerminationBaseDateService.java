package com.inspur.cloud.jtgk.goldwind.rpa.service;

import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.commons.transaction.JpaTransaction;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjuster;
import java.time.temporal.TemporalAdjusters;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/3/26
 */
@Service
public class TerminationBaseDateService {

    public Map<String,Object> getTerminationBaseDate(){
        Map<String,Object> result = new HashMap<>();

        JpaTransaction transaction = JpaTransaction.getTransaction();
        transaction.begin();
        try{
            EntityManager entityManager = SpringBeanUtils.getBean(EntityManager.class);
            String sql = "insert into JFKJNOTERMINATIONBASEDATE (id,bankcategory,banktype,ifzz,startdate,enddate,period,status,TIMESTAMPS_CREATEDBY,TIMESTAMPS_CREATEDON,TIMESTAMPS_LASTCHANGEDBY,TIMESTAMPS_LASTCHANGEDON)\n" +
                    "select uuid_generate_v4() as id,\n" +
                    "(case when BFBANKTYPE.name_chs='财务公司' then '四类' else '三类' end) as bankcategory,\n" +
                    "id as banktype,\n" +
                    "'0' as ifzz,\n" +
                    "? as startdate,\n" +
                    "? as enddate,\n" +
                    "? as period,\n" +
                    "'0' as status,\n" +
                    "? as TIMESTAMPS_CREATEDBY,\n" +
                    "? as TIMESTAMPS_CREATEDON,\n" +
                    "? as TIMESTAMPS_LASTCHANGEDBY,\n" +
                    "? as TIMESTAMPS_LASTCHANGEDON\n" +
                    "from BFBANKTYPE \n" +
                    "where BFBANKTYPE.id not in (select JFKJNOTERMINATIONBASEDATE.banktype from JFKJNOTERMINATIONBASEDATE where JFKJNOTERMINATIONBASEDATE.period = '2025-03-01 00:00:00')";
            Query nativeQuery = entityManager.createNativeQuery(sql);
            LocalDate localDate = LocalDate.now();
            LocalDate firstDay = localDate.with(TemporalAdjusters.firstDayOfMonth());//当月第一天
            LocalDate lastDay = localDate.with(TemporalAdjusters.lastDayOfMonth());//当月最后一天
            nativeQuery.setParameter(1, firstDay);
            nativeQuery.setParameter(2, lastDay);
            nativeQuery.setParameter(3, firstDay);
            nativeQuery.setParameter(4, CAFContext.current.getUserId());
            nativeQuery.setParameter(5, CAFContext.current.getCurrentDateTime());
            nativeQuery.setParameter(6, CAFContext.current.getUserId());
            nativeQuery.setParameter(7, CAFContext.current.getCurrentDateTime());
            int i = nativeQuery.executeUpdate();
            transaction.commit();
            result.put("code","1");
        }catch (Exception e){
         transaction.rollback();
         result.put("code","0");
         result.put("msg",e.getMessage());
        }

        return result;
    }


}
