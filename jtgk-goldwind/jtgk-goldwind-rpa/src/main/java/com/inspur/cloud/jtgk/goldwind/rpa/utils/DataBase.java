package com.inspur.cloud.jtgk.goldwind.rpa.utils;

import io.iec.edp.caf.commons.utils.CollectionUtils;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @project jtgk-sgsk-pkjh
 * @description
 * @date 2024/4/7
 */
public class DataBase {

    public static List<Map<String, Object>> queryList(String sql) {
        EntityManager manager = SpringBeanUtils.getBean(EntityManager.class);
        Query nativeQuery = manager.createNativeQuery(sql);
        nativeQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> resultList = nativeQuery.getResultList();
        for (Map<String, Object> singleResult : resultList) {
            for (String s : new HashSet<>(singleResult.keySet())) {
                String lowerCase = s.toLowerCase();
                if (!singleResult.containsKey(lowerCase)) {
                    singleResult.put(lowerCase, singleResult.get(s));
                }
            }
        }
        return resultList;
    }

    public static Map<String,Object> queryOne(String sql) {
        // 查单个getSingleResult没有数据会抛异常，还是用list
        EntityManager manager = SpringBeanUtils.getBean(EntityManager.class);
        Query nativeQuery1 = manager.createNativeQuery(sql);
        nativeQuery1.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String,Object>> resultList1 = nativeQuery1.getResultList();
        Map<String,Object> result = new HashMap<>();
        if (!CollectionUtils.isEmpty(resultList1) && resultList1.size()>0){
            result = resultList1.get(0);
        }
        return result;
    }

    private static Query createNativeQuery(EntityManager manager, String sql, List<Object> params) {
        Query nativeQuery = manager.createNativeQuery(sql);
        // 设置参数
        if (params != null && params.size()>0) {
            for (int i = 0; i < params.size(); i++) {
                nativeQuery.setParameter(i + 1, params.get(i));
            }
        }
        return nativeQuery;
    }

    public static Query createNativeQuery(String sql){
        EntityManager bean = SpringBeanUtils.getBean(EntityManager.class);
        Query nativeQuery = bean.createNativeQuery(sql);
        return nativeQuery;
    }


    public static int executeUpdate(String sql){
        EntityManager bean = SpringBeanUtils.getBean(EntityManager.class);
        Query nativeQuery = bean.createNativeQuery(sql);
        int i = nativeQuery.executeUpdate();
        return i;
    }
}
