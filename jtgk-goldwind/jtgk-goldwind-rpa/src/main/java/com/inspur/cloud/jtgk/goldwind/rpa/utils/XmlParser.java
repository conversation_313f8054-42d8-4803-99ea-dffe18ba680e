package com.inspur.cloud.jtgk.goldwind.rpa.utils;

import com.inspur.cloud.jtgk.goldwind.rpa.entity.BankQueryTransDetailsReq;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.QueryReq;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.QueryReqHis;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.QueryTransDetailsReq;
import com.inspur.cloud.jtgk.goldwind.rpa.xml.*;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.StringReader;

/**
 * <AUTHOR>
 * @version 1.0
 * @project JFKJSK
 * @description
 * @date 2025/4/15
 */
public class XmlParser {

    public static QueryReq parseXmlToQueryReq(String xmlString) throws JAXBException {
        JAXBContext jaxbContext = JAXBContext.newInstance(DayInBankAccountIssItreasuryReq.class);
        Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

        StringReader reader = new StringReader(xmlString);
        DayInBankAccountIssItreasuryReq issItreasury = (DayInBankAccountIssItreasuryReq) unmarshaller.unmarshal(reader);

        return issItreasury.getQueryReq();
    }

    public static QueryReq HisParseXmlToQueryReq(String xmlString) throws JAXBException {
        JAXBContext jaxbContext = JAXBContext.newInstance(HisInBankAccountIssItreasuryReq.class);
        Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

        StringReader reader = new StringReader(xmlString);
        HisInBankAccountIssItreasuryReq issItreasury = (HisInBankAccountIssItreasuryReq) unmarshaller.unmarshal(reader);

        return issItreasury.getQueryReq();
    }

    public static QueryReqHis HisBankParseXmlToQueryReq(String xmlString) throws JAXBException {
        JAXBContext jaxbContext = JAXBContext.newInstance(HisBankAccountIssItreasuryReq.class);
        Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

        StringReader reader = new StringReader(xmlString);
        HisBankAccountIssItreasuryReq issItreasury = (HisBankAccountIssItreasuryReq) unmarshaller.unmarshal(reader);

        return issItreasury.getQueryReq();
    }



    public static QueryTransDetailsReq InBankStatementParseXmlToQueryReq(String xmlString) throws JAXBException {
        JAXBContext jaxbContext = JAXBContext.newInstance(InBankStatementIssItreasuryReq.class);
        Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

        StringReader reader = new StringReader(xmlString);
        InBankStatementIssItreasuryReq issItreasury = (InBankStatementIssItreasuryReq) unmarshaller.unmarshal(reader);

        return issItreasury.getQueryReq();
    }

    public static BankQueryTransDetailsReq OutBankStatementParseXmlToQueryReq(String xmlString) throws JAXBException {
        JAXBContext jaxbContext = JAXBContext.newInstance(OutBankStatementIssItreasuryReq.class);
        Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

        StringReader reader = new StringReader(xmlString);
        OutBankStatementIssItreasuryReq issItreasury = (OutBankStatementIssItreasuryReq) unmarshaller.unmarshal(reader);

        return issItreasury.getQueryReq();
    }

}
