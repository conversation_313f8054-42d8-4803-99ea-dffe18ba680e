package com.inspur.cloud.jtgk.goldwind.rpa.xml;

import com.inspur.cloud.jtgk.goldwind.rpa.dto.AccountBalanceDto;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 解析查询内部账户当日余额响应
 */
@XmlRootElement(name = "Iss_Itreasury")
@XmlAccessorType(XmlAccessType.FIELD)
public class DayInBankAccountIssItreasuryRes {
    @XmlElement(name = "QueryReq")
    private AccountBalanceDto queryReq;

    // getters and setters
    public AccountBalanceDto getQueryReq() {
        return queryReq;
    }

    public void setQueryReq(AccountBalanceDto queryReq) {
        this.queryReq = queryReq;
    }
}
