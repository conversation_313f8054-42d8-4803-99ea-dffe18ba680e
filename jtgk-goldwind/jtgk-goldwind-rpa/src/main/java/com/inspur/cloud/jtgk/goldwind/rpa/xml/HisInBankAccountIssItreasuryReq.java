package com.inspur.cloud.jtgk.goldwind.rpa.xml;

import com.inspur.cloud.jtgk.goldwind.rpa.entity.QueryReq;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 解析查询内部账户当日余额入参
 */
@XmlRootElement(name = "Iss_Itreasury")
@XmlAccessorType(XmlAccessType.FIELD)
public class HisInBankAccountIssItreasuryReq {
    @XmlElement(name = "QueryReq")
    private QueryReq queryReq;

    // getters and setters
    public QueryReq getQueryReq() {
        return queryReq;
    }

    public void setQueryReq(QueryReq queryReq) {
        this.queryReq = queryReq;
    }
}
