package com.inspur.cloud.jtgk.goldwind.rpa.xml;

import com.inspur.cloud.jtgk.goldwind.rpa.dto.HisAccountBalanceDto;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 解析查询内部账户当日余额响应
 */
@XmlRootElement(name = "Iss_Itreasury")
@XmlAccessorType(XmlAccessType.FIELD)
public class HisInBankAccountIssItreasuryRes {
    @XmlElement(name = "QueryReq")
    private HisAccountBalanceDto queryReq;

    // getters and setters
    public HisAccountBalanceDto getQueryReq() {
        return queryReq;
    }

    public void setQueryReq(HisAccountBalanceDto queryReq) {
        this.queryReq = queryReq;
    }
}
