package com.inspur.cloud.jtgk.goldwind.rpa.xml;

import com.inspur.cloud.jtgk.goldwind.rpa.entity.QueryTransDetailsReq;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 解析银行交易流水入参
 */
@XmlRootElement(name = "Iss_Itreasury")
@XmlAccessorType(XmlAccessType.FIELD)
public class InBankStatementIssItreasuryReq {
    @XmlElement(name = "QueryReq")
    private QueryTransDetailsReq queryReq;

    // getters and setters
    public QueryTransDetailsReq getQueryReq() {
        return queryReq;
    }

    public void setQueryReq(QueryTransDetailsReq queryReq) {
        this.queryReq = queryReq;
    }
}
