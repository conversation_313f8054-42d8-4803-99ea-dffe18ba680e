package com.inspur.cloud.jtgk.goldwind.rpa.xml;

import com.inspur.cloud.jtgk.goldwind.rpa.dto.TransDetailsQueryRenDto;
import com.inspur.cloud.jtgk.goldwind.rpa.entity.QueryTransDetailsReq;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 解析银行交易流水入参
 */
@XmlRootElement(name = "Iss_Itreasury")
@XmlAccessorType(XmlAccessType.FIELD)
public class InBankStatementIssItreasuryRes {
    @XmlElement(name = "QueryReq")
    private TransDetailsQueryRenDto queryReq;

    // getters and setters
    public TransDetailsQueryRenDto getQueryReq() {
        return queryReq;
    }

    public void setQueryReq(TransDetailsQueryRenDto queryReq) {
        this.queryReq = queryReq;
    }
}
