package com.inspur.cloud.jtgk.goldwind.rpa.xml;

import com.inspur.cloud.jtgk.goldwind.rpa.entity.QueryReq;
import javax.xml.bind.annotation.*;
import java.util.List;

@XmlRootElement(name = "Iss_Itreasury")
@XmlAccessorType(XmlAccessType.FIELD)
public class IssItreasury {
    @XmlElement(name = "QueryReq")
    private QueryReq queryReq;

    // getters and setters
    public QueryReq getQueryReq() {
        return queryReq;
    }

    public void setQueryReq(QueryReq queryReq) {
        this.queryReq = queryReq;
    }
}
