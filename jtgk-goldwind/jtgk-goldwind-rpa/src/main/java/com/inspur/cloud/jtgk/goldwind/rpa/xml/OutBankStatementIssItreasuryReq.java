package com.inspur.cloud.jtgk.goldwind.rpa.xml;

import com.inspur.cloud.jtgk.goldwind.rpa.entity.BankQueryTransDetailsReq;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

/**
 * 解析银行交易流水入参
 */
@Data
@XmlRootElement(name = "Iss_Itreasury")
@XmlAccessorType(XmlAccessType.FIELD)
public class OutBankStatementIssItreasuryReq {
    @XmlElement(name = "QueryReq")
    private BankQueryTransDetailsReq queryReq;

}
