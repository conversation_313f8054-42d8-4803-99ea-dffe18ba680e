/**
 * 待付池-执行记录-卡片
 * /apps/jtgk/jfkj/extend/unipay-execute-card.js
 */
/* 联查付款安排表
function(rowdata,row,value){
  if (rowdata.REFDOCID === null || rowdata.REFDOCID === "") {
    return "";
  } else {
    return "<a style='text-decoration:none' href='javascript:jointCheckPayBill(\""+rowdata.REFDOCID+"\", "+rowdata.REFDOCID_DATASRC+");'>"+rowdata.REFDOCNO+"</a>";
  }
} */
function jointCheckPayBill(docId, dataSrc) {
  var url = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=44bb6838-9b25-8e1a-c885-b5c7b66f83e4&status=view&runtime=true"
	+ "&dataid=" + docId + "&fdim=" + dataSrc;
  idp.utils.openurl(docId, "单据卡片", url);
}

/* 联查业务支付申请
function(rowdata,row,value){
  if (rowdata.REQUESTDOCID === null || rowdata.REQUESTDOCID === "") {
    return "";
  } else {
    return "<a style='text-decoration:none' href='javascript:jointCheckBizRequest(\""+rowdata.REQUESTDOCID+"\");'>"+rowdata.REQUESTDOCNO+"</a>";
  }
} */
function jointCheckBizRequest(docId) {
  var url = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?j=true&styleid=efbeb662-aceb-4ba3-9423-642a2b5fa2c9&status=view&dataid=" + docId + "&operation=view&formtype=Nomal&runtime=true&fdim=Nomal&sdim=fsjs";
  idp.utils.openurl(docId, "单据卡片", url);
}

/* 联查付款单
function(rowdata,row,value){
  if (rowdata.PAYDOCID === null || rowdata.PAYDOCID === "") {
    return "";
  } else {
    return "<a style='text-decoration:none' href='javascript:jointCheckSettlement(\""+rowdata.PAYDOCTYPE+"\",\""+rowdata.PAYDOCID+"\");'>"+rowdata.PAYDOCNO+"</a>";
  }
}*/
function jointCheckSettlement(docType, docId) {
  if (docType === "0") {
    var url = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=9b48a96b-ca31-415b-bb13-2c6890192d52&lcbz=splc&runtime=true&status=view&dataid=" + docId + "&FORMTYPE=TM_QYFKD";
    idp.utils.openurl(docId, "企业付款单详情", url);
  } else if (docType === "1") {
    var url = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?runtime=true&fdim=&sdim=&status=view&styleid=ee9a0abf-27af-0156-25f1-e407c30085f8&j=true&djrk=&splc=splc&dataid=" + docId;
    idp.utils.openurl(docId, "开票申请单详情", url);
  } else if (docType === "2") {
    var url = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=0c1cfb1a-770e-f531-8091-c3eed1c1259c&status=view&splc=splc&runtime=true&djrk=zgpj&j=true&dataid=" + docId;
    idp.utils.openurl(docId, "背书申请单详情", url);
  } else if (docType === "5") {
    var url = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=22515a25-d55b-65f6-5b68-d4486ca81c3d&lcbz=splc&runtime=true&status=view&dataid=" + docId + "&FORMTYPE=TM_DWDBD";
    idp.utils.openurl(docId, "单位调拨单详情", url);
  }
}
