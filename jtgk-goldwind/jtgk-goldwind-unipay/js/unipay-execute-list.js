/**
 * 待付池-付款安排记录
 * /apps/jtgk/jfkj/extend/unipay-execute-list.js
 */
idp.event.bind("domReady", function() {
	idp.event.register("grid_main","beforeGridFilter",function(e,filter){
		// 根据来源单据ID过滤
		var logInfoId = idp.utils.getQuery("LOGINFOID");
		if (logInfoId === null || logInfoId === "") {
			logInfoId = "";
		}
        if (filter.length > 0) {
            filter[filter.length - 1].Logic = "and";
        }
        filter.push({
            "Left": "",
            "Field": "PD.LOGINFOID",
            "Operate": "=",
            "IsExpress": false,
            "Value": logInfoId,
            "Right": "",
            "Logic": "and"
        });
        return filter;
    });
});

/* 联查付款安排表
function(row) {
  var url = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=44bb6838-9b25-8e1a-c885-b5c7b66f83e4&status=view"
    + "&runtime=true&dataid=" + row["BILL_ID"] + "&fdim=" + row["DATASRC"];
  idp.utils.openurl("", "单据卡片", url);
}
*/
