/**
 * 待付池-票据补录-应收票据台账选择
 * /apps/jtgk/jfkj/extend/unipay-help-bills-list.js
 */
idp.event.bind("domReady", function() {

    idp.event.register("grid_main","beforeGridFilter",function(e,filter) {
        let PAYUNITID = idp.utils.getQuery("PAYUNITID");

        if (filter.length > 0) {
            filter[filter.length - 1].Logic = "and";
        }
        filter.push({
            "Left": "",
            "Field": "TMBILLRECEIVABLEINVENTORY.RECEIVERUNIT",
            "Operate": "=",
            "IsExpress": false,
            "Value": PAYUNITID,
            "Right": "",
            "Logic": "and"
        });
        return filter;
    });

    //注册列表选中行后事件
    idp.event.register('grid_main', 'selectRow', function (e, data) {
        calculateTotalAmountSeleted();
	});
    idp.event.register('grid_main', 'unSelectRow', function () {
        calculateTotalAmountSeleted();
    });
});

/** 勾选票据后显示张数、合计金额 */
function calculateTotalAmountSeleted(){
    var selectedRows = idp.control.get("grid_main").selected;
    //var amountArrary = [];
    if (selectedRows === null || selectedRows.length === 0) {
		return;
	}
    var totalAmount = 0.00;
    var totalCount = selectedRows.length;
	for(var i = 0; i < selectedRows.length; i++){
		totalAmount += selectedRows[i].SYJE;
	}
    var showMessage = "已选{0}张，合计剩余金额：{1}".replace('{0}', totalCount).replace('{1}', idp.utils.currency(totalAmount, 2, true));
    var totalObj = $("#grid_main .lee-panel-footer #div_total");
    if(totalObj && totalObj.length > 0){
        $("#grid_main .lee-panel-footer #div_total")[0].innerText = showMessage;
    }else{
        $('<div id=\"div_total\" style=\"font-size:14px;margin-top:6px;\">' + showMessage + '</div>').appendTo($("#grid_main .lee-panel-footer"));
    }
}
