/**
 * 待付池-详情
 * /apps/jtgk/jfkj/extend/unipay-logs-card.js
 */
/** 联查业务单据 */
function linkView() {
	var data = idp.uiview.modelController.getMainRowObj();
	var url = data.LINKURL;
	if (url === null || url === "") {
		idp.error("业务系统未提供单据联查地址");
		return false;
	}
	window.open(url, "newwindow");
	return true;
}

/** 查看执行记录 */
function viewDetail() {
	var data = idp.uiview.modelController.getMainRowObj();
	var url = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=e0ac1e92-7f88-17bf-0770-42fb070205cd&status=view&dataid=" + data.ID;
	idp.utils.openurl(data.ID, '付款申请执行记录', url);
	return true;
}

/** 电子影像 */
function viewImage() {
	var data = idp.uiview.modelController.getMainRowObj();
	var billCateGory = 'jtgkPaymentInfo';
	var billywtype = 'jtgkPaymentInfo';
	var billtype = 'jtgkPaymentInfo';
	var imageOperation = 'view';
	var billstate = 'SAVE';
	var billId = data.ID;
	var billCode = data.SRCDOCNO;
	var orgId = data.PAYUNITID;
	var tabId = idp.utils.getQuery('styleid');
    const param = {
        SourceSys: 'IDP', // 来源系统
        BillCATEGORY: billCateGory, // 业务大类
        BillType: billtype, // 单据类型（编号）
        BillTypeID: billtype, // 单据类型ID
        BillNM: billId, // 单据ID
        BillCODE: billCode, // 单据编号
        DWBH: orgId, // 行政组织ID
        OPERATION: imageOperation,
        USERCODE: idp.context.get("UserCode"),
        BILLSTATE: null,
        TabID: tabId,
        IsInvoice: null,
        IsShowInvoice: null,
        autoocr: null,
    };
    idp.service.fetch('/api/BP/EIS/v1.0/imageapi/getyxurlmap', param, true, '').then(rtnInfo => {
        if (rtnInfo.code == "ok") {
            url = rtnInfo.data;
            idp.openurl(billId, "电子影像", url, '', '');
        } else {
            idp.error(rtnInfo.msg);
            return false;
        }
    }).fail(result => {
        idp.error(result.message);
    });
}
