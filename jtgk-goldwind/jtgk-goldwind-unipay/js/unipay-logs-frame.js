/**
 * 待付池-框架
 * /apps/jtgk/jfkj/extend/unipay-logs-frame.js
 */
let global_lastSelectBizSys = '';

// DOM加载后
idp.event.bind("domReady", (e, data) => {
    idp.event.register('grid_main', 'selectRow', naviListSelectRow);
});

// 视图加载后
idp.event.bind("viewReady", (e, data) => {
	loadBizSysList();
});

// // 加载数据后
// idp.event.bind("loadData", function (e, data) {
// 	loadBizSysList();
// });


// 初始化左侧系统列表
function loadBizSysList() {
	var getBizSysResponse = idp.service.fetch("/api/jtgk/goldwind/settlement/v1.0/bizSys", null, false, "GET");
	if (getBizSysResponse.status !== 200) {
		idp.error("请求服务端接口失败");
		return false;
	}
	var getBizSysResult = JSON.parse(getBizSysResponse.responseText);
	if (!getBizSysResult.result) {
		idp.error(getBizSysResult.message);
		return false;
	}
    var bizSys = getBizSysResult.data;
    let arr1 = [];
	arr1.push({
		ID: '00',
		CODE: '',
		NAME: '全部'
	});
	for (var i=0; i<bizSys.length; i++) {
		arr1.push({
			ID: bizSys[i].ID,
			CODE: bizSys[i].CODE,
			NAME: bizSys[i].NAME
		});
	}
    $("#grid_main").leeUI().loadData({
        Rows: arr1
    });
	idp.control.get("grid_main").select(0);
	refreshIFrame("0", "");
}

//左侧列表切换后，待办数据重新加载
function naviListSelectRow(e, selectRow, rowId, rowDom) {
	refreshIFrame(rowId, selectRow.CODE);
}

function refreshIFrame(rowId, code) {
	if (global_lastSelectBizSys === rowId) {
		return;
	}
	if (idp.control.get('grid_main').getSelected()) {
		global_lastSelectBizSys = rowId;
		var url = "/apps/fastdweb/views/runtime/page/query/querypreview.html?styleid=ee9d8ca8-a1ca-0330-1cf9-6b4fa212cb6c&runtime=true"
			+ "&fdim=" + code;
		idp.control.iframe.setUrl("iframe_403797", url);
	}
}
