/**
 * 待付池-付款安排表-列表
 * /apps/jtgk/jfkj/extend/unipay-planbill-list.js
 */
idp.event.bind("domReady", function() {
    idp.event.register("grid_main", "selectRow", setToolbarButtonsEnabled);
});


function addNewOne(){
	$.leeDialog.open({
		title: '新增方式选择',
		name: 'PayBillDialog',
		width: Math.max(window.innerWidth * 0.3, 300),
		height: Math.max(window.innerHeight * 0.1, 200),
		url: `/apps/fastdweb/views/runtime/page/query/querypreview.html?${$.param({
			'styleid': '065faaa6-5695-97f4-cfcf-2e54a94d6a07'
		})}`,
		buttons: [
			{
				id: "dialog_lookup_cancel",
				text: "取消",
				cls: 'lee-dialog-btn-cancel ',
				onclick: function (item, dialog) {
					dialog.close()
				}
			},
			{
				id: "dialog_lookup_register",
				text: "确认",
				cls: 'lee-btn-primary lee-dialog-btn-ok',
				onclick: function (item, dialog) {
					var selectWay = dialog.jiframe[0].contentWindow.idp.control.get("dropdown_44841").getValue()
					if (selectWay == null || selectWay == "") {
						return idp.info('请至少选择一种新增方式');
					}
					if (selectWay == '1') {
						// 1-手工制单
						var newUrl = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=44bb6838-9b25-8e1a-c885-b5c7b66f83e4&status=add&runtime=true&DATASRC=2"
						dialog.jiframe[0].contentWindow.idp.utils.openurl("44bb6838-9b25-8e1a-c885-b5c7b66f83e4", "付款安排详情", newUrl);
					} else if (selectWay == '2') {
						// 2-Excel导入
						var newUrl = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=44bb6838-9b25-8e1a-c885-b5c7b66f83e4&status=add&runtime=true&DATASRC=0"
						dialog.jiframe[0].contentWindow.idp.utils.openurl("44bb6838-9b25-8e1a-c885-b5c7b66f83e4", "付款安排详情", newUrl);
					}
					dialog.close();
				}
			},
		]
	});
}

function setToolbarButtonsEnabled(e, data) {
	if (data !== null && data.DOCSTATUS !== null){
		var state = parseInt(data.DOCSTATUS);
		if(state === 2){
			idp.control.get("toolbar1").setDisabled("baritem_modify");
			idp.control.get("toolbar1").setDisabled("baritem_submit");
			idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");
			idp.control.get("toolbar1").setDisabled("baritem_delete");
		}
		if(state === 1){
			idp.control.get("toolbar1").setDisabled("baritem_modify");
			idp.control.get("toolbar1").setDisabled("baritem_submit");
			idp.control.get("toolbar1").setDisabled("baritem_delete");
			idp.control.get("toolbar1").setEnabled("baritem_cancelSubmit");
		}
		if(state === 0 || state === 3){
			idp.control.get("toolbar1").setEnabled("baritem_modify");
			idp.control.get("toolbar1").setEnabled("baritem_submit");
			idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");
			idp.control.get("toolbar1").setEnabled("baritem_delete");
		}
	}
}

/** 编辑 */
function onEditList() {
	var data = idp.control.get('grid_main').getSelected();
	var getDocStatusResponse = idp.service.fetch("/api/jtgk/goldwind/unipaybill/v1.0/docStatus?ID=" + data.ID, null, false, "GET");
	if (getDocStatusResponse.status !== 200) {
		idp.error("请求服务端接口失败");
		return false;
	}
	var getDocStatusResult = JSON.parse(getDocStatusResponse.responseText);
	if (!getDocStatusResult.result) {
		idp.error(getDocStatusResult.message);
		return false;
	}
    var docStatus = getDocStatusResult.data;
	if (docStatus == null || docStatus == "" || (docStatus != "0" && docStatus != "3")) {
		idp.alert('当前状态不允许修改');
		return false;
	}
	idp.func.editCard();
	return true;
}

/** 删除 */
function onDeleteList() {
	var data = idp.control.get('grid_main').getSelected();
	var getDocStatusResponse = idp.service.fetch("/api/jtgk/goldwind/unipaybill/v1.0/docStatus?ID=" + data.ID, null, false, "GET");
	if (getDocStatusResponse.status !== 200) {
		idp.error("请求服务端接口失败");
		return false;
	}
	var getDocStatusResult = JSON.parse(getDocStatusResponse.responseText);
	if (!getDocStatusResult.result) {
		idp.error(getDocStatusResult.message);
		return false;
	}
    var docStatus = getDocStatusResult.data;
	if (docStatus == null || docStatus == "" || (docStatus != "0" && docStatus != "3")) {
		idp.alert('当前状态不允许删除');
		return false;
	}
	idp.func.delete();
	return true;
}

/** 查看 */
function onViewList() {
	idp.func.viewCard();
	return true;
}

/** 提交 */
function onSubmitList() {
	var data = idp.control.get('grid_main').getSelected();
	idp.confirm("确定要提交当前付款安排单？", function () {
		var url = "/api/jtgk/goldwind/unipaybill/v1.0/submit?ID=" + data.ID;
		idp.loading("处理中...");
		idp.service.fetch(url, null, true, 'GET').then(res => {
			idp.loaded();
			if (!res.result) {
				idp.warn(res.message);
				return false;
			}else{
				idp.info("提交成功");
				idp.control.get("grid_main").loadData();
				return true;
			}
		}).fail(result => {
			idp.error(result.message);
		});
	},function() {});
}

/** 撤回 */
function onCancelSubmitList() {
}

/** 查看流程 */
function onViewProcessList() {
	var data = idp.control.get('grid_main').getSelected();
	var instanceId = data.INSTANCEID;
	if (typeof (instanceId) == "undefined" || instanceId == null || instanceId == "") {
		idp.warn("未获取到审批流程！");
		return false;
	}
	return idp.func.checkFlowChart(instanceId, "url");
}



// /** 电子影像 */
// function onImageViewList() {
// 	var rows = idp.control.get("grid_main").getCheckedRows();
// 	if (rows.length == 0) {
// 		idp.warn("请先选择要操作的单据");
// 		return false;
// 	}
//     var currentData = rows[0];
//     if (currentData.ID == 'null' || currentData.ID == '' || currentData.ID == 'undefined') {
//         //idp.warn("当前没有数据");
//         return false;
//     }
// 	var docStatus = String(currentData.DOCSTATUS);
// 	var billCateGory = 'jtgkPayPlanBill';
// 	var billywtype = 'jtgkPayPlanBill';
// 	var billtype = 'jtgkPayPlanBill';
// 	var imageOperation = (docStatus == '0' || docStatus == '3') ? 'edit' : 'view';
// 	var billId = currentData.ID;
// 	var billCode = currentData.DOCNO;
// 	var orgId = currentData.REQUESTUNIT;
// 	var tabId = idp.utils.getQuery('styleid');
//     const param = {
//         SourceSys: 'IDP', // 来源系统
//         BillCATEGORY: billCateGory, // 业务大类
//         BillType: billtype, // 单据类型（编号）
//         BillTypeID: billtype, // 单据类型ID
//         BillNM: billId, // 单据ID
//         BillCODE: billCode, // 单据编号
//         DWBH: orgId, // 行政组织ID
//         OPERATION: imageOperation,
//         USERCODE: idp.context.get("UserCode"),
//         BILLSTATE: null,
//         TabID: tabId,
//         IsInvoice: null,
//         IsShowInvoice: null,
//         autoocr: null,
//     };
//     idp.service.fetch('/api/BP/EIS/v1.0/imageapi/getyxurlmap', param, true, '').then(rtnInfo => {
//         if (rtnInfo.code == "ok") {
//             url = rtnInfo.data;
//             idp.openurl(billId, "电子影像", url, '', '');
//         } else {
//             idp.error(rtnInfo.msg);
//             return false;
//         }
//     }).fail(result => {
//         idp.error(result.message);
//     });
// }
