/**
 * 待付池-付款安排复核-卡片
 * /apps/jtgk/jfkj/extend/unipay-plancheck-card.js
 */
/** 界面控件初始化 */

var initInfos = {};

idp.event.bind("loadData", function (e, data) {
	setToolbarState(e, data);
	setControlState(e, data);
	// 获取金额
	let totalAmount = 0;
	let rows = idp.control.get('grid_JTGKPAYPLANDETAIL').rows;
	for (var i=0; i<rows.length; i++) {
		initInfos[rows[i].LOGINFOID] = rows[i].AMOUNT;
		totalAmount += rows[i].AMOUNT;
	}
	// 总金额重新赋值
	idp.control.get("TOTALAMOUNT").setValue(totalAmount);
});

/** 支付明细编辑前 */
idp.event.bind("viewReady", function() {
	idp.event.register('grid_JTGKPAYPLANDETAIL', "beforeEdit", (event, rowindex, rowdata, column) => {
		// 票据支付方式
		if (column.colid == 'BILLPAYWAY') {
            if (rowdata.IFACCEPTANCEBILL === null || rowdata.IFACCEPTANCEBILL === "0") {
                return false;
            }
		}
	});
    idp.event.register("grid_JTGKPAYPLANDETAIL", "afterGridHelpSelected", function (e, helpeditor, helpparam, data) {
        var colid = helpparam.host_grid_column.colid;
		if (colid == "SETTLEMENTWAYID_NAME$LANGUAGE$") {
			var ifAcceptanceBill = data[0].IFACCEPTANCEBILL;
			if (ifAcceptanceBill === null || ifAcceptanceBill === "0") {
				helpparam.host_grid_row.BILLPAYWAY = null;
			}
		}
	});

	idp.event.register("grid_JTGKPAYPLANDETAIL","afterEndEdit",function(e,opts){ 
		// 列数据 
		var column=opts.column;
		if (column.colid == "AMOUNT") {
			// 检查金额是否大于申请金额
			let amount = opts.value;
			let applyAmount = opts.record.REQUESTAMOUNT;
			if (amount > applyAmount) {
				idp.error("本次付款金额不能大于申请金额");
				return false;
			}
		}

		return true;
	});
});

// 工具栏按钮
function setToolbarState(e, data) {
	var state = idp.uiview.modelController.getValue("DOCSTATUS");

    if (state == '2') {
		// 已审批时不允许修改
        idp.control.get("toolbar1").setDisabled("baritem_modify");
        idp.control.get("toolbar1").setDisabled("baritem_submit");
        idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");
        idp.control.get("toolbar1").setDisabled("baritem_delete");

		idp.control.get("toolbar1").setDisabled("baritem_submit_new");
        idp.control.get("toolbar1").setDisabled("baritem_retract_new");
    }
    if (state == '1') {
		// 审批中不允许修改
        idp.control.get("toolbar1").setDisabled("baritem_modify");
        idp.control.get("toolbar1").setDisabled("baritem_submit");
        idp.control.get("toolbar1").setDisabled("baritem_delete");
        idp.control.get("toolbar1").setEnabled("baritem_cancelSubmit");

		idp.control.get("toolbar1").setDisabled("baritem_submit_new");
        idp.control.get("toolbar1").setEnabled("baritem_retract_new");
    }
    if (state == '0' || state == '3') {
		let status = idp.utils.getQuery("status");
		if (status == "add") {
			idp.control.get("toolbar1").setDisabled("baritem_modify");
			idp.control.get("toolbar1").setEnabled("baritem_submit");
			idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");
			idp.control.get("toolbar1").setDisabled("baritem_delete");

			idp.control.get("toolbar1").setEnabled("baritem_submit_new");
			idp.control.get("toolbar1").setDisabled("baritem_retract_new");
		}else if (status == "edit") {
			idp.control.get("toolbar1").setDisabled("baritem_modify");
			idp.control.get("toolbar1").setEnabled("baritem_submit");
			idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");
			idp.control.get("toolbar1").setDisabled("baritem_delete");

			idp.control.get("toolbar1").setEnabled("baritem_submit_new");
			idp.control.get("toolbar1").setDisabled("baritem_retract_new");
		}
    }
	// 允许查看流程
	idp.control.get("toolbar1").setEnabled("baritem_viewProcess");
	idp.control.get("toolbar1").setEnabled("baritem_view_new");
	if (idp.utils.getQuery("workItemId") !== '') {
		// 单据审批时按钮隐藏
		// idp.control.get("toolbar1").toggleBtns(["baritem_close","baritem_add","baritem_cancel","baritem_modify","baritem_save","baritem_submit","baritem_cancelSubmit","baritem_delete","baritem_submit_new","baritem_retract_new"], false);
		idp.control.get("toolbar1").toggleBtns(["baritem_close","baritem_add","baritem_cancel","baritem_modify","baritem_save","baritem_cancelSubmit","baritem_delete","baritem_submit_new","baritem_retract_new"], false);
	} else {
		// idp.control.get("toolbar1").toggleBtns(["baritem_close","baritem_add","baritem_cancel","baritem_modify","baritem_save","baritem_submit","baritem_cancelSubmit","baritem_delete","baritem_submit_new","baritem_retract_new"], true);
		idp.control.get("toolbar1").toggleBtns(["baritem_close","baritem_add","baritem_cancel","baritem_modify","baritem_save", "baritem_cancelSubmit","baritem_delete","baritem_submit_new","baritem_retract_new"], true);
	}
}

// 表单控件
function setControlState(e, data) {
	var srcPayMethod = null;
	if (data !== null) {
		if (data[0] !== null && data[0].data !== null && data[0].data[0] !== null) {
			// 查看、编辑
			srcPayMethod = data[0].data[0].TXT01_TXT01;
		}
	}
	// 云信支付时显示到期承兑日期
	if (srcPayMethod != null && srcPayMethod != "" && srcPayMethod == "SAP云信") {
		$("#DATE01").parents(".table-item").show();
	} else {
		$("#DATE01").parents(".table-item").hide();
	}
}


/** 编辑 */
function onEditCard() {
	var data = idp.uiview.modelController.getMainRowObj();
	var getDocStatusResponse = idp.service.fetch("/api/jtgk/goldwind/unipaybill/v1.0/detailStatus?DOCID=" + data.ID, null, false, "GET");
	if (getDocStatusResponse.status !== 200) {
		idp.error("请求服务端接口失败");
		return false;
	}
	var getDocStatusResult = JSON.parse(getDocStatusResponse.responseText);
	if (!getDocStatusResult.result) {
		idp.error(getDocStatusResult.message);
		return false;
	}
    var docStatus = getDocStatusResult.data;
	if (docStatus == null || docStatus == "" || docStatus != "0") {
		idp.alert('当前状态不允许编辑');
		return false;
	}
	return idp.uiview.edit();
}

/** 保存 */
function onSaveCard() {
	var data = idp.uiview.modelController.getMainRowObj();
	if (data.TXT01_TXT01 != null && data.TXT01_TXT01 != "" && data.TXT01_TXT01 == "SAP云信") {
		if (data.DATE01 == null || data.DATE01 === "") {
			idp.error('到期承兑日期不能为空');
			return false;
		}
	}
	var details = idp.control.get("grid_JTGKPAYPLANDETAIL").rows;
	if (details == null || details.length == 0) {
		idp.error("支付明细不能为空");
		return false;
	}

	let checkInfos = [];
	for (var i=0; i<details.length; i++) {
		checkInfos.push({
			LOGINFOID: details[i].LOGINFOID,
			AMOUNT: details[i].AMOUNT
		});
	}

	if (details.length > 0) {
		// 保存前再次校验
		let saveCheckResult = idp.service.fetch('/api/jtgk/goldwind/unipaybill/v1.0/save', {
			checkInfos: checkInfos,
			initInfos: initInfos,
		}, false, 'POST');
		if (saveCheckResult.status !== 200) {
			idp.error("请求服务端接口失败");
			return false;
		}
		var saveCheckResultMsgs = JSON.parse(saveCheckResult.responseText);
		if (!saveCheckResultMsgs.result) {
			idp.error(saveCheckResultMsgs.message);
			return false;
		}
	}

	for (var i=0; i<details.length; i++) {
		details[i].BILLDUEDATE = data.DATE01;
	}
	// 开始保存
	return idp.uiview.saveData();
}

/** 提交 */
function onSubmitCard() {
	var cannotSave = idp.uiview.fsmController.cannot('save');
	if (false == cannotSave) {
		idp.alert('请先保存当前单据');
		return false;
	}
	var data = idp.uiview.modelController.getMainRowObj();
	idp.confirm("确定要复核完成当前的付款计划单吗？", function () {
		idp.loading("处理中...");
		var url = "/api/jtgk/goldwind/unipaybill/v1.0/checkPass?DOCID=" + data.ID + "&DOCNO=" + data.DOCNO + "&ONLYCHECK=false";
		return idp.service.fetch(url, null, true, "GET")
			.then(function (res) {
				idp.loaded();
				if (!res.result) {
					idp.warn(res.message);
					return false;
				}else{
					idp.control.get("toolbar1").setDisabled("baritem_cancel");
					idp.control.get("toolbar1").setDisabled("baritem_modify");
					idp.control.get("toolbar1").setDisabled("baritem_save");
					idp.control.get("toolbar1").setDisabled("baritem_submit");
					idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");
					idp.uiview.reloadData();
					
					idp.info("提交成功");
					setTimeout(() => {
						idp.uiview.close();
					}, 1000);
					return true;
				}
			}).fail(function () {
				idp.loaded();
				return false;
			});
	},function() {});
	return true;
}



/** 提交新 */
function onSubmitCard2New() {
	var cannotSave = idp.uiview.fsmController.cannot('save');
	if (false == cannotSave) {
		idp.alert('请先保存当前单据');
		return false;
	}

	idp.confirm("确定要复核当前付款安排单？", function () {
		var data = idp.uiview.modelController.getMainRowObj();
		let IDs = [];
		IDs.push(data.ID);
		let url = "/api/jtgk/goldwind/settlement/v1.0/managerImageSize";
		let params = {};
		params.IDs = IDs;
		idp.service.fetch(url, params, false, "POST")
			.then(function(result) {
				if (result.result) {
					let BILLPAYWAY = data.BILLPAYWAY;
					let PAYUNITID_CODE = data.PAYUNITID_CODE;
					if (PAYUNITID_CODE != "1001" && BILLPAYWAY == 1){
						idp.confirm("非常规主体开承，是否继续", function () {
							let BILLPAYWAY = data.BILLPAYWAY;
							if (BILLPAYWAY == 2){
								submitNewOne(data);
							}else{
								submitOldOne(data);
							}
						}, function() {});
					}else{
						let BILLPAYWAY = data.BILLPAYWAY;
						if (BILLPAYWAY == 2){
							submitNewOne(data);
						}else{
							submitOldOne(data);
						}
					}
				}else{
					idp.error(result.message);
				}
			}).fail(function(errMsg) {
				idp.error(errMsg);
			});
		},function() {});
}


function submitNewOne(curData){
	// 使用共享提交
	var curData = idp.uiview.modelController.deafaultData[0].data[0];

	var djbh = curData.DOCNO;
	var dwid = curData.PAYUNITID;
	var id = curData.ID;

	var params = {};
	var data = {};
	data.DJBH = djbh;
	data.BILLID = id;
	data.FORMTYPE = "JFPAYPLAN2";
	data.DWID = dwid;

	params.data = data;
	params.BILLID = id;
	params.PFPROCINTERACTINFO = null;

	idp.loading("加载中...");

	let url = "/api/jtgk/goldwind/unipaybill/v1.0/checkPass?DOCID=" + id + "&DOCNO=" + djbh + "&ONLYCHECK=true";
	return idp.service.fetch(url, null, true, "GET")
		.then(function (res) {
			idp.loaded();
			if (!res.result) {
				idp.warn(res.message);
				return false;
			}else{
				var url = "/api/jtgk/goldwind/unipaybill/v1.0/submit2New";
				setTimeout(function() {
					idp.service.fetch(url, params, false, "POST")
						.then(function(result) {
							if (result) {
								if (result.result) {
									if (result.code == "2") {
										openTJTGdilog = $.leeDialog.open({
											title: "",
											name: "",
											isHidden: false,
											showMax: true,
											width: 500,
											slide: false,
											height: 250,
											url: result.value.url,
											urlParms: {},
											onclose: function() {
												//获取提交窗口变量
												var option = idp.store.get("pfoption"); //用于判断操作类型，”cancel”：取消或者关闭按钮；
												if (!option) {
													option = "cancel";
												}
												if (option === "cancel") {
													return true;
												}
												var defer = $.Deferred();
												//流程交互信息 需要在调用退回接口时传入
												var PFPROCINTERACTINFO = idp.store.get("pfprocinteractinfo");
												//再次发起流程
												params.PFPROCINTERACTINFO = PFPROCINTERACTINFO;
												var url = "/api/jtgk/goldwind/unipaybill/v1.0/submit2New";
												idp.service.fetch(url, params, false, "POST")
													.then(function(result) {
														if (result) {
															if (result.result) {
																idp.loaded();
																idp.alert("提交审批成功！");
		
																setTimeout(() => {
																	idp.uiview.close();
																}, 1000);
																
																idp.control.get("toolbar1").setDisabled("baritem_modify");
																idp.control.get("toolbar1").setDisabled("baritem_submit");
																idp.control.get("toolbar1").setDisabled("baritem_delete");
																idp.control.get("toolbar1").setEnabled("baritem_cancelSubmit");
														
																idp.control.get("toolbar1").setDisabled("baritem_submit_new");
																idp.control.get("toolbar1").setEnabled("baritem_retract_new");
		
																idp.uiview.reloadData();
																defer.resolve(true);
															} else {
																idp.error(result.message);
																defer.resolve(false);
															}
														}
													}).fail(function(errMsg) {
														defer.reject();
													});
												var resultValue = true;
												defer.then(function(value) {
													resultValue = value;
												}, function() {
													resultValue = false;
												})
												return resultValue;
											},
										});
			
									} else {
										idp.info("提交审批成功！");
										idp.loaded();
		
										setTimeout(() => {
											idp.uiview.close();
										}, 1000);
										
										idp.control.get("toolbar1").setDisabled("baritem_modify");
										idp.control.get("toolbar1").setDisabled("baritem_submit");
										idp.control.get("toolbar1").setDisabled("baritem_delete");
										idp.control.get("toolbar1").setEnabled("baritem_cancelSubmit");
								
										idp.control.get("toolbar1").setDisabled("baritem_submit_new");
										idp.control.get("toolbar1").setEnabled("baritem_retract_new");
		
										idp.uiview.reloadData();
										return true;
									}
								} else {
									idp.error("提交审批失败！" + result.message);
									return false;
								}
							}
						}).always(function() {
							idp.loaded();
						});
				}, 300);
				return true;
			}
		}).fail(function () {
			idp.loaded();
			return false;
		});
}

/** 撤回新 */
function onRetractCard2New() {
	var row = idp.uiview.modelController.deafaultData[0].data[0];
    var docstatus = row.DOCSTATUS;
    if (docstatus != "1") {
        idp.alert("当前单据状态无法撤回!");
        return false;
    }

    var params = {};
    var data = {};
    data.BILLID = row.ID;
    data.FORMTYPE = "JFPAYPLAN2";
    data.DWID = row.PAYUNITID;
    data.DJBH = row.DOCNO;
    data.PFTASKID = row.APPROVALID;
    data.DQHJBH = "STARTNODE";
    params.BILLID = row.ID;
    params.data = data;

    params.PFPROCINTERACTINFO = {};
    var url = "/api/jtgk/goldwind/unipaybill/v1.0/retract2New";

    idp.loading("加载中...");

    setTimeout(function() {
        idp.service.fetch(url, params, false, "POST")
            .then(function(resInfo) {

                idp.loaded();
                if (resInfo.result) {
                    if (resInfo.code == 0) {
                        idp.alert("撤回成功！");

						idp.control.get("toolbar1").setEnabled("baritem_modify");
						idp.control.get("toolbar1").setEnabled("baritem_submit");
						idp.control.get("toolbar1").setEnabled("baritem_delete");
						idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");
				
						idp.control.get("toolbar1").setEnabled("baritem_submit_new");
						idp.control.get("toolbar1").setDisabled("baritem_retract_new");

                        idp.uiview.reloadData();

                    }
                } else {
                    //弹窗提醒
                    idp.alert("撤回失败！" + resInfo.message);
                }
            }).always(function() {
                idp.loaded();
            });
    }, 300);
}

/** 查看新 */
function onViewCard2New() {
	let url = "/api/jtgk/goldwind/unipaybill/v1.0/view2New";
    var curData = idp.uiview.modelController.deafaultData[0].data[0];

    idp.loading("打开中...");
    var params = {}
    params.BILLID = curData.ID;
    params.FORMTYPE = "JFPAYPLAN2";
    idp.service.fetch(url, params, false, "POST")
        .then(function(result) {
            idp.loaded();
            if (result) {
                if (result.result) {
                    url = encodeURI(encodeURI(result.value.toString()));
                    let mtitile = "查看流程";
                    idp.utils.openurl(curData.ID, mtitile, url, true);
                } else {
                    idp.error(result.message);
                }
            }
        }).always(function() {
            idp.loaded();
        });
}



/** 退回 */
function onCancelSubmitCard() {

	var state = idp.uiview.modelController.getValue("DOCSTATUS");
	if (state == '1') {
		idp.alert("当前单据状态不允许退回");
		return false;
	}

	var data = idp.uiview.modelController.getMainRowObj();
	idp.confirm("确定要退回当前的付款计划单吗？", function () {
		var checkResponse = idp.service.fetch("/api/jtgk/goldwind/unipaybill/v1.0/checkBack?DOCID=" + data.ID + "&DOCNO=" + data.DOCNO, null, false, "GET");
		if (checkResponse.status !== 200) {
			idp.error("请求服务端接口失败");
			return false;
		}
		var checkResult = JSON.parse(checkResponse.responseText);
		if (!checkResult.result) {
			idp.error(checkResult.message);
			return false;
		}
		idp.uiview.reloadData();
	},function() {});
	return true;
}


function managerImage(){
	let IDs = [];
	var data = idp.uiview.modelController.deafaultData[0].data[0];
	IDs.push(data.ID);
	let url = "/api/jtgk/goldwind/settlement/v1.0/managerImage";
	let params = {};
	params.IDs = IDs;
	params.BILLTYPE = "JFPAYPLAN2";
	idp.service.fetch(url, params, false, "POST")
		.then(function(result) {
			if (result.result) {
				let imageId = result.data;
				var dzyxName = "付款安排附件";
				var status = "edit";
				var billtype="";
				const param = {
					BILLSTATE: 'SAVE',
					BillNM: imageId,
					BillCODE: "付款安排附件",
					BillCATEGORY: '',
					BillType: billtype,
					BillTypeID: billtype,
					OPERATION: status,
					USERCODE: idp.context.get("UserCode"),
					SourceSys: 'SYS',
					MKID: 'CM',
					DWBH: '',
					ISAPP: '0',
					TabID: idp.styleId,
					IsInvoice: '1',
					IsShowInvoice: '1'
				};
				idp.service.fetch('/api/BP/EIS/v1.0/imageapi/getyxurlmap', param)
					.then(rtnInfo => {
						if (rtnInfo.code == "ok") {
							urlstr = rtnInfo.data;
							var opts = {
								title: dzyxName,
								name: 'customWindow',
								isHidden: false,
								showMax: false,
								width: 1000,
								slide: false,
								height: 800,
								url: urlstr,
								cls: "",
								content: "", //内容
								onclose: function() {
								},
							};
							var dg = $.leeDialog.open(opts).max();
						} else {
							idp.error(rtnInfo.msg);
							return false;
						}
					}).fail(function (result) {
					idp.error(result.message);
				});

			} else {
				idp.alert(result.message);
			}
		})
		.fail(function(errMsg) {
			console.log(errMsg);
		});
}
