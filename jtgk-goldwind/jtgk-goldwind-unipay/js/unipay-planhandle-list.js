/**
 * 待付池-票据补录-列表
 * /apps/jtgk/jfkj/extend/unipay-planhandle-list.js
 */
idp.event.bind("domReady", function() {
    idp.event.register("grid_main", "selectRow", setToolbarButtonsEnabled);
});

function setToolbarButtonsEnabled(e, data) {
	if (data !== null && data.DOCSTATUS !== null){
		var state = parseInt(data.DOCSTATUS);
		if(state === 2 || state === 3){
			idp.control.get("toolbar1").setDisabled("baritem_modify");
			idp.control.get("toolbar1").setDisabled("baritem_submit");
			idp.control.get("toolbar1").setDisabled("baritem_cancelSubmit");
			idp.control.get("toolbar1").setDisabled("baritem_submit_new");
			idp.control.get("toolbar1").setDisabled("baritem_retract_new");
		}
		if(state === 0){
			idp.control.get("toolbar1").setEnabled("baritem_modify");
			idp.control.get("toolbar1").setEnabled("baritem_submit");
			idp.control.get("toolbar1").setEnabled("baritem_cancelSubmit");
			idp.control.get("toolbar1").setEnabled("baritem_submit_new");
			idp.control.get("toolbar1").setEnabled("baritem_retract_new");
		}
	}
}

/** 查看 */
function onView() {
	var data = idp.control.get('grid_main').selected;
		for (var i = 0; i < data.length; i++) {
			var newUrl = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=4d7e2ab0-61bb-3e09-6ad2-d331747b5527&status=view&runtime=true"
				+ "&dataid=" + data[i].ID;
			idp.utils.openurl(data[i].ID, "票据补录详情", newUrl);
		}
	return true;
}

/** 编辑 */
function onEditList() {
	var data = idp.control.get('grid_main').selected;
	for (var i = 0; i < data.length; i++) {
		var getDocStatusResponse = idp.service.fetch("/api/jtgk/goldwind/unipaybill/v1.0/handleStatus?DOCID=" + data[i].ID, null, false, "GET");
		if (getDocStatusResponse.status !== 200) {
			idp.error("请求服务端接口失败");
			return false;
		}
		var getDocStatusResult = JSON.parse(getDocStatusResponse.responseText);
		if (!getDocStatusResult.result) {
			idp.error(getDocStatusResult.message);
			return false;
		}
		var docStatus = getDocStatusResult.data[i];
		if (docStatus == null || docStatus == "" || docStatus != "0") {
			idp.alert('当前状态不允许编辑');
			return false;
		}
		var newUrl = "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=4d7e2ab0-61bb-3e09-6ad2-d331747b5527&status=edit&runtime=true"
			+ "&dataid=" + data[i].ID;
		idp.utils.openurl(data[i].ID, "票据补录详情", newUrl);
	}
	return true;
}

/** 提交 */
function onSubmitList() {
	var data = idp.control.get('grid_main').getSelected();
	idp.confirm("确定要完成当前单据的票据补录吗？", function () {
		idp.loading("处理中...");
		var url = "/api/jtgk/goldwind/unipaybill/v1.0/handlePass?DOCID=" + data.ID + "&DOCNO=" + data.DOCNO;
		return idp.service.fetch(url, null, true, "GET")
			.then(function (res) {
				idp.loaded();
				if (!res.result) {
					idp.warn(res.message);
					return false;
				}else{
					idp.info("提交成功");
					idp.control.get("grid_main").loadData();
					return true;
				}
			}).fail(function () {
				idp.loaded();
				return false;
			});
		},function() {});
	return true;
}


/** 提交新 */
function onSubmitCardNew() {
	var curDatas = idp.control.get('grid_main').selected;
	for (var i = 0; i < curDatas.length; i++) {
		var curData = curDatas[i];

		idp.confirm("确定要复核当前票据补录单？", function () {
			// 使用共享提交
			var djbh = curData.DOCNO;
			var dwid = curData.PAYUNITID;
			var id = curData.ID;
		
			var params = {};
			var data = {};
			data.DJBH = djbh;
			data.BILLID = id;
			data.FORMTYPE = "JFPAYPLAN3";
			data.DWID = dwid;
		
			params.data = data;
			params.BILLID = id;
			params.PFPROCINTERACTINFO = null;
		
			idp.loading("加载中...");
			var url = "/api/jtgk/goldwind/unipaybill/v1.0/submit3New";
			setTimeout(function() {
				idp.service.fetch(url, params, false, "POST")
					.then(function(result) {
						if (result) {
							if (result.result) {
								idp.info("提交审批成功！");
								idp.loaded();
								idp.control.get("grid_main").loadData();
								return true;
							} else {
								idp.error("提交审批失败！" + result.message);
								return false;
							}
						}
					}).always(function() {
						idp.loaded();
					});
			}, 300);

		},function() {});
	}
}

/** 撤回新 */
function onRetractCardNew() {
	var rows = idp.control.get('grid_main').selected;
	for (var i = 0; i < rows.length; i++) {
		var row = rows[i];
		var docstatus = row.DOCSTATUS;
		if (docstatus != "1") {
			idp.alert("当前单据状态无法撤回!");
			return false;
		}

		var params = {};
		var data = {};
		data.BILLID = row.ID;
		data.FORMTYPE = "JFPAYPLAN3";
		data.DWID = row.PAYUNITID;
		data.DJBH = row.DOCNO;
		data.PFTASKID = row.APPROVALID;
		data.DQHJBH = "STARTNODE";
		params.BILLID = row.ID;
		params.data = data;

		params.PFPROCINTERACTINFO = {};
		var url = "/api/jtgk/goldwind/unipaybill/v1.0/retract3New";

		idp.loading("加载中...");

		setTimeout(function() {
			idp.service.fetch(url, params, false, "POST")
				.then(function(resInfo) {

					idp.loaded();
					if (resInfo.result) {
						if (resInfo.code == 0) {
							idp.alert("撤回成功！");
							idp.control.get("grid_main").loadData();
						}
					} else {
						//弹窗提醒
						idp.alert("撤回失败！" + resInfo.message);
					}
				}).always(function() {
					idp.loaded();
				});
		}, 300);
	}
}

/** 查看新 */
function onViewCardNew() {
	let url = "/api/jtgk/goldwind/unipaybill/v1.0/view3New";
    var curDatas = idp.control.get('grid_main').selected;
	for (var i = 0; i < curDatas.length; i++) {
		var curData = curDatas[i];

		idp.loading("打开中...");
		var params = {}
		params.BILLID = curData.ID;
		params.FORMTYPE = "JFPAYPLAN2";
		idp.service.fetch(url, params, false, "POST")
			.then(function(result) {
				idp.loaded();
				if (result) {
					if (result.result) {
						url = encodeURI(encodeURI(result.value.toString()));
						let mtitile = "查看流程";
						idp.utils.openurl(curData.ID, mtitile, url, true);
					} else {
						idp.error(result.message);
					}
				}
			}).always(function() {
				idp.loaded();
			});
	}
}


/** 退回 */
function onCancelSubmitList() {
	var data = idp.control.get('grid_main').getSelected();
	idp.confirm("确定要退回当前的票据补录单吗？", function () {
		var checkResponse = idp.service.fetch("/api/jtgk/goldwind/unipaybill/v1.0/checkBillBack?DOCID=" + data.ID + "&DOCNO=" + data.DOCNO, null, false, "GET");
		if (checkResponse.status !== 200) {
			idp.error("请求服务端接口失败");
			return false;
		}
		var checkResult = JSON.parse(checkResponse.responseText);
		if (!checkResult.result) {
			idp.error(checkResult.message);
			return false;
		}
		idp.control.get("grid_main").loadData();
	},function() {});
	return true;
}


function managerImage(){
	let IDs = [];
	var data = idp.control.get('grid_main').selected;
	for (var i = 0; i < data.length; i++) {
		IDs.push(data[i].ID);
	}
	let url = "/api/jtgk/goldwind/settlement/v1.0/managerImage";
	let params = {};
	params.IDs = IDs;
	params.BILLTYPE = "JFPAYPLAN3";
	idp.service.fetch(url, params, false, "POST")
		.then(function(result) {
			if (result.result) {
				let imageId = result.data;
				var dzyxName = "付款安排附件";
				var status = "edit";
				var billtype="";
				const param = {
					BILLSTATE: 'SAVE',
					BillNM: imageId,
					BillCODE: "付款安排附件",
					BillCATEGORY: '',
					BillType: billtype,
					BillTypeID: billtype,
					OPERATION: status,
					USERCODE: idp.context.get("UserCode"),
					SourceSys: 'SYS',
					MKID: 'CM',
					DWBH: '',
					ISAPP: '0',
					TabID: idp.styleId,
					IsInvoice: '1',
					IsShowInvoice: '1'
				};
				idp.service.fetch('/api/BP/EIS/v1.0/imageapi/getyxurlmap', param)
					.then(rtnInfo => {
						if (rtnInfo.code == "ok") {
							urlstr = rtnInfo.data;
							var opts = {
								title: dzyxName,
								name: 'customWindow',
								isHidden: false,
								showMax: false,
								width: 1000,
								slide: false,
								height: 800,
								url: urlstr,
								cls: "",
								content: "", //内容
								onclose: function() {
								},
							};
							var dg = $.leeDialog.open(opts).max();
						} else {
							idp.error(rtnInfo.msg);
							return false;
						}
					}).fail(function (result) {
					idp.error(result.message);
				});

			} else {
				idp.alert(result.message);
			}
		})
		.fail(function(errMsg) {
			console.log(errMsg);
		});
}
