package com.inspur.cloud.jtgk.goldwind.unipay.config;

import com.inspur.cloud.jtgk.goldwind.unipay.api.JfskPayBillApi;
import com.inspur.cloud.jtgk.goldwind.unipay.api.JfskUnipayApi;
import com.inspur.cloud.jtgk.goldwind.unipay.controller.JfskSettlementAutoDealController;
import io.iec.edp.caf.rest.RESTEndpoint;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@EntityScan({"com.inspur.cloud.jtgk.goldwind.unipay.entity"})
@EnableJpaRepositories(basePackages ="com.inspur.cloud.jtgk.goldwind.unipay.repository")
@ComponentScan({"com.inspur.cloud.jtgk.goldwind.unipay.**"})
@Configuration
public class JfskUnipayConfiguration {
    @Bean
    public RESTEndpoint jfskUnipayApi(JfskUnipayApi jfskUnipayApi) {
        return new RESTEndpoint("/jtgk/goldwind/settlement/v1.0/",
                jfskUnipayApi
        );
    }

    @Bean
    public RESTEndpoint jfskPayBillApi(JfskPayBillApi jfskPayBillApi) {
        return new RESTEndpoint("/jtgk/goldwind/unipaybill/v1.0/",
                jfskPayBillApi
        );
    }

    @Bean("9ebdc9cb-49cc-4cbd-b616-eb3f536cd5b5")
    public JfskSettlementAutoDealController jfskSettlementAutoDealController() {
        return new JfskSettlementAutoDealController();
    }
}
