package com.inspur.cloud.jtgk.goldwind.unipay.controller;

import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskPaymentAutoDealService;
import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskUnipayResultService;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.SchedulerLoggerUtil;
import com.inspur.edp.cdf.component.api.annotation.GspComponent;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.lockservice.api.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;

/**
 * 付款结果回写
 */
@GspComponent("jtgk-Payment-Auto-Deal-Scheduler")
@Slf4j
public class JfskPaymentAutoDealScheduler {
    @Autowired
    private ILockService lockService;
    @Autowired
    private LogService logService;
    @Autowired
    private JfskPaymentAutoDealService paymentAutoDealService;

    /**
     * 异构系统付款结果处理
     */
    public void processPaymentBiils() {
        String jobId = "c08c2188-3c18-64c0-ae6f-d2c10219853f";
        String jobName = "jtgk业务支付申请自动办理";
        String lockId;
        try {
            String moduleId = "JfskPaymentAutoDealScheduler";
            String funcId = "processPaymentBiils";
            String categoryId = "String";
            String dataID = "jtgk-Payment-Auto-Deal-Scheduler";
            String comment = jobName;
            LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID, new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, LockedScope.AppInstance, Duration.ofMinutes(30)), funcId, comment);
            if (lockResult == null || !lockResult.isSuccess()) {
                SchedulerLoggerUtil.error(jobId, jobName, "加锁失败");
                log.error(jobName + "加锁失败");
                return;
            }
            lockId = lockResult.getLockId();
            log.info(jobName + "加锁结果：lockId=" + lockId);
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, "加锁过程发送异常：" + ex.toString());
            log.error(jobName + "加锁过程发生异常：", ex);
            return;
        }
        logService.init("K1099");
        try {
            paymentAutoDealService.processBills(logService);
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, ex.toString());
            log.error("业务支付申请自动办理过程发生异常：", ex);
        }
        logService.flush();
        try {
            lockService.removeLock(lockId);
            log.info(jobName + "已解锁");
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, "解锁过程发生异常：" + ex.toString());
            log.error(jobName + "解锁过程发生异常：", ex);
        }
    }
}
