package com.inspur.cloud.jtgk.goldwind.unipay.controller;

import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskUnipayResultService;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.SchedulerLoggerUtil;
import com.inspur.edp.cdf.component.api.annotation.GspComponent;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.lockservice.api.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;

/**
 * 付款结果回写
 */
@GspComponent("jtgk-Unipay-Result-Scheduler")
@Slf4j
public class JfskUnipayResultScheduler {
    @Autowired
    private ILockService lockService;
    @Autowired
    private LogService logService;
    @Autowired
    private JfskUnipayResultService unipayResultService;

    /**
     * 异构系统付款结果处理
     */
    public void processPaymentResults() {
        String jobId = "87a1e062-79d4-035d-cf7c-f6bff4018149";
        String jobName = "jtgk待付池付款结果处理";
        String lockId;
        try {
            String moduleId = "JfskUnipayResultScheduler";
            String funcId = "processPaymentResults";
            String categoryId = "String";
            String dataID = "jtgk-unipay-result-process";
            String comment = jobName;
            LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID, new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, LockedScope.AppInstance, Duration.ofMinutes(30)), funcId, comment);
            if (lockResult == null || !lockResult.isSuccess()) {
                SchedulerLoggerUtil.error(jobId, jobName, "加锁失败");
                log.error(jobName + "加锁失败");
                return;
            }
            lockId = lockResult.getLockId();
            log.info(jobName + "加锁结果：lockId=" + lockId);
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, "加锁过程发送异常：" + ex.toString());
            log.error(jobName + "加锁过程发生异常：", ex);
            return;
        }
        logService.init("K0104");
        try {
            unipayResultService.processResult(logService);
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, ex.toString());
            log.error("付款结果回写过程发生异常：", ex);
        }
        logService.flush();
        try {
            lockService.removeLock(lockId);
            log.info(jobName + "已解锁");
        } catch (Throwable ex) {
            SchedulerLoggerUtil.error(jobId, jobName, "解锁过程发生异常：" + ex.toString());
            log.error(jobName + "解锁过程发生异常：", ex);
        }
    }
}
