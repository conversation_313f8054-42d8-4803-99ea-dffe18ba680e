package com.inspur.cloud.jtgk.goldwind.unipay.dto;

import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanBillEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanDetailEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 背书票据分配内部接口返回结果
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AssignBillsResult {
    /**
     * 分配后的背书票据明细
     */
    private List<Map<String, Object>> bills;
    /**
     * 分配后的支付明细
     */
    private List<Map<String, Object>> details;
}
