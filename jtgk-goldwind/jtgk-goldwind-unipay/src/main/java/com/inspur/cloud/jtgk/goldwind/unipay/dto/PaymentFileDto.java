package com.inspur.cloud.jtgk.goldwind.unipay.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 待付池付款申请接收接口:入参，文件明细
 */
@Data
public class PaymentFileDto {
    /** 文件名称 */
    @JSONField(name = "fileName")
    private String fileName;
    /** 影像类型 */
    @JSONField(name = "fileType")
    private String fileType;
    // 文件内容：Base64编码
    @JSONField(name = "fileContent")
    private String encodedFileContent;
    /** 文件说明 */
    @JSONField(name = "remark")
    private String remark;
}
