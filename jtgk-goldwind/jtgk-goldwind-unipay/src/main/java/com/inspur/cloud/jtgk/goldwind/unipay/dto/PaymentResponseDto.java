package com.inspur.cloud.jtgk.goldwind.unipay.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 付款申请接口出参
 */
@Data
public class PaymentResponseDto {
    @JSONField
    private String requestId;
    @JSONField
    private List<PaymentResultDto> results;

    public PaymentResponseDto() {
        this.results = new ArrayList<>();
    }

    public PaymentResponseDto(String requestId) {
        this.requestId = requestId;
        this.results = new ArrayList<>();
    }

    public PaymentResponseDto(String requestId, String error) {
        this.results = new ArrayList<>();
        this.requestId = requestId;
        this.results.add(new PaymentResultDto(null, null, null, false, error));
    }
    
    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
