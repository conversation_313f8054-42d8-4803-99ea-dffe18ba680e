package com.inspur.cloud.jtgk.goldwind.unipay.dto;

import lombok.Data;

/**
 * 待付池付款申请接收接口：出参
 */
public class PaymentResultDto {
    /**
     * 来源系统标识
     */
    private String srcBizSys;
    public String getSrcBizSys() { return this.srcBizSys; }
    public void setSrcBizSys(String srcBizSys) { this.srcBizSys = srcBizSys; }
    /**
     * 来源单据类型
     */
    private String srcDocType;
    public String getSrcDocType() { return this.srcDocType; }
    public void setSrcDocType(String srcDocType) { this.srcDocType = srcDocType; }
    /**
     * 来源单据内码
     */
    private String srcDocId;
    public String getSrcDocId() { return this.srcDocId; }
    public void setSrcDocId(String srcDocId) { this.srcDocId = srcDocId; }
    /**
     * 是否处理成功
     */
    private boolean result;
    public boolean getResult() { return this.result; }
    public void setResult(boolean result) { this.result = result; }
    /**
     * 处理失败时的详细错误说明
     */
    private String message;
    public String getMessage() { return this.message; }
    public void setMessage(String message) { this.message = message; }

    public PaymentResultDto() {}
    public PaymentResultDto(String srcBizSys, String srcDocType, String srcDocId, boolean result, String message) {
        this.srcBizSys = srcBizSys;
        this.srcDocType = srcDocType;
        this.srcDocId = srcDocId;
        this.result = result;
        this.message = message;
    }
}
