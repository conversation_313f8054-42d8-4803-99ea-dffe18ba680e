package com.inspur.cloud.jtgk.goldwind.unipay.dto;

import com.alibaba.fastjson.JSON;

/**
 * 内部接口或方法执行后返回的结果
 * { "result":true, "message":null }
 * <AUTHOR>
 */
public class R {
    /**
     * 处理是否成功
     */
    private Boolean result;
    public void setResult(Boolean result) {
        this.result = result;
    }
    public Boolean getResult() {
        return this.result;
    }

    /**
     * 处理失败说明
     */
    private String message;
    public void setMessage(String message) {
        this.message = message;
    }
    public String getMessage() {
        return this.message;
    }

    public R() {}

    /**
     * 方法执行成功时返回结果（无详细说明）
     * @return 返回结果
     */
    public static R ok() {
        R result = new R();
        result.setResult(true);
        return result;
    }

    /**
     * 方法执行成功时返回结果（有详细说明）
     * @param message 详细说明
     * @return 返回结果
     */
    public static R ok(String message) {
        R result = new R();
        result.setResult(true);
        result.setMessage(message);
        return result;
    }

    /**
     * 方法处理失败时返回结果（有详细说明）
     * @param message 详细说明
     * @return 返回结果
     */
    public static R error(String message) {
        R result = new R();
        result.setResult(false);
        result.setMessage(message);
        return result;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
