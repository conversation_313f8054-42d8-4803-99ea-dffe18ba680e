package com.inspur.cloud.jtgk.goldwind.unipay.dto;

import com.alibaba.fastjson.JSON;

/**
 * 内部接口或方法执行后返回的结果（附带处理结果）
 * { "result":true, "message":null, "data":{} }
 * <AUTHOR>
 */
public class RD<TData> {
    /**
     * 处理是否成功
     */
    private Boolean result;
    public void setResult(Boolean result) {
        this.result = result;
    }
    public Boolean getResult() {
        return this.result;
    }

    /**
     * 处理失败说明
     */
    private String message;
    public void setMessage(String message) {
        this.message = message;
    }
    public String getMessage() {
        return this.message;
    }

    /**
     * 处理成功时应返回的有效数据
     */
    private TData data;
    public void setData(TData data) { this.data = data; }
    public TData getData() { return this.data; }

    public RD() {}

    /**
     * 方法执行成功时返回结果（无详细说明、无处理结果）
     * @param <TData> 处理结果数据类型
     * @return 返回结果
     */
    public static <TData> RD<TData> ok() {
        RD<TData> result = new RD<>();
        result.setResult(true);
        result.setData(null);
        return result;
    }

    /**
     * 方法执行成功时返回结果（有详细说明、无处理结果）
     * @param message 详细说明
     * @param <TData> 处理结果数据类型
     * @return 返回结果
     */
    public static <TData> RD<TData> ok(String message) {
        RD<TData> result = new RD<>();
        result.setResult(true);
        result.setMessage(message);
        result.setData(null);
        return result;
    }

    /**
     * 方法执行成功时返回结果（有详细说明、无处理结果）
     * @param message 详细说明
     * @param data 处理结果
     * @param <TData> 处理结果数据类型
     * @return 返回结果
     */
    public static <TData> RD<TData> ok(String message, TData data) {
        RD<TData> result = new RD<>();
        result.setResult(true);
        result.setMessage(message);
        result.setData(data);
        return result;
    }

    /**
     * 方法处理失败时返回结果（有详细说明、无处理结果）
     * @param message 详细说明
     * @param <TData> 处理结果数据类型
     * @return 返回结果
     */
    public static <TData> RD<TData> error(String message) {
        RD<TData> result = new RD<>();
        result.setResult(false);
        result.setMessage(message);
        result.setData(null);
        return result;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
