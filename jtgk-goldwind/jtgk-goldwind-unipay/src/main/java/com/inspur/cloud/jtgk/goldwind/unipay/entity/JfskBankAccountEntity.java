package com.inspur.cloud.jtgk.goldwind.unipay.entity;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Entity
@Table(name = "BFBANKACCOUNTS")
public class JfskBankAccountEntity {
    @Id
    private String id;
    /**
     * 账号
     */
    private String accountNo;
    /**
     * 账户状态
     * 2,已启用
     */
    private Integer accountStatus;
    /**
     * 账户名称
     */
    @Column(name = "ACCOUNTNAME_CHS")
    private String accountName;
    /**
     * 开户单位ID
     */
    @Column
    private String openAccountUnit;
    /**
     * 直联账户开通状态
     * 1,未开通;2,开通查询;3,开通支付和查询
     */
    @Column
    private Integer onlineBankOpenStatus;
}
