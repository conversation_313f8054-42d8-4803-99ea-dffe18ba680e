package com.inspur.cloud.jtgk.goldwind.unipay.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 付款安排计划
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Entity
@Table(name = "JTGKPAYPLANEXCEL")
public class JfskPayPlanExcelEntity {
    @Id
    private String id;
    /**
     * 付款安排表ID
     */
    @Column(name = "PARENTID")
    private String parentId;
    /**
     * 资金计划编号
     */
    @Column(name = "PLANNO")
    private String planNo;
    /**
     * 付款单位ID
     */
    @Column(name = "PAYUNITID")
    private String payUnitId;
    /**
     * 付款单位编号
     */
    @Column(name = "PAYUNITCODE")
    private String payUnitCode;
    /**
     * 付款单位名称
     */
    @Column(name = "PAYUNITNAME")
    private String payUnitName;
    /**
     * 供应商ID
     */
    @Column(name = "RECEIVINGUNITID")
    private String receivingUnitId;
    /**
     * 供应商编号
     */
    @Column(name = "RECEIVINGUNITCODE")
    private String receivingUnitCode;
    /**
     * 供应商名称
     */
    @Column(name = "RECEIVINGUNITNAME")
    private String receivingUnitName;
    /**
     * 币种ID
     */
    @Column(name = "CURRENCYID")
    private String currencyId;
    /**
     * 币种编号
     */
    @Column(name = "CURRENCYCODE")
    private String currencyCode;
    /**
     * 计划付款总金额
     */
    @Column(name = "TOTALAMOUNT")
    private BigDecimal totalAmount;
    /**
     * 制单人ID
     */
    @Column(name = "TIMESTAMPS_CREATEDBY")
    private String createdBy;
    /**
     * 制单时间
     */
    @Column(name = "TIMESTAMPS_CREATEDON")
    private Date createdOn;
    /**
     * 最后修改人ID
     */
    @Column(name = "TIMESTAMPS_LASTCHANGEDBY")
    private String lastChangedBy;
    /**
     * 最后修改时间
     */
    @Column(name = "TIMESTAMPS_LASTCHANGEDON")
    private Date lastChangedOn;
    /**
     * TXT01
     */
    @Column(name = "TXT01")
    private String txt01;
    /**
     * TXT02
     */
    @Column(name = "TXT02")
    private String txt02;
    /**
     * TXT03
     */
    @Column(name = "TXT03")
    private String txt03;
    /**
     * TXT04
     */
    @Column(name = "TXT04")
    private String txt04;
    /**
     * TXT05
     */
    @Column(name = "TXT05")
    private String txt05;
    /**
     * TXT06
     */
    @Column(name = "TXT06")
    private String txt06;
    /**
     * TXT07
     */
    @Column(name = "TXT07")
    private String txt07;
    /**
     * TXT08
     */
    @Column(name = "TXT08")
    private String txt08;
    /**
     * TXT09
     */
    @Column(name = "TXT09")
    private String txt09;
    /**
     * DATE01
     */
    @Column(name = "DATE01")
    private Date date01;
    /**
     * DATE02
     */
    @Column(name = "DATE02")
    private Date date02;
    /**
     * DATE03
     */
    @Column(name = "DATE03")
    private Date date03;
    /**
     * DATE04
     */
    @Column(name = "DATE04")
    private Date date04;
    /**
     * DATE05
     */
    @Column(name = "DATE05")
    private Date date05;
    /**
     * AMT01
     */
    @Column(name = "AMT01")
    private BigDecimal amt01;
    /**
     * AMT02
     */
    @Column(name = "AMT02")
    private BigDecimal amt02;
    /**
     * AMT03
     */
    @Column(name = "AMT03")
    private BigDecimal amt03;
    /**
     * AMT04
     */
    @Column(name = "AMT04")
    private BigDecimal amt04;
    /**
     * AMT05
     */
    @Column(name = "AMT05")
    private BigDecimal amt05;
}
