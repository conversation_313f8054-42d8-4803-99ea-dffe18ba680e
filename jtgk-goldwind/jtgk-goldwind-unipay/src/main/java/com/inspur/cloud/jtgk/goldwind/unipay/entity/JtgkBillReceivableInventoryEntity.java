package com.inspur.cloud.jtgk.goldwind.unipay.entity;

import lombok.Data;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 应收票据库存
 */
@Data
@Entity
@Table(name = "TMBILLRECEIVABLEINVENTORY")
public class JtgkBillReceivableInventoryEntity {
    @Id
    private String id;
    /** 票据号码 */
    @Column
    private String billNo;
    /** 出票日期 */
    @Column
    private Date billOpenDate;
    /** 到期日期 */
    @Column
    private Date billDueDate;
    /** 票据类型 */
    @Column
    private String billType;
    /**
     * 票据形式：0,纸质票据;1,电子票据
     */
    @Column
    private Integer billForm;
    /**
     * 是否新一代票据：1,是;0,否
     */
    @Column
    private Character newbillFlag;
    /** 使用子票区间起 */
    @Column
    private String subbillStartSn;
    /** 使用子票区间止 */
    @Column
    private String subbillEndSn;
    /**
     * 可分包流转：1,可分包;0,不可分包
     */
    @Column
    private Character splitFlag;
    /** 票据金额 */
    @Column
    private BigDecimal billAmt;
    /**
     * 可转让标记：EM00,可再转让;EM01,不可转让
     */
    @Column
    private String avaendorse;
    /**
     * 后手可转让标记：EM00,可再转让;EM01,不可转让
     */
    @Column
    private String endFlag;
    /**
     * 持票方式：1,自管;2,统管
     */
    @Column
    private Integer cpfs;
    /**
     * 是否直联：0,否;1,是
     */
    @Column
    private Character isEbc;
    /** 票据状态 */
    @Column
    private String billStatus;
    /** 供应链产品 */
    @Column
    private String gylcp;
    /**
     * 计算字段：剩余金额
     */
    @Transient
    private BigDecimal syje;
}
