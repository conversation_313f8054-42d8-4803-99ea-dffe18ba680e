package com.inspur.cloud.jtgk.goldwind.unipay.repository;

import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanDetailEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface JfskPayPlanDetailRepository extends JpaRepository<JfskPayPlanDetailEntity, String> {
    /**
     * 查询付款安排的支付明细
     * @param parentId 付款安排表ID
     * @return 支付明细
     */
    List<JfskPayPlanDetailEntity> findAllByParentId(String parentId);

    /**
     * 查询付款安排复核表关联的支付明细
     * @param parentId2 付款安排复核表ID
     * @return 支付明细
     */
    List<JfskPayPlanDetailEntity> findAllByParentId2(String parentId2);

    /**
     * 查询票据背书办理表关联的支付明细
     * @param parentId3 票据背书办理表ID
     * @return 支付明细
     */
    List<JfskPayPlanDetailEntity> findAllByParentId3(String parentId3);

    /**
     * 查询票据背书办理表关联的支付明细
     * @param logInfoId 接口日志表ID
     * @return 支付明细
     */
    List<JfskPayPlanDetailEntity> findAllByLogInfoId(String logInfoId);
}
