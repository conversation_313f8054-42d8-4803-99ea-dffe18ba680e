package com.inspur.cloud.jtgk.goldwind.unipay.repository;

import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanExcelEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface JfskPayPlanExcelRepository extends JpaRepository<JfskPayPlanExcelEntity, String> {
    List<JfskPayPlanExcelEntity> findAllByParentId(String parentId);
}
