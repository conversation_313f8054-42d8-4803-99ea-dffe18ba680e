package com.inspur.cloud.jtgk.goldwind.unipay.repository;

import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPaymentDetailEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 付款申请执行记录表
 */
@Repository
public interface JfskPaymentDetailRepository extends JpaRepository<JfskPaymentDetailEntity, String> {
    List<JfskPaymentDetailEntity> findAllByParentId(String parentId);
}
