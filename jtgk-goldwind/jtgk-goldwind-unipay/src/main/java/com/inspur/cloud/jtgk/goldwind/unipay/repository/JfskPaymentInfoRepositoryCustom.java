package com.inspur.cloud.jtgk.goldwind.unipay.repository;

import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPaymentInfoEntity;

import java.util.List;

public interface JfskPaymentInfoRepositoryCustom {
    /**
     * 查找指定付款单位、供应商的待支付记录
     * @param payUnitId 付款单位ID
     * @param receivingUnitId 供应商ID
     * @return 待支付明细
     */
    List<JfskPaymentInfoEntity> findUnpayRequests(String payUnitId, String receivingUnitId, String planNo);
}
