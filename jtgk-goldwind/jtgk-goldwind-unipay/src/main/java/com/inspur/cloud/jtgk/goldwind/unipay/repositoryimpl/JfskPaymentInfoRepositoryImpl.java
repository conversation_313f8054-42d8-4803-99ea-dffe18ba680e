package com.inspur.cloud.jtgk.goldwind.unipay.repositoryimpl;

import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.R;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPaymentInfoEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.repository.JfskPaymentInfoRepositoryCustom;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.JfskException;
import com.inspur.fastdweb.model.qry.Qry;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.rpc.api.service.RpcClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.*;

@Repository
@Slf4j
public class JfskPaymentInfoRepositoryImpl implements JfskPaymentInfoRepositoryCustom {
    /**
     * 查找指定付款单位、供应商的待支付记录
     * @param payUnitId 付款单位ID
     * @param receivingUnitId 供应商ID
     * @return 待支付明细
     */
    @Override
    public List<JfskPaymentInfoEntity> findUnpayRequests(String payUnitId, String receivingUnitId, String planNo) {
        List<JfskPaymentInfoEntity> results = new ArrayList<>();
        String selectSql = null;
        try {
            LinkedHashMap<String, Object> rpcGetQryPams = new LinkedHashMap<>();
            rpcGetQryPams.put("qryId", "ee9d8ca8-a1ca-0330-1cf9-6b4fa212cb6c");
            Qry qryEntity = null;
            try {
                RpcClient rpcClient = SpringBeanUtils.getBean(RpcClient.class);
                qryEntity = rpcClient.invoke(Qry.class, "idp.rpc.common.data.service.getQry", "Idp", rpcGetQryPams, null);
            } catch (Throwable ex) {
                log.error("没有找到指定的IDP查询");
                throw new JfskException("没有找到指定的IDP查询", ex);
            }
            selectSql = "select * from (" + qryEntity.qSql + " and JTGKPAYMENTINFO.PAYUNITID='" + payUnitId + "' and JTGKPAYMENTINFO.RECEIVINGUNITID='" + receivingUnitId + "' and JTGKPAYMENTINFO.TXT01='" + planNo + "') T where UNPLANAMOUNT>0 and DOCSTATUS=1 and SRCBIZSYS='SFS-PAY' order by UNPLANAMOUNT DESC";
            log.info(selectSql);
        } catch (Throwable ex) {
            log.error("获取取数SQL时发生异常：", ex);
            throw new JfskException("获取取数SQL时发生异常", ex);
        }
        List<Map<String, Object>> rowsOfRequest = DBUtil.querySql(selectSql);
        log.info(JSON.toJSONString(rowsOfRequest));
        if (rowsOfRequest == null || rowsOfRequest.size() == 0) {
            return results;
        }
        for (int i=0; i<rowsOfRequest.size(); i++) {
            JfskPaymentInfoEntity result = new JfskPaymentInfoEntity();
            result.setId((String)rowsOfRequest.get(i).get("ID"));
            result.setReceivingBankAccountId((String)rowsOfRequest.get(i).get("RECEIVINGBANKACCOUNTID"));
            result.setFundnatureCode((String)rowsOfRequest.get(i).get("FUNDNATURECODE"));
            result.setSettlementWayId((String)rowsOfRequest.get(i).get("SETTLEMENTWAYID"));
            result.setRequestUnitId((String)rowsOfRequest.get(i).get("PAYUNITID"));
            result.setApplicantCode((String)rowsOfRequest.get(i).get("APPLICANTCODE"));
            result.setApplicantName((String)rowsOfRequest.get(i).get("APPLICANTNAME"));
            result.setBillPayWay((Integer)rowsOfRequest.get(i).get("BILLPAYWAY"));
            result.setCreatedOn((Date)rowsOfRequest.get(i).get("TIMESTAMPS_CREATEDON"));
            result.setLastchangedOn((Date)rowsOfRequest.get(i).get("TIMESTAMPS_LASTCHANGEDON"));
            result.setDirectPay((String)rowsOfRequest.get(i).get("ISDIRECTPAY"));
            result.setTransCurrencyCode((String)rowsOfRequest.get(i).get("TRANSCURRENCYCODE"));
            result.setSrcDocId((String)rowsOfRequest.get(i).get("SRCDOCID"));
            result.setApplyDate((String)rowsOfRequest.get(i).get("APPLYDATE"));
            result.setReceivingUnitCode((String)rowsOfRequest.get(i).get("RECEIVINGUNITCODE"));
            result.setCreatedBy((String)rowsOfRequest.get(i).get("TIMESTAMPS_CREATEDBY"));
            result.setRefSrcDocId((String)rowsOfRequest.get(i).get("REFSRCDOCID"));
            result.setRequestDeptId((String)rowsOfRequest.get(i).get("REQUESTDEPTID"));
            result.setTransCurrencyId((String)rowsOfRequest.get(i).get("TRANSCURRENCYID"));
            result.setDescription((String)rowsOfRequest.get(i).get("DESCRIPTION"));
            result.setSrcBizSys((String)rowsOfRequest.get(i).get("SRCBIZSYS"));
            result.setDocStatus((Integer)rowsOfRequest.get(i).get("DOCSTATUS"));
            result.setReceivingBankId((String)rowsOfRequest.get(i).get("RECEIVINGBANKID"));
            result.setTransExchangeRate((BigDecimal)rowsOfRequest.get(i).get("TRANSEXCHANGERATE"));
            result.setRequestAmount((BigDecimal)rowsOfRequest.get(i).get("REQUESTAMOUNT"));
            result.setReceivingUnitId((String)rowsOfRequest.get(i).get("RECEIVINGUNITID"));
            result.setTransAmount((BigDecimal)rowsOfRequest.get(i).get("TRANSAMOUNT"));
            result.setReceivingUnitName((String)rowsOfRequest.get(i).get("RECEIVINGUNITNAME"));
            result.setLastchangedBy((String)rowsOfRequest.get(i).get("TIMESTAMPS_LASTCHANGEDBY"));
            result.setSupplyChainProducts((String)rowsOfRequest.get(i).get("SUPPLYCHAINPRODUCTS"));
            result.setSummary((String)rowsOfRequest.get(i).get("SUMMARY"));
            result.setSrcDocNo((String)rowsOfRequest.get(i).get("SRCDOCNO"));
            result.setRequestDeptCode((String)rowsOfRequest.get(i).get("REQUESTDEPTCODE"));
            result.setFundnatureId((String)rowsOfRequest.get(i).get("FUNDNATUREID"));
            result.setCurrencyCode((String)rowsOfRequest.get(i).get("CURRENCYCODE"));
            result.setExpectPayDate((String)rowsOfRequest.get(i).get("EXPECTPAYDATE"));
            result.setPayAccountId((String)rowsOfRequest.get(i).get("PAYACCOUNTID"));
            result.setSrcDocType((String)rowsOfRequest.get(i).get("SRCDOCTYPE"));
            result.setRequestUnitCode((String)rowsOfRequest.get(i).get("PAYUNITCODE"));
            result.setCurrencyId((String)rowsOfRequest.get(i).get("CURRENCYID"));
            result.setApplicantId((String)rowsOfRequest.get(i).get("APPLICANTID"));
            result.setPayAccountNo((String)rowsOfRequest.get(i).get("PAYACCOUNTID_NO"));
            result.setRequestUnitName((String)rowsOfRequest.get(i).get("PAYUNITID_NAME"));
            result.setPayAccountName((String)rowsOfRequest.get(i).get("PAYACCOUNTID_NAME"));
            result.setRequestDeptName((String)rowsOfRequest.get(i).get("REQUESTDEPTID_NAME"));
            result.setReceivingBankAccountNo((String)rowsOfRequest.get(i).get("RECEIVINGBANKACCOUNTID_NO"));
            result.setReceivingBankAccountName((String)rowsOfRequest.get(i).get("RECEIVINGBANKACCOUNTID_NAME"));
            result.setReceivingBankName((String)rowsOfRequest.get(i).get("RECEIVINGBANKID_NAME"));
            result.setReceivingBankNo((String)rowsOfRequest.get(i).get("RECEIVINGBANKID_CODE"));
            result.setPaidAmount((BigDecimal)rowsOfRequest.get(i).get("PAIDAMOUNT"));
            result.setPayingAmount((BigDecimal)rowsOfRequest.get(i).get("PAYINGAMOUNT"));
            result.setUnpayAmount((BigDecimal)rowsOfRequest.get(i).get("UNPAYAMOUNT"));
            result.setUnplanAmount((BigDecimal)rowsOfRequest.get(i).get("UNPLANAMOUNT"));
            result.setExtPurchaseCode((String)rowsOfRequest.get(i).get("EXTPURCHASECODE"));
            result.setExtCostCenter((String)rowsOfRequest.get(i).get("EXTCOSTCENTER"));
            result.setExtInnerOrder( (String)rowsOfRequest.get(i).get("EXTINNERORDER"));
            result.setExtProjectCode((String)rowsOfRequest.get(i).get("EXTPROJECTCODE"));
            result.setExtProfitCenter((String)rowsOfRequest.get(i).get("EXTPROFITCENTER"));
            result.setTxt01((String)rowsOfRequest.get(i).get("TXT01"));
            result.setTxt02((String)rowsOfRequest.get(i).get("TXT02"));
            result.setTxt03((String)rowsOfRequest.get(i).get("TXT03"));
            result.setTxt04((String)rowsOfRequest.get(i).get("TXT04"));
            result.setTxt05((String)rowsOfRequest.get(i).get("TXT05"));
            result.setTxt06((String)rowsOfRequest.get(i).get("TXT06"));
            result.setTxt07((String)rowsOfRequest.get(i).get("TXT07"));
            result.setTxt08((String)rowsOfRequest.get(i).get("TXT08"));
            result.setTxt09((String)rowsOfRequest.get(i).get("TXT09"));
            result.setTxt10((String)rowsOfRequest.get(i).get("TXT10"));
            result.setTxt11((String)rowsOfRequest.get(i).get("TXT11"));
            result.setTxt12((String)rowsOfRequest.get(i).get("TXT12"));
            result.setTxt13((String)rowsOfRequest.get(i).get("TXT13"));
            result.setTxt14((String)rowsOfRequest.get(i).get("TXT14"));
            result.setTxt15((String)rowsOfRequest.get(i).get("TXT15"));
            result.setTxt16((String)rowsOfRequest.get(i).get("TXT16"));
            result.setTxt17((String)rowsOfRequest.get(i).get("TXT17"));
            result.setTxt18((String)rowsOfRequest.get(i).get("TXT18"));
            result.setTxt19((String)rowsOfRequest.get(i).get("TXT19"));
            result.setTxt20((String)rowsOfRequest.get(i).get("TXT20"));
            result.setAmt01((BigDecimal)rowsOfRequest.get(i).get("AMT01"));
            result.setAmt02((BigDecimal)rowsOfRequest.get(i).get("AMT02"));
            result.setAmt03((BigDecimal)rowsOfRequest.get(i).get("AMT03"));
            result.setAmt04((BigDecimal)rowsOfRequest.get(i).get("AMT04"));
            result.setAmt05((BigDecimal)rowsOfRequest.get(i).get("AMT05"));
            result.setAmt06((BigDecimal)rowsOfRequest.get(i).get("AMT06"));
            result.setAmt07((BigDecimal)rowsOfRequest.get(i).get("AMT07"));
            result.setAmt08((BigDecimal)rowsOfRequest.get(i).get("AMT08"));
            result.setAmt09((BigDecimal)rowsOfRequest.get(i).get("AMT09"));
            result.setAmt10((BigDecimal)rowsOfRequest.get(i).get("AMT10"));
            result.setDate01((String)rowsOfRequest.get(i).get("DATE01"));
            result.setDate02((String)rowsOfRequest.get(i).get("DATE02"));
            result.setDate03((String)rowsOfRequest.get(i).get("DATE03"));
            result.setDate04((String)rowsOfRequest.get(i).get("DATE04"));
            result.setDate05((String)rowsOfRequest.get(i).get("DATE05"));
            result.setDate06((String)rowsOfRequest.get(i).get("DATE06"));
            result.setDate07((String)rowsOfRequest.get(i).get("DATE07"));
            result.setDate08((String)rowsOfRequest.get(i).get("DATE08"));
            result.setDate09((String)rowsOfRequest.get(i).get("DATE09"));
            result.setDate10((String)rowsOfRequest.get(i).get("DATE10"));
            results.add(result);
        }
        return results;
    }
}
