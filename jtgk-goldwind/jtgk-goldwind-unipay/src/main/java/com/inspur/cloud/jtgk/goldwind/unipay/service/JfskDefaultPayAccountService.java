package com.inspur.cloud.jtgk.goldwind.unipay.service;

import com.inspur.cloud.jtgk.goldwind.unipay.dto.RD;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskBankAccountEntity;
import com.inspur.idd.log.api.controller.LogService;

/**
 * 内部接口：默认付款账户
 */
public interface JfskDefaultPayAccountService {
    /**
     * SFS系统：根据付款公司、支付方式、采购组织获取默认付款账户
     * @param logService 接口日志
     * @param srcBizSys 来源系统标识
     * @param srcDocNo 来源单据编号
     * @param payUnitCode 付款公司编号
     * @param purchaseCode 采购组织编号
     * @param payMethod 支付方式编号
     * @return 默认付款账户
     */
    RD<JfskBankAccountEntity> getDefaultPayAccount(LogService logService, String srcBizSys, String srcDocNo, String payUnitCode, String purchaseCode, String payMethod);

    /**
     * CES、HLY：根据付款公司、会计科目编号获取默认付款账户
     * @param logService 接口日志
     * @param srcBizSys 来源系统标识
     * @param srcDocNo 来源单据编号
     * @param payUnitCode 付款公司编号
     * @param subjectCode 会计科目编号
     * @return 默认付款账户
     */
    RD<JfskBankAccountEntity> getDefaultPayAccount(LogService logService, String srcBizSys, String srcDocNo, String payUnitCode, String subjectCode);
}
