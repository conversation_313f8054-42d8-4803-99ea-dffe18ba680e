package com.inspur.cloud.jtgk.goldwind.unipay.service;

import com.inspur.cloud.jtgk.goldwind.unipay.dto.RD;

/**
 * 内部接口：利润中心
 */
public interface JfskProfitCenterService {
    /**
     * 根据项目/成本中心/内部订单获取关联的利润中心
     * @param projectCode 项目WBS编号（非统计性项目根据WBS编号映射利润中心）
     * @param costCenter 成本中心（统计性根据成本中心映射利润中心）
     * @param innerOrder 内部订单（天润根据内部订单映射利润中心）
     * @return 获取的利润中心
     */
    RD<String> getProfitCenter(String projectCode, String costCenter, String innerOrder);
}
