package com.inspur.cloud.jtgk.goldwind.unipay.service;

import com.inspur.cloud.jtgk.goldwind.unipay.dto.*;
import com.inspur.idd.log.api.controller.LogService;

/**
 * 待付池接口
 */
public interface JfskUnipayService {
    /**
     * 业务系统发起付款申请写入代付池并生成业务支付申请或单位调拨单
     * @param logService 接口日志
     * @param request 付款申请信息
     * @return 付款处理结果JSON字符串
     */
    String request(LogService logService, PaymentRequestDto request);

    /**
     * 付款申請退回到業務系統
     * @param docId 付款申請ID
     * @param reason 退回原因
     * @return 是否操作成功
     */
    R backToBizSys(String docId, String reason);
}
