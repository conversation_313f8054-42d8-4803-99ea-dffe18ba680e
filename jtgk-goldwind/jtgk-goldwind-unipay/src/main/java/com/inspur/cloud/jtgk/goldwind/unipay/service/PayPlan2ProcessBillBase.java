package com.inspur.cloud.jtgk.goldwind.unipay.service;

import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.JfskException;
import com.inspur.gs.tm.tmfnd.fsjspub.api.dto.FsspResultRet;
import com.inspur.gs.tm.tmfnd.fsjspub.core.entity.TMProcessBillBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @project jtgk-goldwind
 * @description
 * @date 2025/5/20 07:32:34
 */

@Slf4j
@Component
@Transactional(rollbackFor = Exception.class)
public class PayPlan2ProcessBillBase extends TMProcessBillBase {

    @Override
    public String getFormType() {
        return "JFPAYPLAN2";
    }

    @Override
    public String getFormUrl() {
        return "/apps/fastdweb/views/runtime/page/card/cardpreview.html?styleid=ad2b927e-689f-d432-c8e6-bb6b642beb76&modid=ad2b927e-689f-d432-c8e6-bb6b642beb76";
    }

    @Override
    public Map<String, Object> getBillInfo(String billId) {
        Map<String, Object> mapParams = new HashMap<String, Object>();
        String sql = "select DOCNO,PAYUNITID,ID from JTGKPAYPLANBILL2 where ID=?1 ";
        List<Map<String, Object>> list = DBUtil.querySql(sql, billId);
        if (list.size() > 0) {
            Map<String, Object> map = list.get(0);
            mapParams.put("DJBH", map.get("DOCNO"));
            mapParams.put("DWID", map.get("PAYUNITID"));
            mapParams.put("OTHERINFO", "付款安排审批");
            mapParams.put("PROCESSID", map.get("ID"));
        } else {
            throw new JfskException("goldwind", "PayPlanProcessBillBaseEvent-001", "付款安排审批获取表单信息失败", null);
        }
        return mapParams;
    }

    @Override
    public boolean setBillProcessID(String billId, String processId) {
        String sql = "update JTGKPAYPLANBILL2 set TXT07=?1 where ID=?2 ";
        int upcount = DBUtil.executeUpdateSQL(sql, processId, billId);
        if (upcount == 0) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 提交后事件
     *
     * @return
     */
    @Override
    public FsspResultRet fspfsubmitApprove(Map<String, Object> data) {
        Map<String, Object> map = (Map<String, Object>) data.get("contextParam");
        String id = (String) map.get("BILLID");
        
        String updateSql = "update JTGKPAYPLANBILL2 set DOCSTATUS = '1' where ID = ?1";

        int count = DBUtil.executeUpdateSQL(updateSql, id);
        if (count == 0) {
            throw new JfskException("goldwind", "PayPlanProcessBillBaseEvent-002", "付款安排审批更新失败", null);
        }
        return super.fspfsubmitApprove(data);
    }

    /**
     * 通过后后事件
     *
     * @return
     */
    @Override
    public FsspResultRet fspfapprovePass(Map<String, Object> data) {
//        Map<String, Object> map = (Map<String, Object>) data.get("contextParam");
//        String DQHJBH = (String) map.get("DQHJBH");
//        if (StringUtils.equals(DQHJBH, "APPROVAL-1")) {
//            String docId = (String) map.get("BILLID");
//            checkPass(docId);
//        }
        return super.fspfapprovePass(data);
    }

    /**
     * 退回后事件
     *
     * @return
     */
    @Override
    public FsspResultRet fspfapproveBack(Map<String, Object> data) {
        Map<String, Object> map = (Map<String, Object>) data.get("contextParam");
        String id = (String) map.get("BILLID");

        String updateSql = "update JTGKPAYPLANBILL2 set DOCSTATUS = '3' where ID = ?1";

        int count = DBUtil.executeUpdateSQL(updateSql, id);
        if (count == 0) {
            throw new JfskException("goldwind", "PayPlanProcessBillBaseEvent-002", "付款安排审批更新失败", null);
        }

        return super.fspfapproveBack(map);
    }

    /**
     * 流程结束
     */
    @Override
    public FsspResultRet fspfwflowfinish(Map<String, Object> data) {
        Map<String, Object> map = (Map<String, Object>) data.get("contextParam");
        String id = (String) map.get("BILLID");

        return super.fspfwflowfinish(data);
    }

}
