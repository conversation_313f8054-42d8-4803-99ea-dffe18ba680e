package com.inspur.cloud.jtgk.goldwind.unipay.service.impl;

import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanBillEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanDetailEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPaymentInfoEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.tm.GenerateResultDto;
import com.inspur.cloud.jtgk.goldwind.unipay.tm.bizpaymentrequest.JfskBizPayRequestService;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.TransactionCoordinator;
import com.inspur.idd.log.api.controller.LogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 业务支付申请服务包装器
 * 用于支持全局事务的回滚
 */
@Service
@Slf4j
public class JfskBizPayRequestServiceWrapper {
    // 在共享事务中，用于存储已失败的事务ID
    // key=事务ID，value=Boolean.TRUE表示已失败
    private static final ConcurrentHashMap<String, Boolean> TRANSACTION_FAILED_MAP = new ConcurrentHashMap<>();
    
    @Autowired
    private JfskBizPayRequestService bizPayRequestService;
    
    // 跟踪事务处理阶段
    private enum TxStatus {
        INACTIVE,   // 没有激活事务
        ACTIVE,     // 事务处于活动状态
        COMMIT_PENDING,  // 等待提交阶段
        ROLLBACK_PENDING // 等待回滚阶段
    }
    
    // 存储全局状态
    private static final ConcurrentHashMap<String, TxStatus> GLOBAL_TX_STATUS = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, Boolean> GLOBAL_TX_FAILURE = new ConcurrentHashMap<>();
    
    // 当前事务ID
    private final ThreadLocal<String> txId = new ThreadLocal<>();
    
    // 存储事务期间创建的单据信息
    private final ThreadLocal<List<GenerateResultDto>> transactionResults = new ThreadLocal<>();
    
    /**
     * 开始一个新的事务跟踪会话
     * @param coordinator 事务协调器
     */
    public void beginTracking(TransactionCoordinator coordinator) {
        String id = UUID.randomUUID().toString();
        txId.set(id);
        GLOBAL_TX_STATUS.put(id, TxStatus.ACTIVE);
        GLOBAL_TX_FAILURE.put(id, false);
        transactionResults.set(new ArrayList<>());
        log.info("开始跟踪业务支付申请生成事务 ID: {}", id);
    }
    
    /**
     * 标记事务为失败状态
     */
    public void markTransactionFailed() {
        String id = txId.get();
        if (id != null) {
            log.warn("事务 {} 被标记为失败状态", id);
            GLOBAL_TX_FAILURE.put(id, true);
            GLOBAL_TX_STATUS.put(id, TxStatus.ROLLBACK_PENDING);
        }
        
        // 同时也标记共享状态
        String transactionId = getTransactionId();
        if (transactionId != null) {
            TRANSACTION_FAILED_MAP.put(transactionId, Boolean.TRUE);
        }
    }
    
    /**
     * 检查当前事务是否已经失败
     */
    public boolean isTransactionFailed() {
        String id = txId.get();
        if (id != null) {
            // 首先检查线程内状态
            if (GLOBAL_TX_FAILURE.getOrDefault(id, false)) {
                return true;
            }
        }
        
        // 然后检查共享状态
        String transactionId = getTransactionId();
        if (transactionId != null) {
            Boolean failed = TRANSACTION_FAILED_MAP.get(transactionId);
            return failed != null && failed;
        }
        
        return false;
    }
    
    /**
     * 获取当前事务ID
     * @return 当前事务ID，如果不在事务中则返回null
     */
    private String getTransactionId() {
        return Thread.currentThread().getName() + "-" + Thread.currentThread().getId();
    }
    
    /**
     * 清除事务失败标记
     * 通常在事务结束时调用
     */
    public void clearTransactionFailedMark() {
        String transactionId = getTransactionId();
        if (transactionId != null) {
            log.info("清除事务失败标记：{}", transactionId);
            TRANSACTION_FAILED_MAP.remove(transactionId);
        }
    }
    
    /**
     * 结束事务跟踪会话
     * 如果事务失败，需要通过其他方式回滚已创建的单据
     * @param success 事务是否成功
     * @return 已创建的单据列表
     */
    public List<GenerateResultDto> endTracking(boolean success) {
        String id = txId.get();
        List<GenerateResultDto> results = transactionResults.get();
        
        if (id != null) {
            log.info("结束业务支付申请跟踪，事务ID: {}，成功: {}", id, success);
            
            // 如果不成功，标记为回滚状态
            if (!success) {
                GLOBAL_TX_STATUS.put(id, TxStatus.ROLLBACK_PENDING);
                GLOBAL_TX_FAILURE.put(id, true);
            } else {
                GLOBAL_TX_STATUS.put(id, TxStatus.COMMIT_PENDING);
            }
            
            // 如果事务失败，记录需要回滚的单据
            if (!success && results != null && !results.isEmpty()) {
                log.warn("事务失败，需要回滚 {} 个业务支付申请单据", results.size());
                
                // 这里可以实现补偿逻辑，例如通过API撤销单据
                for (GenerateResultDto dto : results) {
                    if (dto.getResult()) {
                        log.warn("需要回滚文档: {} ({})", dto.getDocNo(), dto.getDocId());
                        // TODO: 调用撤销或删除API
                    }
                }
            }
            
            // 清理
            GLOBAL_TX_STATUS.remove(id);
            GLOBAL_TX_FAILURE.remove(id);
        }
        
        txId.remove();
        transactionResults.remove();
        
        return results != null ? results : new ArrayList<>();
    }
}