package com.inspur.cloud.jtgk.goldwind.unipay.service.impl;

import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskPaymentAutoDealService;
import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskUnipayResultService;
import com.inspur.cloud.jtgk.goldwind.unipay.tm.paymentsettlement.PaymentSettlementRpcGenerateResult;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.RpcUtils;
import com.inspur.fastdweb.util.StringUtil;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.commons.transaction.JpaTransaction;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.lockservice.api.*;
import io.iec.edp.caf.rpc.api.service.RpcClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;

/**
 * 付款结果回写
 */
@Service
@Slf4j
public class JfskPaymentAutoDealServiceImpl implements JfskPaymentAutoDealService {
    @Autowired
    private LogService logService;

    /**
     * 定时获取付款结果
     */
    @Override
    public void processBills(LogService logService) {
        String billCode = "JSBL"; // 业务支付申请自动办理的日志代码
        logService.info(billCode, "开始业务支付申请自动办理任务");

        // 注入必要的服务
        ILockService lockService = SpringBeanUtils.getBean(ILockService.class);
        RpcClient rpcClient = SpringBeanUtils.getBean(RpcClient.class);

        // 处理现汇业务
        processCashBills(billCode, lockService, rpcClient, logService);
        
        // 处理票据业务
        processBillBills(billCode, lockService, rpcClient, logService);

        logService.info(billCode, "业务支付申请自动办理任务执行完成");
        logService.flush();
    }

    /**
     * 处理现汇业务
     */
    private void processCashBills(String billCode, ILockService lockService, RpcClient rpcClient, LogService logService) {
        // 查询待办理的现汇业务支付申请
        String cashSQL = "select BPBizPaymentReqReceiver.ID,BPBizPaymentRequest.DOCNO,BPBizPaymentRequest.ExtBizType,BFAdminOrganization.Code as PayUnitCode,BPBizPaymentReqReceiver.OrderNo,BFSettlementWay.Code as ExpSettleWayCode,BPBizPaymentReqReceiver.TXT01,BPBizPaymentRequest.SrcBizSys,BPBizPaymentRequest.SrcDocNo,BPBizPaymentReqReceiver.BillPayWay,BPBizPaymentReqReceiver.PayAccount\n" +
                "from BPBizPaymentReqReceiver\n" +
                "inner join BPBizPaymentRequest on BPBizPaymentReqReceiver.PARENTID=BPBizPaymentRequest.ID\n" +
                "inner join BFAdminOrganization on BFAdminOrganization.ID=BPBizPaymentRequest.PayUnit\n" +
                "inner join BFBankAccounts on BPBizPaymentReqReceiver.PayAccount=BFBankAccounts.ID\n" +
                "inner join BFSettlementWay on BFSettlementWay.ID=BPBizPaymentReqReceiver.ExpSettleWay\n" +
                "inner join JTGKPAYMENTDETAIL on JTGKPAYMENTDETAIL.requestDocId=BPBizPaymentRequest.ID\n" +
                "inner join JTGKPAYMENTINFO on JTGKPAYMENTINFO.ID=JTGKPAYMENTDETAIL.parentID\n" +
                "where 1=1\n" +
                "-- 未办理\n" +
                "and BPBizPaymentReqReceiver.TXT06 is null\n" +
                "-- 待办理\n" +
                "and BPBizPaymentReqReceiver.REQRECEIVERSTATUS=3\n" +
                "and exists (select 1 from tmjsdata where tmjsdata.rwlx='JSBL' and BPBizPaymentRequest.ID=tmjsdata.DJNM)\n" +
                "-- 结算方式为财企直连或者金风云信\n" +
                "and BFSettlementWay.Code in ('01', '04')";

        logService.info(billCode, "执行现汇业务查询SQL：{}", cashSQL);
        List<Map<String, Object>> mapOfBills = DBUtil.querySql(cashSQL);

        if (mapOfBills == null || mapOfBills.isEmpty()) {
            logService.info(billCode, "未找到待办理的现汇业务支付申请");
            return;
        }

        logService.info(billCode, "查询到{}条待办理的现汇业务支付申请", mapOfBills.size());

        // 处理每条业务支付申请
        for (int i = 0; i < mapOfBills.size(); i++) {
            Map<String, Object> rowOfBill = mapOfBills.get(i);
            String id = (String) rowOfBill.get("ID");
            String docNo = (String) rowOfBill.get("DOCNO");
            logService.info(billCode, "准备办理第{}条/共{}条现汇业务支付申请：{}", (i + 1), mapOfBills.size(), JSON.toJSON(rowOfBill));

            String lockId;
            try {
                String moduleId = "spgtm";
                String funcId = "SpgtmPaymentRequestScheduler";
                String categoryId = "autoHandleCore";
                String dataID = id;
                String comment = "业务支付申请结算自动办理前加锁";
                LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID,
                        new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, LockedScope.AppInstance, Duration.ofMinutes(30)),
                        funcId, comment);

                if (lockResult == null || !lockResult.isSuccess()) {
                    logService.error(billCode, "业务支付申请结算自动办理前加锁失败");
                    continue;
                }
                lockId = lockResult.getLockId();
                logService.info(billCode, "业务支付申请结算自动办理前加锁结果：lockId={}", lockId);
            } catch (Throwable e) {
                logService.error(billCode, "业务支付申请结算自动办理前加锁过程发生异常");
                logService.error(billCode, e);
                continue;
            }

            // 第一步：生成企业付款单
            boolean generateSuccess = false;
            String qyfkdId = null;
            JpaTransaction transOfHandle = JpaTransaction.getTransaction();
            try {
                transOfHandle.begin();
                logService.info(billCode, "JpaTransaction事务(生单)已启动");

                LinkedHashMap<String, Object> paramsOfHandleRpc = new LinkedHashMap<String, Object>();
                Map<String, Object> mapOfHandleRpc = new HashMap<>();
                mapOfHandleRpc.put("bizReceiverId", id);
                paramsOfHandleRpc.put("params", mapOfHandleRpc);
                logService.info(billCode, "产品自动结算办理生成企业付款单接口入参：{}", JSON.toJSONString(paramsOfHandleRpc));

                Map resultOfHandleRpc = rpcClient.invoke(Map.class,
                        "com.inspur.gs.bp.brpc.settlementplatform.api.service.ITmSettlementHandleRPCService.autoHandleTMSettlement",
                        "Brpc", paramsOfHandleRpc, null);
                logService.info(billCode, "产品自动结算办理生成企业付款单接口调用结果：{}", JSON.toJSONString(resultOfHandleRpc));

                if (null == resultOfHandleRpc) {
                    transOfHandle.rollback();
                    logService.error(billCode, "(生单事务回滚)产品自动结算办理生成企业付款单接口未返回有效结果");
                } else {
                    String messageOfRpc = (String)resultOfHandleRpc.get("message");
                    if (0 == ((int)resultOfHandleRpc.get("code"))) {
                        transOfHandle.rollback();
                        updateAutoHandleStatus(id, docNo, 0, messageOfRpc, logService, billCode);
                        logService.error(billCode, "(生单事务回滚)产品自动结算办理生成企业付款单接口未处理:{}", messageOfRpc);
                    } else if (1 == ((int)resultOfHandleRpc.get("code"))) {
                        String valueOfHandleRpc = (String) resultOfHandleRpc.get("value");
                        if (!StringUtil.isNullOrEmpty(valueOfHandleRpc)) {
                            Map<String, Object> mapValueOfHandleRpc = JSON.parseObject(valueOfHandleRpc, Map.class);
                            if (mapValueOfHandleRpc != null && mapValueOfHandleRpc.get("success") != null) {
                                List<PaymentSettlementRpcGenerateResult> generateResultList = JSON.parseArray((String)mapValueOfHandleRpc.get("success"), PaymentSettlementRpcGenerateResult.class);
                                if (generateResultList != null && generateResultList.size() > 0) {
                                    qyfkdId = generateResultList.get(0).getDataId();
                                }
                            }
                        }
                        if (!StringUtil.isNullOrEmpty(qyfkdId)) {
                            transOfHandle.commit();
                            generateSuccess = true;
                            logService.info(billCode, "(事务提交)产品自动结算办理生成企业付款单接口处理成功：TMPaymentSettlement.ID={}", qyfkdId);
                        } else {
                            messageOfRpc = "未能从报文" + valueOfHandleRpc + "中解析到企业付款单ID";
                            transOfHandle.rollback();
                            updateAutoHandleStatus(id, docNo, -1, messageOfRpc, logService, billCode);
                            logService.error(billCode, "(生单事务回滚)产品自动结算办理生成企业付款单接口处理失败:{}", messageOfRpc);
                        }
                    } else {
                        transOfHandle.rollback();
                        updateAutoHandleStatus(id, docNo, -1, messageOfRpc, logService, billCode);
                        logService.error(billCode, "(生单事务回滚)产品自动结算办理生成企业付款单接口处理失败:{}", messageOfRpc);
                    }
                }
            } catch (Throwable ex) {
                transOfHandle.rollback();
                String message = RpcUtils.getBizErrorInException(ex);
                message = "结算自动办理失败: " + (StringUtil.isNullOrEmpty(message) ? ex.getMessage() : message);
                updateAutoHandleStatus(id, docNo, -1, message, logService, billCode);
                logService.error(billCode, "(生单事务回滚)业务支付申请结算自动办理出错：{}", JSON.toJSONString(rowOfBill));
                logService.error(billCode, ex);
            }

            if (generateSuccess) {
                // 第二步：提交流程
                boolean submitSuccess = false;
                JpaTransaction transOfSubmit = JpaTransaction.getTransaction();
                try {
                    transOfSubmit.begin();
                    logService.info(billCode, "JpaTransaction事务(提交)已启动");

                    LinkedHashMap<String, Object> paramsOfSubmitRpc = new LinkedHashMap<String, Object>();
                    Map<String, Object> mapOfSubmitRpc = new HashMap<>();
                    // 需要提交的企业付款单
                    mapOfSubmitRpc.put("settlementId", qyfkdId);
                    // 企业付款单检查是否通过：1通过，0不通过
                    mapOfSubmitRpc.put("checkPassConfirm", "1");
                    paramsOfSubmitRpc.put("params", mapOfSubmitRpc);
                    logService.info(billCode, "产品自动结算提交企业付款单接口入参：{}", JSON.toJSONString(paramsOfSubmitRpc));

                    Map resultOfSubmitRpc = rpcClient.invoke(Map.class,
                            "com.inspur.gs.bp.brpc.settlementplatform.api.service.ITmSettlementHandleRPCService.submitTMSettlement",
                            "Brpc", paramsOfSubmitRpc, null);
                    logService.info(billCode, "产品自动结算提交企业付款单接口调用结果：{}", JSON.toJSONString(resultOfSubmitRpc));

                    if (null == resultOfSubmitRpc) {
                        transOfSubmit.rollback();
                        logService.error(billCode, "(提交事务回滚)产品自动结算提交企业付款单接口未返回有效结果");
                    } else {
                        String messageOfRpc = (String) resultOfSubmitRpc.get("message");
                        if (1 == ((int) resultOfSubmitRpc.get("code"))) {
                            transOfSubmit.commit();
                            submitSuccess = true;
                            updateAutoHandleStatus(id, docNo, 1, messageOfRpc, logService, billCode);
                            logService.info(billCode, "(事务提交)产品自动结算提交企业付款单接口处理成功");
                        } else {
                            transOfSubmit.rollback();
                            updateAutoHandleStatus(id, docNo, -1, messageOfRpc, logService, billCode);
                            logService.error(billCode, "(提交事务回滚)产品自动结算提交企业付款单接口处理失败:{}", messageOfRpc);
                        }
                    }
                } catch (Throwable ex) {
                    transOfSubmit.rollback();
                    String message = RpcUtils.getBizErrorInException(ex);
                    message = "企业付款单提交失败：" + (StringUtil.isNullOrEmpty(message) ? ex.getMessage() : message);
                    updateAutoHandleStatus(id, docNo, -1, message, logService, billCode);
                    logService.error(billCode, "(提交事务回滚)业务支付申请自动提交出错：{}", JSON.toJSONString(rowOfBill));
                    logService.error(billCode, ex);
                }

                // 第三步：提交失败时取消办理
                if (!submitSuccess) {
                    logService.warn(billCode, "业务支付申请提交失败时取消办理");
                    try {
                        Map<String, Object> mapOfDrawRpc = new HashMap<>();
                        mapOfDrawRpc.put("FORMTYPE", "TM_QYFKD");
                        mapOfDrawRpc.put("ID", qyfkdId);
                        LinkedHashMap<String, Object> paramsOfDrawRpc = new LinkedHashMap<>();
                        paramsOfDrawRpc.put("map", mapOfDrawRpc);
                        logService.info(billCode, "产品企业付款单提交失败取消办理接口入参：{}", JSON.toJSONString(paramsOfDrawRpc));

                        rpcClient.invoke(Map.class,
                                "com.inspur.gs.tm.cm.paymentsettlementidp.api.controller.PaymentSettlementIdpController.payDocCancelHandle",
                                "CM", paramsOfDrawRpc, null);

                        String deleteSql = "delete from TMPAYMENTSETTLEMENT where ID='" + qyfkdId + "'";
                        logService.info(billCode, "准备删除企业付款单: {}", deleteSql);
                        int rowsCount = DBUtil.executeUpdateSQL(deleteSql);
                        logService.info(billCode, "受影响行数：{}", rowsCount);
                    } catch (Throwable ex) {
                        String message = RpcUtils.getBizErrorInException(ex);
                        message = "取消办理失败：" + (StringUtil.isNullOrEmpty(message) ? ex.getMessage() : message);
                        updateAutoHandleStatus(id, docNo, -1, message, logService, billCode);
                        logService.error(billCode, "产品企业付款单提交失败取消办理接口出错：{}", JSON.toJSONString(rowOfBill));
                        logService.error(billCode, ex);
                    }
                }
            }

            try {
                lockService.removeLock(lockId);
                logService.info(billCode, "业务支付申请结算自动办理后解锁");
            } catch (Throwable e) {
                logService.error(billCode, "业务支付申请结算自动办理后解锁过程发生异常");
                logService.error(billCode, e);
            }
        }
    }

    /**
     * 处理票据业务
     */
    private void processBillBills(String billCode, ILockService lockService, RpcClient rpcClient, LogService logService) {
        // 查询待办理的票据业务支付申请
        String billSQL = "select distinct BPBizPaymentReqReceiver.ID,BPBizPaymentRequest.DOCNO,BPBizPaymentRequest.ExtBizType,BPBizPaymentReqReceiver.OrderNo,BFSettlementWay.Code as ExpSettleWayCode,BPBizPaymentReqReceiver.TXT01,BPBizPaymentRequest.SrcBizSys,BPBizPaymentRequest.SrcDocNo,BPBizPaymentReqReceiver.BillPayWay,BPBizPaymentReqReceiver.PayAccount\n" +
                "from BPBizPaymentReqReceiver\n" +
                "inner join BPBizPaymentRequest on BPBizPaymentReqReceiver.PARENTID=BPBizPaymentRequest.ID\n" +
                "-- inner join BFAdminOrganization on BFAdminOrganization.ID=BPBizPaymentRequest.PayUnit\n" +
                "inner join BFSettlementWay on BFSettlementWay.ID=BPBizPaymentReqReceiver.ExpSettleWay\n" +
                "inner join JTGKPAYMENTDETAIL on JTGKPAYMENTDETAIL.requestDocId=BPBizPaymentRequest.ID\n" +
                "inner join JTGKPAYMENTINFO on JTGKPAYMENTINFO.ID=JTGKPAYMENTDETAIL.parentID\n" +
                "where 1=1\n" +
                "-- 未办理\n" +
                "and BPBizPaymentReqReceiver.TXT06 is null\n" +
                "-- 待办理\n" +
                "and BPBizPaymentReqReceiver.REQRECEIVERSTATUS=3\n" +
                "and exists (select 1 from tmjsdata where tmjsdata.rwlx='JSBL' and BPBizPaymentRequest.ID=tmjsdata.DJNM)\n" +
                "-- 结算方式为财企直连或者金风云信\n" +
                "and BFSettlementWay.Code in ('02', '03')\n" +
                "-- 来源系统是SFS-PAY\n" +
                "and JTGKPAYMENTINFO.SRCBIZSYS = 'SFS-PAY'";

        logService.info(billCode, "执行票据业务查询SQL：{}", billSQL);
        List<Map<String, Object>> mapOfBills = DBUtil.querySql(billSQL);

        if (mapOfBills == null || mapOfBills.isEmpty()) {
            logService.info(billCode, "未找到待办理的票据业务支付申请");
            return;
        }

        logService.info(billCode, "查询到{}条待办理的票据业务支付申请", mapOfBills.size());

        // 处理每条票据业务支付申请
        for (int i = 0; i < mapOfBills.size(); i++) {
            Map<String, Object> rowOfBill = mapOfBills.get(i);
            String id = (String) rowOfBill.get("ID");
            String docNo = (String) rowOfBill.get("DOCNO");
            logService.info(billCode, "准备办理第{}条/共{}条票据业务支付申请：{}", (i + 1), mapOfBills.size(), JSON.toJSON(rowOfBill));

            String lockId;
            try {
                String moduleId = "spgtm";
                String funcId = "SpgtmPaymentRequestScheduler";
                String categoryId = "autoHandleCore";
                String dataID = id;
                String comment = "票据业务支付申请结算自动办理前加锁";
                LockResult lockResult = lockService.addLock(moduleId, categoryId, dataID,
                        new DataLockOptions(Duration.ofMinutes(30), ReplacedScope.Exclusive, LockedScope.AppInstance, Duration.ofMinutes(30)),
                        funcId, comment);

                if (lockResult == null || !lockResult.isSuccess()) {
                    logService.error(billCode, "票据业务支付申请结算自动办理前加锁失败");
                    continue;
                }
                lockId = lockResult.getLockId();
                logService.info(billCode, "票据业务支付申请结算自动办理前加锁结果：lockId={}", lockId);
            } catch (Throwable e) {
                logService.error(billCode, "票据业务支付申请结算自动办理前加锁过程发生异常");
                logService.error(billCode, e);
                continue;
            }

            // 票据业务直接一步生成提交
            JpaTransaction transOfBill = JpaTransaction.getTransaction();
            try {
                transOfBill.begin();
                logService.info(billCode, "JpaTransaction事务(票据业务)已启动");

                LinkedHashMap<String, Object> paramsOfBillRpc = new LinkedHashMap<String, Object>();
                Map<String, Object> mapOfBillRpc = new HashMap<>();
                mapOfBillRpc.put("bizReceiverId", id);
                paramsOfBillRpc.put("params", mapOfBillRpc);
                logService.info(billCode, "票据业务自动结算办理接口入参：{}", JSON.toJSONString(paramsOfBillRpc));

                Map resultOfBillRpc = rpcClient.invoke(Map.class,
                        "com.inspur.gs.bp.brpc.settlementplatform.api.service.ITmSettlementHandleRPCService.autoHandleBillSettlement",
                        "Brpc", paramsOfBillRpc, null);
                logService.info(billCode, "票据业务自动结算办理接口调用结果：{}", JSON.toJSONString(resultOfBillRpc));

                if (null == resultOfBillRpc) {
                    transOfBill.rollback();
                    logService.error(billCode, "(票据业务事务回滚)票据业务自动结算办理接口未返回有效结果");
                } else {
                    String messageOfRpc = (String) resultOfBillRpc.get("message");
                    if (1 == ((int) resultOfBillRpc.get("code"))) {
                        transOfBill.commit();
                        updateAutoHandleStatus(id, docNo, 1, messageOfRpc, logService, billCode);
                        logService.info(billCode, "(事务提交)票据业务自动结算办理接口处理成功");
                    } else {
                        transOfBill.rollback();
                        updateAutoHandleStatus(id, docNo, -1, messageOfRpc, logService, billCode);
                        logService.error(billCode, "(票据业务事务回滚)票据业务自动结算办理接口处理失败:{}", messageOfRpc);
                    }
                }
            } catch (Throwable ex) {
                transOfBill.rollback();
                String message = RpcUtils.getBizErrorInException(ex);
                message = "票据业务结算自动办理失败: " + (StringUtil.isNullOrEmpty(message) ? ex.getMessage() : message);
                updateAutoHandleStatus(id, docNo, -1, message, logService, billCode);
                logService.error(billCode, "(票据业务事务回滚)票据业务支付申请结算自动办理出错：{}", JSON.toJSONString(rowOfBill));
                logService.error(billCode, ex);
            }

            try {
                lockService.removeLock(lockId);
                logService.info(billCode, "票据业务支付申请结算自动办理后解锁");
            } catch (Throwable e) {
                logService.error(billCode, "票据业务支付申请结算自动办理后解锁过程发生异常");
                logService.error(billCode, e);
            }
        }
    }

    /**
     * 更新自动办理状态
     */
    private void updateAutoHandleStatus(String id, String docNo, int status, String message, LogService logService, String billCode) {
        try {
            // 这里需要根据实际业务需求更新状态，可能是更新 BPBizPaymentReqReceiver.TXT03 字段
            // 示例：
            String updateSql = "update BPBizPaymentReqReceiver set TXT06 = ? where ID = ?";
            String statusText = status == 1 ? "办理成功" : status == 0 ? "未处理" : "办理失败";
            statusText += "：" + message;
            int count = DBUtil.executeUpdateSQL(updateSql, statusText, id);
            logService.info(billCode, "更新业务支付申请办理状态：ID={}, DOCNO={}, STATUS={}, MESSAGE={}, 影响行数={}",
                    id, docNo, status, message, count);
        } catch (Exception e) {
            logService.error(billCode, "更新业务支付申请办理状态发生异常");
            logService.error(billCode, e);
        }
    }
}
