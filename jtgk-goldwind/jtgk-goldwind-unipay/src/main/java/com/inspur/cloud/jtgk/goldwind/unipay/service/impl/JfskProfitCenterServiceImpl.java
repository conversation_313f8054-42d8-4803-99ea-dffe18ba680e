package com.inspur.cloud.jtgk.goldwind.unipay.service.impl;

import com.inspur.cloud.jtgk.goldwind.unipay.dto.RD;
import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskProfitCenterService;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.JfskException;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 内部接口：利润中心
 */
@Service
@Slf4j
public class JfskProfitCenterServiceImpl implements JfskProfitCenterService {
    /**
     * 根据项目/成本中心/内部订单获取关联的利润中心
     * @param projectCode 项目WBS编号（非统计性项目根据WBS编号映射利润中心）
     * @param costCenter 成本中心（统计性根据成本中心映射利润中心）
     * @param innerOrder 内部订单（天润根据内部订单映射利润中心）
     * @return 获取的利润中心
     */
    @Override
    public RD<String> getProfitCenter(String projectCode, String costCenter, String innerOrder) {
        if (!StringUtil.isNullOrEmpty(projectCode)) {
            throw new JfskException("未实现的方法");
        }
        if (!StringUtil.isNullOrEmpty(costCenter)) {
            String selectSql = "select ID,COST_CENTER_CODE,PROFIT_CENTER_CODE from JTGKCBZXRESULT where COST_CENTER_CODE=?1";
            log.info(selectSql + ", ?1=" + costCenter);
            List<Map<String, Object>> rowsOfCostCenter = DBUtil.querySql(selectSql, costCenter);
            if (rowsOfCostCenter == null || rowsOfCostCenter.isEmpty()) {
                log.error("未找到指定的成本中心: costCenterCode=" + costCenter);
                return RD.error("未找到指定的成本中心: costCenterCode=" + costCenter);
            }
            String profitCenterCode = (String) rowsOfCostCenter.get(0).get("PROFIT_CENTER_CODE");
            if (StringUtil.isNullOrEmpty(profitCenterCode)) {
                log.error("成本中心" + costCenter + "未定义对应的利润中心");
                return RD.error("成本中心" + costCenter + "未定义对应的利润中心");
            }
            return RD.ok(null, profitCenterCode);
        }
        if (!StringUtil.isNullOrEmpty(innerOrder)) {
            throw new JfskException("未实现的方法");
        }
        return RD.error("项目WBS编号、成本中心编号、内部订单编号不能同时为空");
    }
}
