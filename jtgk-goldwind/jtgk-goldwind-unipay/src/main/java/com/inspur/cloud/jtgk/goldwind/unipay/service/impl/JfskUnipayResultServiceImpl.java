package com.inspur.cloud.jtgk.goldwind.unipay.service.impl;

import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.service.JfskUnipayResultService;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.idd.log.api.controller.LogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * 付款结果回写
 */
@Service
@Slf4j
public class JfskUnipayResultServiceImpl implements JfskUnipayResultService {
    @Autowired
    private LogService logService;


    private void updatePaymentInfoStatus(String logInfoId, String srcDocNo, BigDecimal paidAmount) {
        // 付款申请（已安排、已付款）更新状态为已完成
        // 确保所有JTGKPAYMENTINFO对应的JTGKPAYMENTDETAIL都已完成再更新
        String querySql = "select ID, AMOUNT from JTGKPAYMENTDETAIL where DOCSTATUS=1 and PARENTID='" + logInfoId + "'";
        List<Map<String, Object>> rowsOfPaymentDetail = DBUtil.querySql(querySql);
        if (rowsOfPaymentDetail != null && !rowsOfPaymentDetail.isEmpty()) {
            BigDecimal totalPaidAmount = new BigDecimal(0);
            for (Map<String, Object> rowOfPaymentDetail : rowsOfPaymentDetail) {
                totalPaidAmount = totalPaidAmount.add((BigDecimal) rowOfPaymentDetail.get("AMOUNT"));
            }
            if (totalPaidAmount.compareTo(paidAmount) == 0) {
                // 所有JTGKPAYMENTDETAIL都已完成，更新JTGKPAYMENTINFO状态为已完成
                String updateSql2 = "update JTGKPAYMENTINFO set DOCSTATUS=3 where DOCSTATUS=2 and ID='" + logInfoId + "' and not exists (select 1 from JTGKPAYMENTDETAIL WHERE DOCSTATUS=0 and PARENTID=JTGKPAYMENTINFO.ID)";
                log.info(updateSql2);
                logService.info(srcDocNo, updateSql2);
                int count2 = DBUtil.executeUpdateSQL(updateSql2);
                log.info("受影响行数：" + count2);
                logService.info(srcDocNo, "受影响行数：" + count2);
            }
        }
    }

    /**
     * 定时获取付款结果
     */
    @Override
    public void processResult(LogService logService) {
        Date now = new Date();
        log.info("准备获取已完成的企业付款单");
        String selectOfPaymentSettlement = "SELECT LD.ID,LD.PARENTID,LM.SRCBIZSYS,LM.SRCDOCNO,LM.ISDIRECTPAY,\n" +
                "PB.ID AS PAYDOCID, -- 付款单ID\n" +
                "PB.DOCNO AS PAYDOCNO, -- 付款单号\n" +
                "BF.ID AS BANKFLOWID, -- 银行流水ID\n" +
                "PB.BANKFLOWNO, -- 银行流水号\n" +
                "(CASE WHEN BF.TRANSACTIONTIME IS NOT NULL THEN BF.TRANSACTIONTIME ELSE PB.BOOKINGDATE END) AS PAYEDON, -- 支付时间\n" +
                "BR.PAIDAMOUNT -- 支付金额\n" +
                "FROM TMPAYMENTSETTLEMENT PB\n" +
                "INNER JOIN BPBIZPAYMENTREQRECEIVER BR ON BR.ID=PB.SRCDOCID\n" +
                "INNER JOIN JTGKPAYMENTDETAIL LD ON LD.REQUESTDOCID=BR.PARENTID\n" +
                "inner join JTGKPAYMENTINFO LM ON LD.PARENTID=LM.ID\n" +
                "LEFT JOIN BPBANKTRANSCATIONDETAILS BF ON BF.BANKFLOWNO=PB.BANKFLOWNO\n" +
                "WHERE LD.PAYDOCTYPE='0' AND PB.DOCSTATUS=11 \n" +
                "AND LD.DOCSTATUS=0 ";
        log.info(selectOfPaymentSettlement);
        List<Map<String, Object>> rowsOfPaymentSettlement = DBUtil.querySql(selectOfPaymentSettlement);
        log.info(JSON.toJSONString(rowsOfPaymentSettlement));
        if (rowsOfPaymentSettlement != null && !rowsOfPaymentSettlement.isEmpty()) {
            log.info("待处理的企业付款单：" + rowsOfPaymentSettlement.size());
            for (int i=0; i<rowsOfPaymentSettlement.size(); i++) {
                log.info("准备处理第" + (i+1) + "条/共" + rowsOfPaymentSettlement.size() + "条企业付款单");
                try {
                    String logDetailId = (String) rowsOfPaymentSettlement.get(i).get("ID");
                    String logInfoId = (String) rowsOfPaymentSettlement.get(i).get("PARENTID");
                    String srcBizSys = (String) rowsOfPaymentSettlement.get(i).get("SRCBIZSYS");
                    String srcDocNo = (String) rowsOfPaymentSettlement.get(i).get("SRCDOCNO");
                    String payDocId = (String) rowsOfPaymentSettlement.get(i).get("PAYDOCID");
                    String payDocNo = (String) rowsOfPaymentSettlement.get(i).get("PAYDOCNO");
                    String bankflowId = (String) rowsOfPaymentSettlement.get(i).get("BANKFLOWID");
                    String bankflowNo = (String) rowsOfPaymentSettlement.get(i).get("BANKFLOWNO");
                    Date payedOn = (Date) rowsOfPaymentSettlement.get(i).get("PAYEDON");
                    BigDecimal paidAmount = (BigDecimal) rowsOfPaymentSettlement.get(i).get("PAIDAMOUNT");
                    String msg1 = "准备更新待付池付款结果：ID=" + logDetailId + ", 来源系统=" + srcBizSys +", 来源单据编号=" + srcDocNo + ", 付款单号=" + payDocNo + ", 已付金额=" + paidAmount;
                    log.info(msg1);
                    logService.info(srcDocNo, msg1);
                    String updateSql1 = "update JTGKPAYMENTDETAIL set DOCSTATUS=1,RESULTBACKSTATUS=1,TIMESTAMPS_LASTCHANGEDON=?7,OPERATEDON=?6,PAIDAMOUNT=?5,PAYDOCID=?1,PAYDOCNO=?2,BANKFLOWID=?3,BANKFLOWNO=?4 where DOCSTATUS=0 and ID='" + logDetailId + "'";
                    String msg2 = updateSql1 + ", ?1=" + payDocId + ", ?2=" + payDocNo + ", ?3=" + bankflowId + ", ?4=" + bankflowNo + ", ?5=" + paidAmount + ", ?6=" + payedOn + ", ?7=" + now;
                    log.info(msg2);
                    logService.info(srcDocNo, msg2);
                    int count1 = DBUtil.executeUpdateSQL(updateSql1, payDocId, payDocNo, bankflowId, bankflowNo, paidAmount, payedOn, now);
                    log.info("受影响行数：" + count1);
                    logService.info(srcDocNo, "受影响行数：" + count1);

                    updatePaymentInfoStatus(logInfoId, srcDocNo, paidAmount);
                    
                } catch (Throwable ex) {
                    log.error("待付池付款结果处理付款过程发生异常：" + JSON.toJSONString(rowsOfPaymentSettlement.get(i)) + "\n" + ex.toString());
                }
            }
        }

        
            

        log.info("准备获取正在进行的的企业付款单");
        String selectOfPaymentSettlement2 = "SELECT LD.ID,LD.PARENTID,LM.SRCBIZSYS,LM.SRCDOCNO,LM.ISDIRECTPAY,\n" +
                "PB.ID AS PAYDOCID, -- 付款单ID\n" +
                "PB.DOCNO AS PAYDOCNO, -- 付款单号\n" +
                "BF.ID AS BANKFLOWID, -- 银行流水ID\n" +
                "PB.BANKFLOWNO, -- 银行流水号\n" +
                "(CASE WHEN BF.TRANSACTIONTIME IS NOT NULL THEN BF.TRANSACTIONTIME ELSE PB.BOOKINGDATE END) AS PAYEDON, -- 支付时间\n" +
                "BR.PAIDAMOUNT, -- 支付金额\n" +
                "PB.DOCSTATUS -- 状态\n" +
                "FROM TMPAYMENTSETTLEMENT PB\n" +
                "INNER JOIN BPBIZPAYMENTREQRECEIVER BR ON BR.ID=PB.SRCDOCID\n" +
                "INNER JOIN JTGKPAYMENTDETAIL LD ON LD.REQUESTDOCID=BR.PARENTID\n" +
                "inner join JTGKPAYMENTINFO LM ON LD.PARENTID=LM.ID\n" +
                "LEFT JOIN BPBANKTRANSCATIONDETAILS BF ON BF.BANKFLOWNO=PB.BANKFLOWNO\n" +
                "WHERE LD.PAYDOCTYPE='0' AND PB.DOCSTATUS<>-1 and PB.DOCSTATUS<>11 \n" +
                "AND LD.DOCSTATUS=0 and (LD.TXT05 is null or LD.TXT05 <> CAST(PB.DOCSTATUS AS VARCHAR))";
        log.info(selectOfPaymentSettlement2);
        List<Map<String, Object>> rowsOfPaymentSettlement2 = DBUtil.querySql(selectOfPaymentSettlement2);
        log.info(JSON.toJSONString(rowsOfPaymentSettlement2));
        if (rowsOfPaymentSettlement2 != null && !rowsOfPaymentSettlement2.isEmpty()) {
            log.info("待处理的企业付款单：" + rowsOfPaymentSettlement2.size());
            for (int i=0; i<rowsOfPaymentSettlement2.size(); i++) {
                log.info("准备处理第" + (i+1) + "条/共" + rowsOfPaymentSettlement2.size() + "条企业付款单");
                try {
                    String logDetailId = (String) rowsOfPaymentSettlement2.get(i).get("ID");
                    String logInfoId = (String) rowsOfPaymentSettlement2.get(i).get("PARENTID");
                    String srcBizSys = (String) rowsOfPaymentSettlement2.get(i).get("SRCBIZSYS");
                    String srcDocNo = (String) rowsOfPaymentSettlement2.get(i).get("SRCDOCNO"); 
                    String payDocId = (String) rowsOfPaymentSettlement2.get(i).get("PAYDOCID");
                    String payDocNo = (String) rowsOfPaymentSettlement2.get(i).get("PAYDOCNO");
                    String bankflowId = (String) rowsOfPaymentSettlement2.get(i).get("BANKFLOWID");
                    String bankflowNo = (String) rowsOfPaymentSettlement2.get(i).get("BANKFLOWNO");
                    BigDecimal paidAmount = (BigDecimal) rowsOfPaymentSettlement2.get(i).get("PAIDAMOUNT");
                    String docStatus = String.valueOf(rowsOfPaymentSettlement2.get(i).get("DOCSTATUS"));
                    String msg1 = "准备更新待付池付款结果：ID=" + logDetailId + ", 来源系统=" + srcBizSys +", 来源单据编号=" + srcDocNo + ", 付款单号=" + payDocNo + ", 已付金额=" + paidAmount;
                    log.info(msg1);
                    logService.info(srcDocNo, msg1);
                    String updateSql1 = "update JTGKPAYMENTDETAIL set TXT05 =?7, TIMESTAMPS_LASTCHANGEDON=?6,PAIDAMOUNT=?5,PAYDOCID=?1,PAYDOCNO=?2,BANKFLOWID=?3,BANKFLOWNO=?4 where DOCSTATUS=0 and ID='" + logDetailId + "'";
                    String msg2 = updateSql1 + ", ?1=" + payDocId + ", ?2=" + payDocNo + ", ?3=" + bankflowId + ", ?4=" + bankflowNo + ", ?5=" + paidAmount + ", ?6=" + now + ", ?7=" + docStatus;
                    log.info(msg2);
                    logService.info(srcDocNo, msg2);
                    int count1 = DBUtil.executeUpdateSQL(updateSql1, payDocId, payDocNo, bankflowId, bankflowNo, paidAmount, now, docStatus);
                    log.info("受影响行数：" + count1);
                    logService.info(srcDocNo, "受影响行数：" + count1);
                } catch (Throwable ex) {
                    log.error("待付池付款结果处理付款过程发生异常：" + JSON.toJSONString(rowsOfPaymentSettlement2.get(i)) + "\n" + ex.toString());
                }
            }
        }

        log.info("准备获取已完成的单位调拨单");
        String selectOfUnitTransfer = "SELECT LD.ID,LD.PARENTID,LM.SRCBIZSYS,LM.SRCDOCNO,LM.ISDIRECTPAY,\n" +
                "PB.ID AS PAYDOCID, -- 付款单ID\n" +
                "PB.DOCNO AS PAYDOCNO, -- 付款单号\n" +
                "BF.ID AS BANKFLOWID, -- 银行流水ID\n" +
                "PB.BANKFLOWNO, -- 付款交易流水号\n" +
                "(CASE WHEN PB.BOOKINGDATE IS NOT NULL THEN PB.BOOKINGDATE ELSE PB.TIMESTAMPS_LASTCHANGEDON END) AS PAYEDON, -- 支付时间\n" +
                "PB.PAYMENTAMOUNT as PAIDAMOUNT -- 支付金额\n" +
                "FROM TMPAYMENTSETTLEMENT PB\n" +
                "INNER JOIN (JTGKPAYMENTDETAIL LD INNER JOIN JTGKPAYMENTINFO LM ON LD.PARENTID=LM.ID) ON LD.PAYDOCTYPE='5' AND LD.PAYDOCID=PB.ID\n" +
                "LEFT JOIN BPSETTLEMENTANDRECEIPT LINK ON LINK.SETTLEMENTID=PB.ID\n" +
                "LEFT JOIN BPBANKTRANSCATIONDETAILS BF ON BF.ID=LINK.BANKTRANSID\n" +
                "WHERE PB.BIZTYPE='4' AND PB.DOCSTATUS=11 -- 付款成功\n" +
                "AND LD.DOCSTATUS=0";
        log.info(selectOfUnitTransfer);
        List<Map<String, Object>> rowsOfUnitTransfer = DBUtil.querySql(selectOfUnitTransfer);
        log.info(JSON.toJSONString(rowsOfUnitTransfer));
        if (rowsOfUnitTransfer != null && !rowsOfUnitTransfer.isEmpty()) {
            log.info("待处理的单位调拨单：" + rowsOfUnitTransfer.size());
            for (int i=0; i<rowsOfUnitTransfer.size(); i++) {
                log.info("准备处理第" + (i+1) + "条/共" + rowsOfUnitTransfer.size() + "条单位调拨单");
                try {
                    String logDetailId = (String) rowsOfUnitTransfer.get(i).get("ID");
                    String logInfoId = (String) rowsOfUnitTransfer.get(i).get("PARENTID");
                    String srcBizSys = (String) rowsOfUnitTransfer.get(i).get("SRCBIZSYS");
                    String srcDocNo = (String) rowsOfUnitTransfer.get(i).get("SRCDOCNO");
                    String payDocId = (String) rowsOfUnitTransfer.get(i).get("PAYDOCID");
                    String payDocNo = (String) rowsOfUnitTransfer.get(i).get("PAYDOCNO");
                    String bankflowId = (String) rowsOfUnitTransfer.get(i).get("BANKFLOWID");
                    String bankflowNo = (String) rowsOfUnitTransfer.get(i).get("BANKFLOWNO");
                    Date payedOn = (Date) rowsOfUnitTransfer.get(i).get("PAYEDON");
                    BigDecimal paidAmount = (BigDecimal) rowsOfUnitTransfer.get(i).get("PAIDAMOUNT");
                    String msg1 = "准备更新待付池付款结果：ID=" + logDetailId + ", 来源系统=" + srcBizSys +", 来源单据编号=" + srcDocNo + ", 付款单号=" + payDocNo + ", 已付金额=" + paidAmount;
                    log.info(msg1);
                    logService.info(srcDocNo, msg1);
                    String updateSql1 = "update JTGKPAYMENTDETAIL set DOCSTATUS=1,RESULTBACKSTATUS=1,TIMESTAMPS_LASTCHANGEDON=?7,OPERATEDON=?6,PAIDAMOUNT=?5,PAYDOCID=?1,PAYDOCNO=?2,BANKFLOWID=?3,BANKFLOWNO=?4 where DOCSTATUS=0 and ID='" + logDetailId + "'";
                    String msg2 = updateSql1 + ", ?1=" + payDocId + ", ?2=" + payDocNo + ", ?3=" + bankflowId + ", ?4=" + bankflowNo + ", ?5=" + paidAmount + ", ?6=" + payedOn;
                    log.info(msg2);
                    logService.info(srcDocNo, msg2);
                    int count1 = DBUtil.executeUpdateSQL(updateSql1, payDocId, payDocNo, bankflowId, bankflowNo, paidAmount, payedOn, now);
                    log.info("受影响行数：" + count1);
                    logService.info(srcDocNo, "受影响行数：" + count1);

                    updatePaymentInfoStatus(logInfoId, srcDocNo, paidAmount);
                } catch (Throwable ex) {
                    log.error("待付池付款结果处理调拨过程发生异常：" + JSON.toJSONString(rowsOfUnitTransfer.get(i)) + "\n" + ex.toString());
                }
            }
        }

        log.info("准备获取正在进行的的单位调拨单");
        String selectOfUnitTransfer2 = "SELECT LD.ID,LD.PARENTID,LM.SRCBIZSYS,LM.SRCDOCNO,LM.ISDIRECTPAY,\n" +
                "PB.ID AS PAYDOCID, -- 付款单ID\n" +
                "PB.DOCNO AS PAYDOCNO, -- 付款单号\n" +
                "BF.ID AS BANKFLOWID, -- 银行流水ID\n" +
                "PB.BANKFLOWNO, -- 付款交易流水号\n" +
                "(CASE WHEN PB.BOOKINGDATE IS NOT NULL THEN PB.BOOKINGDATE ELSE PB.TIMESTAMPS_LASTCHANGEDON END) AS PAYEDON, -- 支付时间\n" +
                "PB.PAYMENTAMOUNT as PAIDAMOUNT, -- 支付金额\n" +
                "PB.DOCSTATUS -- 状态\n" +
                "FROM TMPAYMENTSETTLEMENT PB\n" +
                "INNER JOIN (JTGKPAYMENTDETAIL LD INNER JOIN JTGKPAYMENTINFO LM ON LD.PARENTID=LM.ID) ON LD.PAYDOCTYPE='5' AND LD.PAYDOCID=PB.ID\n" +
                "LEFT JOIN BPSETTLEMENTANDRECEIPT LINK ON LINK.SETTLEMENTID=PB.ID\n" +
                "LEFT JOIN BPBANKTRANSCATIONDETAILS BF ON BF.ID=LINK.BANKTRANSID\n" +
                "WHERE PB.BIZTYPE='4' AND PB.DOCSTATUS<>11 and PB.DOCSTATUS<>-1 \n" +
                "AND LD.DOCSTATUS=0 and (LD.TXT05 is null or LD.TXT05 <> CAST(PB.DOCSTATUS AS VARCHAR))";
        log.info(selectOfUnitTransfer2);
        List<Map<String, Object>> rowsOfUnitTransfer2 = DBUtil.querySql(selectOfUnitTransfer2);
        log.info(JSON.toJSONString(rowsOfUnitTransfer2));
        if (rowsOfUnitTransfer2 != null && !rowsOfUnitTransfer2.isEmpty()) {
            log.info("待处理的单位调拨单：" + rowsOfUnitTransfer2.size());
            for (int i=0; i<rowsOfUnitTransfer2.size(); i++) {
                log.info("准备处理第" + (i+1) + "条/共" + rowsOfUnitTransfer2.size() + "条单位调拨单");
                try {
                    String logDetailId = (String) rowsOfUnitTransfer2.get(i).get("ID");
                    String logInfoId = (String) rowsOfUnitTransfer2.get(i).get("PARENTID");
                    String srcBizSys = (String) rowsOfUnitTransfer2.get(i).get("SRCBIZSYS");
                    String srcDocNo = (String) rowsOfUnitTransfer2.get(i).get("SRCDOCNO");
                    String payDocId = (String) rowsOfUnitTransfer2.get(i).get("PAYDOCID");
                    String payDocNo = (String) rowsOfUnitTransfer2.get(i).get("PAYDOCNO");
                    String bankflowId = (String) rowsOfUnitTransfer2.get(i).get("BANKFLOWID");
                    String bankflowNo = (String) rowsOfUnitTransfer2.get(i).get("BANKFLOWNO");
                    Date payedOn = (Date) rowsOfUnitTransfer2.get(i).get("PAYEDON");
                    BigDecimal paidAmount = (BigDecimal) rowsOfUnitTransfer2.get(i).get("PAIDAMOUNT");
                    String docStatus = String.valueOf(rowsOfUnitTransfer2.get(i).get("DOCSTATUS"));
                    String msg1 = "准备更新待付池付款结果：ID=" + logDetailId + ", 来源系统=" + srcBizSys +", 来源单据编号=" + srcDocNo + ", 付款单号=" + payDocNo + ", 已付金额=" + paidAmount;
                    log.info(msg1);
                    logService.info(srcDocNo, msg1);
                    String updateSql1 = "update JTGKPAYMENTDETAIL set TXT05 =?8, TIMESTAMPS_LASTCHANGEDON=?7,OPERATEDON=?6,PAIDAMOUNT=?5,PAYDOCID=?1,PAYDOCNO=?2,BANKFLOWID=?3,BANKFLOWNO=?4 where DOCSTATUS=0 and ID='" + logDetailId + "'";
                    String msg2 = updateSql1 + ", ?1=" + payDocId + ", ?2=" + payDocNo + ", ?3=" + bankflowId + ", ?4=" + bankflowNo + ", ?5=" + paidAmount + ", ?6=" + payedOn + ", ?7=" + now + ", ?8=" + docStatus;
                    log.info(msg2);
                    logService.info(srcDocNo, msg2);
                    int count1 = DBUtil.executeUpdateSQL(updateSql1, payDocId, payDocNo, bankflowId, bankflowNo, paidAmount, payedOn, now, docStatus);
                    log.info("受影响行数：" + count1);
                    logService.info(srcDocNo, "受影响行数：" + count1);
                } catch (Throwable ex) {
                    log.error("待付池付款结果处理调拨过程发生异常：" + JSON.toJSONString(rowsOfUnitTransfer2.get(i)) + "\n" + ex.toString());
                }
            }
        }


        log.info("准备获取已完成的开票申请单");
        String selectOfBillOpen = "SELECT LD.ID,LD.PARENTID,LM.SRCBIZSYS,LM.SRCDOCNO,LM.ISDIRECTPAY,\n" +
                "BM.ID AS PAYDOCID, -- 开票申请单ID\n" +
                "BM.DOCNO AS PAYDOCNO, -- 开票申请单号\n" +
                "STRING_AGG(\n" +
                "    BD.BILLNO || '(' || \n" +
                "    CASE \n" +
                "        WHEN BD.SUBBILLSTARTSN IS NULL OR BD.SUBBILLSTARTSN = '' OR \n" +
                "             BD.SUBBILLENDSN IS NULL OR BD.SUBBILLENDSN = '' \n" +
                "        THEN '0-0'\n" +
                "        ELSE BD.SUBBILLSTARTSN || '-' || BD.SUBBILLENDSN\n" +
                "    END || ')-' || CAST(ROUND(BD.BILLAMT, 2) AS VARCHAR), \n" +
                "    ',' ORDER BY BD.BILLNO\n" +
                ") AS BILLNO, -- 票据号(格式化)\n" +
                "STRING_AGG(\n" +
                "    CASE \n" +
                "        WHEN BD.SUBBILLSTARTSN IS NULL OR BD.SUBBILLSTARTSN = '' \n" +
                "        THEN '0' \n" +
                "        ELSE BD.SUBBILLSTARTSN \n" +
                "    END, \n" +
                "    ',' ORDER BY BD.BILLNO\n" +
                ") AS SUBBILLSTARTSN, -- 子票区间起(聚合)\n" +
                "STRING_AGG(\n" +
                "    CASE \n" +
                "        WHEN BD.SUBBILLENDSN IS NULL OR BD.SUBBILLENDSN = '' \n" +
                "        THEN '0' \n" +
                "        ELSE BD.SUBBILLENDSN \n" +
                "    END, \n" +
                "    ',' ORDER BY BD.BILLNO\n" +
                ") AS SUBBILLENDSN, -- 子票区间止(聚合)\n" +
                "MIN(BD.BILLOPENDATE) AS BILLOPENDATE, -- 开票日期(取最早)\n" +
                "MAX(BD.BILLDUEDATE) AS BILLDUEDATE, -- 到期日期(取最晚)\n" +
                "MAX(BD.TIMESTAMP_LASTCHANGEDON) AS PAYEDON, -- 支付时间(取最新)\n" +
                "BR.PAIDAMOUNT -- 支付金额\n" +
                "FROM (TMBILLOPENDETAIL BD INNER JOIN TMBILLINGREQUEST BM ON BD.PARENTID=BM.ID)\n" +
                "INNER JOIN BPBIZPAYMENTREQRECEIVER BR ON BM.SRCDOCID=BR.ID\n" +
                "INNER JOIN JTGKPAYMENTDETAIL LD ON LD.PAYDOCTYPE='0' AND LD.REQUESTDOCID=BR.PARENTID\n" +
                "inner join JTGKPAYMENTINFO LM ON LD.PARENTID=LM.ID\n" +
                "WHERE BM.DOCSTATUS=5\n" +
                "AND LD.DOCSTATUS=0\n" +
                "GROUP BY LD.ID,LD.PARENTID,LM.SRCBIZSYS,LM.SRCDOCNO,LM.ISDIRECTPAY,BM.ID,BM.DOCNO,BR.PAIDAMOUNT";
        log.info(selectOfBillOpen);
        List<Map<String, Object>> rowsOfBillOpen = DBUtil.querySql(selectOfBillOpen);
        log.info(JSON.toJSONString(rowsOfBillOpen));
        if (rowsOfBillOpen != null && !rowsOfBillOpen.isEmpty()) {
            log.info("待处理的开票申请单：" + rowsOfBillOpen.size());
            for (int i=0; i<rowsOfBillOpen.size(); i++) {
                log.info("准备处理第" + (i+1) + "条/共" + rowsOfBillOpen.size() + "条开票申请单");
                try {
                    String logDetailId = (String) rowsOfBillOpen.get(i).get("ID");
                    String logInfoId = (String) rowsOfBillOpen.get(i).get("PARENTID");
                    String srcBizSys = (String) rowsOfBillOpen.get(i).get("SRCBIZSYS");
                    String srcDocNo = (String) rowsOfBillOpen.get(i).get("SRCDOCNO");
                    String payDocId = (String) rowsOfBillOpen.get(i).get("PAYDOCID");
                    String payDocNo = (String) rowsOfBillOpen.get(i).get("PAYDOCNO");
                    String billNo = (String) rowsOfBillOpen.get(i).get("BILLNO");
                    String subbillStartSn = (String) rowsOfBillOpen.get(i).get("SUBBILLSTARTSN");
                    String subbillEndSn = (String) rowsOfBillOpen.get(i).get("SUBBILLENDSN");
                    Date billOpenDate = (Date) rowsOfBillOpen.get(i).get("BILLOPENDATE");
                    Date billDueDate = (Date) rowsOfBillOpen.get(i).get("BILLDUEDATE");
                    Date payedOn = (Date) rowsOfBillOpen.get(i).get("PAYEDON");
                    BigDecimal paidAmount = (BigDecimal) rowsOfBillOpen.get(i).get("PAIDAMOUNT");
                    String msg1 = "准备更新待付池付款结果：ID=" + logDetailId + ", 来源系统=" + srcBizSys +", 来源单据编号=" + srcDocNo + ", 开票申请单号=" + payDocNo + ", 票据号=" + billNo + ", 开票金额=" + paidAmount;
                    log.info(msg1);
                    logService.info(srcDocNo, msg1);
                    String updateSql1 = "update JTGKPAYMENTDETAIL set PAYDOCTYPE='1',DOCSTATUS=1,RESULTBACKSTATUS=1," +
                            "TIMESTAMPS_LASTCHANGEDON=?10,OPERATEDON=?9,PAIDAMOUNT=?8,PAYDOCID=?1,PAYDOCNO=?2," +
                            "BILLNO=?3,SUBBILLSTARTSN=?4,SUBBILLENDSN=?5,BILLOPENDATE=?6,BILLDUEDATE=?7"
                            + " where DOCSTATUS=0 and ID='" + logDetailId + "'";
                    String msg2 = updateSql1 + ", ?1=" + payDocId + ", ?2=" + payDocNo + ", ?3=" + billNo +
                            ", ?4=" + subbillStartSn + ", ?5=" + subbillEndSn + ", ?6=" + billOpenDate + ", ?7=" + billDueDate +
                            ", ?8=" + paidAmount + ", ?9=" + payedOn + ", ?10=" + now;
                    log.info(msg2);
                    logService.info(srcDocNo, msg2);
                    int count1 = DBUtil.executeUpdateSQL(updateSql1, payDocId, payDocNo, billNo,
                            subbillStartSn, subbillEndSn, billOpenDate, billDueDate,
                            paidAmount, payedOn, now);
                    log.info("受影响行数：" + count1);
                    logService.info(srcDocNo, "受影响行数：" + count1);

                    updatePaymentInfoStatus(logInfoId, srcDocNo, paidAmount);
                } catch (Throwable ex) {
                    log.error("待付池付款结果处理开票过程发生异常：" + JSON.toJSONString(rowsOfBillOpen.get(i)) + "\n" + ex.toString());
                }
            }
        }

        log.info("准备获取进行中的的开票申请单");
        String selectOfBillOpen2 = "SELECT LD.ID,LD.PARENTID,LM.SRCBIZSYS,LM.SRCDOCNO,LM.ISDIRECTPAY,\n" +
                "BM.ID AS PAYDOCID, -- 开票申请单ID\n" +
                "BM.DOCNO AS PAYDOCNO, -- 开票申请单号\n" +
                "STRING_AGG(\n" +
                "    BD.BILLNO || '(' || \n" +
                "    CASE \n" +
                "        WHEN BD.SUBBILLSTARTSN IS NULL OR BD.SUBBILLSTARTSN = '' OR \n" +
                "             BD.SUBBILLENDSN IS NULL OR BD.SUBBILLENDSN = '' \n" +
                "        THEN '0-0'\n" +
                "        ELSE BD.SUBBILLSTARTSN || '-' || BD.SUBBILLENDSN\n" +
                "    END || ')-' || CAST(ROUND(BD.BILLAMT, 2) AS VARCHAR), \n" +
                "    ',' ORDER BY BD.BILLNO\n" +
                ") AS BILLNO, -- 票据号(格式化)\n" +
                "STRING_AGG(\n" +
                "    CASE \n" +
                "        WHEN BD.SUBBILLSTARTSN IS NULL OR BD.SUBBILLSTARTSN = '' \n" +
                "        THEN '0' \n" +
                "        ELSE BD.SUBBILLSTARTSN \n" +
                "    END, \n" +
                "    ',' ORDER BY BD.BILLNO\n" +
                ") AS SUBBILLSTARTSN, -- 子票区间起(聚合)\n" +
                "STRING_AGG(\n" +
                "    CASE \n" +
                "        WHEN BD.SUBBILLENDSN IS NULL OR BD.SUBBILLENDSN = '' \n" +
                "        THEN '0' \n" +
                "        ELSE BD.SUBBILLENDSN \n" +
                "    END, \n" +
                "    ',' ORDER BY BD.BILLNO\n" +
                ") AS SUBBILLENDSN, -- 子票区间止(聚合)\n" +
                "MIN(BD.BILLOPENDATE) AS BILLOPENDATE, -- 开票日期(取最早)\n" +
                "MAX(BD.BILLDUEDATE) AS BILLDUEDATE, -- 到期日期(取最晚)\n" +
                "MAX(BD.TIMESTAMP_LASTCHANGEDON) AS PAYEDON, -- 支付时间(取最新)\n" +
                "BR.PAIDAMOUNT, -- 支付金额\n" +
                "BM.DOCSTATUS -- 状态\n" +
                "FROM (TMBILLOPENDETAIL BD INNER JOIN TMBILLINGREQUEST BM ON BD.PARENTID=BM.ID)\n" +
                "INNER JOIN BPBIZPAYMENTREQRECEIVER BR ON BM.SRCDOCID=BR.ID\n" +
                "INNER JOIN JTGKPAYMENTDETAIL LD ON LD.PAYDOCTYPE='0' AND LD.REQUESTDOCID=BR.PARENTID\n" +
                "inner join JTGKPAYMENTINFO LM ON LD.PARENTID=LM.ID\n" +
                "WHERE BM.DOCSTATUS<>-1 and BM.DOCSTATUS<>5\n" +
                "AND LD.DOCSTATUS=0 and (LD.TXT05 is null or LD.TXT05 <> CAST(BM.DOCSTATUS AS VARCHAR))\n" +
                "GROUP BY LD.ID,LD.PARENTID,LM.SRCBIZSYS,LM.SRCDOCNO,LM.ISDIRECTPAY,BM.ID,BM.DOCNO,BR.PAIDAMOUNT,BM.DOCSTATUS";
        log.info(selectOfBillOpen2);
        List<Map<String, Object>> rowsOfBillOpen2 = DBUtil.querySql(selectOfBillOpen2);
        log.info(JSON.toJSONString(rowsOfBillOpen2));
        if (rowsOfBillOpen2 != null && !rowsOfBillOpen2.isEmpty()) {
            log.info("待处理的开票申请单：" + rowsOfBillOpen2.size());
            for (int i=0; i<rowsOfBillOpen2.size(); i++) {
                log.info("准备处理第" + (i+1) + "条/共" + rowsOfBillOpen2.size() + "条开票申请单");
                try {
                    String logDetailId = (String) rowsOfBillOpen2.get(i).get("ID");
                    String logInfoId = (String) rowsOfBillOpen2.get(i).get("PARENTID");
                    String srcBizSys = (String) rowsOfBillOpen2.get(i).get("SRCBIZSYS");
                    String srcDocNo = (String) rowsOfBillOpen2.get(i).get("SRCDOCNO");
                    String payDocId = (String) rowsOfBillOpen2.get(i).get("PAYDOCID");
                    String payDocNo = (String) rowsOfBillOpen2.get(i).get("PAYDOCNO");
                    String billNo = (String) rowsOfBillOpen2.get(i).get("BILLNO");
                    String subbillStartSn = (String) rowsOfBillOpen2.get(i).get("SUBBILLSTARTSN");
                    String subbillEndSn = (String) rowsOfBillOpen2.get(i).get("SUBBILLENDSN");
                    Date billOpenDate = (Date) rowsOfBillOpen2.get(i).get("BILLOPENDATE");
                    Date billDueDate = (Date) rowsOfBillOpen2.get(i).get("BILLDUEDATE");
                    Date payedOn = (Date) rowsOfBillOpen2.get(i).get("PAYEDON");
                    BigDecimal paidAmount = (BigDecimal) rowsOfBillOpen2.get(i).get("PAIDAMOUNT");
                    String docStatus = String.valueOf(rowsOfBillOpen2.get(i).get("DOCSTATUS"));
                    String msg1 = "准备更新待付池付款结果：ID=" + logDetailId + ", 来源系统=" + srcBizSys +", 来源单据编号=" + srcDocNo + ", 开票申请单号=" + payDocNo + ", 票据号=" + billNo + ", 开票金额=" + paidAmount;
                    log.info(msg1);
                    logService.info(srcDocNo, msg1);
                    String updateSql1 = "update JTGKPAYMENTDETAIL set TXT05=?10,TIMESTAMPS_LASTCHANGEDON=?9,OPERATEDON=?8,PAIDAMOUNT=?7,PAYDOCID=?1,PAYDOCNO=?2," +
                            "BILLNO=?3,SUBBILLSTARTSN=?4,SUBBILLENDSN=?5,BILLOPENDATE=?6,BILLDUEDATE=?11"
                            + " where DOCSTATUS=0 and ID='" + logDetailId + "'";
                    String msg2 = updateSql1 + ", ?1=" + payDocId + ", ?2=" + payDocNo + ", ?3=" + billNo +
                            ", ?4=" + subbillStartSn + ", ?5=" + subbillEndSn + ", ?6=" + billOpenDate + ", ?7=" + paidAmount +
                            ", ?8=" + payedOn + ", ?9=" + now + ", ?10=" + docStatus + ", ?11=" + billDueDate;
                    log.info(msg2);
                    logService.info(srcDocNo, msg2);
                    int count1 = DBUtil.executeUpdateSQL(updateSql1, payDocId, payDocNo, billNo,
                            subbillStartSn, subbillEndSn, billOpenDate, paidAmount,
                            payedOn, now, docStatus, billDueDate);
                    log.info("受影响行数：" + count1);
                    logService.info(srcDocNo, "受影响行数：" + count1);

                    updatePaymentInfoStatus(logInfoId, srcDocNo, paidAmount);
                } catch (Throwable ex) {
                    log.error("待付池付款结果处理开票过程发生异常：" + JSON.toJSONString(rowsOfBillOpen2.get(i)) + "\n" + ex.toString());
                }
            }
        }

        log.info("准备获取已完成的背书申请单");
        String selectOfBillEndorse = "SELECT \n" +
                "    LD.ID,\n" +
                "    LD.PARENTID,\n" +
                "    LM.SRCBIZSYS,\n" +
                "    LM.SRCDOCNO,\n" +
                "    LM.ISDIRECTPAY,\n" +
                "    EB.ID as PAYDOCID, -- 背书申请单ID\n" +
                "    EB.DOCNO as PAYDOCNO, -- 背书申请单号\n" +
                "    STRING_AGG(BED.BILLID, ',' ORDER BY BED.BILLNO) AS BILLID, -- 票据台账ID(聚合)\n" +
                "    STRING_AGG(\n" +
                "        BED.BILLNO || '(' || \n" +
                "        CASE \n" +
                "            WHEN BED.SUBBILLSTARTSN IS NULL OR BED.SUBBILLSTARTSN = '' OR \n" +
                "                 BED.SUBBILLENDSN IS NULL OR BED.SUBBILLENDSN = '' \n" +
                "            THEN '0-0'\n" +
                "            ELSE BED.SUBBILLSTARTSN || '-' || BED.SUBBILLENDSN\n" +
                "        END || ')-' || CAST(ROUND(BED.USEAMOUNT, 2) AS VARCHAR), \n" +
                "        ',' ORDER BY BED.BILLNO\n" +
                "    ) AS BILLNO, -- 票据号(格式化)\n" +
                "    STRING_AGG(\n" +
                "        CASE \n" +
                "            WHEN BED.SUBBILLSTARTSN IS NULL OR BED.SUBBILLSTARTSN = '' \n" +
                "            THEN '0' \n" +
                "            ELSE BED.SUBBILLSTARTSN \n" +
                "        END, \n" +
                "        ',' ORDER BY BED.BILLNO\n" +
                "    ) AS SUBBILLSTARTSN, -- 子票区间起(聚合)\n" +
                "    STRING_AGG(\n" +
                "        CASE \n" +
                "            WHEN BED.SUBBILLENDSN IS NULL OR BED.SUBBILLENDSN = '' \n" +
                "            THEN '0' \n" +
                "            ELSE BED.SUBBILLENDSN \n" +
                "        END, \n" +
                "        ',' ORDER BY BED.BILLNO\n" +
                "    ) AS SUBBILLENDSN, -- 子票区间止(聚合)\n" +
                "    MAX(EB.HANDLEDATE) as PAYEDON, -- 支付时间(取最新)\n" +
                "    SUM(BED.USEAMOUNT) as PAIDAMOUNT -- 支付金额(求和)\n" +
                "FROM JTGKPAYMENTDETAIL LD \n" +
                "INNER JOIN JTGKPAYMENTINFO LM ON LD.PARENTID = LM.ID\n" +
                "INNER JOIN BPBIZPAYMENTREQRECEIVER BR ON LD.REQUESTDOCID = BR.PARENTID\n" +
                "INNER JOIN TMBILLENDORSEREQUEST EB ON BR.ID = EB.SRCDOCID\n" +
                "INNER JOIN TMBILLENDORSEDETAIL BED ON EB.ID = BED.PARENTID\n" +
                "WHERE BR.REQRECEIVERSTATUS = 5\n" +
                "  AND LD.DOCSTATUS = 0\n" +
                "GROUP BY LD.ID, LD.PARENTID, LM.SRCBIZSYS, LM.SRCDOCNO, LM.ISDIRECTPAY, EB.ID, EB.DOCNO";
        log.info(selectOfBillEndorse);
        List<Map<String, Object>> rowsOfBillEndorse = DBUtil.querySql(selectOfBillEndorse);
        log.info(JSON.toJSONString(rowsOfBillEndorse));
        if (rowsOfBillEndorse != null && !rowsOfBillEndorse.isEmpty()) {
            log.info("待处理的背书申请单：" + rowsOfBillEndorse.size());
            for (int i=0; i<rowsOfBillEndorse.size(); i++) {
                log.info("准备处理第" + (i+1) + "条/共" + rowsOfBillEndorse.size() + "条背书申请单");
                try {
                    String logDetailId = (String) rowsOfBillEndorse.get(i).get("ID");
                    String logInfoId = (String) rowsOfBillEndorse.get(i).get("PARENTID");
                    String srcBizSys = (String) rowsOfBillEndorse.get(i).get("SRCBIZSYS");
                    String srcDocNo = (String) rowsOfBillEndorse.get(i).get("SRCDOCNO");
                    String payDocId = (String) rowsOfBillEndorse.get(i).get("PAYDOCID");
                    String payDocNo = (String) rowsOfBillEndorse.get(i).get("PAYDOCNO");
                    String billId = (String) rowsOfBillEndorse.get(i).get("BILLID");
                    String billNo = (String) rowsOfBillEndorse.get(i).get("BILLNO");
                    String subbillStartSn = (String) rowsOfBillEndorse.get(i).get("SUBBILLSTARTSN");
                    String subbillEndSn = (String) rowsOfBillEndorse.get(i).get("SUBBILLENDSN");
                    Date payedOn = (Date) rowsOfBillEndorse.get(i).get("PAYEDON");
                    BigDecimal paidAmount = (BigDecimal) rowsOfBillEndorse.get(i).get("PAIDAMOUNT");
                    String msg1 = "准备更新待付池付款结果：ID=" + logDetailId + ", 来源系统=" + srcBizSys +", 来源单据编号=" + srcDocNo + ", 背书申请单号=" + payDocNo + ", 票据号=" + billNo + ", 背书金额=" + paidAmount;
                    log.info(msg1);
                    logService.info(srcDocNo, msg1);
                    String updateSql1 = "update JTGKPAYMENTDETAIL set PAYDOCTYPE='2',DOCSTATUS=1,RESULTBACKSTATUS=1," +
                            "TIMESTAMPS_LASTCHANGEDON=?9,OPERATEDON=?8,PAIDAMOUNT=?7,PAYDOCID=?1,PAYDOCNO=?2," +
                            "BILLID=?3,BILLNO=?4,SUBBILLSTARTSN=?5,SUBBILLENDSN=?6" +
                            " where DOCSTATUS=0 and ID='" + logDetailId + "'";
                    String msg2 = updateSql1 + ", ?1=" + payDocId + ", ?2=" + payDocNo + ", ?3=" + billId + ", ?4=" + billNo +
                            ", ?5=" + subbillStartSn + ", ?6=" + subbillEndSn + ", ?7=" + paidAmount + ", ?8=" + payedOn;
                    log.info(msg2);
                    logService.info(srcDocNo, msg2);
                    int count1 = DBUtil.executeUpdateSQL(updateSql1, payDocId, payDocNo, billId, billNo,
                            subbillStartSn, subbillEndSn, paidAmount, payedOn, now);
                    log.info("受影响行数：" + count1);
                    logService.info(srcDocNo, "受影响行数：" + count1);

                    updatePaymentInfoStatus(logInfoId, srcDocNo, paidAmount);
                    
                } catch (Throwable ex) {
                    log.error("待付池付款结果处理背书过程发生异常：" + JSON.toJSONString(rowsOfBillEndorse.get(i)) + "\n" + ex.toString());
                }
            }
        }

        log.info("准备获取进行中的的背书申请单");
        String selectOfBillEndorse2 = "SELECT \n" +
                "    LD.ID,\n" +
                "    LD.PARENTID,\n" +
                "    LM.SRCBIZSYS,\n" +
                "    LM.SRCDOCNO,\n" +
                "    LM.ISDIRECTPAY,\n" +
                "    EB.ID as PAYDOCID, -- 背书申请单ID\n" +
                "    EB.DOCNO as PAYDOCNO, -- 背书申请单号\n" +
                "    STRING_AGG(BED.BILLID, ',' ORDER BY BED.BILLNO) AS BILLID, -- 票据台账ID(聚合)\n" +
                "    STRING_AGG(\n" +
                "        BED.BILLNO || '(' || \n" +
                "        CASE \n" +
                "            WHEN BED.SUBBILLSTARTSN IS NULL OR BED.SUBBILLSTARTSN = '' OR \n" +
                "                 BED.SUBBILLENDSN IS NULL OR BED.SUBBILLENDSN = '' \n" +
                "            THEN '0-0'\n" +
                "            ELSE BED.SUBBILLSTARTSN || '-' || BED.SUBBILLENDSN\n" +
                "        END || ')-' || CAST(ROUND(BED.USEAMOUNT, 2) AS VARCHAR), \n" +
                "        ',' ORDER BY BED.BILLNO\n" +
                "    ) AS BILLNO, -- 票据号(格式化)\n" +
                "    STRING_AGG(\n" +
                "        CASE \n" +
                "            WHEN BED.SUBBILLSTARTSN IS NULL OR BED.SUBBILLSTARTSN = '' \n" +
                "            THEN '0' \n" +
                "            ELSE BED.SUBBILLSTARTSN \n" +
                "        END, \n" +
                "        ',' ORDER BY BED.BILLNO\n" +
                "    ) AS SUBBILLSTARTSN, -- 子票区间起(聚合)\n" +
                "    STRING_AGG(\n" +
                "        CASE \n" +
                "            WHEN BED.SUBBILLENDSN IS NULL OR BED.SUBBILLENDSN = '' \n" +
                "            THEN '0' \n" +
                "            ELSE BED.SUBBILLENDSN \n" +
                "        END, \n" +
                "        ',' ORDER BY BED.BILLNO\n" +
                "    ) AS SUBBILLENDSN, -- 子票区间止(聚合)\n" +
                "    MAX(EB.HANDLEDATE) as PAYEDON, -- 支付时间(取最新)\n" +
                "    SUM(BED.USEAMOUNT) as PAIDAMOUNT, -- 支付金额(求和)\n" +
                "    BR.REQRECEIVERSTATUS as DOCSTATUS -- 状态\n" +
                "FROM JTGKPAYMENTDETAIL LD \n" +
                "INNER JOIN JTGKPAYMENTINFO LM ON LD.PARENTID = LM.ID\n" +
                "INNER JOIN BPBIZPAYMENTREQRECEIVER BR ON LD.REQUESTDOCID = BR.PARENTID\n" +
                "INNER JOIN TMBILLENDORSEREQUEST EB ON BR.ID = EB.SRCDOCID\n" +
                "INNER JOIN TMBILLENDORSEDETAIL BED ON EB.ID = BED.PARENTID\n" +
                "WHERE BR.REQRECEIVERSTATUS <> -1 \n" +
                "  AND BR.REQRECEIVERSTATUS <> 5\n" +
                "  AND LD.DOCSTATUS = 0 \n" +
                "  AND (LD.TXT05 IS NULL OR LD.TXT05 <> CAST(BR.REQRECEIVERSTATUS AS VARCHAR))\n" +
                "GROUP BY LD.ID, LD.PARENTID, LM.SRCBIZSYS, LM.SRCDOCNO, LM.ISDIRECTPAY, EB.ID, EB.DOCNO, BR.REQRECEIVERSTATUS";
        log.info(selectOfBillEndorse2);
        List<Map<String, Object>> rowsOfBillEndorse2 = DBUtil.querySql(selectOfBillEndorse2);
        log.info(JSON.toJSONString(rowsOfBillEndorse2));
        if (rowsOfBillEndorse2 != null && !rowsOfBillEndorse2.isEmpty()) {
            log.info("待处理的背书申请单：" + rowsOfBillEndorse2.size());
            for (int i=0; i<rowsOfBillEndorse2.size(); i++) {
                log.info("准备处理第" + (i+1) + "条/共" + rowsOfBillEndorse2.size() + "条背书申请单");
                try {
                    String logDetailId = (String) rowsOfBillEndorse2.get(i).get("ID");
                    String logInfoId = (String) rowsOfBillEndorse2.get(i).get("PARENTID");
                    String srcBizSys = (String) rowsOfBillEndorse2.get(i).get("SRCBIZSYS");
                    String srcDocNo = (String) rowsOfBillEndorse2.get(i).get("SRCDOCNO");
                    String payDocId = (String) rowsOfBillEndorse2.get(i).get("PAYDOCID");
                    String payDocNo = (String) rowsOfBillEndorse2.get(i).get("PAYDOCNO");
                    String billId = (String) rowsOfBillEndorse2.get(i).get("BILLID");
                    String billNo = (String) rowsOfBillEndorse2.get(i).get("BILLNO");
                    String subbillStartSn = (String) rowsOfBillEndorse2.get(i).get("SUBBILLSTARTSN");
                    String subbillEndSn = (String) rowsOfBillEndorse2.get(i).get("SUBBILLENDSN");
                    BigDecimal paidAmount = (BigDecimal) rowsOfBillEndorse2.get(i).get("PAIDAMOUNT");
                    String docStatus = String.valueOf(rowsOfBillEndorse2.get(i).get("DOCSTATUS"));
                    String msg1 = "准备更新待付池付款结果：ID=" + logDetailId + ", 来源系统=" + srcBizSys +", 来源单据编号=" + srcDocNo + ", 背书申请单号=" + payDocNo + ", 票据号=" + billNo + ", 背书金额=" + paidAmount;
                    log.info(msg1);
                    logService.info(srcDocNo, msg1);
                    String updateSql1 = "update JTGKPAYMENTDETAIL set TXT05=?9,TIMESTAMPS_LASTCHANGEDON=?8,PAIDAMOUNT=?7,PAYDOCID=?1,PAYDOCNO=?2," +
                            "BILLID=?3,BILLNO=?4,SUBBILLSTARTSN=?5,SUBBILLENDSN=?6" +
                            " where DOCSTATUS=0 and ID='" + logDetailId + "'";
                    String msg2 = updateSql1 + ", ?1=" + payDocId + ", ?2=" + payDocNo + ", ?3=" + billId + ", ?4=" + billNo +
                            ", ?5=" + subbillStartSn + ", ?6=" + subbillEndSn + ", ?7=" + paidAmount + ", ?8=" + now + ", ?9=" + docStatus;
                    log.info(msg2);
                    logService.info(srcDocNo, msg2);
                    int count1 = DBUtil.executeUpdateSQL(updateSql1, payDocId, payDocNo, billId, billNo,
                            subbillStartSn, subbillEndSn, paidAmount, now, docStatus);
                    log.info("受影响行数：" + count1);
                    logService.info(srcDocNo, "受影响行数：" + count1);
                } catch (Throwable ex) {
                    log.error("待付池付款结果处理背书过程发生异常：" + JSON.toJSONString(rowsOfBillEndorse2.get(i)) + "\n" + ex.toString());
                }
            }
        }

        log.info("准备获取已退回或终止办理的业务支付申请");
        String selectOfCancelBill = "SELECT LD.ID,LD.REFDETAILID,LD.PARENTID,LM.SRCBIZSYS,LM.SRCDOCNO,LM.ISDIRECTPAY,LM.DOCSTATUS,\n" +
                "BP.DOCNO AS REQUESTDOCNO,\n" +
                "(CASE BR.REQRECEIVERSTATUS WHEN 8 THEN 2 ELSE 3 END)AS PAYSTATUS,\n" +
                "(CASE BR.REQRECEIVERSTATUS WHEN 8 THEN BR.TERMINATIONREASON ELSE BR.RETURNREASON END) AS CANCELREASON,\n" +
                "(CASE BR.REQRECEIVERSTATUS WHEN 8 THEN BR.TERMINATIONHANDLER ELSE BP.RETURNER END) AS OPERATOR,\n" +
                "BR.TIMESTAMPS_LASTCHANGEDON\n" +
                "FROM BPBIZPAYMENTREQRECEIVER BR\n" +
                "inner join BPBIZPAYMENTREQUEST BP on BR.PARENTID=BP.ID\n" +
                "INNER JOIN JTGKPAYMENTDETAIL LD ON LD.REQUESTDOCID=BR.PARENTID\n" +
                "inner join JTGKPAYMENTINFO LM ON LD.PARENTID=LM.ID\n" +
                "WHERE BR.REQRECEIVERSTATUS in (8,-2)\n" +
                "AND LD.DOCSTATUS=0";
        log.info(selectOfCancelBill);
        List<Map<String, Object>> rowsOfCancelBill = DBUtil.querySql(selectOfCancelBill);
        log.info(JSON.toJSONString(rowsOfCancelBill));
        if (rowsOfCancelBill != null && !rowsOfCancelBill.isEmpty()) {
            log.info("待处理的业务支付申请：" + rowsOfCancelBill.size());
            for (int i=0; i<rowsOfCancelBill.size(); i++) {
                log.info("准备处理第" + (i+1) + "条/共" + rowsOfCancelBill.size() + "条业务支付申请");
                try {
                    String logDetailId = (String) rowsOfCancelBill.get(i).get("ID");
                    String logInfoId = (String) rowsOfCancelBill.get(i).get("PARENTID");
                    String refDetailId = (String) rowsOfCancelBill.get(i).get("REFDETAILID");
                    String srcBizSys = (String) rowsOfCancelBill.get(i).get("SRCBIZSYS");
                    String srcDocNo = (String) rowsOfCancelBill.get(i).get("SRCDOCNO");
                    String payDocNo = (String) rowsOfCancelBill.get(i).get("REQUESTDOCNO");
                    Integer payStatus = new Integer((rowsOfCancelBill.get(i).get("PAYSTATUS")).toString());
                    String reason = (String) rowsOfCancelBill.get(i).get("CANCELREASON");
                    String operatorId = (String) rowsOfCancelBill.get(i).get("OPERATOR");
                    Date operatedOn = (Date) rowsOfCancelBill.get(i).get("TIMESTAMPS_LASTCHANGEDON");
                    String isDirectPay = (String) rowsOfCancelBill.get(i).get("ISDIRECTPAY");
                    String docStatus = String.valueOf(rowsOfCancelBill.get(i).get("DOCSTATUS"));
                    String msg1 = "准备更新待付池付款结果：ID=" + logDetailId + ", 来源系统=" + srcBizSys +", 来源单据编号=" + srcDocNo + ", 业务支付申请单号=" + payDocNo + ", 操作(3退回/2终止付款)=" + payStatus + ", 原因=" + reason;
                    log.info(msg1);
                    logService.info(srcDocNo, msg1);
                    String updateSql1 = "update JTGKPAYMENTDETAIL set RESULTBACKSTATUS=1,PAIDAMOUNT=0.0," +
                            "TIMESTAMPS_LASTCHANGEDON=?5,DOCSTATUS=?1,CANCELREASON=?2,OPERATEDON=?3,OPERATOR=?4" +
                            " where DOCSTATUS=0 and ID='" + logDetailId + "'";
                    String msg2 = updateSql1 + ", ?1=" + payStatus + ", ?2=" + reason + ", ?3=" + operatedOn + ", ?4=" + operatorId;
                    log.info(msg2);
                    logService.info(srcDocNo, msg2);
                    int count1 = DBUtil.executeUpdateSQL(updateSql1, payStatus, reason, operatedOn, operatorId, now);
                    log.info("受影响行数：" + count1);
                    logService.info(srcDocNo, "受影响行数：" + count1);

                    if ("1".equals(isDirectPay)) {
                        // 1、直接支付的付款申请更新状态为已退回、并推送异构系统；2、需付款安排的付款申请（SFS排程）退回到待付池可重新安排、或手工退回异构系统
                        String updateSql2 = "update JTGKPAYMENTINFO set DOCSTATUS=-2, TXT20='退回/终止' where DOCSTATUS=2 and ID='" + logInfoId + "'";
                        log.info(updateSql2);
                        logService.info(srcDocNo, updateSql2);
                        int count2 = DBUtil.executeUpdateSQL(updateSql2);
                        log.info("受影响行数：" + count2);
                        logService.info(srcDocNo, "受影响行数：" + count2);
                    }else if ("2".equals(docStatus)) {
                        // 对于已完成付款安排的，要更改状态到可安排付款
                        String updateSql2 = "update JTGKPAYMENTINFO set DOCSTATUS=1 where DOCSTATUS=2 and ID='" + logInfoId + "'";
                        log.info(updateSql2);
                        logService.info(srcDocNo, updateSql2);
                        int count2 = DBUtil.executeUpdateSQL(updateSql2);
                        log.info("受影响行数：" + count2);
                        logService.info(srcDocNo, "受影响行数：" + count2);
                    }

                    // 更新JTGKPAYPLANDETAIL
                    String updateSql2 = "update JTGKPAYPLANDETAIL set DOCSTATUS=-1 where ID='" + refDetailId + "'";
                    log.info(updateSql2);
                    logService.info(srcDocNo, updateSql2);
                    int count2 = DBUtil.executeUpdateSQL(updateSql2);
                    log.info("受影响行数：" + count2);
                    logService.info(srcDocNo, "受影响行数：" + count2);

                    // 获取JTGKPAYPLANBILL的ID
                    String selectOfPlanBillId = "select JTGKPAYPLANBILL.ID from JTGKPAYPLANBILL\n" +
                            "inner join JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.parentid = JTGKPAYPLANBILL.id\n" +
                            "where JTGKPAYPLANDETAIL.id = '" + refDetailId + "'";
                    log.info(selectOfPlanBillId);
                    List<Map<String, Object>> rowsOfPlanBillId = DBUtil.querySql(selectOfPlanBillId);
                    if (rowsOfPlanBillId != null && !rowsOfPlanBillId.isEmpty()) {
                        String planBillId = (String) rowsOfPlanBillId.get(0).get("ID");
                        // 查询是否需要更新JTGKPAYPLANBILL
                        String selectOfPlanBill = "select sum(JTGKPAYPLANDETAIL.AMOUNT) AS SUMAMOUNT,TOTALAMOUNT,JTGKPAYPLANBILL.ID from JTGKPAYPLANBILL \n" +
                                "inner join JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.parentid = JTGKPAYPLANBILL.id\n" +
                                "where JTGKPAYPLANBILL.id = '" + planBillId + "'\n" +
                                "group by JTGKPAYPLANBILL.ID";
                        log.info(selectOfPlanBill);
                        List<Map<String, Object>> rowsOfPlanBill = DBUtil.querySql(selectOfPlanBill);
                        if (rowsOfPlanBill != null && !rowsOfPlanBill.isEmpty()) {
                            BigDecimal sumamount = (BigDecimal) rowsOfPlanBill.get(0).get("SUMAMOUNT");
                            BigDecimal totalamount = (BigDecimal) rowsOfPlanBill.get(0).get("TOTALAMOUNT");
                            if (sumamount.compareTo(totalamount) >= 0) {
                                String updateSql3 = "update JTGKPAYPLANBILL set DOCSTATUS=-1 where ID='" + rowsOfPlanBill.get(0).get("ID") + "'";
                                log.info(updateSql3);
                                logService.info(srcDocNo, updateSql3);
                                int count3 = DBUtil.executeUpdateSQL(updateSql3);
                                log.info("受影响行数：" + count3);
                                logService.info(srcDocNo, "受影响行数：" + count3);
                            }
                        }

                    }

                    // 获取JTGKPAYPLANBILL2的ID
                    String selectOfPlanBillId2 = "select JTGKPAYPLANBILL2.ID from JTGKPAYPLANBILL2\n" +
                            "inner join JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.parentid2 = JTGKPAYPLANBILL2.id\n" +
                            "where JTGKPAYPLANDETAIL.id = '" + refDetailId + "'";
                    log.info(selectOfPlanBillId2);
                    List<Map<String, Object>> rowsOfPlanBillId2 = DBUtil.querySql(selectOfPlanBillId2);
                    if (rowsOfPlanBillId2 != null && !rowsOfPlanBillId2.isEmpty()) {
                        String planBillId2 = (String) rowsOfPlanBillId2.get(0).get("ID");
                        // 查询是否需要更新JTGKPAYPLANBILL2
                        String selectOfPlanBill2 = "select sum(JTGKPAYPLANDETAIL.AMOUNT) AS SUMAMOUNT,TOTALAMOUNT,JTGKPAYPLANBILL2.ID from JTGKPAYPLANBILL2 \n" +
                                "inner join JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.parentid2 = JTGKPAYPLANBILL2.id\n" +
                                "where JTGKPAYPLANBILL2.id = '" + planBillId2 + "'\n" +
                                "group by JTGKPAYPLANBILL2.ID";
                        log.info(selectOfPlanBill2);
                        List<Map<String, Object>> rowsOfPlanBill2 = DBUtil.querySql(selectOfPlanBill2);
                        if (rowsOfPlanBill2 != null && !rowsOfPlanBill2.isEmpty()) {
                            BigDecimal sumamount = (BigDecimal) rowsOfPlanBill2.get(0).get("SUMAMOUNT");
                            BigDecimal totalamount = (BigDecimal) rowsOfPlanBill2.get(0).get("TOTALAMOUNT");
                            if (sumamount.compareTo(totalamount) >= 0) {
                                String updateSql4 = "update JTGKPAYPLANBILL2 set DOCSTATUS=-1 where ID='" + rowsOfPlanBill2.get(0).get("ID") + "'";
                                log.info(updateSql4);
                                logService.info(srcDocNo, updateSql4);
                                int count4 = DBUtil.executeUpdateSQL(updateSql4);
                                log.info("受影响行数：" + count4);
                                logService.info(srcDocNo, "受影响行数：" + count4);
                            }
                        }
                    }


                    // 获取JTGKPAYPLANBILL3的ID
                    String selectOfPlanBillId3 = "select JTGKPAYPLANBILL3.ID from JTGKPAYPLANBILL3\n" +
                            "inner join JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.parentid3 = JTGKPAYPLANBILL3.id\n" +
                            "where JTGKPAYPLANDETAIL.id = '" + refDetailId + "'";
                    log.info(selectOfPlanBillId3);
                    List<Map<String, Object>> rowsOfPlanBillId3 = DBUtil.querySql(selectOfPlanBillId3);
                    if (rowsOfPlanBillId3 != null && !rowsOfPlanBillId3.isEmpty()) {
                        String planBillId3 = (String) rowsOfPlanBillId3.get(0).get("ID");
                        // 查询是否需要更新JTGKPAYPLANBILL3
                        String selectOfPlanBill3 = "select sum(JTGKPAYPLANDETAIL.AMOUNT) AS SUMAMOUNT,TOTALAMOUNT,JTGKPAYPLANBILL3.ID from JTGKPAYPLANBILL3 \n" +
                                "inner join JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.parentid3 = JTGKPAYPLANBILL3.id\n" +
                                "where JTGKPAYPLANBILL3.id = '" + planBillId3 + "'\n" +
                                "group by JTGKPAYPLANBILL3.ID";
                        log.info(selectOfPlanBill3);
                        List<Map<String, Object>> rowsOfPlanBill3 = DBUtil.querySql(selectOfPlanBill3);
                        if (rowsOfPlanBill3 != null && !rowsOfPlanBill3.isEmpty()) {
                            BigDecimal sumamount = (BigDecimal) rowsOfPlanBill3.get(0).get("SUMAMOUNT");
                            BigDecimal totalamount = (BigDecimal) rowsOfPlanBill3.get(0).get("TOTALAMOUNT");
                            if (sumamount.compareTo(totalamount) >= 0) {
                                String updateSql5 = "update JTGKPAYPLANBILL3 set DOCSTATUS=-1 where ID='" + rowsOfPlanBill3.get(0).get("ID") + "'";
                                log.info(updateSql5);
                                logService.info(srcDocNo, updateSql5);
                                int count5 = DBUtil.executeUpdateSQL(updateSql5);
                                log.info("受影响行数：" + count5);
                                logService.info(srcDocNo, "受影响行数：" + count5);
                            }
                        }

                    }
                } catch (Throwable ex) {
                    log.error("待付池付款结果处理退回过程发生异常：" + JSON.toJSONString(rowsOfCancelBill.get(i)) + "\n" + ex.toString());
                }
            }
        }


        log.info("准备获取已退回或终止办理的单位调拨");
        String selectOfCancelUnitTransfer = "SELECT LD.ID,LD.REFDETAILID,LD.PARENTID,LM.SRCBIZSYS,LM.SRCDOCNO,LM.ISDIRECTPAY,LM.DOCSTATUS,\n" +
                "BR.DOCNO AS REQUESTDOCNO,\n" +
                "3 AS PAYSTATUS,\n" +
                "BR.RETURNREASON AS CANCELREASON,\n" +
                "BR.RETURNER AS OPERATOR,\n" +
                "BR.TIMESTAMPS_LASTCHANGEDON\n" +
                "FROM TMPAYMENTSETTLEMENT BR \n" +
                "INNER JOIN JTGKPAYMENTDETAIL LD ON LD.PAYDOCID=BR.ID\n" +
                "inner join JTGKPAYMENTINFO LM ON LD.PARENTID=LM.ID\n" +
                "where BR.BizType = '4' and (BR.DocStatus = '-1' or BR.DocStatus = '-3') and LD.DOCSTATUS = 0";
        log.info(selectOfCancelUnitTransfer);
        List<Map<String, Object>> rowsOfCancelUnitTransfer = DBUtil.querySql(selectOfCancelUnitTransfer);
        log.info(JSON.toJSONString(rowsOfCancelUnitTransfer));
        if (rowsOfCancelUnitTransfer != null && !rowsOfCancelUnitTransfer.isEmpty()) {
            log.info("待处理的业务支付申请：" + rowsOfCancelUnitTransfer.size());
            for (int i=0; i<rowsOfCancelUnitTransfer.size(); i++) {
                log.info("准备处理第" + (i+1) + "条/共" + rowsOfCancelUnitTransfer.size() + "条业务支付申请");
                try {
                    String logDetailId = (String) rowsOfCancelUnitTransfer.get(i).get("ID");
                    String logInfoId = (String) rowsOfCancelUnitTransfer.get(i).get("PARENTID");
                    String refDetailId = (String) rowsOfCancelUnitTransfer.get(i).get("REFDETAILID");
                    String srcBizSys = (String) rowsOfCancelUnitTransfer.get(i).get("SRCBIZSYS");
                    String srcDocNo = (String) rowsOfCancelUnitTransfer.get(i).get("SRCDOCNO");
                    String payDocNo = (String) rowsOfCancelUnitTransfer.get(i).get("REQUESTDOCNO");
                    Integer payStatus = new Integer((rowsOfCancelUnitTransfer.get(i).get("PAYSTATUS")).toString());
                    String reason = (String) rowsOfCancelUnitTransfer.get(i).get("CANCELREASON");
                    String operatorId = (String) rowsOfCancelUnitTransfer.get(i).get("OPERATOR");
                    Date operatedOn = (Date) rowsOfCancelUnitTransfer.get(i).get("TIMESTAMPS_LASTCHANGEDON");
                    String isDirectPay = (String) rowsOfCancelUnitTransfer.get(i).get("ISDIRECTPAY");
                    String docStatus = String.valueOf(rowsOfCancelUnitTransfer.get(i).get("DOCSTATUS"));
                    String msg1 = "准备更新待付池付款结果：ID=" + logDetailId + ", 来源系统=" + srcBizSys +", 来源单据编号=" + srcDocNo + ", 业务支付申请单号=" + payDocNo + ", 操作(3退回/2终止付款)=" + payStatus + ", 原因=" + reason;
                    log.info(msg1);
                    logService.info(srcDocNo, msg1);
                    String updateSql1 = "update JTGKPAYMENTDETAIL set RESULTBACKSTATUS=1,PAIDAMOUNT=0.0," +
                            "TIMESTAMPS_LASTCHANGEDON=?5,DOCSTATUS=?1,CANCELREASON=?2,OPERATEDON=?3,OPERATOR=?4" +
                            " where DOCSTATUS=0 and ID='" + logDetailId + "'";
                    String msg2 = updateSql1 + ", ?1=" + payStatus + ", ?2=" + reason + ", ?3=" + operatedOn + ", ?4=" + operatorId;
                    log.info(msg2);
                    logService.info(srcDocNo, msg2);
                    int count1 = DBUtil.executeUpdateSQL(updateSql1, payStatus, reason, operatedOn, operatorId, now);
                    log.info("受影响行数：" + count1);
                    logService.info(srcDocNo, "受影响行数：" + count1);

                    if ("1".equals(isDirectPay)) {
                        // 1、直接支付的付款申请更新状态为已退回、并推送异构系统；2、需付款安排的付款申请（SFS排程）退回到待付池可重新安排、或手工退回异构系统
                        String updateSql2 = "update JTGKPAYMENTINFO set DOCSTATUS=-2, TXT20='退回/终止' where DOCSTATUS=2 and ID='" + logInfoId + "'";
                        log.info(updateSql2);
                        logService.info(srcDocNo, updateSql2);
                        int count2 = DBUtil.executeUpdateSQL(updateSql2);
                        log.info("受影响行数：" + count2);
                        logService.info(srcDocNo, "受影响行数：" + count2);
                    }else if ("2".equals(docStatus)) {
                        // 对于已完成付款安排的，要更改状态到可安排付款
                        String updateSql2 = "update JTGKPAYMENTINFO set DOCSTATUS=1 where DOCSTATUS=2 and ID='" + logInfoId + "'";
                        log.info(updateSql2);
                        logService.info(srcDocNo, updateSql2);
                        int count2 = DBUtil.executeUpdateSQL(updateSql2);
                        log.info("受影响行数：" + count2);
                        logService.info(srcDocNo, "受影响行数：" + count2);
                    }

                    // 更新JTGKPAYPLANDETAIL
                    String updateSql2 = "update JTGKPAYPLANDETAIL set DOCSTATUS=-1 where ID='" + refDetailId + "'";
                    log.info(updateSql2);
                    logService.info(srcDocNo, updateSql2);
                    int count2 = DBUtil.executeUpdateSQL(updateSql2);
                    log.info("受影响行数：" + count2);
                    logService.info(srcDocNo, "受影响行数：" + count2);

                    // 获取JTGKPAYPLANBILL的ID
                    String selectOfPlanBillId = "select JTGKPAYPLANBILL.ID from JTGKPAYPLANBILL\n" +
                            "inner join JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.parentid = JTGKPAYPLANBILL.id\n" +
                            "where JTGKPAYPLANDETAIL.id = '" + refDetailId + "'";
                    log.info(selectOfPlanBillId);
                    List<Map<String, Object>> rowsOfPlanBillId = DBUtil.querySql(selectOfPlanBillId);
                    if (rowsOfPlanBillId != null && !rowsOfPlanBillId.isEmpty()) {
                        String planBillId = (String) rowsOfPlanBillId.get(0).get("ID");
                        // 查询是否需要更新JTGKPAYPLANBILL
                        String selectOfPlanBill = "select sum(JTGKPAYPLANDETAIL.AMOUNT) AS SUMAMOUNT,TOTALAMOUNT,JTGKPAYPLANBILL.ID from JTGKPAYPLANBILL \n" +
                                "inner join JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.parentid = JTGKPAYPLANBILL.id\n" +
                                "where JTGKPAYPLANBILL.id = '" + planBillId + "'\n" +
                                "group by JTGKPAYPLANBILL.ID";
                        log.info(selectOfPlanBill);
                        List<Map<String, Object>> rowsOfPlanBill = DBUtil.querySql(selectOfPlanBill);
                        if (rowsOfPlanBill != null && !rowsOfPlanBill.isEmpty()) {
                            BigDecimal sumamount = (BigDecimal) rowsOfPlanBill.get(0).get("SUMAMOUNT");
                            BigDecimal totalamount = (BigDecimal) rowsOfPlanBill.get(0).get("TOTALAMOUNT");
                            if (sumamount.compareTo(totalamount) >= 0) {
                                String updateSql3 = "update JTGKPAYPLANBILL set DOCSTATUS=-1 where ID='" + rowsOfPlanBill.get(0).get("ID") + "'";
                                log.info(updateSql3);
                                logService.info(srcDocNo, updateSql3);
                                int count3 = DBUtil.executeUpdateSQL(updateSql3);
                                log.info("受影响行数：" + count3);
                                logService.info(srcDocNo, "受影响行数：" + count3);
                            }
                        }

                    }

                    // 获取JTGKPAYPLANBILL2的ID
                    String selectOfPlanBillId2 = "select JTGKPAYPLANBILL2.ID from JTGKPAYPLANBILL2\n" +
                            "inner join JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.parentid2 = JTGKPAYPLANBILL2.id\n" +
                            "where JTGKPAYPLANDETAIL.id = '" + refDetailId + "'";
                    log.info(selectOfPlanBillId2);
                    List<Map<String, Object>> rowsOfPlanBillId2 = DBUtil.querySql(selectOfPlanBillId2);
                    if (rowsOfPlanBillId2 != null && !rowsOfPlanBillId2.isEmpty()) {
                        String planBillId2 = (String) rowsOfPlanBillId2.get(0).get("ID");
                        // 查询是否需要更新JTGKPAYPLANBILL2
                        String selectOfPlanBill2 = "select sum(JTGKPAYPLANDETAIL.AMOUNT) AS SUMAMOUNT,TOTALAMOUNT,JTGKPAYPLANBILL2.ID from JTGKPAYPLANBILL2 \n" +
                                "inner join JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.parentid2 = JTGKPAYPLANBILL2.id\n" +
                                "where JTGKPAYPLANBILL2.id = '" + planBillId2 + "'\n" +
                                "group by JTGKPAYPLANBILL2.ID";
                        log.info(selectOfPlanBill2);
                        List<Map<String, Object>> rowsOfPlanBill2 = DBUtil.querySql(selectOfPlanBill2);
                        if (rowsOfPlanBill2 != null && !rowsOfPlanBill2.isEmpty()) {
                            BigDecimal sumamount = (BigDecimal) rowsOfPlanBill2.get(0).get("SUMAMOUNT");
                            BigDecimal totalamount = (BigDecimal) rowsOfPlanBill2.get(0).get("TOTALAMOUNT");
                            if (sumamount.compareTo(totalamount) >= 0) {
                                String updateSql4 = "update JTGKPAYPLANBILL2 set DOCSTATUS=-1 where ID='" + rowsOfPlanBill2.get(0).get("ID") + "'";
                                log.info(updateSql4);
                                logService.info(srcDocNo, updateSql4);
                                int count4 = DBUtil.executeUpdateSQL(updateSql4);
                                log.info("受影响行数：" + count4);
                                logService.info(srcDocNo, "受影响行数：" + count4);
                            }
                        }
                    }


                    // 获取JTGKPAYPLANBILL3的ID
                    String selectOfPlanBillId3 = "select JTGKPAYPLANBILL3.ID from JTGKPAYPLANBILL3\n" +
                            "inner join JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.parentid3 = JTGKPAYPLANBILL3.id\n" +
                            "where JTGKPAYPLANDETAIL.id = '" + refDetailId + "'";
                    log.info(selectOfPlanBillId3);
                    List<Map<String, Object>> rowsOfPlanBillId3 = DBUtil.querySql(selectOfPlanBillId3);
                    if (rowsOfPlanBillId3 != null && !rowsOfPlanBillId3.isEmpty()) {
                        String planBillId3 = (String) rowsOfPlanBillId3.get(0).get("ID");
                        // 查询是否需要更新JTGKPAYPLANBILL3
                        String selectOfPlanBill3 = "select sum(JTGKPAYPLANDETAIL.AMOUNT) AS SUMAMOUNT,TOTALAMOUNT,JTGKPAYPLANBILL3.ID from JTGKPAYPLANBILL3 \n" +
                                "inner join JTGKPAYPLANDETAIL on JTGKPAYPLANDETAIL.parentid3 = JTGKPAYPLANBILL3.id\n" +
                                "where JTGKPAYPLANBILL3.id = '" + planBillId3 + "'\n" +
                                "group by JTGKPAYPLANBILL3.ID";
                        log.info(selectOfPlanBill3);
                        List<Map<String, Object>> rowsOfPlanBill3 = DBUtil.querySql(selectOfPlanBill3);
                        if (rowsOfPlanBill3 != null && !rowsOfPlanBill3.isEmpty()) {
                            BigDecimal sumamount = (BigDecimal) rowsOfPlanBill3.get(0).get("SUMAMOUNT");
                            BigDecimal totalamount = (BigDecimal) rowsOfPlanBill3.get(0).get("TOTALAMOUNT");
                            if (sumamount.compareTo(totalamount) >= 0) {
                                String updateSql5 = "update JTGKPAYPLANBILL3 set DOCSTATUS=-1 where ID='" + rowsOfPlanBill3.get(0).get("ID") + "'";
                                log.info(updateSql5);
                                logService.info(srcDocNo, updateSql5);
                                int count5 = DBUtil.executeUpdateSQL(updateSql5);
                                log.info("受影响行数：" + count5);
                                logService.info(srcDocNo, "受影响行数：" + count5);
                            }
                        }

                    }
                } catch (Throwable ex) {
                    log.error("待付池付款结果处理退回过程发生异常：" + JSON.toJSONString(rowsOfCancelBill.get(i)) + "\n" + ex.toString());
                }
            }
        }


        log.info("准备获取银行交易失败退汇记录");
        String selectOfRefundBill = "SELECT LD.ID,LM.ID AS LOGID,LD.PARENTID,LM.SRCBIZSYS,LM.SRCDOCNO,LM.ISDIRECTPAY,\n" +
                "RCV.RECEIVINGAMOUNT AS PAIDAMOUNT, -- 支付金额\n" +
                "(CASE WHEN BF.ID IS NOT NULL THEN BF.TIMESTAMP_CREATEDON ELSE RDRQ.TIMESTAMPS_CREATEDON END) AS REDDOCTIME, -- 退汇时间\n" +
                "(select RLYY from ZJRLJL WHERE BZDNM=RDRQ.ID) AS REDDOCREASON, -- 退汇原因\n" +
                "BF.ID AS REDDOCID,\n" +
                //"RCV.BANKFLOWNO AS REDDOCNO, -- 退汇流水号\n" +
                "RDRQ.TXT02 AS TXT02, -- 退汇凭证号\n" +
                "(select RLRID from ZJRLJL WHERE BZDNM=RDRQ.ID) AS OPERATOR,\n" +
                "(select RLSJ from ZJRLJL WHERE BZDNM=RDRQ.ID) AS OPERATEDON,\n" +
                "RDRQ.DOCNO as REDDOCNO\n" +
                "from (BPBIZPAYMENTREQRECEIVER RDRC inner join BPBIZPAYMENTREQUEST RDRQ on RDRC.PARENTID=RDRQ.ID) -- 红冲业务支付申请\n" +
                "inner join BPBIZCLAIMCAPITALACCTITEM RDCC on RDRC.PARENTID=RDCC.PARENTID -- 认领记录\n" +
                "inner join (BPBRPCCAPITALACCOUNTNOTICE RDN inner join TMRECEIVINGSETTLEMENT RCV on RCV.ID=RDN.SRCDOCID) on RDCC.NOTICEID=RDN.ID -- 收款登记单\n" +
                "INNER JOIN (BPBIZPAYMENTREQRECEIVER BR INNER JOIN BPBIZPAYMENTREQUEST BQ ON BR.PARENTID=BQ.ID) ON BR.ID=RDRC.SRCDOCDETAILID -- 原业务支付申请\n" +
                "INNER JOIN (JTGKPAYMENTDETAIL LD INNER JOIN JTGKPAYMENTINFO LM ON LD.PARENTID=LM.ID) ON LD.PAYDOCTYPE='0' and LD.REQUESTDOCID=BQ.ID\n" +
                "left join BPBANKTRANSCATIONDETAILS BF on BF.BANKFLOWNO=RCV.BANKFLOWNO\n" +
                "WHERE RDRQ.REFUNDWAY='1' \n" +
                "AND LD.DOCSTATUS=1 AND LD.REDDOCSTATUS IS NULL\n" +
                "AND RDRQ.TXT02 <> '' AND RDRQ.TXT02 IS NOT NULL";

        log.info(selectOfRefundBill);
        List<Map<String, Object>> rowsOfRefundBill = DBUtil.querySql(selectOfRefundBill);
        log.info(JSON.toJSONString(rowsOfRefundBill));
        if (rowsOfRefundBill != null && !rowsOfRefundBill.isEmpty()) {
            log.info("待处理的退汇记录：" + rowsOfRefundBill.size());
            for (int i=0; i<rowsOfRefundBill.size(); i++) {
                log.info("准备处理第" + (i+1) + "条/共" + rowsOfRefundBill.size() + "条退汇记录");
                try {
                    String logDetailId = (String) rowsOfRefundBill.get(i).get("ID");
                    String srcBizSys = (String) rowsOfRefundBill.get(i).get("SRCBIZSYS");
                    String srcDocNo = (String) rowsOfRefundBill.get(i).get("SRCDOCNO");
                    String redDocId = (String) rowsOfRefundBill.get(i).get("REDDOCID");
                    String redDocNo = (String) rowsOfRefundBill.get(i).get("REDDOCNO");
                    String reason = (String) rowsOfRefundBill.get(i).get("REDDOCREASON");
                    String operatorId = (String) rowsOfRefundBill.get(i).get("OPERATOR");
                    Date operatedOn = (Date) rowsOfRefundBill.get(i).get("OPERATEDON");
                    String logInfoId = (String) rowsOfRefundBill.get(i).get("LOGID");
                    String txt02 = (String) rowsOfRefundBill.get(i).get("TXT02");
                    String msg1 = "准备更新待付池退汇结果：ID=" + logDetailId + ", 来源系统=" + srcBizSys +", 来源单据编号=" + srcDocNo + ", 退汇流水号=" + redDocNo + ", 原因=" + reason;
                    log.info(msg1);
                    logService.info(srcDocNo, msg1);
                    String updateSql = "update JTGKPAYMENTDETAIL set DOCSTATUS=4,REDDOCSTATUS=1,TIMESTAMPS_LASTCHANGEDON=?6,REDDOCID=?1,REDDOCNO=?2,CANCELREASON=?5,OPERATEDON=?3,OPERATOR=?4, REDDOCRESPONSE=?7, REDDOCPROCESSON=?8 where DOCSTATUS=1 and REDDOCSTATUS IS NULL and ID='" + logDetailId + "'";
                    String msg2 = updateSql + ", ?1=" + redDocId + ", ?2=" + redDocNo + ", ?3=" + operatedOn + ", ?4=" + operatorId + ", ?5=" + reason + ", ?6=" + now + ", ?7=" + txt02 + ", ?8=" + now;
                    log.info(msg2);
                    logService.info(srcDocNo, msg2);
                    int count = DBUtil.executeUpdateSQL(updateSql, redDocId, redDocNo, operatedOn, operatorId, reason, now, txt02, now);
                    log.info("受影响行数：" + count);
                    logService.info(srcDocNo, "受影响行数：" + count);

                    if (count > 0) {
                        // 更新JTGKPAYMENTINFO
                        String updateSql2 = "update JTGKPAYMENTINFO set TXT20='退汇' where ID='" + logInfoId + "'";
                        log.info(updateSql2);
                        logService.info(srcDocNo, updateSql2);
                        int count2 = DBUtil.executeUpdateSQL(updateSql2);
                        log.info("受影响行数：" + count2);
                        logService.info(srcDocNo, "受影响行数：" + count2);
                    }
                } catch (Throwable ex) {
                    log.error("待付池付款结果处理退汇过程发生异常：" + JSON.toJSONString(rowsOfRefundBill.get(i)) + "\n" + ex.toString());
                }
            }
        }
    }
}
