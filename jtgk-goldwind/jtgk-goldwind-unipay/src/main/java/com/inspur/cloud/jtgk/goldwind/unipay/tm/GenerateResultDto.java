package com.inspur.cloud.jtgk.goldwind.unipay.tm;

import com.alibaba.fastjson.JSON;

/**
 * 生单结果
 */
public class GenerateResultDto {
    /**
     * 处理是否成功
     */
    private Boolean result;
    public void setResult(Boolean result) {
        this.result = result;
    }
    public Boolean getResult() {
        return this.result;
    }

    /**
     * 处理失败说明
     */
    private String message;
    public void setMessage(String message) {
        this.message = message;
    }
    public String getMessage() {
        return this.message;
    }

    /**
     * 付款单ID
     */
    private String docId;
    public void setDocId(String docId) {
        this.docId = docId;
    }
    public String getDocId() {
        return this.docId;
    }

    /**
     * 付款单号
     */
    private String docNo;
    public void setDocNo(String docNo) {
        this.docNo = docNo;
    }
    public String getDocNo() {
        return this.docNo;
    }

    public GenerateResultDto() {}

    /**
     * 方法执行成功时返回结果（有详细说明）
     * @param docId 付款单ID
     * @param docNo 付款单号
     * @return 返回结果
     */
    public static GenerateResultDto ok(String docId, String docNo) {
        GenerateResultDto result = new GenerateResultDto();
        result.setResult(true);
        result.setMessage(null);
        result.setDocId(docId);
        result.setDocNo(docNo);
        return result;
    }

    /**
     * 方法处理失败时返回结果（有详细说明）
     * @param message 详细说明
     * @return 返回结果
     */
    public static GenerateResultDto error(String message) {
        GenerateResultDto result = new GenerateResultDto();
        result.setResult(false);
        result.setMessage(message);
        return result;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
