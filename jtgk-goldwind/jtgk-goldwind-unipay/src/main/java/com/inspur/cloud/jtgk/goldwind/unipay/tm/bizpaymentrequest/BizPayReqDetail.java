package com.inspur.cloud.jtgk.goldwind.unipay.tm.bizpaymentrequest;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 业务明细
 */
@Setter
@Getter
public class BizPayReqDetail {
    private String ID;
    /**
     * 项目ID
     */
    private String Project;
    /**
     * 合同ID
     */
    private String Contract;
    /**
     * 合同编号
     */
    private String ContractNo;
    /**
     * 合同金额
     */
    private BigDecimal ContractAmount;
    /**
     * 订单ID
     */
    private String PurchaseOrderID;
    /**
     * 订单编号
     */
    private String PurchaseOrderNo;
    /**
     * 订单金额
     */
    private BigDecimal PurchaseOrderAmount;
    /**
     * 付款计划ID
     */
    private String PaymentPlanID;
    /**
     * 付款计划编号
     */
    private String PaymentPlanNo;
    /**
     * 付款计划金额
     */
    private BigDecimal PaymentPlanAmount;
    /**
     * 业务应付金额
     */
    private BigDecimal BizPayableAmount;
    /**
     * 业务已付金额
     */
    private BigDecimal BizPaidAmount;
    /**
     * 消费明细ID
     */
    private String BizCardExpenseDetailID;
    /**
     * 资金计划ID
     */
    private String FundPlanIds;
    /**
     * 资金计划编号
     */
    private String FundPlanNos;
    /**
     * 业务事项ID
     */
    private String BizItem;
    /**
     * 款项性质ID
     */
    private String FundNature;
    /**
     * 现金流量项目ID
     */
    private String CashFlowItem;
    /**
     * 币种ID
     */
    private String Currency;
    /**
     * 申请金额
     * 必填
     */
    private BigDecimal RequestAmount;
    /**
     * 详细说明
     */
    private String Description;
    /**
     * 汇率
     */
    private BigDecimal ExchangeRate;
    /**
     * 收款单位
     */
    private String ReceivingUnit;
    /**
     * 收款单位名称
     */
    private String ReceivingUnitName;
    /**
     * （期望）结算方式
     */
    private String ExpSettleWay;
    /**
     * 本次收票金额
     */
    private BigDecimal InvoiceAmount;
    /**
     * 已收票金额
     */
    private BigDecimal ReceivedInvoiceAmount;
    /**
     * 来源业务系统
     */
    private String SrcBizSys;
    /**
     * 来源单据类型主键
     */
    private String SrcDocTypeID;
    /**
     * 来源单据类型编号
     */
    private String SrcDocTypeCode;

    private String ExpenseItem;

    private String SrcDocID;

    private String SrcBizID;

    private String SrcDocNo;

    private String RequestDept;

    /**
     * 银行借款借据ID
     */
    private String bankIou;
    /**
     * 银行借款借据号
     */
    private String bankIouNo;
    /**
     * 银行借据还款计划ID
     */
    private String bankIouPlanId;
    /**
     * 银行借款还本金额
     */
    private BigDecimal bankIouAmount;
    /**
     * 银行借款还息金额
     */
    private BigDecimal bankIouInterest;
    /**
     * 银行借款还款释放授信金额
     */
    private BigDecimal bankIouReleaseAmt;

    /**
     * 票据类型
     */
    private String payBillType;
    /**
     * 付款票据ID
     */
    private String payBillId;
    /**
     * 付款票据编号
     */
    private String payBillNo;

    /**
     * 债券ID
     */
    private String bondId;
    /**
     * 债券代码
     */
    private String bondCode;
    /**
     * 债券还本金额
     */
    private BigDecimal bondAmount;
    /**
     * 债券还息金额
     */
    private BigDecimal bondInterest;
    /**
     * 债券还款释放授信金额
     */
    private BigDecimal bondReleaseAmt;

    /**
     * 信用证ID
     */
    private String lcId;
    /**
     * 信用证编号
     */
    private String lcNo;
    /**
     * 信用证到单ID
     */
    private String lcDocId;
    /**
     * 信用证单据编号
     */
    private String lcDocNo;
    /**
     * 信用证到单金额
     */
    private BigDecimal lcDocAmount;

    /**
     * 扣款金额
     */
    private BigDecimal debitAmount;

    private Integer NUM01;
    private Integer NUM02;
    private Integer NUM03;
    private Integer NUM04;
    private Integer NUM05;
    private Date Time01;
    private Date Time02;
    private Date Time03;
    private Date Time04;
    private Date Time05;
    private BigDecimal Amt01;
    private BigDecimal Amt02;
    private BigDecimal Amt03;
    private BigDecimal Amt04;
    private BigDecimal Amt05;
    private BigDecimal Amt06;
    private BigDecimal Amt07;
    private BigDecimal Amt08;
    private BigDecimal Amt09;
    private BigDecimal Amt10;
    private BigDecimal Amt11;
    private BigDecimal Amt12;
    private BigDecimal Amt13;
    private BigDecimal Amt14;
    private BigDecimal Amt15;
    private String FK01;
    private String FK02;
    private String FK03;
    private String FK04;
    private String FK05;
    private String FK06;
    private String FK07;
    private String FK08;
    private String FK09;
    private String FK10;
    private String FK11;
    private String FK12;
    private String FK13;
    private String FK14;
    private String FK15;
    private String FK16;
    private String FK17;
    private String FK18;
    private String FK19;
    private String FK20;
    private String TXT01;
    private String TXT02;
    private String TXT03;
    private String TXT04;
    private String TXT05;
    private String TXT06;
    private String TXT07;
    private String TXT08;
    private String TXT09;
    private String TXT10;
    private String TXT11;
    private String TXT12;
    private String TXT13;
    private String TXT14;
    private String TXT15;
    private String TXT16;
    private String TXT17;
    private String TXT18;
    private String TXT19;
    private String TXT20;
}