package com.inspur.cloud.jtgk.goldwind.unipay.tm.bizpaymentrequest;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付明细
 */
@Setter
@Getter
public class BizPayReqReceiver {
    private String ID;
    /**
     * 付款账户
     * 如果接口为空，取主表
     */
    private String PayAccount;
    /**
     * 付款账号
     * 如果接口为空，取主表
     */
    private String PayAccountNo;
    /**
     * 付款户名
     * 如果接口为空，取主表
     */
    private String PayAccountName;
    /**
     * 收款单位ID
     * 一般往来支付为往来单位ID，调拨类的为行政组织ID
     */
    private String ReceivingUnit;
    /**
     * 收款单位名称
     * 往来单位付款不允许空
     */
    private String ReceivingUnitName;
    /**
     * 员工ID
     * 为考虑支持异构系统对私支付，内部员工ID不做强制要求
     */
    private String Staff;
    /**
     * 员工编号
     */
    private String StaffCode;
    /**
     * 员工姓名
     * 内部员工付款不允许空
     */
    private String StaffName;
    /**
     * 收款账户
     * 对公付款为往来单位账户或调拨调入账户（账户信息BFBankAccountsID）
     * 对私付款为员工个人报销账户
     */
    private String ReceivingAccount;
    /**
     * 收款账号
     * 对公付款为往来单位账号或调拨调入账号，对私付款为员工个人报销账号
     * 内部员工付款不允许空
     */
    private String ReceivingAccountNo;
    /**
     * 收款户名
     * 内部员工付款不允许空
     */
    private String ReceivingAccountName;
    /**
     * 收款银行ID
     */
    private String ReceivingAccountBank;
    /**
     * 收款银行名称
     */
    private String ReceivingAccountBankName;
    /**
     * 收款银行联行号
     */
    private String ReceivingAccountBankNo;
    /**
     * 对方国家ID
     * 内部员工付款不允许空
     */
    private String ReciprocalCountry;
    /**
     * 对方省份ID
     */
    private String ReciprocalProvince;
    /**
     * 对方省份名称
     * 内部员工付款不允许空
     */
    private String ReciprocalProvinceName;
    /**
     * 对方城市ID
     */
    private String ReciprocalCity;
    /**
     * 对方城市名称
     * 内部员工付款不允许空
     */
    private String ReciprocalCityName;
    /**
     * 是否同城
     */
    private Boolean IsIdenticalCity;
    /**
     * 是否同行
     */
    private Boolean IsIdenticalBank;
    /**
     * 收款人地址
     */
    private String ReceiverAddr;
    /**
     * 收款人电话
     */
    private String ReceiverTel;
    /**
     * 收款人SWIFT码
     */
    private String ReceiverSwiftCode;
    /**
     * 收款人类型
     * 1 个人、2 公司、3 政府机构、4 汇款
     */
    private Integer BeneficiaryType;
    /**
     * 收款人身份
     * 1 居民、2 非居民、3 汇款
     */
    private Integer BeneficiaryStatus;
    /**
     * 业务事项ID
     * 如果接口值空：如果主表不为空，取主表值
     */
    private String BizItem;
    /**
     * 现金流量项目ID
     */
    private String CashFlowItem;
    /**
     * 是否公务卡报销
     */
    private Boolean IsBizCardExpense;
    /**
     * 期望付款日期
     */
    private Date ExpPayDate;
    /**
     * 实际付款日期
     * 补录（主扣）类型
     */
    private Date ActualPayDate;
    /**
     * 期望结算方式ID
     * 如果接口值空：如果主表不为空，取主表值
     */
    private String ExpSettleWay;
    /**
     * 是否银企直联
     * false：否，true：是
     */
    private Boolean IsBankCommPay;
    /**
     * 是否加急
     * false：普通，true：加急
     * 如果接口值空：如果主表不为空，取主表值
     */
    private Boolean IsUrgent;
    /**
     * 对私标志/对方性质
     * 1：往来单位，2：内部员工
     * 如果接口值空：如果主表不为空，取主表值
     */
    private Integer PrivateFlag;
    /**
     * 是否对私账户
     * true：是，false：否
     * 对方性质为往来单位时允许为true或false
     */
    private Boolean IsPrivateAccount;
    /**
     * 付款账户币种ID
     * 如果接口值空&如果主表不为空，取主表值
     * 必填
     */
    private String Currency;
    /**
     * 申请金额
     * 付款账户付款金额：
     * 1、同币种付款时为账户实际付款金额
     * 2、跨币种时为预计申请付款金额
     * 必填
     */
    private BigDecimal RequestAmount;
    /**
     * 交易对手币种ID
     * （接口为空时默认为付款币种）
     */
    private String TransCurrency;
    /**
     * 交易汇率
     * 1、同币种付款默认1
     * 2、跨币种付款不允许空
     */
    private BigDecimal TransExchangeRate;
    /**
     * 交易金额
     * 付给对方金额/交易对手金额
     * 1、同币种付款时默认申请金额
     * 2、跨币种付款时不允许空
     */
    private BigDecimal TransAmount;
    /**
     * 资金组织ID
     * 结算办理使用
     */
    private String TreasureOrgID;
    /**
     * 是否结算办理
     * 结算办理使用
     */
    private Boolean IsSettleDeal;
    /**
     * 国际标准用途
     * 结算办理使用
     */
    private String StandardUse;
    /**
     * 流程标识
     * 结算办理使用
     */
    private Integer ProcessFlag;
    /**
     * 办理类型
     * 结算办理使用
     */
    private Integer DealType;
    /**
     * 转账附言
     * 为空取主表
     */
    private String Summary;
    /**
     * 详细说明
     * 为空取主表
     */
    private String Description;
    /**
     * 来源明细ID
     * 如果业务单据有与支付明细对应的明细，同之前的SrcBizID
     * 必填
     */
    private String SrcDocDetailID;
    /**
     * 汇率
     */
    private BigDecimal ExchangeRate;
    /**
     * 境外付款标志
     * 境外付款传1
     */
    private Character OverSeasIdent;
    /**
     * 费用承担方式
     */
    private String CostBearWay;

    /**
     * 20220506 IBAN编号
     */
    private String IBANCode;
    /**
     * 20220506 收款人账户类型编号
     * 01 Checking、02 Savings、03 Cuenta Contable、04 Cta Especial de pago、05 Cuenta Inversiones、06 Otras Operaciones
     */
    private String ReceiverAccType;
    /**
     * 20220506 收款人ID
     */
    private String ReceiverID;
    /**
     * ******** 票据支付方式
     */
    private Integer BILLPAYWAY;
    /** 票据数量 */
    private Integer BILLCOUNT;
    /** 期望开票期限（月） */
    private Integer DUETIME;
    /** 新一代票据 */
    private Character NEWBILLFLAG;
    /** 可分包流转 */
    private Character SPLITFLAG;
    /** 供应链产品 */
    private String SUPPLYCHAINPRODUCTS;

    private String INTRBKACNO;
    private String FUNDPLANNOS;
    private String FUNDPLANIDS;
    private String INNERACCOUNT;
    private String INTRADDR;
    private String INTRSWIFTCODE;
    private String EXCHANGERATECONTRACTCODE;
    private String PAYMENTTRANSCODE;
    private String SUPPLEMENTINFORMATION;
    private String REMITPURPOSE;
    private String VirtualSubAccount;
    private String VirtualSubAccountNo;
    /**
     * 银行总户
     */
    //private String TotalPayAccount;
    private String TOTALPAYACCOUNT;
    /**
     * 银行总户资金组织
     */
    //private String TotalTreasureOrgId;
    private String TOTALTREASUREORGID;

    private String TXT01;
    private String TXT02;
    private String TXT03;
    private String TXT04;
    private String TXT05;
    private String TXT06;
    private String TXT07;
    private String TXT08;
    private String TXT09;
    private String TXT10;
    private String TXT11;
    private String TXT12;
    private String TXT13;
    private String TXT14;
    private String TXT15;
    private String TXT16;
    private String TXT17;
    private String TXT18;
    private String TXT19;
    private String TXT20;
    private Date Time01;
    private Date Time02;
    private Date Time03;
    private Date Time04;
    private Date Time05;
    private BigDecimal Amt01;
    private BigDecimal Amt02;
    private BigDecimal Amt03;
    private BigDecimal Amt04;
    private BigDecimal Amt05;
    private BigDecimal Amt06;
    private BigDecimal Amt07;
    private BigDecimal Amt08;
    private BigDecimal Amt09;
    private BigDecimal Amt10;
    private String FK01;
    private String FK02;
    private String FK03;
    private String FK04;
    private String FK05;
    private String FK06;
    private String FK07;
    private String FK08;
    private String FK09;
    private String FK10;
    private String FK11;
    private String FK12;
    private String FK13;
    private String FK14;
    private String FK15;
    private String FK16;
    private String FK17;
    private String FK18;
    private String FK19;
    private String FK20;
    private Integer NUM01;
    private Integer NUM02;
    private Integer NUM03;
    private Integer NUM04;
    private Integer NUM05;
}