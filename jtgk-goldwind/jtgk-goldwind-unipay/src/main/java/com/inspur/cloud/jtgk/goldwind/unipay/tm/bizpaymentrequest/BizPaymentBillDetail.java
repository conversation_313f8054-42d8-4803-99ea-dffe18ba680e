package com.inspur.cloud.jtgk.goldwind.unipay.tm.bizpaymentrequest;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 票据明细
 */
@Data
public class BizPaymentBillDetail {
    private String id;
    private String parentid;
    /** 支付明细ID */
    private String receiverid;
    /** 票据ID */
    private String billid;
    /** 票据号码 */
    private String billno;
    /** 票据类型 */
    private String billtype;
    /** 票据形式 */
    private Integer billform;
    /** 出票账号 */
    private String draweraccountno;
    /** 出票人 */
    private String drawername;
    /** 承兑人 */
    private String acceptorname;
    /** 收款单位ID */
    private String receivingunit;
    /** 收款单位名称 */
    private String receivingunitname;
    /** 新一代票据 */
    private Character newbillflag;
    /** 子票区间起 */
    private String subbillstartsn;
    /** 子票区间止 */
    private String subbillendsn;
    /** 可分包流转 */
    private Character splitflag;
    /** 票据金额 */
    private BigDecimal billamount;
    /** 本次用票金额 */
    private BigDecimal useamount;
    /** 开票日期 */
    private Date billopendate;
    /** 到期日期 */
    private String billduedate;
    private String avaendorse;
    private String lastindorserid;
    private String lastindorsername;
    private BigDecimal balanceamount;
    private String createdby;
    private Date createdon;
    private String lastChangedby;
    private Date lastChangedon;
    private String payabledetailid;
    private String billpayway;
}
