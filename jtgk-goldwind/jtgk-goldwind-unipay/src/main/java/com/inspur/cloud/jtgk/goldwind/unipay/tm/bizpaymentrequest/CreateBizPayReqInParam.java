package com.inspur.cloud.jtgk.goldwind.unipay.tm.bizpaymentrequest;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 业务支付申请
 */
@Setter
@Getter
public class CreateBizPayReqInParam {
    /**
     * 单据ID，可空
     * 与前端业务系统保持一致时可传递
     */
    private String ID;

    /**
     * 单据编号，可空
     */
    private String DocNo;

    private String RequestID;

    /**
     * 付款依据
     * 1：合同/订单付款；
     * 2：应付付款；
     * 3：其他
     */
    private Integer PaymentBasis;

    /**
     * 单据付款类型
     * 0-Normal：一般付款，
     * 1-OutsourcingFee：合同付款单，
     * 2-TaxPayment：税金缴纳业务；
     * 3-SalaryPayment：薪酬发放业务
     * 4-TravelPayment：差旅结算
     * 5-PositionAllocation：单位头寸调拨
     * （6）ConvertedPay-单位内转付款，
     * （7）AllocatedTransfers-单位下拨申请，
     * （8）CapitalAllocation-单位主动上划，
     * （9）BankDebitNote-单位主扣，
     * （10）TrsyCtrPayment-资金中心付款，
     * （11）TrsyCtrFundAllocation-资金中心下拨
     * （12）TrsyCtrFundTransfer-资金中心调拨
     * （13）TrsyCtrBankDebitNote-资金中心主扣
     */
    private Integer ExpenseDocType;

    /**
     * 是否进共享流程
     * false：否，true：是
     * 只支持ExpenseDocType为0,1,3，4，9
     */
    private Boolean IsSharingProcess;

    /**
     * 是否启用收付中心流程
     * false：否，true：是；
     * 和IsSharingProcess互斥
     */
    private Boolean IsBRPCProcess;

    /**
     * 是否启动共享流程（提交）
     * false：否，true：是
     */
    private Boolean startProcess;

    /**
     * 扩展业务类型（业务收付类型）
     * 业务中台-收付中心-业务收付类型，类型内码
     * IsSharingProcess为是时，必须
     */
    private String ExtBizType;

    /**
     * （业务收付类型的）单据业务类型
     * NFKSGZWCL手工账务处理
     * ZQRZHK债券融资还款
     * JYXFK经营性付款
     * PJDF票据兑付
     * YHJKHK银行借款还款
     * BZJFK保证金付款
     * NZFK内转付款
     * NBDKHKFK内部贷款_还款付款
     * NBDKHXFK内部贷款_还息付款
     * NBZZTCDC内部转账_头寸调出（通过头寸调入通知单报账）
     * NBDKHKZC内部贷款还款转出
     * NBHQTZLXZC内部活期透支利息转出
     */
    private String DocBizType;

    /**
     * 申请单位ID
     * 必填
     */
    private String RequestUnit;

    /**
     * 申请单位名称
     * 必填
     */
    private String RequestUnitName;

    /**
     * 申请部门ID
     */
    private String RequestDept;

    /**
     * 申请部门名称
     */
    private String RequestDeptName;

    /**
     * 付款单位ID
     * 如果付款单位为空：如果核算单位有值就把核算单位对应的行政组织赋值给付款单位，如果核算单位没有值或者找不到对应的行政组织就把申请单位赋值给付款单位
     */
    private String PayUnit;

    /**
     * 付款单位名称
     */
    private String PayUnitName;

    /**
     * 核算单位ID
     */
    private String AccountingUnit;

    /**
     * 付款账户ID
     */
    private String PayAccount;

    /**
     * 付款账号
     */
    private String PayAccountNo;

    /**
     * 付款户名
     */
    private String PayAccountName;

    /**
     * 资金池内部户ID
     * 如果传递了内部账户ID，使用内部账户ID，并同步存储到收款方信息上
     */
    private String InnerAccount;

    /**
     * 资金池内部账号
     * 如果未传递内部账户ID，传递了内部账号，则系统根据内部账号找出对应的内部账户ID，并同步存储到收款方信息上
     */
    private String InnerAccountNo;

    /**
     * 付款人地址
     */
    private String PayerAddr;

    /**
     * 付款人电话
     */
    private String PayerTel;

    /**
     * 付款人SWIFT码
     */
    private String PayerSwiftCode;

    /**
     * 业务事项ID
     */
    private String BizItem;

    /**
     * 现金流量项目ID
     */
    private String CashFlowItem;

    /**
     * 期望付款日期
     * 必填
     */
    private Date ExpPayDate;

    /**
     * 实际付款日期
     * 补录（主扣）类型
     */
    private Date ActualPayDate;

    /**
     * 期望结算方式ID
     */
    private String ExpSettleWay;

    // 是否银企直联：不接受传入

    /**
     * 是否加急
     * false：普通，true：加急
     */
    private Boolean IsUrgent;

    /**
     * 付款账户币种ID
     * 必填
     */
    private String Currency;

    /**
     * 申请金额
     * 付款账户付款金额：
     * 3、同币种付款时为账户实际付款金额
     * 4、跨币种时为预计申请付款金额
     * 必填
     */
    private BigDecimal RequestAmount;

    /**
     * 交易币种ID
     * 交易对手币种
     * （接口为空时默认为付款币种）
     */
    private String TransCurrency;

    /**
     * 交易汇率
     * 3、同币种付款默认1
     * 4、跨币种付款不允许空
     */
    private BigDecimal TransExchangeRate;

    /**
     * 交易金额
     * 付给对方金额/交易对手金额
     * 3、同币种付款时默认申请金额
     * 4、跨币种付款时不允许空
     */
    private BigDecimal TransAmount;

    /**
     * 对方性质
     * 1：往来单位，2：内部员工
     * 必填
     */
    private Integer PrivateFlag;

    /**
     * 是否主扣
     * false：否，true：是
     */
    private Boolean Isdeduction;

    /**
     * 资金用途
     * BFCodeItems.SetID='335b496a-d8c1-477a-93e7-5e2976088e10'
     */
    private String FundsUse;

    /**
     * 转账附言
     * 12字节（超长截取）
     * 必填
     */
    private String Summary;

    /**
     * 详细说明
     */
    private String Description;

    /**
     * 来源业务系统
     * 上游业务系统SU编号
     * 业务公共->业务配置->菜单定义
     * 必填
     */
    private String SrcBizSys;

    /**
     * 来源单据类型主键
     * 上游单据类型主键
     * 业务公共->业务配置->业务配置->业务种类
     */
    private String SrcDocType;

    /**
     * 来源业务类型主键
     * 上游业务类型主键
     */
    private String SrcBizType;

    /**
     * 来源单据主键
     * 必填，最长36位
     */
    private String SrcDocID;

    /**
     * 来源单据编号
     */
    private String SrcDocNo;

    //private String SrcBizID;
    //private Boolean IsAllowSplit;
    //private Boolean IsWagesPay;

    /**
     * 计划外
     */
    private String PlanOut;

    /**
     * 申请人
     * isSharingProcess为true进共享且StartProcess时，此人为流程发起人
     * 必填
     */
    private String RequestUser;

    /**
     * 申请人名称
     * 必填
     */
    private String RequestUserName;

    /**
     * 申请日期
     * 必填
     */
    private Date RequestDate;

    /**
     * 支付明细
     */
    private List<BizPayReqReceiver> BizPayReqReceivers;

    /**
     * 业务明细
     */
    private List<BizPayReqDetail> BizPayReqDetails;

    /**
     * 票据明细
     */
    private List<BizPaymentBillDetail> BizPaymentBillDetails;

    /**
     * 发票明细
     */
    //private List<BizReqInvoiceDetail> BizReqInvoiceDetails;

    private String TXT01;
    private String TXT02;
    private String TXT03;
    private String TXT04;
    private String TXT05;
    private String TXT06;
    private String TXT07;
    private String TXT08;
    private String TXT09;
    private String TXT10;
    private String TXT11;
    private String TXT12;
    private String TXT13;
    private String TXT14;
    private String TXT15;
    private String TXT16;
    private String TXT17;
    private String TXT18;
    private String TXT19;
    private String TXT20;
    private Integer NUM01;
    private Integer NUM02;
    private Integer NUM03;
    private Integer NUM04;
    private Integer NUM05;
    private BigDecimal Amt01;
    private BigDecimal Amt02;
    private BigDecimal Amt03;
    private BigDecimal Amt04;
    private BigDecimal Amt05;
    private BigDecimal Amt06;
    private BigDecimal Amt07;
    private BigDecimal Amt08;
    private BigDecimal Amt09;
    private BigDecimal Amt10;
    private BigDecimal Amt11;
    private BigDecimal Amt12;
    private BigDecimal Amt13;
    private BigDecimal Amt14;
    private BigDecimal Amt15;
    private BigDecimal Amt16;
    private BigDecimal Amt17;
    private BigDecimal Amt18;
    private BigDecimal Amt19;
    private BigDecimal Amt20;
    private BigDecimal Amt21;
    private BigDecimal Amt22;
    private BigDecimal Amt23;
    private BigDecimal Amt24;
    private BigDecimal Amt25;
    private Date Time01;
    private Date Time02;
    private Date Time03;
    private Date Time04;
    private Date Time05;
    private String FK01;
    private String FK02;
    private String FK03;
    private String FK04;
    private String FK05;
    private String FK06;
    private String FK07;
    private String FK08;
    private String FK09;
    private String FK10;
    private String FK11;
    private String FK12;
    private String FK13;
    private String FK14;
    private String FK15;
    private String FK16;
    private String FK17;
    private String FK18;
    private String FK19;
    private String FK20;
    private String FK21;
    private String FK22;
    private String FK23;
    private String FK24;
    private String FK25;
}
