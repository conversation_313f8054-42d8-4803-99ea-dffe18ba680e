package com.inspur.cloud.jtgk.goldwind.unipay.tm.bizpaymentrequest;

import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanBillEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanDetailEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPaymentInfoEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.tm.GenerateResultDto;
import com.inspur.idd.log.api.controller.LogService;

import java.util.List;

/**
 * 内部接口：业务支付申请
 */
public interface JfskBizPayRequestService {
    /**
     * 由待付池生成业务支付申请
     * @param logService 接口日志
     * @param logEntity 待付池
     * @return 生单结果
     */
    GenerateResultDto generate(LogService logService, JfskPaymentInfoEntity logEntity);

    /**
     * 由付款安排表生成业务支付申请
     * @param logService 接口日志
     * @param docNo 单据编号
     * @param jtgkPayPlanDetail 付款安排明细
     * @return 生单结果
     */
    GenerateResultDto generate(LogService logService, String docNo, JfskPayPlanDetailEntity jtgkPayPlanDetail, String note);

    /**
     * 由票据补录表生成业务支付申请
     * @param logService 接口日志
     * @param docNo 单据编号
     * @param details 付款安排明细
     * @param bills 背书票据明细
     * @return 生单结果
     */
    GenerateResultDto generate(LogService logService, String docNo, List<JfskPayPlanDetailEntity> details, List<JfskPayPlanBillEntity> bills, String note);
}
