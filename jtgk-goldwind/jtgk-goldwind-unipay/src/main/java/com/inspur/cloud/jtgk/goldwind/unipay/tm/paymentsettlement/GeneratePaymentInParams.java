package com.inspur.cloud.jtgk.goldwind.unipay.tm.paymentsettlement;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 单位上划/调拨单
 */
@Getter
@Setter
public class GeneratePaymentInParams {
    /**
     * 单据状态（1制单，11完成）
     */
    private Integer DocStatus;
    /**
     * 单位来源（1申请，2补录，3业务支付申请，4银行回单，5银行代付，
     * 6上存款变动通知，7冲账，8导入，9结算平台，10银行扣款单，
     * 11银行存单，12内部贷款还款，13下拨款项退回，14资金池上划申请）
     */
    private Integer DocSrc;
    /**
     * 单据内码（可选）
     */
    private String ID;
    /**
     * 单据编号（可选）
     */
    private String DocNo;
    /**
     * 付款单位ID（行政组织-必填）
     */
    private String PayUnit;
    /**
     * 付款单位名称（必填）
     */
    private String PayUnitName;
    /**
     * 付款账户ID（非票据付款时必填）
     */
    private String PayAccount;
    /**
     * 付款账号（非票据付款时必填）
     */
    private String PayAccountNo;
    /**
     * 付款户名（非票据付款时必填）
     */
    private String PayAccountName;
    /**
     * 收款单位ID（往来单位）
     */
    private String ReceivingUnit;
    /**
     * 收款单位名称
     */
    private String ReceivingUnitName;
    /**
     * 收款账户ID（往来单位账户）
     */
    private String ReceivingAccount;
    /**
     * 收款账号
     */
    private String ReceivingAccountNo;
    /**
     * 收款户名
     */
    private String ReceivingAccountName;
    /**
     * 收款银行ID（银行定义）
     */
    private String ReceivingAccountBank;
    /**
     * 收款银行联行号（直联跨行必填）
     */
    private String ReceivingAccountBankNo;
    /**
     * 收款银行名称
     */
    private String ReceivingAccountBankName;
    /**
     * 收款国家ID（国家地区）
     */
    private String ReceivingCountry;
    /**
     * 付款金额（必填）
     */
    private BigDecimal PaymentAmount;
    /**
     * 汇率（必填）
     */
    private BigDecimal ExchangeRate;
    /**
     * 期望付款日期（生成到制单状态的申请单时必填）
     */
    private Date ExpectPayDate;
    /**
     * 申请单位ID（行政组织）
     */
    private String RequestUnit;
    /**
     * 申请单位名称
     */
    private String RequestUnitName;
    /**
     * 是否银企直联付款
     */
    private Boolean IsBankCommPay;
    /**
     * 是否同城
     */
    private Boolean IsIdenticalCity;
    /**
     * 是否同行
     */
    private Boolean IsIdenticalBank;
    /**
     * 是否加急
     */
    private Boolean IsUrgent;
    /**
     * 往来性质（1对公，2对私）
     */
    private Boolean ReciprocalNature;
    /**
     * 付款币种（必填）
     */
    private String Currency;
    /**
     * 结算方式ID（必填）
     */
    private String SettleWay;
    /**
     * 来源单据ID
     */
    private String SrcDocID;
    /**
     * 来源单据编号
     */
    private String SrcDocNo;
    /**
     * 摘要（必填）
     */
    private String Summary;
    /**
     * 详细说明
     */
    private String Description;
    /**
     * 申请人ID（gspuser用户表）
     */
    private String Applicant;
    /**
     * 申请人名称
     */
    private String ApplicantName;
    /**
     * 申请日期
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date ApplyDate;
    /**
     * 省ID（行政区划）
     */
    private String ReciprocalProvince;
    /**
     * 省名称（直联必填）
     */
    private String ReciprocalProvinceName;
    /**
     * 市ID（行政区划）
     */
    private String ReciprocalCity;
    /**
     * 市名称（直联必填）
     */
    private String ReciprocalCityName;
    /**
     * 业务类型（1付款结算，2资金上划，3资金下拨，4头寸调拨，5单位调拨单，6银行扣款单）
     */
    private Integer BizType;
    /**
     * 生成时机（1申请，2补录）
     */
    private Integer GeneratedTime;
    /**
     * 调入单位ID（行政组织-上划、调拨的收款方）
     */
    private String TransferInUnit;
    /**
     * 调入单位名称
     */
    private String TransferInUnitName;
    /**
     * 调入账户ID（账户）
     */
    private String TransferInAccount;
    /**
     * 调入账号
     */
    private String TransferInAccountNo;
    /**
     * 调入户名
     */
    private String TransferInAccountName;
    /**
     * 是否下拨
     */
    private Boolean IsPayDown;

    /**
     * 制单单位ID（行政组织）
     */
    private String CreateUnit;
    /**
     * 银行处理时间（生成补录单据或生成完成状态的单据时必填）
     */
    private Date BankHandelTime;
    /**
     * 员工ID（行政人员）
     */
    private String Staff;
    /**
     * 员工名称
     */
    private String StaffName;
    /**
     * 是否公务卡报销
     */
    private Boolean IsBizCardExpense;
    /**
     * 消费明细主键
     */
    private String BizCardExpenseDetailID;
    /**
     * 业务单位ID（行政组织）
     */
    private String BizUnit;
    /**
     * 业务事项
     */
    private String BizItem;
    /**
     * 款项性质
     */
    private String FundsUse;
    /**
     * 是否自动办理（业务支付自动办理）
     */
    private Boolean AutoHandled;
    /**
     * 是否原始业务推送
     */
    private Boolean IsOrigDocDirectPush;
    /**
     * 是否冲账
     */
    private Boolean IsReverse;
    /**
     * 是否红单
     */
    private Boolean IsRedDoc;
    /**
     * 冲账人ID
     */
    private String Reverser;
    /**
     * 冲账人名称
     */
    private String ReverserName;
    /**
     * 冲账日期
     */
    private Date ReverserDate;
    /**
     * 冲账原因
     */
    private String ReverserReason;
    /**
     * 流出现金流量项目
     */
    private String CashFlowItem;
    /**
     * 商户编号
     */
    private String POSNumber;
    /**
     * 是否任务自动生成
     */
    private Boolean IsGenAuto;
    /**
     * 付款人地址
     */
    private String PayerAddr;
    /**
     * 付款人电话
     */
    private String PayerTel;
    /**
     * 付款人Swift码
     */
    private String PayerSwiftCode;
    /**
     * 收款人地址
     */
    private String ReceiverAddr;
    /**
     * 收款人电话
     */
    private String ReceiverTel;
    /**
     * 收款人Swift码
     */
    private String ReceiverSwiftCode;
    /**
     * IBAN码
     */
    private String IbanCode;
    /**
     * 费用承担方式(DEBT-付款人，CRED-收款人，SHAR-共同）
     */
    private String CostBearWay;
    /**
     * 代理银行ID
     */
    private String AgentBank;
    /**
     * 代理行账号
     */
    private String IntrBkAcNo;
    /**
     * 代理行地址
     */
    private String IBnkNmAdr;
    /**
     * 代理行Swift码
     */
    private String IBnkSWIFTNo;
    /**
     * 交易币种ID
     */
    private String TransCurrency;
    /**
     * 交易金额
     */
    private BigDecimal TransAmount;
    /**
     * 交易汇率
     */
    private BigDecimal TransExchangeRate;
    /**
     * 预计付款金额
     */
    private BigDecimal EstimatePaymentAmount;
    /**
     * 自动支付状态（1待自动支付，2已自动支付处理，3自动支付失败）
     */
    private Integer AutoPaymentStatus;
    /**
     * 是否对私账户
     */
    private Boolean IsPrivateAccount;
    /**
     * 来源业务系统
     */
    private String SrcBizSys;
    /**
     * 来源业务单据类型主键
     */
    private String SrcBizDocTypeID;
    /**
     * 来源业务单据主键
     */
    private String SrcBizDocID;
    /**
     * 来源业务单据类型主键
     */
    private String SrcBizDocNo;
    /**
     * 来源业务主键
     */
    private String SrcBizID;
    /**
     * 收款人类型（1个人，2公司，3政府机构，4汇款）
     */
    private Integer BeneficiaryType;
    /**
     * 收款人身份（1居民，2非居民，3汇款）
     */
    private Integer BeneficiaryStatus;
    /**
     * 银行流水号
     */
    private String BankFlowNo;
    /**
     * 认领状态（1已发布，2认领中，3认领完成，4撤回认领，5制单）
     */
    private Integer ClaimStatus;
    /**
     * 是否认领单据
     */
    private Boolean IsClaimDoc;
    /**
     * 无需生成财务凭证
     */
    private Boolean WXSCCWPZ;
    /**
     * 来源流程框架
     */
    private Boolean IsTask;
    /**
     * 动作：生成单据到制单-ZD，提交-TJ
     * 虚拟字段
     */
    private String Action;
    /**
     * 资金组织
     */
    private String ZJZZ;
    /**
     * 是否跨级
     */
    private Boolean IsKJ;
    /**
     * 是否共享
     */
    private Boolean IsShare;
    /**
     * 内部账户ID
     */
    private String InnerAccount;

    /**
     * 实付本币金额
     */
    private BigDecimal ActualPayLocalCurrAmt;
    /**
     * 总户ID
     */
    private String TotalAccountID;
    /**
     * 总户编号
     */
    private String TotalAccountNo;
    /**
     * 总户名称
     */
    private String TotalAccountName;
    /**
     * 核算组织ID
     */
    private String AccountingUnit;
    /**
     * 业务收付类型
     */
    private Integer SrcExpenseDocType;
    /**
     * 来源扩展业务类型
     */
    private String SrcExtBizType;
    /**
     * 来源原始业务类型ID
     */
    private String SrcOrigBizTypeID;
    /**
     * 来源原始业务类型编号
     */
    private String SrcOrigBizTypeCode;
    /**
     * 业务明细列表
     */
    private List<PaymentSettlementDetail> PaymentSettlementDetails;
    /**
     * 是否境外
     */
    private Boolean SFJW;
    /**
     * 汇率合约码
     */
    private String HLHYM;
    /**
     * 国际收支交易编码
     */
    private String GJSZJYBM;
    /**
     * 补充资料
     */
    private String BCZL;
    /**
     * 汇款用途
     */
    private String RemitPurpose;
    /**
     * 计划外
     */
    private String JHW;
    /**
     * 汇率种类
     */
    private String HLZL;
    /**
     * 交易汇率种类
     */
    private String JYHLZL;
}
