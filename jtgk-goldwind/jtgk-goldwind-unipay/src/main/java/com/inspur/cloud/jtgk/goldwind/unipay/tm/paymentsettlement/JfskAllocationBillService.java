package com.inspur.cloud.jtgk.goldwind.unipay.tm.paymentsettlement;

import com.inspur.cloud.jtgk.goldwind.unipay.tm.GenerateResultDto;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPaymentInfoEntity;
import com.inspur.idd.log.api.controller.LogService;

/**
 * 内部接口：单位调拨单
 */
public interface JfskAllocationBillService {
    /**
     * 由待付池生成单位调拨单
     * @param logService 接口日志
     * @param logEntity 待付池记录
     * @return 生成结果
     */
    GenerateResultDto generate(LogService logService, JfskPaymentInfoEntity logEntity);

    GenerateResultDto generateNew(LogService logService, JfskPaymentInfoEntity logEntity);
}
