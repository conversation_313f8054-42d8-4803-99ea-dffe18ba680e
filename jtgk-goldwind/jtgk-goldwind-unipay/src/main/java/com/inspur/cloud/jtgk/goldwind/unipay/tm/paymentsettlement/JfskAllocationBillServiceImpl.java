package com.inspur.cloud.jtgk.goldwind.unipay.tm.paymentsettlement;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.R;
import com.inspur.cloud.jtgk.goldwind.unipay.tm.GenerateResultDto;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPaymentInfoEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.DBUtil;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.RpcUtils;
import com.inspur.edp.internalservice.api.proxy.InternalServiceProxy;
import com.inspur.idd.log.api.controller.LogService;
import io.iec.edp.caf.common.JSONSerializer;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.rpc.api.service.RpcClient;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 内部接口：单位调拨单
 */
@Service
@Slf4j
public class JfskAllocationBillServiceImpl implements JfskAllocationBillService {
    /**
     * 由待付池生成单位调拨单
     * @param logService 接口日志
     * @param logEntity 待付池记录
     * @return 生成结果
     */
    @Override
    public GenerateResultDto generate(LogService logService, JfskPaymentInfoEntity logEntity) {
        GenerateResultDto resultDto = GenerateResultDto.error(null);
        try {
            GeneratePaymentInParams inParam = convertParam(logEntity);
            // 20240124 产品RPC接口需特定补丁开启参数
            List<GeneratePaymentInParams> inParamsList = new ArrayList<>();
            inParamsList.add(inParam);
            List<Object> parameters = new ArrayList<>();
            parameters.add(JSONSerializer.serialize(inParamsList));
            String temp = JSONSerializer.serialize(parameters);
            log.info("产品单位调拨单生单接口入参：" + temp);
            logService.info(logEntity.getSrcDocNo(), "产品单位调拨单生单接口入参：" + temp);
            String url = "com.inspur.gs.tm.cm.paymentsettlementidp.api.service.PaymentSettlementIdpService.batchGenDWDBD";
            String su = "CM";
            RpcClient rpcClient = SpringBeanUtils.getBean(RpcClient.class);
            LinkedHashMap<String, Object> rpcParams = new LinkedHashMap<>();
            rpcParams.put("params", inParamsList);
            List<CreateResultRet> rpcGenerateResultObj = rpcClient.invoke(List.class, url, su, rpcParams, null);
            String rpcGenerateResult = JSONSerializer.serialize(rpcGenerateResultObj);
            
            log.info("产品接口返回结果: " + rpcGenerateResult);
            logService.info(logEntity.getSrcDocNo(), "产品接口返回结果: " + rpcGenerateResult);
            if (!StringUtil.isNullOrEmpty(rpcGenerateResult)) {
                List<CreateResultRet> resultRets = JSON.parseArray(rpcGenerateResult, CreateResultRet.class);
                if (resultRets != null && resultRets.size() > 0) {
                    if (resultRets.get(0).isResult()) {
                        String docId = resultRets.get(0).getDataId();
                        resultDto.setResult(true);
                        resultDto.setDocId(docId);
                        //20240123 docNo未返回
                        String selectDocNo = "select ID,DOCNO from TMPAYMENTSETTLEMENT where ID='" + docId + "'";
                        log.info(selectDocNo);
                        logService.info(logEntity.getSrcDocNo(), selectDocNo);
                        List<Map<String, Object>> rowsOfBill = DBUtil.querySql(selectDocNo);
                        log.info(JSON.toJSONString(rowsOfBill));
                        String docNo = null;
                        if (rowsOfBill != null && rowsOfBill.size() > 0) {
                            docNo = (String) rowsOfBill.get(0).get("DOCNO");
                            log.info("docNo=" + docNo);
                            logService.info(logEntity.getSrcDocNo(), "docNo=" + docNo);
                            resultDto.setDocNo(docNo);
                        }

                        // 更新调拨单扩展
                        String updateSql = "update TMPAYMENTSETTLEMENT set FK01=?1,TXT07=?2,TXT09=?3 where ID=?4";
                        String msg = updateSql + ", ?1=" + logEntity.getSrcPayMethodCode() + ", ?2=" + logEntity.getSrcBizSys() + ", ?3=" + logEntity.getSrcDocId() + ", ?4=" + docId;
                        log.info(msg);
                        logService.info(logEntity.getSrcDocNo(), msg);
                        DBUtil.executeUpdateSQL(updateSql, logEntity.getSrcPayMethodCode(), logEntity.getSrcBizSys(), logEntity.getSrcDocId(), docId);
                        log.info("更新调拨单扩展完成");
                        logService.info(logEntity.getSrcDocNo(), "更新调拨单扩展完成");

                        submit(logService, logEntity.getSrcDocNo(), logEntity.getRequestUnitId(), docId, docNo);
                    } else {
                        resultDto.setResult(false);
                        resultDto.setMessage(resultRets.get(0).getMessage());
                    }
                } else {
                    resultDto.setResult(false);
                    resultDto.setMessage("产品接口未返回有效结果");
                }
            } else {
                resultDto.setResult(false);
                resultDto.setMessage("产品接口未返回结果");
            }
        } catch (Throwable ex) {
            log.error("产品接口执行异常: ", ex);
            String message = RpcUtils.getBizErrorInException(ex);
            resultDto.setResult(false);
            resultDto.setMessage(message);
        }
        return resultDto;
    }

    private R submit(LogService logService, String logDocNo, String payUnitId, String payDocId, String payDocNo) {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("CZID", "TJ");
            // //业务类型：TM_QYFKD、TM_DWSHD、TM_DWXBD、TM_DWDBD
            map.put("FORMTYPE", "TM_DWDBD");
            map.put("DJNM", payDocId );
            map.put("JYMXID", "");
            map.put("DJBH", payDocNo);
            //付款单位ID
            map.put("DWID", payUnitId);
            map.put("CZLX", "");
            String jsonOfMap = JSONSerializer.serialize(map);
            log.error("付款结算单提交接口入参：" + jsonOfMap);
            logService.error(logDocNo, "付款结算单提交接口入参：" + jsonOfMap);
            LinkedHashMap<String, Object> params = new LinkedHashMap<>();
            params.put("map", map);
            RpcClient rpcClient = SpringBeanUtils.getBean(RpcClient.class);
            String fsspResultRet = rpcClient.invoke(String.class,
                    "com.inspur.gs.tm.cm.paymentsettlementidp.api.controller.PaymentSettlementIdpController.PaymentDocJSPTOperation",
                    "CM", params, null);
            log.error("付款结算单提交接口返回：" + fsspResultRet);
            logService.error(logDocNo, "付款结算单提交接口返回：" + fsspResultRet);
            return R.ok();
        } catch (Throwable ex) {
            String message = RpcUtils.getBizErrorInException(ex);
            log.error("付款结算单提交发生异常：", ex);
            logService.error(logDocNo, "付款结算单提交发生异常：" + ExceptionUtils.getStackTrace(ex));
            return R.error(message);
        }
    }

    /**
     * 组织产品接口入参
     * @param dto 共享系统传入的付款信息
     * @return 产品接口入参
     */
    private static GeneratePaymentInParams convertParam(JfskPaymentInfoEntity dto) {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        GeneratePaymentInParams inParam = new GeneratePaymentInParams();
//        /*
//         * 单位来源（1申请，2补录，3业务支付申请，4银行回单，5银行代付，
//         * 6上存款变动通知，7冲账，8导入，9结算平台，10银行扣款单，
//         * 11银行存单，12内部贷款还款，13下拨款项退回，14资金池上划申请）
//         */
        inParam.setDocSrc(99);
        //业务类型（1付款结算，2资金上划，3资金下拨，4头寸调拨，5单位调拨单，6银行扣款单）
//        inParam.setBizType(4);
        //是否下拨
//        inParam.setIsPayDown(false);
        //核算组织
        inParam.setAccountingUnit(dto.getRequestUnitId());
        //期望付款日期（生成到制单状态的申请单时必填）
        if (!StringUtil.isNullOrEmpty(dto.getExpectPayDate())) {
            try {
                Date expPayDate = sdf.parse(dto.getExpectPayDate());
                inParam.setExpectPayDate(expPayDate);//期望付款日期
            } catch (Throwable ex) {
                log.error("期望付款日期无效", ex);
            }
        } else {
            inParam.setExpectPayDate(new Date());
        }
        //是否银企直联付款
        Boolean isBankCommPay = dto.getIsBankCommPay() == null ? false : dto.getIsBankCommPay();
        Boolean isRequiredSettleWay = false;
        String sql = "select CODE from BFSETTLEMENTWAY where ID = '" + dto.getSettlementWayId() + "'";
        List<Map<String, Object>> rowsOfPlanBill = DBUtil.querySql(sql);
        if (rowsOfPlanBill != null && rowsOfPlanBill.size() > 0) {
            String planBillCode = (String) rowsOfPlanBill.get(0).get("CODE");
            if ("01".equals(planBillCode)) {
                isRequiredSettleWay = true;
            }
        }
        inParam.setIsBankCommPay(isBankCommPay && isRequiredSettleWay);
        //是否同城&是否同行
        inParam.setIsIdenticalCity(null);
        inParam.setIsIdenticalBank(null);
        //是否对私账户
        inParam.setIsPrivateAccount(false);
        //往来性质（1对公，2对私）
        inParam.setReciprocalNature(true);
        //是否加急
        inParam.setIsUrgent(false);
        //款项性质
        //inParam.setFundsUse(dto.getFundsUseId());
        //申请单位
        inParam.setRequestUnit(dto.getRequestUnitId());
        inParam.setRequestUnitName(dto.getRequestUnitName());
        inParam.setCreateUnit(dto.getRequestUnitId());
        inParam.setBizUnit(dto.getRequestUnitId());
        //付款单位
        inParam.setPayUnit(dto.getRequestUnitId());
        inParam.setPayUnitName(dto.getRequestUnitName());
        //付款账户
        inParam.setPayAccount(dto.getPayAccountId());
        inParam.setPayAccountNo(dto.getPayAccountNo());
        inParam.setPayAccountName(dto.getPayAccountName());
        //币种
        inParam.setCurrency(dto.getCurrencyId());
        inParam.setTransCurrency(dto.getTransCurrencyId());
        //结算方式
        inParam.setSettleWay(dto.getSettlementWayId());
        //预计付款金额
        inParam.setEstimatePaymentAmount(dto.getRequestAmount());
        //付款金额（必填）
        inParam.setPaymentAmount(dto.getRequestAmount());
        inParam.setTransAmount(dto.getTransAmount());
        //汇率
        inParam.setExchangeRate(dto.getTransExchangeRate());
        inParam.setTransExchangeRate(dto.getTransExchangeRate());
        //调入单位-单位调拨
        inParam.setTransferInUnit(dto.getRequestUnitId());
        inParam.setTransferInUnitName(dto.getRequestUnitName());
        //调入账户
        inParam.setTransferInAccount(dto.getReceivingBankAccountId());
        inParam.setTransferInAccountNo(dto.getReceivingBankAccountNo());
        inParam.setTransferInAccountName(dto.getReceivingBankAccountName());
        //收款银行
        inParam.setReceivingAccountBank(dto.getReceivingBankId());
        inParam.setReceivingAccountBankNo(dto.getReceivingBankNo());
        inParam.setReceivingAccountBankName(dto.getReceivingBankName());
        inParam.setReceivingCountry(dto.getReciprocalCountry());
        //摘要
        inParam.setSummary(dto.getSummary());
        //详细说明
        inParam.setDescription(dto.getDescription());
        //申请人
        inParam.setApplicant(dto.getApplicantId());
        inParam.setApplicantName(dto.getApplicantName());
        if (!StringUtil.isNullOrEmpty(dto.getExpectPayDate())) {
            //申请日期
            try {
                Date applyDate = sdf.parse(dto.getExpectPayDate());
                inParam.setApplyDate(applyDate);
            } catch (Throwable ex) {
                log.error("申请日期无效", ex);
            }
        } else {
            inParam.setApplyDate(new Date());
        }
        inParam.setSrcBizSys(dto.getSrcBizSys());
        inParam.setSrcDocID(dto.getId());
//        inParam.setSrcDocID(dto.getSrcDocId());
        inParam.setSrcDocNo(dto.getSrcDocNo());
//        inParam.setSrcBizDocID(dto.getSrcDocId());
        inParam.setSrcBizDocID(dto.getId());
        inParam.setSrcBizDocNo(dto.getSrcDocNo());
//        inParam.setSrcBizID(dto.getSrcDocId());
        inParam.setSrcBizID(dto.getId());
        inParam.setSrcBizDocTypeID(null);
        //单据状态（1制单，11完成）
//        inParam.setDocStatus(1);
        //生成时机（1申请，2补录）
//        inParam.setGeneratedTime(1);
        //是否共享
        inParam.setIsShare(true);
        //来源流程框架
//        inParam.setIsTask(true);
        //2025-02-22 付款结算单自动提交时返回单据数据不存在，需要在制单后单独提交
//        inParam.setAction("ZD");
        inParam.setAutoHandled(false);
        //是否冲账
        inParam.setIsReverse(false);
        inParam.setIsRedDoc(false);
        //是否任务自动生成
        inParam.setIsGenAuto(true);
        // 自动支付状态（1待自动支付，2已自动支付处理，3自动支付失败）
//        inParam.setAutoPaymentStatus(1);
//        inParam.setPaymentSettlementDetails(null);
        return inParam;
    }



    @Override
    public GenerateResultDto generateNew(LogService logService, JfskPaymentInfoEntity logEntity) {
        GenerateResultDto resultDto = GenerateResultDto.error(null);
        try {
            GeneratePaymentInParams inParam = convertParamNew(logEntity);
            // 20240124 产品RPC接口需特定补丁开启参数
            List<GeneratePaymentInParams> inParamsList = new ArrayList<>();
            inParamsList.add(inParam);
            List<Object> parameters = new ArrayList<>();
            parameters.add(JSONSerializer.serialize(inParamsList));
            parameters.add("");
            String temp = JSONSerializer.serialize(parameters);
            log.info("产品单位调拨单生单接口入参：" + temp);
            logService.info(logEntity.getSrcDocNo(), "产品单位调拨单生单接口入参：" + temp);
            String url = "TM/CM/v1906/PaymentSettlementInnerService";
            String method = "BatchAutoSettlement";
            String rpcGenerateResult = InternalServiceProxy.invoke(url, method, parameters, new TypeReference<String>() {
            });

            log.info("产品接口返回结果: " + rpcGenerateResult);
            logService.info(logEntity.getSrcDocNo(), "产品接口返回结果: " + rpcGenerateResult);
            if (!StringUtil.isNullOrEmpty(rpcGenerateResult)) {
                List<CreateResultRet> resultRets = JSON.parseArray(rpcGenerateResult, CreateResultRet.class);
                if (resultRets != null && resultRets.size() > 0) {
                    if (resultRets.get(0).isResult()) {
                        String docId = resultRets.get(0).getDataId();
                        resultDto.setResult(true);
                        resultDto.setDocId(docId);
                        //20240123 docNo未返回
                        String selectDocNo = "select ID,DOCNO from TMPAYMENTSETTLEMENT where ID='" + docId + "'";
                        log.info(selectDocNo);
                        logService.info(logEntity.getSrcDocNo(), selectDocNo);
                        List<Map<String, Object>> rowsOfBill = DBUtil.querySql(selectDocNo);
                        log.info(JSON.toJSONString(rowsOfBill));
                        String docNo = null;
                        if (rowsOfBill != null && rowsOfBill.size() > 0) {
                            docNo = (String) rowsOfBill.get(0).get("DOCNO");
                            log.info("docNo=" + docNo);
                            logService.info(logEntity.getSrcDocNo(), "docNo=" + docNo);
                            resultDto.setDocNo(docNo);
                        }

                        // 更新调拨单扩展
                        String updateSql = "update TMPAYMENTSETTLEMENT set FK01=?1,TXT07=?2,TXT09=?3 where ID=?4";
                        String msg = updateSql + ", ?1=" + logEntity.getSrcPayMethodCode() + ", ?2=" + logEntity.getSrcBizSys() + ", ?3=" + logEntity.getSrcDocId() + ", ?4=" + docId;
                        log.info(msg);
                        logService.info(logEntity.getSrcDocNo(), msg);
                        DBUtil.executeUpdateSQL(updateSql, logEntity.getSrcPayMethodCode(), logEntity.getSrcBizSys(), logEntity.getSrcDocId(), docId);
                        log.info("更新调拨单扩展完成");
                        logService.info(logEntity.getSrcDocNo(), "更新调拨单扩展完成");

                        submit(logService, logEntity.getSrcDocNo(), logEntity.getRequestUnitId(), docId, docNo);
                    } else {
                        resultDto.setResult(false);
                        resultDto.setMessage(resultRets.get(0).getMessage());
                    }
                } else {
                    resultDto.setResult(false);
                    resultDto.setMessage("产品接口未返回有效结果");
                }
            } else {
                resultDto.setResult(false);
                resultDto.setMessage("产品接口未返回结果");
            }
        } catch (Throwable ex) {
            log.error("产品接口执行异常: ", ex);
            String message = RpcUtils.getBizErrorInException(ex);
            resultDto.setResult(false);
            resultDto.setMessage(message);
        }
        return resultDto;
    }

    /**
     * 组织产品接口入参
     * @param dto 共享系统传入的付款信息
     * @return 产品接口入参
     */
    private static GeneratePaymentInParams convertParamNew(JfskPaymentInfoEntity dto) {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        GeneratePaymentInParams inParam = new GeneratePaymentInParams();
//        /*
//         * 单位来源（1申请，2补录，3业务支付申请，4银行回单，5银行代付，
//         * 6上存款变动通知，7冲账，8导入，9结算平台，10银行扣款单，
//         * 11银行存单，12内部贷款还款，13下拨款项退回，14资金池上划申请）
//         */
        inParam.setDocSrc(99);
        //业务类型（1付款结算，2资金上划，3资金下拨，4头寸调拨，5单位调拨单，6银行扣款单）
        inParam.setBizType(4);
        //是否下拨
        inParam.setIsPayDown(false);
        //核算组织
        inParam.setAccountingUnit(dto.getRequestUnitId());
        //期望付款日期（生成到制单状态的申请单时必填）
        if (!StringUtil.isNullOrEmpty(dto.getExpectPayDate())) {
            try {
                Date expPayDate = sdf.parse(dto.getExpectPayDate());
                inParam.setExpectPayDate(expPayDate);//期望付款日期
            } catch (Throwable ex) {
                log.error("期望付款日期无效", ex);
            }
        } else {
            inParam.setExpectPayDate(new Date());
        }
        //是否银企直联付款
        inParam.setIsBankCommPay(dto.getIsBankCommPay() == null ? false : dto.getIsBankCommPay());
        //是否同城&是否同行
        inParam.setIsIdenticalCity(null);
        inParam.setIsIdenticalBank(null);
        //是否对私账户
        inParam.setIsPrivateAccount(false);
        //往来性质（1对公，2对私）
        inParam.setReciprocalNature(true);
        //是否加急
        inParam.setIsUrgent(false);
        //款项性质
        //inParam.setFundsUse(dto.getFundsUseId());
        //申请单位
        inParam.setRequestUnit(dto.getRequestUnitId());
        inParam.setRequestUnitName(dto.getRequestUnitName());
        inParam.setCreateUnit(dto.getRequestUnitId());
        inParam.setBizUnit(dto.getRequestUnitId());
        //付款单位
        inParam.setPayUnit(dto.getRequestUnitId());
        inParam.setPayUnitName(dto.getRequestUnitName());
        //付款账户
        inParam.setPayAccount(dto.getPayAccountId());
        inParam.setPayAccountNo(dto.getPayAccountNo());
        inParam.setPayAccountName(dto.getPayAccountName());
        //币种
        inParam.setCurrency(dto.getCurrencyId());
        inParam.setTransCurrency(dto.getTransCurrencyId());
        //结算方式
        inParam.setSettleWay(dto.getSettlementWayId());
        //预计付款金额
        inParam.setEstimatePaymentAmount(dto.getRequestAmount());
        //付款金额（必填）
        inParam.setPaymentAmount(dto.getRequestAmount());
        inParam.setTransAmount(dto.getTransAmount());
        //汇率
        inParam.setExchangeRate(dto.getTransExchangeRate());
        inParam.setTransExchangeRate(dto.getTransExchangeRate());
        //调入单位-单位调拨
        inParam.setTransferInUnit(dto.getRequestUnitId());
        inParam.setTransferInUnitName(dto.getRequestUnitName());
        //调入账户
        inParam.setTransferInAccount(dto.getReceivingBankAccountId());
        inParam.setTransferInAccountNo(dto.getReceivingBankAccountNo());
        inParam.setTransferInAccountName(dto.getReceivingBankAccountName());
        //收款银行
        inParam.setReceivingAccountBank(dto.getReceivingBankId());
        inParam.setReceivingAccountBankNo(dto.getReceivingBankNo());
        inParam.setReceivingAccountBankName(dto.getReceivingBankName());
        inParam.setReceivingCountry(dto.getReciprocalCountry());
        //摘要
        inParam.setSummary(dto.getSummary());
        //详细说明
        inParam.setDescription(dto.getDescription());
        //申请人
        inParam.setApplicant(dto.getApplicantId());
        inParam.setApplicantName(dto.getApplicantName());
        if (!StringUtil.isNullOrEmpty(dto.getExpectPayDate())) {
            //申请日期
            try {
                Date applyDate = sdf.parse(dto.getExpectPayDate());
                inParam.setApplyDate(applyDate);
            } catch (Throwable ex) {
                log.error("申请日期无效", ex);
            }
        } else {
            inParam.setApplyDate(new Date());
        }
        inParam.setSrcBizSys(dto.getSrcBizSys());
        inParam.setSrcDocID(dto.getId());
//        inParam.setSrcDocID(dto.getSrcDocId());
        inParam.setSrcDocNo(dto.getSrcDocNo());
//        inParam.setSrcBizDocID(dto.getSrcDocId());
        inParam.setSrcBizDocID(dto.getId());
        inParam.setSrcBizDocNo(dto.getSrcDocNo());
//        inParam.setSrcBizID(dto.getSrcDocId());
        inParam.setSrcBizID(dto.getId());
        inParam.setSrcBizDocTypeID(null);
        //单据状态（1制单，11完成）
        inParam.setDocStatus(1);
        //生成时机（1申请，2补录）
        inParam.setGeneratedTime(1);
        //是否共享
        inParam.setIsShare(false);
        //来源流程框架
        inParam.setIsTask(true);
        //2025-02-22 付款结算单自动提交时返回单据数据不存在，需要在制单后单独提交
        inParam.setAction("ZD");
        inParam.setAutoHandled(false);
        //是否冲账
        inParam.setIsReverse(false);
        inParam.setIsRedDoc(false);
        //是否任务自动生成
        inParam.setIsGenAuto(true);
        // 自动支付状态（1待自动支付，2已自动支付处理，3自动支付失败）
        inParam.setAutoPaymentStatus(1);
        inParam.setPaymentSettlementDetails(null);
        return inParam;
    }
}
