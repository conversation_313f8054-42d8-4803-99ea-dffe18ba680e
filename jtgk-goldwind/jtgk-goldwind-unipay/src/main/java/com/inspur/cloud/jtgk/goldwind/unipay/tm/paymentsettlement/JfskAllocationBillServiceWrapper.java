package com.inspur.cloud.jtgk.goldwind.unipay.tm.paymentsettlement;

import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPaymentInfoEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.tm.GenerateResultDto;
import com.inspur.cloud.jtgk.goldwind.unipay.utils.JfskException;
import com.inspur.idd.log.api.controller.LogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 单位调拨单生成服务包装类
 * 为了支持共享事务失败时不创建单据，添加了事务失败检查功能
 */
@Slf4j
@Component
public class JfskAllocationBillServiceWrapper {
    /**
     * 在共享事务中，用于存储已失败的事务ID
     * key=事务ID，value=Boolean.TRUE表示已失败
     */
    private static final Map<String, Boolean> TRANSACTION_FAILED_MAP = new ConcurrentHashMap<>();

    /**
     * 原始单位调拨单服务
     */
    private final JfskAllocationBillService allocationBillService;

    @Autowired
    public JfskAllocationBillServiceWrapper(JfskAllocationBillService allocationBillService) {
        this.allocationBillService = allocationBillService;
    }

    /**
     * 获取当前事务ID
     * @return 当前事务ID，如果不在事务中则返回null
     */
    private String getTransactionId() {
        return Thread.currentThread().getName() + "-" + Thread.currentThread().getId();
    }

    /**
     * 检查当前事务是否已失败
     * @return true表示事务已失败，false表示事务未失败
     */
    public boolean isTransactionFailed() {
        String transactionId = getTransactionId();
        if (transactionId == null) {
            return false;
        }
        Boolean failed = TRANSACTION_FAILED_MAP.get(transactionId);
        return failed != null && failed;
    }

    /**
     * 标记事务已失败
     */
    public void markTransactionFailed() {
        String transactionId = getTransactionId();
        if (transactionId != null) {
            log.info("标记事务已失败：{}", transactionId);
            TRANSACTION_FAILED_MAP.put(transactionId, Boolean.TRUE);
        }
    }

    /**
     * 清除事务失败标记
     * 通常在事务结束时调用
     */
    public void clearTransactionFailedMark() {
        String transactionId = getTransactionId();
        if (transactionId != null) {
            log.info("清除事务失败标记：{}", transactionId);
            TRANSACTION_FAILED_MAP.remove(transactionId);
        }
    }

}