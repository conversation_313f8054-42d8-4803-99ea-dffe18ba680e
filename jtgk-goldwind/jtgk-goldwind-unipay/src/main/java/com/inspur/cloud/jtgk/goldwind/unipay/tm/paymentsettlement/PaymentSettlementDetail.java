package com.inspur.cloud.jtgk.goldwind.unipay.tm.paymentsettlement;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 单位上划/调拨单业务明细
 */
@Getter
@Setter
public class PaymentSettlementDetail {
    /**
     * 项目ID
     */
    private String project;
    /**
     * 合同ID
     */
    private String contract;
    /**
     * 费用项目
     */
    private String expenseItem;
    /**
     * 申请金额
     */
    private BigDecimal requestAmount;
    /**
     * 摘要
     */
    private String summary;
    /**
     * 详细说明
     */
    private String description;
    /**
     * 来源业务系统
     */
    private String srcBizSys;
    /**
     * 来源业务单据类型主键
     */
    private String srcBizDocTypeID;
    /**
     * 来源业务单据类型编号
     */
    private String srcBizDocTypeCode;
    /**
     * 来源业务单据主键
     */
    private String srcBizDocID;
    /**
     * 来源业务单据编号
     */
    private String srcBizDocNo;
    /**
     * 来源业务主键
     */
    private String srcBizID;
    /**
     * 来源业务类型主键
     */
    private String srcBizTypeID;
    /**
     * 来源业务类型编号
     */
    private String srcBizTypeCode;
    /**
     * 款项性质
     */
    private String FundNature;
    /**
     * 资金计划IDs
     */
    private String FundPlanIDs;
    /**
     * 资金计划编号
     */
    private String FundPlanNos;
}
