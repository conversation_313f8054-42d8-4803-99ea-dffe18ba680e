package com.inspur.cloud.jtgk.goldwind.unipay.tm.paymentsettlement;

import lombok.Data;

/**
 * 业务支付申请办理生成企业付款单RPC接口返回结果
 */
@Data
public class PaymentSettlementRpcGenerateResult {
    private String cerbase64STRING;
    private String checkResultDetail;
    private String dataId;
    private String dataNo;
    private String dataType;
    private String ifUserConfirm;
    private String message;
    private String payUnit;
    private String result;
    private String seemRptPayDetail;
    private String seemRptPayHtml;
    private String seemRptPayRequestHtml;
    private String signmessage;
    private String srcDetailId;
    private String srcDocId;
    private String srcDocNo;
    private String srcDocOrder;
    private String value;
}
