package com.inspur.cloud.jtgk.goldwind.unipay.utils;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.Base64;

/**
 * 回调消息解密工具类
 * 无需额外依赖
 */
@Slf4j
public class CallbackUtil {

    private static final Charset CHARSET = StandardCharsets.UTF_8;
    private byte[] aesKey;
    private String token;
    private String companyId;

    private CallbackUtil() {
    }

    /**
     * 解密报文
     *
     * @param signature
     * @param timestamp
     * @param nonce
     * @param postData
     * @return
     * @throws AesException
     */
    public String decryptMSg(String signature, String timestamp, String nonce, String postData) throws AesException {
        String msgSignature = getSHA1(this.token, timestamp, nonce, postData);
        if (!signature.equals(msgSignature)) {
            throw new AesException(-40001);
        } else {
            byte[] original;
            byte[] networkOrder;
            try {
                Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
                SecretKeySpec key_spec = new SecretKeySpec(this.aesKey, "AES");
                IvParameterSpec iv = new IvParameterSpec(Arrays.copyOfRange(this.aesKey, 0, 16));
                cipher.init(2, key_spec, iv);
                networkOrder = Base64.getDecoder().decode(postData);
                original = cipher.doFinal(networkOrder);
            } catch (Exception var13) {
                var13.printStackTrace();
                throw new AesException(-40007);
            }

            String decryptMsg;
            String _companyId;
            try {
                byte[] bytes = decode(original);
                networkOrder = Arrays.copyOfRange(bytes, 16, 20);
                int xmlLength = this.recoverNetworkBytesOrder(networkOrder);
                decryptMsg = new String(Arrays.copyOfRange(bytes, 20, 20 + xmlLength), CHARSET);
                _companyId = new String(Arrays.copyOfRange(bytes, 20 + xmlLength, bytes.length), CHARSET);
            } catch (Exception var12) {
                throw new AesException(-40008);
            }

            if (!_companyId.equals(this.companyId)) {
                log.error("创建实例companyId:{},解析companyId:{}", this.companyId, _companyId);
                throw new AesException(-40005);
            } else {
                return decryptMsg;
            }
        }
    }


    /**
     * @param token
     * @param timestamp
     * @param nonce
     * @param encrypt
     * @return
     * @throws AesException
     */
    public static String getSHA1(String token, String timestamp, String nonce, String encrypt) throws AesException {
        try {
            String[] array = new String[]{token, timestamp, nonce, encrypt};
            StringBuffer sb = new StringBuffer();
            Arrays.sort(array);

            for (int i = 0; i < 4; ++i) {
                sb.append(array[i]);
            }

            String str = sb.toString();
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            md.update(str.getBytes());
            byte[] digest = md.digest();
            StringBuffer hexstr = new StringBuffer();
            String shaHex = "";

            for (int i = 0; i < digest.length; ++i) {
                shaHex = Integer.toHexString(digest[i] & 255);
                if (shaHex.length() < 2) {
                    hexstr.append(0);
                }

                hexstr.append(shaHex);
            }

            return hexstr.toString();
        } catch (Exception var12) {
            var12.printStackTrace();
            throw new AesException(-40003);
        }
    }

    private int recoverNetworkBytesOrder(byte[] orderBytes) {
        int sourceNumber = 0;

        for (int i = 0; i < 4; ++i) {
            sourceNumber <<= 8;
            sourceNumber |= orderBytes[i] & 255;
        }

        return sourceNumber;
    }

    /**
     * 初始化解密工具
     *
     * @param key
     * @param token
     * @param companyIdOrTenantId
     * @return
     */
    public static CallbackUtil init(String key, String token, String companyIdOrTenantId) {
        CallbackUtil callbackUtil = new CallbackUtil();
        callbackUtil.aesKey = Base64.getDecoder().decode(key);
        callbackUtil.token = token;
        callbackUtil.companyId = companyIdOrTenantId;
        return callbackUtil;
    }


    public static byte[] decode(byte[] decrypted) {
        int pad = decrypted[decrypted.length - 1];
        if (pad < 1 || pad > 32) {
            pad = 0;
        }
        return Arrays.copyOfRange(decrypted, 0, decrypted.length - pad);
    }

    /**
     * 异常
     */
    static class AesException extends RuntimeException {
        public static final int OK = 0;
        public static final int ValidateSignatureError = -40001;
        public static final int ParseXmlError = -40002;
        public static final int ComputeSignatureError = -40003;
        public static final int IllegalAesKey = -40004;
        public static final int ValidateCompanyIdError = -40005;
        public static final int EncryptAESError = -40006;
        public static final int DecryptAESError = -40007;
        public static final int IllegalBuffer = -40008;
        private int code;

        private static String getMessage(int code) {
            switch (code) {
                case -40008:
                    return "解密后得到的buffer非法";
                case -40007:
                    return "aes解密失败";
                case -40006:
                    return "aes加密失败";
                case -40005:
                    return "companyId校验失败";
                case -40004:
                    return "encodingAesKey非法";
                case -40003:
                    return "sha加密生成签名失败";
                case -40002:
                    return "xml解析失败";
                case -40001:
                    return "签名验证错误";
                default:
                    return null;
            }
        }

        public int getCode() {
            return this.code;
        }

        public AesException(int code) {
            super(getMessage(code));
            this.code = code;
        }
    }


//    public static void main(String[] args) {
//        /**
//         String callbackStr = "";
//         JSONObject jsonObject = JSONObject.parseObject(callbackStr);
//         String message = jsonObject.getString("message");
//         String companyIdOrTenantId = jsonObject.getString("tenantId");
//         CallbackUtil callbackUtil = CallbackUtil.init("key", "token", companyIdOrTenantId);
//         String decryptMSg = callbackUtil.decryptMSg(jsonObject.getString("signature"), jsonObject.getString("timestamp"), jsonObject.getString("nonce"), message);
//         System.out.println(decryptMSg);
//         * */
//    }
}
