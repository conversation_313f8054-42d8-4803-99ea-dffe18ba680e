package com.inspur.cloud.jtgk.goldwind.unipay.utils;

import io.iec.edp.caf.commons.transaction.JpaTransaction;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.*;

@Slf4j
public class DBUtil {
    public static List<Map<String, Object>> querySql(String sql, Object...param){
        EntityManager entityManager = SpringBeanUtils.getBean(EntityManager.class);
        Query nativeQuery = entityManager.createNativeQuery(sql).
                unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        for(int i=0;i<param.length;i++) {
            nativeQuery.setParameter(i+1,param[i]);
        }
        List<Map<String, Object>> queryList = nativeQuery.getResultList();
        if (queryList == null || queryList.size() == 0) {
            return queryList;
        }
        List<Map<String, Object>> resultList = new ArrayList<>();
        // 将PostgreSQL返回结果的字段名称转为大写格式
        for (Map<String, Object> queryRow : queryList) {
            Map<String, Object> resultRow = new HashMap<>();
            for (String s : new HashSet<>(queryRow.keySet())) {
                String upperCase = s.toUpperCase();
                if (!resultRow.containsKey(upperCase)) {
                    resultRow.put(upperCase, queryRow.get(s));
                }
            }
            resultList.add(resultRow);
        }
        return resultList;
    }

    public static int executeUpdateSQL(String upSQL, Object...param) {
        EntityManager entityManager = SpringBeanUtils.getBean(EntityManager.class);
        // 20240129 取消EntityManagerFactory
        JpaTransaction transaction = JpaTransaction.getTransaction();
        try {
            transaction.begin();
            Query query = entityManager.createNativeQuery(upSQL);
            for(int i=0;i<param.length;i++) {
                query.setParameter(i+1,param[i]);
            }
            int a =0;
            a=query.executeUpdate();
            transaction.commit();
            return a;
        } catch (Throwable e) {
            transaction.rollback();
            log.error(ExceptionUtils.getStackTrace(e));
            log.error(e.toString());
            return 0;
        }
    }

    public static int executeWithoutTrans(String upSQL, Object...param) {
        EntityManager entityManager = SpringBeanUtils.getBean(EntityManager.class);
        try {
            Query query = entityManager.createNativeQuery(upSQL);
            for(int i=0;i<param.length;i++) {
                query.setParameter(i+1,param[i]);
            }
            int a =0;
            a=query.executeUpdate();
            return a;
        } catch (Throwable e) {
            log.error(e.toString());
            return 0;
        }
    }
}
