package com.inspur.cloud.jtgk.goldwind.unipay.utils;

import io.iec.edp.caf.commons.exception.CAFRuntimeException;
import io.iec.edp.caf.commons.exception.ExceptionLevel;

public class JfskException extends CAFRuntimeException {
    public JfskException(String message) {
        super("goldwind", null, message, null);
    }
    public JfskException(String message, Exception innerException) {
        super("goldwind", null, message, innerException);
    }
    public JfskException(String serviceUnitCode, String exceptionCode, String message, Exception innerException) {
        super(serviceUnitCode, exceptionCode, message, innerException);
    }
    public JfskException(String message, Throwable e) {
        super("goldwind", "", message, new String[0], (Exception) e, ExceptionLevel.Warning, true);
    }
}
