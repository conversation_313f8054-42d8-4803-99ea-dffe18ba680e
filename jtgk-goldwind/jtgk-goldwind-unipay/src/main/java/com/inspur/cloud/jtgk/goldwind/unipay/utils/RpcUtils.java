package com.inspur.cloud.jtgk.goldwind.unipay.utils;

import io.netty.util.internal.StringUtil;

public class RpcUtils {
    /**
     * 获取捕获异常中的业务信息
     * 在内部存在RPC调用时，扔出的异常会套两层“RPC调用异常”，不可直接拿外层的message。单独的web api请求可能前端处理了此种情况
     * @param e 捕获的异常
     * @return Message
     */
    public static String getBizErrorInException(Throwable e) {
        String message = e.getMessage();
        Throwable innerException = e;
        if(innerException.getCause() != null){
            do {
                innerException = innerException.getCause();
                message = innerException.getMessage();
            } while(innerException.getCause() != null);
        }
        if (StringUtil.isNullOrEmpty(message)) {
            message = innerException.toString();
        }
        return message;
    }
}
