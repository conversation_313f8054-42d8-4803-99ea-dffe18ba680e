package com.inspur.cloud.jtgk.goldwind.unipay.utils;

import io.iec.edp.caf.boot.context.CAFContext;
import io.iec.edp.caf.commons.utils.SpringBeanUtils;
import io.iec.edp.caf.core.session.CafSession;
import io.iec.edp.caf.scheduler.api.data.GspScheduleLog;
import io.iec.edp.caf.scheduler.api.data.GspSchedulerLogLevel;
import io.iec.edp.caf.scheduler.api.logger.RtfSchedulerLogger;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

@Slf4j
public class SchedulerLoggerUtil {
    public static void info(String jobId, String jobName, String message) {
        RtfSchedulerLogger schedulerLogger = SpringBeanUtils.getBean(RtfSchedulerLogger.class);
        try {
            GspScheduleLog scheduleLog = new GspScheduleLog();
            scheduleLog.setLoglevel(GspSchedulerLogLevel.error);
            scheduleLog.setJobDetailId(jobId);
            scheduleLog.setJobDetailName(jobName);
            scheduleLog.setId(null);
            CafSession currentSession = CAFContext.current.getCurrentSession();
            String activityId = currentSession.getItems().get("activeId");
            scheduleLog.setActivityId(activityId);
            scheduleLog.setLogTime(new Date());
            scheduleLog.setMessage(message);
            scheduleLog.setScheduleAction("任务正在执行");
            scheduleLog.setTenantId(currentSession.getTenantId() + "");
            schedulerLogger.info(scheduleLog);
        } catch (Exception ex) {
            log.error("计划任务日志记录异常：", ex);
        }
    }

    public static void error(String jobId, String jobName, String message) {
        RtfSchedulerLogger schedulerLogger = SpringBeanUtils.getBean(RtfSchedulerLogger.class);
        try {
            GspScheduleLog scheduleLog = new GspScheduleLog();
            scheduleLog.setLoglevel(GspSchedulerLogLevel.error);
            scheduleLog.setJobDetailId(jobId);
            scheduleLog.setJobDetailName(jobName);
            scheduleLog.setId(null);
            CafSession currentSession = CAFContext.current.getCurrentSession();
            String activityId = currentSession.getItems().get("activeId");
            scheduleLog.setActivityId(activityId);
            scheduleLog.setLogTime(new Date());
            scheduleLog.setMessage(message);
            scheduleLog.setScheduleAction("任务正在执行");
            scheduleLog.setTenantId(currentSession.getTenantId() + "");
            schedulerLogger.error(scheduleLog);
        } catch (Exception ex) {
            log.error("计划任务日志记录异常：", ex);
        }
    }
}
