import com.alibaba.fastjson.JSON;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.AssignBillsResult;
import com.inspur.cloud.jtgk.goldwind.unipay.dto.RD;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JfskPayPlanDetailEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.entity.JtgkBillReceivableInventoryEntity;
import com.inspur.cloud.jtgk.goldwind.unipay.service.impl.JfskUnipayAssignBillsServiceImpl;
import org.junit.Test;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

public class JfskUnipayAssginBillsServiceTest {
    @Test
    public void assginBills() throws Exception {
        List<JfskPayPlanDetailEntity> details = new ArrayList<JfskPayPlanDetailEntity>() {
            {
                add(createJfskPayPlanDetailEntity("B", "B", "************-B", new BigDecimal(400)));
                add(createJfskPayPlanDetailEntity("A01","A", "************-A", new BigDecimal(100)));
                add(createJfskPayPlanDetailEntity("A02", "A", "************-A", new BigDecimal(200)));
                add(createJfskPayPlanDetailEntity("C01", "C", "************-C", new BigDecimal(250)));
                add(createJfskPayPlanDetailEntity("C02", "C", "************-C", new BigDecimal(250)));
            }
        };
        List<JtgkBillReceivableInventoryEntity> bills = new ArrayList<JtgkBillReceivableInventoryEntity>() {
            {
                add(createBillReceivableInventoryEntity("BILL-02", "060001", "100000", "2025-01-01", "2025-05-01", new BigDecimal(1000), new BigDecimal(400), '1'));
                add(createBillReceivableInventoryEntity("BILL-01", "000001", "100000", "2025-03-01", "2025-06-01", new BigDecimal(1000), new BigDecimal(1000), '1'));
            }
        };
        AssignBillsResult result1 = JfskUnipayAssignBillsServiceImpl.assignBills(details, bills);
        RD<AssignBillsResult> result = RD.ok(null, result1);
        String jsonOfResult = JSON.toJSONString(result);
        System.out.println("背书票据分配结果：" + jsonOfResult);
    }

    private JtgkBillReceivableInventoryEntity createBillReceivableInventoryEntity(
            String billNo, String subbillStartSn, String subbillEndSn, String billOpenDate, String billDueDate,
            BigDecimal billAmount, BigDecimal syje,
            Character splitFlag
    ) throws Exception {
        DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        JtgkBillReceivableInventoryEntity entity = new JtgkBillReceivableInventoryEntity();
        entity.setId(billNo);
        entity.setBillNo(billNo);
        entity.setSubbillStartSn(subbillStartSn);
        entity.setSubbillEndSn(subbillEndSn);
        entity.setBillOpenDate(sdf.parse(billOpenDate));
        entity.setBillDueDate(sdf.parse(billDueDate));
        entity.setBillType("AC01");
        entity.setBillForm(1);
        entity.setNewbillFlag('1');
        entity.setSplitFlag(splitFlag);
        entity.setEndFlag("EM00");
        entity.setSplitFlag('1');
        entity.setIsEbc('1');
        entity.setBillStatus("20");
        entity.setBillAmt(billAmount);
        entity.setSyje(syje);
        return entity;
    }

    private JfskPayPlanDetailEntity createJfskPayPlanDetailEntity(
            String id, String oppUnitId, String oppAccountId, BigDecimal requestAmount
    ) {
        JfskPayPlanDetailEntity entity = new JfskPayPlanDetailEntity();
        entity.setId(id);
        entity.setParentId("BILL-0001");
        entity.setParentId2("BILL-0002");
        entity.setParentId3("BILL-0003");
        entity.setLogInfoId("LOG-0001");
        entity.setSrcBizSys("SFS");
        entity.setSrcDocId("SFS-ID-0001");
        entity.setSrcDocType("付款计划");
        entity.setSrcDocNo("TEST-SFS-0001");
        entity.setPayUnitId("2000");
        entity.setPayUnitName("东方电子股份有限公司");
        entity.setPayAccountId("a971b382-c72b-1e8a-eb0d-c2b36ad2c6fa");
        entity.setPayAccountNo("1606020309022102997");
        entity.setPayAccountName("东方电子股份有限公司");
        entity.setReceivingUnitId(oppUnitId);
        entity.setReceivingUnitCode("CODE-" + oppUnitId);
        entity.setReceivingUnitName("NAME-" + oppUnitId);
        entity.setReceivingBankAccountId(oppAccountId);
        entity.setReceivingBankAccountNo("CODE-" + oppAccountId);
        entity.setReceivingBankAccountName("NAME-" + oppAccountId);
        entity.setReceivingBankId("************");
        entity.setReceivingBankNo("************");
        entity.setReceivingBankName("中国银行股份有限公司北京通州运河商务区绿色支行");
        entity.setCurrencyId("CNY");
        entity.setRequestAmount(requestAmount);
        entity.setSummary("2000-" + oppUnitId + "-" + oppAccountId + "-测试");
        entity.setSettlementWayId("604ed628-38fa-11eb-a48e-03ceebbd3c6e");
        entity.setBillPayWay(2);
        entity.setDocStatus(2);
        entity.setAmt01(new BigDecimal(999999.99));
        return entity;
    }
}
